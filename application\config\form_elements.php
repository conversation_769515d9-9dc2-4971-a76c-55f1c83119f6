<?php

defined('BASEPATH') OR exit('No direct script access allowed');

$config['nationality'] = ['Indian','American','Canadian','Australian','American Indian','African','Austrian','Argentinian','Bangladesh','Belgian','Brazilian','British','British(OCI)','Cambodian','Chilean','Chinese','Colombian','Czech','Danish','Finnish','French','German','Greek', 'Hungarian','Icelandic','Indonesian','Iranian','Iraq','Irish','Israeli','Jamaican','Japanese','Korea','Malaysian', 'Malawi', 'Mexican','Moroccan','Myanmar','Nepalese','North Korea','Norwegian','Oman','Peruvian','Philippine','Polish','Portuguese','Rumanian','Russian','Saudi, Saudi Arabian','Spain','Scottish','Serbian','Scottish','Singapore','Slovak','South Korea',"Sri Lankan",'Swedish','Swiss','Thai','Tunisian','Turkish','Vietnamese','Welsh','Yugoslav','OCI','UAE','USA','USA-OCI','Indian-OCI','UK','Other'];

$config['country'] = ['India','America','Canada','Australia','Britain','Brunei','Africa','Austria','Argentina','Bangladesh',' Belgium','Brazil','Cambodia','Chile','China','Colombia','Czech','Denmark','Finland','France','Germany','Greece', 'Hungary','Iceland','Indonesia','Iran','Iraq','Ireland','Israel','Jamaica','Japan','North Korea', 'Malaysia', 'Malawi', 'Mexico','Morocco','Myanmar','Nepal','Norway','Oman','Peru','Philippine','Poland','Portugal','Romania','Russia','Saudi, Saudi Arabia','Scottland','Serbia','Singapore','South Korea','Slovakia',"Spain",'Sweden','Switzerland','Thailand','Tunisia','Turkey','Vietnam','Welsh','UAE','yugoslavia','OCI'];

$config['states'] = ['Andhra Pradesh','Arunachal Pradesh','Assam','Bihar','Chhattisgarh','Delhi','Goa','Gujarat','Haryana',' Himachal Pradesh','Jharkhand','Karnataka','Kerala','Madhya Pradesh','Maharashtra','Manipur','Meghalaya','Mizoram','Nagaland','Odisha','Punjab', 'Rajasthan','Sikkim','Tamil Nadu','Telangana','Tripura','Uttar Pradesh','Uttarakhand','West Bengal'];

$config['religions'] = ['Hindu','Christianity','Islam','Jainism','Buddhism','Judaism','Atheism','Shinto','Taoism','Candomble','Santeria','Paganism','Bahai','Zoroastrianism','Rastsfari','Unitarianism','Sikh','Korean','Other','Not Applicable'];

$config['languages'] = ['Kannada','Hindi','Telugu','Tamil','Malayalam','Bengali','English','Gujarati','Kashmiri','Konkani','Coorgi','Kshtriya','Marathi','Maithili','Oriya','Punjabi','Rajasthani','Sankethi','Sindhi','Tulu','Urdu','Baduga','Assamese','Kodava','Manipuri','Marwari','Sourashtra','Korean','Other'];

$config['employmentType'] = ['Regular', 'Consultant'];

$config['country_codes'] =["+91", "******","+93","+355","+213","******","+376","+244",  "******","******","+54","+374","+297", "+247","+61","+672","+43","+994","******","+973","+880","******","******","+375","+32","+501",  "+229", "******","+975",  "+591","+387","+267","+55",  "+246","******","+673",  "+359","+226","+257","+855","+237","+1",  "+238","+ 345","+236","+235","+56", "+86", "+61","+61","+57","+269","+242", "+243","+682","+506","+385","+53","+599","+537",  "+420","+45","+246","+253","******","******","+670","+56","+593","+20", "+503","+240","+291","+372","+251","+500","+298","+679","+358","+33",  "+596","+594","+689","+241", "+220",  "+995","+49","+233", "+350","+30",  "+299","+1 473","+590","+1 671","+502","+224",  "+245","+595",  "+509", "+504","+852","+36","+354","+62","+98","+964","+353","+972",  "+39", "+225","+1 876","+81", "+962",  "+7 7","+254", "+686","+965",  "+996","+856","+371",  "+961","+266","+231","+218", "+423","+370","+352","+853","+389","+261","+265",  "+60","+960","+223","+356", "+692","+596","+222","+230","+262","+52",  "+691","+1 808","+373","+377",  "+976","+382","+1664","+212","+95","+264","+674", "+977", "+31","+599","+1 869", "+687","+64","+505","+227", "+234","+683","+672","+850","+1 670","+47",  "+968","+92","+680", "+970","+507",  "+675","+595","+51","+63","+48",  "+351","+1 787","+974", "+262","+40","+7",  "+250",  "+685", "+378","+966","+221","+381",  "+248","+232","+65","+421","+386","+677","+27","+500","+82","+34", "+94","+249", "+597","+268","+46",  "+41","+963", "+886",  "+992","+255","+66","+670","+228","+690","+676", "+1 868","+216","+90",  "+993","+1 649","+688",  "+1 340","+256",  "+380","+971","+44","+1","+598","+998","+678","+58","+84","+1 808","+681","+967", "+260",  "+255","+263"];
$config['after_school_sports_options'] = ['Swimming', 'Karate', 'Rifle Shooting', 'Cricket', 'Basketball', 'Soccer', 'Badminton', 'Tennis'];

$config['blood_groups'] = ['A +ve','B +ve','O +ve','AB +ve','A -ve','B -ve','O -ve','AB -ve','A1+ve','A1-ve', 'Unknown'];
$config['to_meet'] = ['Principal', 'Manager', 'Co-ordinator'];
$config['numbers'] = [
					'Principal'=>'',
					'Manager'=>'',
					'Co-ordinator'=>''
					];

$config['images'] = [
					'female' => 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/female_profile_photo.png',
					'male' => 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/male_profile_photo.png'
					];

$config['marital_status'] = ['Married','Divorced','Single'];

$config['subjectColors'] = [
	[ 'subject'=>'2nd Language', 'value' => '00FF00' ],
	[ 'subject'=>'3rd Language', 'value' => 'FFFF99' ],
	[ 'subject'=>'Biology', 'value' => '#EC0609' ],
	[ 'subject'=>'Chemistry', 'value' => '#313BE3' ],
	[ 'subject'=>'Computer Science', 'value' => '#52D14C' ],
	[ 'subject'=>'English', 'value' => '#3CC92F' ],
	[ 'subject'=>'EVS', 'value' => '#D51F8D' ],
	[ 'subject'=>'Maths', 'value' => '#FB8901' ],
	[ 'subject'=>'Other Activity', 'value' => '#D7060C' ],
	[ 'subject'=>'Practicals', 'value' => '#399DBF' ],
	[ 'subject'=>'Physical Activity', 'value' => '#400547' ],
	[ 'subject'=>'Physics', 'value' => '#FE1EE4' ],
	[ 'subject'=>'Science', 'value' => '#F96700' ],
	[ 'subject'=>'Music/Dance', 'value' => '#989217' ],
	[ 'subject'=>'Aerobics', 'value' => '#5467FF' ],
	[ 'subject'=>'Hindi', 'value' => '#FFF712' ],
	[ 'subject'=>'Social Studies', 'value' => 'FF6633' ]
];

$config['lbr_cardColors'] = [
	[ 'name'=>'Golden yellow', 'value' => '#FFDF00' ],
	[ 'name'=>'Platinum', 'value' => '#D4AF37' ],
	[ 'name'=>'Silver', 'value' => '#C0C0C0' ],
	[ 'name'=>'Bronze', 'value' => '#CD7F32' ],
	[ 'name'=>'Brass', 'value' => '#b5a642' ],
];

$config['inventory_attributes'] = ['brand','colour','height','width','length','weight','flavour', 'type', 'size'];

$config['asset_attributes']=['brand','colour','height','width','length','dimensions','weight','serial no'];

$config['procurement_units']= ["Boxes", "Packets", "Pieces", "Numbers", "Rolls", "Litres", "Bottles", "Sheets", "Bundles", "Kilograms", "Grams", "Dozen", "Days", "Man Days", "Man Weeks", "Unitless"];

$config['user_provisioning']['sections'] = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];

$config['sub_language'] = ['NA','Kannada','Sanskrit','Hindi','English','French','German','Tamil'];

$config['concession_name'] = ['nps_agara'];

$config['bank_names'] = ['HDFC Bank Ltd', 'Canara bank','Axis bank Ltd','ICICI Bank Ltd','Vijaya Bank','State Bank of India','Punjab National Bank','Corporation Bank','Kotak Mahindra Bank','Citibank','IDBI Bank','Karur vysya bank ltd', 'Karnataka Gramin Bank', 'Bank of Baroda','The Karnataka Bank Ltd','The Federal bank ltd','Bank of Maharashtra','Indian Bank','Syndicate Bank','Bank of India','Allahabad Bank','HSBC Bank Ltd','Indian Overseas Bank', 'IDFC First Bank', 'Dhanlaxmi','Andhra Bank','The Lakshmi Vilas Bank Ltd','Sree Charan Bank','Deutsche Bank','Yes Bank','Standard Chartered Bank','UCO Bank','Union Bank of India','IndusInd','Industrial Development Bank of India','Saraswat Bank','ING Vysya Bank','The South Indian Bank','Central Bank of India','Apex Bank','Post Office','Savings Bank','State Bank of Patiala','KBL','Oriental Bank of Commerce', 'Ujjivan Bank', 'Swarna Bharathi Sahakara Bank' ,'SVC Co-Operative Bank','Paytm Payments Bank', 'Development Bank of Singapore','Jana Small Finance bank','City Union Bank','The National Co-op Bank', 'The Rajajinagar Co-op Bank','Bandhan Bank','Punjab & Sind Bank','The Karnataka state Co-op Bank','The Bangalore City Co-op Bank','Development Credit Bank','Maheshwari Souharda Credit Co-op Bank'];

$config['ticketing_status'] = ['Open', 'Response_from_parent', 'Closed'];

$config['qualification_list'] = ["BA","BArch.", "BAS", "BBA", "BBS", "BCA", "BCom", "BDS", "BE","BEM", "BFA", "BFD", "BMS", "BPharma", "BPT", "BSc", "BTech","BTTM", "LLB",  "MA", "MBA", "MCA", "MCom", "MPharma", "MSc","MTech", "Phd"];

$config['degree_type'] = ["Graduate", "Post Graduate", "Doctorate", "Diploma"];

$config['periods_list']=["P1","P2","P3","P4","P5","P6"];

$config['vaccine_master']=["HEPATITIS B","COVID-19 I Dose","COVID-19 II Dose","COVID-19 III Dose","POLIO","RABIES", "FLU", "SHINGLES", "TETANUS"];

$config['payroll_columns'] = [
        [
            "column_name" => "monthly_gross",
            "type" => "earnings",
            "display_name" => "Monthly CTC",
            "order_by" => "0",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "0",
            "total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            "default" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "slab",
            "type" => "earnings",
            "display_name" => "Slab",
            "order_by" => "1",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "0",
            "total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            "default" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "yearly_gross",
            "type" => "earnings",
            "display_name" => "Yearly Basic Gross",
            "order_by" => "2",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "0",
            "total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            "default" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "yearly_ctc",
            "type" => "earnings",
            "display_name" => "Yearly CTC",
            "order_by" => "3",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "0",
            "total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            "default" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "monthly_basic_salary",
            "type" => "earnings",
            "display_name" => "Monthly Basic Salary",
            "order_by" => "4",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            "default" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "staff_da",
            "type" => "earnings",
            "display_name" => "DA",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            "calculate_number_of_days_present" => "1",
            "order_by" => "5",
            "mandatory" => "0",
            "include_ctc" => "0",
            "upload_csv" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "staff_hra",
            "type" => "earnings",
            "display_name" => "HRA",
            "order_by" => "5",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "conveyance",
            "type" => "earnings",
            "display_name" => "Conveyance",
            "order_by" => "6",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "special_allowance",
            "type" => "earnings",
            "display_name" => "Special Allowance",
            "order_by" => "7",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "medical_allowance",
            "type" => "earnings",
            "display_name" => "Medical Allowance",
            "order_by" => "8",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "lta",
            "type" => "earnings",
            "display_name" => "LTA",
            "order_by" => "9",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "cca",
            "type" => "earnings",
            "display_name" => "CCA",
            "order_by" => "10",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "transport_allowance",
            "type" => "earnings",
            "display_name" => "Transport Allowance",
            "order_by" => "11",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "1",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "extra_allowance",
            "type" => "earnings",
            "display_name" => "Extra Allowance",
            "order_by" => "12",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "1",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "pf_for_employer",
            "type" => "earnings",
            "display_name" => "PF from Employer",
            "order_by" => "13",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "hra_fixed",
            "type" => "earnings",
            "display_name" => "Additional HRA",
            "order_by" => "14",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "1",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "co_ordinator_allowance",
            "type" => "earnings",
            "display_name" => "Co-ordinator Allowance",
            "order_by" => "15",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "1",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "ib_retention_allowance",
            "type" => "earnings",
            "display_name" => "Ib / Retention Allowance",
            "order_by" => "16",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "1",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "house_master_allowance",
            "type" => "earnings",
            "display_name" => "House Master Allowance",
            "order_by" => "17",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "1",
            "outside_ctc_salary_strucutre" => "1",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "advance",
            "type" => "earnings",
            "display_name" => "Advance",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "bonus_amount",
            "type" => "earnings",
            "display_name" => "Bonus",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "arrears",
            "type" => "earnings",
            "display_name" => "Arrears",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "reimbursement",
            "type" => "earnings",
            "display_name" => "Reimbursement",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "performance_bonus",
            "type" => "earnings",
            "display_name" => "Performance Bonus",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "leave_encashment",
            "type" => "earnings",
            "display_name" => "Leave Encashment",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "rent_reimbursment",
            "type" => "earnings",
            "display_name" => "Rent Reimbursment",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "other_addition",
            "type" => "earnings",
            "display_name" => "Other Addition",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
            "total_earings_include" => "1",
            "total_deduction_include" => "0",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "1",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "tds",
            "type" => "deduction",
            "display_name" => "TDS",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "pf_employee_contribution",
            "type" => "deduction",
            "display_name" => "PF Employee Contribution ",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "professional_tax",
            "type" => "deduction",
            "display_name" => "Professional Tax",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "esi",
            "type" => "deduction",
            "display_name" => "ESI",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "vpf",
            "type" => "deduction",
            "display_name" => "VPF",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "1",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "loan_repayment",
            "type" => "deduction",
            "display_name" => "Loan Repayment",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "transport",
            "type" => "deduction",
            "display_name" => "Transport",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "student_fee",
            "type" => "deduction",
            "display_name" => "Student Fee",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "telephone_expense",
            "type" => "deduction",
            "display_name" => "Telephone Expense",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "electricity_charges",
            "type" => "deduction",
            "display_name" => "Electricity Charges",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "other_deductions",
            "type" => "deduction",
            "display_name" => "Other Deductions",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "medical_insurance",
            "type" => "deduction",
            "display_name" => "Medical Insurance",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "itari_fees",
            "type" => "deduction",
            "display_name" => "Itari Fees",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "1",
			"total_earings_include" => "0",
            "total_deduction_include" => "1",
            "upload_csv" => "1",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        // custom
        [
            "column_name" => "custom1",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom2",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom3",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom4",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom5",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom6",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom7",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom8",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom9",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
        [
            "column_name" => "custom10",
            "type" => "",
            "display_name" => "",
            "order_by" => "",
            "mandatory" => "0",
            "include_ctc" => "0",
            "salary_structure" => "0",
            "monthly_structure" => "0",
			"total_earings_include" => "0",
            "total_deduction_include" => "0",
            "upload_csv" => "0",
            "calculate_number_of_days_present" => "0",
            "outside_ctc_salary_strucutre" => "0",
            "additional_income_monthly_strcuture" => "0",
            'is_consultant' => '0'
        ],
    ];

$config['parent_side_columns'] = [
    [
      'display_name'=>'Student Photo',
      'data_input'=>'file',
      'column_name'=>'STUDENT_PHOTO',
      'tabs'=>'student_info',
      'table_column_name'=> 'sy.picture_url',
      'default'=>1
    ],
    [
      'display_name'=>'Admission Number',
      'data_input'=>'text',
      'column_name'=>'ADMISSION_NO',
      'tabs'=>'student_info',
      'table_column_name'=> 'sa.admission_no',
      'default'=>1
  
    ],
    [
      'display_name'=>'Class Section',
      'data_input'=>'text',
      'column_name'=>'CLASS_SECTION',
      'tabs'=>'student_info',
      'table_column_name' => "concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,''))",
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Mail',
      'data_input'=>'text',
      'column_name'=>'STUDENT_EMAIL',
       'table_column_name' => 'sa.email',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Mother Tongue',
      'data_input'=>'text',
      'column_name'=>'STUDENT_MOTHER_TONGUE',
      'table_column_name' => 'sa.mother_tongue',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Name',
      'data_input'=>'text',
      'column_name'=>'STUDENT_NAME',
       'table_column_name' => "concat(ifnull(sa.first_name,''),' ', ifnull(sa.student_middle_name,''),' ',ifnull(sa.last_name,''))",
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Gender',
      'data_input'=>'text',
      'column_name'=>'STUDENT_GENDER',
      'table_column_name' => 'sa.gender',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Address',
      'data_input'=>'text',
      'column_name'=>'STUDENT_ADDRESS',
      'table_column_name' => '', 
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Blood Group',
      'data_input'=>'text',
      'column_name'=>'STUDENT_BLOOD_GROUP',
      'tabs'=>'student_info',
       'table_column_name' => 'sa.blood_group',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Nationality',
      'data_input'=>'text',
      'column_name'=>'STUDENT_NATIONALITY',
      'table_column_name' => 'sa.nationality',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Category',
      'data_input'=>'text',
      'column_name'=>'CATEGORY',
      'table_column_name' => 'sa.category',
      'tabs'=>'student_info',
    
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Caste',
      'data_input'=>'text',
      'column_name'=>'STUDENT_CASTE',
        'table_column_name' => 'sa.caste',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Religion',
      'data_input'=>'text',
      'column_name'=>'STUDENT_RELIGION',
        'table_column_name' => 'sa.religion',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Aadhar',
      'data_input'=>'text',
      'column_name'=>'STUDENT_AADHAR',
      'table_column_name' => 'sa.aadhar_no',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Stop',
      'data_input'=>'text',
      'column_name'=>'STUDENT_STOP',
      'tabs'=>'student_info',
       'table_column_name' => 'sy.stop',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Pickup Mode',
      'data_input'=>'text',
      'column_name'=>'STUDENT_PICKUP_MODE',
      'tabs'=>'student_info',
      'table_column_name' => 'sy.pickup_mode',
      'default'=>1
  
    ],
    [
      'display_name'=>'COMBINATION',
      'data_input'=>'text',
      'column_name'=>'COMBINATION',
       'table_column_name' => 'sy.combination',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'FAMILY PHOTO',
      'data_input'=>'text',
      'column_name'=>'FAMILY_PHOTO',
       'table_column_name' => 'sa.family_picture_url',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Father Photo',
      'data_input'=>'file',
      'column_name'=>'FATHER_PHOTO',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.picture_url',
      'default'=>1

    ],
    [
      'display_name'=>'Father Name',
      'data_input'=>'text',
      'column_name'=>'FATHER_NAME',
      'tabs'=>'father_info',
      'table_column_name'=> "concat(ifnull(pf.first_name,''),' ',ifnull(pf.last_name,''))",
      'default'=>1

    ],
    [
      'display_name'=>'Father Address',
      'data_input'=>'text',
      'column_name'=>'FATHER_ADDRESS',
      'tabs'=>'father_info',
      'table_column_name'=> '',
      'default'=>1

    ],
    [
      'display_name'=>'Father Qualification',
      'data_input'=>'text',
      'column_name'=>'FATHER_QUALIFICATION',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.qualification',
      'default'=>1

    ],
    [
      'display_name'=>'Father Occupation',
      'data_input'=>'text',
      'column_name'=>'FATHER_OCCUPATION',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.occupation',
      'default'=>1

    ],
    [
      'display_name'=>'Father Contact Number',
      'data_input'=>'text',
      'column_name'=>'FATHER_CONTACT_NO',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.mobile_no',
      'default'=>1

    ],
    [
      'display_name'=>'Father Email',
      'data_input'=>'text',
      'column_name'=>'FATHER_EMAIL',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.email',
      'default'=>1

    ],
    [
      'display_name'=>'Father Annual Income',
      'data_input'=>'text',
      'column_name'=>'FATHER_ANNUAL_INCOME',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.annual_income',
      'default'=>1

    ],
    [
      'display_name'=>'Father Company',
      'data_input'=>'text',
      'column_name'=>'FATHER_COMPANY',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.company',
      'default'=>1

    ],
    [
      'display_name'=>'Father Aadhar',
      'data_input'=>'text',
      'column_name'=>'FATHER_AADHAR',
      'tabs'=>'father_info',
      'table_column_name'=> 'pf.aadhar_no',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Photo',
      'data_input'=>'file',
      'column_name'=>'MOTHER_PHOTO',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.picture_url',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Name',
      'data_input'=>'text',
      'column_name'=>'MOTHER_NAME',
      'tabs'=>'mother_info',
      'table_column_name'=> "concat(ifnull(pm.first_name,''),' ',ifnull(pm.last_name,''))",
      'default'=>1

    ],
    [
      'display_name'=>'Mother Address',
      'data_input'=>'text',
      'column_name'=>'MOTHER_ADDRESS',
      'tabs'=>'mother_info',
      'table_column_name'=> '',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Qualification',
      'data_input'=>'text',
      'column_name'=>'MOTHER_QUALIFICATION',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.qualification',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Occupation',
      'data_input'=>'text',
      'column_name'=>'MOTHER_OCCUPATION',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.occupation',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Contact Number',
      'data_input'=>'text',
      'column_name'=>'MOTHER_CONTACT_NO',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.mobile_no',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Email',
      'data_input'=>'text',
      'column_name'=>'MOTHER_EMAIL',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.email',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Annual Income',
      'data_input'=>'text',
      'column_name'=>'MOTHER_ANNUAL_INCOME',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.annual_income',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Aadhar',
      'data_input'=>'text',
      'column_name'=>'MOTHER_AADHAR',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.aadhar_no',
      'default'=>1

    ],
    [
      'display_name'=>'Mother Company',
      'data_input'=>'text',
      'column_name'=>'MOTHER_COMPANY',
      'tabs'=>'mother_info',
      'table_column_name'=> 'pm.company',
      'default'=>1

    ],
    [
      'display_name'=>'Student Date of Birth',
      'data_input'=>'text',
      'column_name'=>'STUDENT_DOB',
      'tabs'=>'student_info',
      'table_column_name' => 'sa.dob',
      'default'=>1
  
    ],
    [
      'display_name'=>'Guardian Photo',
      'data_input'=>'file',
      'column_name'=>'GUARDIAN_PHOTO',
      'tabs'=>'guardian_info',
      'table_column_name'=> 'pg.picture_url',
      'default'=>1

    ],
    [
      'display_name'=>'Guardian Name',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_NAME',
      'tabs'=>'guardian_info',
      'table_column_name'=> "concat(ifnull(pg.first_name,''),' ',ifnull(pg.last_name,''))",
      'default'=>1

    ],
    [
      'display_name'=>'Guardian Contact Number',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_CONTACT_NO',
      'tabs'=>'guardian_info',
      'table_column_name'=> 'pg.mobile_no',
      'default'=>1

    ],
    [
      'display_name'=>'Guardian Email',
      'data_input'=>'text',
      'column_name'=>'GUARDIAN_EMAIL',
      'tabs'=>'guardian_info',
      'table_column_name'=> 'pg.email',
      'default'=>1

    ],
    [
      'display_name'=>'Student House',
      'data_input'=>'text',
      'column_name'=>'STUDENT_HOUSE',
      'tabs'=>'student_info',
       'table_column_name' => 'sy.student_house',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Remarks',
      'data_input'=>'text',
      'column_name'=>'STUDENT_REMARKS',
      'tabs'=>'student_info',
       'table_column_name' => 'sa.student_remarks',
      'default'=>1
  
    ],
    [
      'display_name'=>'Family Photo',
      'data_input'=>'text',
      'column_name'=>'FAMILY_PHOTO',
      'tabs'=>'family_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Electives',
      'data_input'=>'text',
      'column_name'=>'ELECTIVES',
      'tabs'=>'Electives',
      'default'=>1
  
    ],
    [
      'display_name'=>'Name as Per Aadhar',
      'data_input'=>'text',
      'column_name'=>'NAME_AS_PER_AADHAR',
      'tabs'=>'student_info',
      'table_column_name' => 'name_as_per_aadhar',
      'default'=>1
  
    ],
    [
      'display_name'=>'Enrollment Number',
      'data_input'=>'text',
      'column_name'=>'ENROLLMENT_NUMBER',
      'tabs'=>'student_info',
       'table_column_name' => 'enrollment_number',
      'default'=>1
  
    ],
    [
      'display_name'=>'Student Mobile Number',
      'data_input'=>'text',
      'column_name'=>'STUDENT_MOBILE_NUMBER',
      'table_column_name' => 'student_mobile_no',
      'tabs'=>'student_info',
      'default'=>1
  
    ],
    [
      'display_name'=>'Alpha Roll Number',
      'data_input'=>'text',
      'column_name'=>'ALPHA_ROLL_NUMBER',
      'tabs'=>'student_info',
        'table_column_name' => 'sy.alpha_rollnum',
      'default'=>1
  
    ],
    [
      'display_name'=>'Preffered Contact Number',
      'data_input'=>'text',
      'column_name'=>'PREFFERED_CONTACT_NUMBER',
      'tabs'=>'student_info',
     'table_column_name' => 'sa.preferred_contact_no',
      'default'=>1
  
    ]
];