<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  16 July 2018
 *
 * Description:  
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Library Management
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */

class Library_controller extends CI_Controller {
               
	function __construct(){
		parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    // if (!$this->authorization->isModuleEnabled('LIBRARY')) {
    //   redirect('dashboard', 'refresh');
    // }
    $this->load->model('library_model');
    $this->load->library('filemanager');
    $this->load->library('ciqrcode');
    $this->config->load('form_elements');
    $this->load->model('parent_model');
    $this->load->helper('texting_helper');
	}

  public function index(){

    $data['permitBorrowReturn'] = $this->authorization->isAuthorized('LIBRARY.BORROW_RETURN');
    $data['permitFineCollection'] = $this->authorization->isAuthorized('LIBRARY.FINE_COLLECTION');
    $data['permitBulk'] = $this->authorization->isAuthorized('LIBRARY.BULK_CURCULATION');
    $data['permitBulkCirculation'] = $this->authorization->isAuthorized('LIBRARY.BULK_CURCULATION');
    $data['permitDLB'] = $this->authorization->isAuthorized('LIBRARY.DAMAGE_OR_LOST_BOOK');
    $data['permitSC'] = $this->authorization->isAuthorized('LIBRARY.STOCK_CHECK');
    $data['permitDtxR'] = $this->authorization->isAuthorized('LIBRARY.DAILY_TX_REPORT');
    $data['permitBBR'] = $this->authorization->isAuthorized('LIBRARY.BOOK_BORROWED_REPORT');
    $data['permitFDR'] = $this->authorization->isAuthorized('LIBRARY.FINE_DEFAULTER_REPORT');
    $data['permitSR'] = $this->authorization->isAuthorized('LIBRARY.STOCK_REPORT');
    $data['permitMTR'] = $this->authorization->isAuthorized('LIBRARY.MEMBER_TX_REPORT');
    $data['permitBooks'] = $this->authorization->isAuthorized('LIBRARY.BOOKS');
    $data['permitAC'] = $this->authorization->isAuthorized('LIBRARY.ASSIGN_CARD');
    $data['permitBulk'] = $this->authorization->isAuthorized('LIBRARY.BULK_ASSIGN_CARD');
    $data['permitCards'] = $this->authorization->isAuthorized('LIBRARY.CARD_MASTER');
    $data['permitBrorrowRenewal'] = $this->authorization->isAuthorized('LIBRARY.BORROW_RETURN_RENEWAL');

    $libraries = $this->library_model->get_libraries_details();
    $count = count($libraries);
    $lbrId = $this->session->userdata('libraries');     
    if (empty($lbrId) && !empty($libraries)) {
      $this->session->set_userdata('libraries',$libraries[0]['id']);
    }
  
    if ($count > 1) {
      $data['libraries'] = $libraries;
    }

    $site_url = site_url();
    $data['tiles'] = array(
      // [
      //   'title' => 'Borrow',
      //   'sub_title' => 'Borrow Books',
	    //   'icon' => 'svg_icons/borrow.svg',
      //   'url' => $site_url.'library_controller/issue_book',
      //   'permission' => $data['permitBorrowReturn']
      // ],
      // [
      //   'title' => 'Return',
      //   'sub_title' => 'Return Books',
	    //   'icon' => 'svg_icons/return.svg',
      //   'url' => $site_url.'library_controller/return_book',
      //   'permission' => $data['permitBorrowReturn']
      // ],
      [
        'title' => 'Borrow / Return / Renewal',
        'sub_title' => 'Borrow / Return Books',
	      'icon' => 'svg_icons/return.svg',
        'url' => $site_url.'library_controller/borrow_return',
        'permission' => $data['permitBrorrowRenewal']
      ],
      [
        'title' => 'Fine Collection',
        'sub_title' => 'Fine Collection',
	      'icon' => 'svg_icons/finecollection.svg',
        'url' => $site_url.'library_controller/lbr_fine_amount',
        'permission' => $data['permitFineCollection']
      ],
      [
        'title' => 'Bulk Circulation',
        'sub_title' => 'Bulk Circulation',
	      'icon' => 'svg_icons/inventory.svg',
        'url' => $site_url.'library_controller/lbr_bulk_circulation',
        'permission' => $data['permitBulkCirculation']
      ],
      [
        'title' => 'Damage/Lost Book',
        'sub_title' => 'Damage/Lost Book',
	      'icon' => 'svg_icons/books.svg',
        'url' => $site_url.'library_controller/damage_lost_books',
        'permission' => $data['permitDLB']
      ],
      [
        'title' => 'Stock Check',
        'sub_title' => 'Stock Check',
	      'icon' => 'svg_icons/stockcheck.svg',
        'url' => $site_url.'library_controller/stock_check',
        'permission' => $data['permitSC']
      ],
      [
        'title' => 'Checkin/Checkout Report',
        'sub_title' => 'Stock Check',
	      'icon' => 'svg_icons/stockcheck.svg',
        'url' => $site_url.'library_controller/libCheckInReport',
        'permission' => $data['permitSC']
      ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
      [
        'title' => 'Daily Transaction',
        'sub_title' => 'Daily Transaction',
	      'icon' => 'svg_icons/dailytransaction.svg',
        'url' => $site_url.'library_controller/daily_transaction',
        'permission' => $data['permitDtxR']
      ],
      [
        'title' => 'Book Borrowed',
        'sub_title' => 'Book Borrowed',
	      'icon' => 'svg_icons/bookborrowed.svg',
        'url' => $site_url.'library_controller/due_list_all',
        'permission' => $data['permitBBR']
      ],
      [
        'title' => 'Fine Defaulter',
        'sub_title' => 'Fine Defaulter',
	      'icon' => 'svg_icons/finedefaulterreport.svg',
        'url' => $site_url.'library_controller/fine_due_list_all',
        'permission' => $data['permitFDR']
      ],
      [
        'title' => 'Stock Report',
        'sub_title' => 'Stock Report',
	      'icon' => 'svg_icons/stockreport.svg',
        'url' => $site_url.'library_controller/summary_report',
        'permission' => $data['permitSR']
      ],
      [
        'title' => 'Member Transactions',
        'sub_title' => 'Member Transactions',
	      'icon' => 'svg_icons/transaction.svg',
        'url' => $site_url.'library_controller/history_student_staff',
        'permission' => $data['permitMTR']
      ],
      [
        'title' => 'Fine Daily Transaction',
        'sub_title' => 'Fine Daily Transaction',
	      'icon' => 'svg_icons/finedailytransaction.svg',
        'url' => $site_url.'library_controller/fine_daily_transaction',
        'permission' => $data['permitFDR']
      ],
      [
        'title' => 'Disabled Books',
        'sub_title' => 'Disabled Books',
        'icon' => 'svg_icons/finedailytransaction.svg',
        'url' => $site_url.'library_controller/disabled_books_report',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Damage / Lost Books Report',
        'sub_title' => 'Disabled Books',
        'icon' => 'svg_icons/finedailytransaction.svg',
        'url' => $site_url.'library_controller/damage_lost_books_report',
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Books',
        'sub_title' => 'Books',
	      'icon' => 'svg_icons/books.svg',
        'url' => $site_url.'library_controller/book_details',
        'permission' => $data['permitBooks']
      ],
      [
        'title' => 'Libraries',
        'sub_title' => 'Add Library',
	      'icon' => 'svg_icons/libraries.svg',
        'url' => $site_url.'library_controller/libraries',
        'permission' => $data['permitCards']
      ],
      [
        'title' => 'Card Master',
        'sub_title' => 'Create library card',
	      'icon' => 'svg_icons/cardmaster.svg',
        'url' => $site_url.'library_controller/library_master',
        'permission' => $data['permitCards']
      ],
      [
        'title' => 'Assign card',
        'sub_title' => 'Assign student/staff card',
	      'icon' => 'svg_icons/assigncard.svg',
        'url' => $site_url.'library_controller/library_cards_individual',
        'permission' => $data['permitAC']
      ],
      [
        'title' => 'Mass Assign Cards',
        'sub_title' => 'Assign student/staff cards',
	      'icon' => 'svg_icons/massassigncards.svg',
        'url' => $site_url.'library_controller/library_cards',
        'permission' => $data['permitBulk']
      ],
      [
        'title' => 'Export Books',
        'sub_title' => 'Export books in library',
	      'icon' => 'svg_icons/books.svg',
        'url' => $site_url.'library_controller/books_export',
        'permission' => $data['permitBooks']
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/transaction/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/transaction/index_mobile';
    }else{
      $data['main_content']='library/transaction/index';   	
    }
    
    
    $this->load->view('inc/template',$data);
  } 
// Master Data
	public function library_master(){
    $data['memData']=$this->library_model->get_info_admin_data();
   // echo "<pre>"; print_r($data['memData']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'library/master/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'library/master/index_mobile';
    }else{
      $data['main_content']    = 'library/master/index';      	
    }
    $this->load->view('inc/template', $data);
	}

	public function add(){
    $data['books_type'] = $this->settings->getSetting('books_type');
    $data['lbr_cardColors'] = $this->config->item('lbr_cardColors');
    $data['libraries'] = $this->library_model->get_libraries_details();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'library/master/add_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'library/master/add_mobile';
    }else{
      $data['main_content']    = 'library/master/add';      	
    }
    $this->load->view('inc/template', $data);
	}

	public function insert_data(){
		$result=$this->library_model->submit_data_admin();
    if( $result){
      $this->session->set_flashdata('flashSuccess', 'Data Succesfully Added');
      redirect('library_controller/library_master');
    }else{
       $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
      redirect('library_controller/library_master');
    }
	}

  public function edit($id){
    $data['libraries'] = $this->library_model->get_libraries_details();
    $data['books_type'] = $this->settings->getSetting('books_type');
    $data['memberEdit']=$this->library_model->get_info_edit_idby_admindata($id);
    $data['lbr_cardColors'] = $this->config->item('lbr_cardColors');
    // echo "<pre>"; print_r($data['memberEdit']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']    = 'library/master/edit_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'library/master/edit_mobile';
    }else{
      $data['main_content']    = 'library/master/edit';     	
    }
    $this->load->view('inc/template', $data);
	}

	public function update_adminData($id){
  	$result=$this->library_model->update_data_admin($id);
    if( $result){
      $this->session->set_flashdata('flashSuccess', 'Update Succesfully.');
      redirect('library_controller/library_master/');
    }else{
       $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
      redirect('library_controller/library_master/');
    }
	}

 	public function delete_admin_data($id){
    $result=$this->library_model->delete_admin_data($id);
	  $this->session->set_flashdata('flashSuccess', 'Delete Succesfully.');
	  redirect('library_controller/library_master/');
	}

  public function s3FileUpload($file) {
    if($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }        
    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'library_books');
  }

    // Mass  assign cards
    public function library_cards(){     
      $data['member_type'] = $this->library_model->get_all_type_ofCards();
      $data['classes'] = $this->library_model->get_class_names();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'library/mass_cards/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']    = 'library/mass_cards/index_mobile';
      }else{
        $data['main_content']    = 'library/mass_cards/index';     	
      }
      $this->load->view('inc/template', $data);
    } 

    public function library_cards_assign(){

      $result = $this->library_model->insert_library_card_details();
      if($result){
        redirect('library_controller/update_card_access_code');
      }else{
         $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
        redirect('library_controller/library_cards');
      }
    }

    public function update_card_access_code(){
      $result = $this->library_model->update_card_access_codeAll();
      if( $result){
       // $this->update_card_access_code();
        $this->session->set_flashdata('flashSuccess', 'Cards assign succesfully');
        redirect('library_controller/library_cards');
      }else{
         $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
        redirect('library_controller/library_cards');
      }
    }

    public function get_selectedTypewiseData(){
      $result = $this->library_model->get_selected_typewise();
      echo json_encode($result);
    }

    public function get_studentData(){
      $classId = $this->input->post('classId');
     // $member_type = $this->input->post('member_type');
      $result = $this->library_model->get_student_typewise($classId);
      echo json_encode($result);
    }
    
    public function get_selected_member_typewiseData(){
      $member_type = $this->input->post('member_type');
      $type = $this->input->post('type');
      $result = $this->library_model->get_selected_member_typewise($member_type,$type);
      echo json_encode($result);
    }

    public function access_code_validation(){
      $access_codeId = $this->input->post('access_codeId');
      $result = $this->library_model->check_access_code_validation($access_codeId);
      if($result == '1'){
       echo '<span style="color:red;text-align:center;">Access code already exists. Choose a different unique code.</span>';
      }
    }

    public function delete_accce_codebyId(){
      $a_id = $this->input->post('a_id');
      $result = $this->library_model->delete_book_access_code($a_id);
      if( $result){
        echo 1;
      }else{
        echo 0;
      }
    }
  // individual Assign  Cards

    public function library_cards_individual($type='',$class_id='') {
      $data['type'] = $type;
      $data['class_id'] = $class_id;
      $data['member_type'] = $this->library_model->get_all_type_ofCards();
      $data['classes'] = $this->library_model->get_class_info();
      $data['staff_libr'] = $this->library_model->get_staffLibrary();
     //echo "<pre>"; print_r($data['member_type'] ); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'library/individual_cards/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']    = 'library/individual_cards/index_mobile';
      }else{
        $data['main_content']    = 'library/individual_cards/index';     	
      }
      $this->load->view('inc/template', $data); 
    }

    public function fetch_typewise_data(){
      $type = $this->input->post('type');
      if ($type == 'staff') {
        $res_data = $this->library_model->get_staffLibrary();
      }elseif ($type == "student") {
        $classId = $this->input->post('classId');
        $res_data = $this->library_model->get_student_library($classId);
      }
      // $i=1;
      // $template = "";
      // foreach ($res_data as $key => $data) {
      // $template .='<tr>';
      // $template .='<td>'.$i++.'</td>';
      // $template .='<td>'.$data->name.'</td>';
      // $template .='<td>'.$data->library_id.'</td>';
      // if ($type == 'staff') {
      //   $template .='<td><a  href='.site_url('library_controller/add_library_staffcards/'.$data->staff_id.'/'.$type).' class="btn btn-warning " data-placement="top" data-toggle="tooltip" data-original-title="View and add"><i class="fa fa fa-sitemap"></i>
      //             </a></td>';
      // }elseif ($type == 'student') {
      //   $template .='<td><a  href='.site_url('library_controller/add_library_studentcards/'.$data->stdId.'/'.$type.'/'.$classId).' class="btn btn-warning " data-placement="top" data-toggle="tooltip" data-original-title="View and add"><i class="fa fa fa-sitemap"></i>
      //             </a></td>';
      // }
    
      // $template .="</tr>";
      // }
      // print($template);
      echo json_encode($res_data);
    }

    public function add_library_staffcards($sfId,$type){
      $data['type'] = $type;
      $data['class_id'] = '';
      $data['member_type'] = $this->library_model->get_all_type_ofCards();
      $data['lbrDetails'] = $this->library_model->getlibrDetailsbyStaffId($sfId);
      // echo "<pre>"; print_r($data['member_type']); die();    
      $data['main_content']    = 'library/individual_cards/details';
      $this->load->view('inc/template', $data);
    }

    public function add_library_studentcards($sfId,$type,$classId = ''){
      $data['type'] = $type;
      $data['class_id'] = $classId;
      $data['member_type'] = $this->library_model->get_all_type_ofCards();
      $data['lbrDetails'] = $this->library_model->getlibrDetailsbyStdId($sfId);
      // echo "<pre>"; print_r($data['lbrDetails']); die();    
      $data['main_content']    = 'library/individual_cards/details';
      $this->load->view('inc/template', $data);
    }

    public function library_cardsadd($id,$class_id=''){
      $type = $this->input->post('type');
      $result = $this->library_model->insert_librarycards_individual($id,$type);
      if($result){
        $res = $this->library_model->update_card_access_codeAll(); 
        if ($res) {
          $this->session->set_flashdata('flashSuccess', 'Insert succesfully');
        }
        if ($type == 'staff') {
          redirect('library_controller/add_library_staffcards/'.$id.'/'.$type);
        }elseif ($type == "student") {
          redirect('library_controller/add_library_studentcards/'.$id.'/'.$type.'/'.$class_id);
        }
      }else{
        $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
        if ($type == 'staff') {
          redirect('library_controller/add_library_staffcards/'.$id.'/'.$type);
        }elseif ($type == "student") {
          redirect('library_controller/add_library_studentcards/'.$id.'/'.$type.'/'.$class_id);
        }
      }
    }

    public function delete_card($lbId,$id,$type,$class_id = ''){
      $result = $this->library_model->delete_libraryCards($lbId);
      if( $result){
        $this->session->set_flashdata('flashSuccess', 'Delete succesfully');
        if ($type == 'staff') {
          redirect('library_controller/add_library_staffcards/'.$id.'/'.$type);
        }elseif ($type == "student") {
          redirect('library_controller/add_library_studentcards/'.$id.'/'.$type.'/'.$class_id);
        }
      }else{
        $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
        if ($type == 'staff') {
          redirect('library_controller/add_library_staffcards/'.$id.'/'.$type);
        }elseif ($type == "student") {
          redirect('library_controller/add_library_studentcards/'.$id.'/'.$type.'/'.$class_id);
        }
      }
    }

  // Book Details
  	public function book_details(){

      $data['count_cart'] = $this->library_model->get_add_cartCount();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'library/book_details/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']    = 'library/book_details/index_mobile';
      }else{
        $data['main_content']    = 'library/book_details/index';     	
      }
    	$this->load->view('inc/template', $data);
  	}

    public function books_export(){
      // $data['count_cart'] = $this->library_model->get_add_cartCount();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'library/book_details/index_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']    = 'library/book_details/index_mobile';
      }else{
        $data['main_content']    = 'library/book_details/export_books';     	
      }
      $fields1 = $this->db->list_fields('library_books');
      $fields2 = $this->db->list_fields('library_books_copies');
      $fields1 = array_diff($fields1, array('volume'));
      $columnsToRemove = array('id','created_on', 'modified_on', 'last_modified_by', 'source', 'file_url', 'contains', 'pages', 'call_number', 'supplier', 'bill_no_date', 'b_author2', 'b_sub_title', 'b_book_keyword', 'b_copies', 'shelf_no_of', 'issn_no', 'book_course', 'bill_no', 'date_of_accession', 'remarks', 'discarded_date', 'b_invoice_no', 'rack_name', 'currency','acc_no');
      $fixedOrderFields = ['access_code', 'book_title', 'description', 'subject', 'publisher_name','yearof_publishing'];

      $fields = array_merge($fields1, $fields2);
      $fields = array_diff($fields, $columnsToRemove);
      $remainingFields = array_diff($fields, $fixedOrderFields);
      sort($remainingFields);
      $fields = array_merge($fixedOrderFields, $remainingFields);
      $data['column_list'] = $fields;
      $data['saved_filters'] = $this->library_model->get_saved_filters();
    	$this->load->view('inc/template', $data);
  	}

    public function get_book_export_data() {
      $columns = $_POST['selectedCols'];
      $from_date = $_POST['from_date'];
      $to_date = $_POST['to_date'];
      $book_status = $_POST['book_status'];
      $result = $this->library_model->get_book_export_data($columns, $from_date, $to_date, $book_status);
      echo json_encode($result);
    }
      
  	public function add_book_details(){ 
      $data['books_type'] = $this->settings->getSetting('books_type');
      $data['libraries'] = $this->library_model->get_libraries_details();
      $data['popUpName'] = $this->library_model->get_allpopNames();
      $data['vendors'] = $this->library_model->getVendorList();
      $data['book_details'] = $this->library_model->get_qrCodeDetails_add_edit();


      // echo "<pre>"; print_r($data['books_type']);die();

  		$data['main_content']    = 'library/book_details/add';
    	$this->load->view('inc/template', $data);
  	}

  	public function insert_bookDetails(){
      
     
      $bId=$this->library_model->submit_data_book_details($this->s3FileUpload($_FILES['book_image']));
	    if($bId){
        $acces_no_receipt_book_id = $this->settings->getSetting('library_accession_no_receiptbook_id');
        $total_numberof_copies = $this->input->post('total_numberof_copies');
        if ($this->settings->getSetting('library_manual_accession_no')) {
          $copiesId = explode(',', $this->input->post('acces_num'));
          $result = $this->library_model->update_access_code_manually($copiesId,$bId);
        }else if($acces_no_receipt_book_id){
          $access_no = [];
          for ($i=1; $i <= $total_numberof_copies ; $i++) { 
            $access_no = $this->library_model->get_access_number_from_receipt_book($acces_no_receipt_book_id);
            if(!empty($access_no)){
              $access_no_arr[] = $access_no;
            }
          }
          $result = $this->library_model->update_access_code_manually($access_no_arr,$bId);
        }else{
          $result = $this->generate_code($bId);
        }
       
        if($result) {
          $lbcId = $this->library_model->get_barcode_details($bId);
          // $result = $this->library_model->insert_access_number_forprint($lbcId);
          $this->session->set_flashdata('flashSuccess', 'Book(s) Succesfully Added. Also added to the cart.');
          redirect('library_controller/book_details');
        }else{
          $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
          redirect('library_controller/book_details');
        }
	    }else{
        $this->session->set_flashdata('flashError', 'Some Thing Wrong.');
	      redirect('library_controller/book_details');
	    }
  	}


  	public function barcode($id){
  		$data['barcode'] = $this->library_model->get_barcode_details($id);
	 	  $data['main_content']='library/book_details/barcode';
    	$this->load->view('inc/template',$data);
  	}

  	public function bookd_details_edit($id){
      $data['books_type'] = $this->settings->getSetting('books_type');
  		$data['book_details']=$this->library_model->get_books_detailsbyId($id);
      $data['libraries'] = $this->library_model->get_libraries_details();
      // echo "<pre>"; print_r($data['book_details']); die();
      $data['popUpName'] = $this->library_model->get_allpopNames();
  		$data['main_content']='library/book_details/edit';
    	$this->load->view('inc/template',$data);
  	}

  	public function delete_booksDetails(){
      $bookId = $_POST['bookId'];
  		$delete= $this->library_model->delete_booksdetailsall($bookId);
      echo $deleted;
  	}

  	public function update_bookdetails($id){
      if(isset($_FILES['book_image'])){
         $update= $this->library_model->update_data_book_detailsbyId($id,$this->s3FileUpload($_FILES['book_image']));
      }else{
         $update=$this->library_model->update_data_book_detailsbyId($id,null);
      }
      if($update){
        echo 1;
      }else{
        echo 0;
      }
  	}

    public function access_code_view($id){
      $data['bookId'] = $id;
      $data['access_code'] = $this->library_model->get_qrCodeDetails($id);
      // echo "<pre>"; print_r($data['access_code']); die();
      // if ($this->mobile_detect->isTablet()) {
      //   $data['main_content']='library/book_details/access_index';
      // }else if($this->mobile_detect->isMobile()){
      //   $data['main_content']='library/book_details/access_index';
      // }else{
      //   $data['main_content']='library/book_details/access_index';     	
      // }
      $data['main_content']='library/book_details/access_index';
      $this->load->view('inc/template',$data);
    }

    public function add_book_detailsbybook_Id($id){
      $data['bookId'] = $id;
      $data['libraries'] = $this->library_model->get_libraries_details();
      // if ($this->mobile_detect->isTablet()) {
      //   $data['main_content']='library/book_details/access_code_tablet';
      // }else if($this->mobile_detect->isMobile()){
      //   $data['main_content']='library/book_details/access_code_mobile';
      // }else{
      //   $data['main_content']='library/book_details/access_code';    	
      // }
      $data['main_content']='library/book_details/access_code';       
      $this->load->view('inc/template',$data);
    }

    public function edit_books_accessId($bookId, $lbcId){
      // echo "<pre>"; print_r($_POST); die();
      $data['bookId'] = $bookId;
      $data['access_number'] = $this->library_model->edit_accessnumberById($lbcId);
      // echo "<pre>"; print_r($data['access_number']); die();
      $data['main_content']='library/book_details/access_code_edit';
      $this->load->view('inc/template',$data);
    }

    public function update_access_copies($bookId, $lbcId){
      // echo "<pre>"; print_r($_POST);die();
  
     $result= $this->library_model->access_code_updated($lbcId);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Update Succesfully.');
      }else{
        $this->session->set_flashdata('flashError', 'No changes ');
      }
       redirect('library_controller/access_code_view/'.$bookId);
    }
    // public function qr_code_view($id){
    //   $data['bookId'] = $id;
    //   $lbrSetting = $this->settings->getSetting('lbr');
    //   $data['access'] = $lbrSetting['lbr_access_code'];
    //   $data['access_code'] = $this->library_model->get_qrCodeDetails($id);
    //   $sName = $this->settings->getSetting('school_short_name');
    //   foreach ($data['access_code'] as $code) {
    //     // $data['image_name'] = bin2hex( $code->qr_code).'-'.'Mohan'.'-'.'1A'.'.png';
    //     $data['image_name'] = $code->access_code.'.png';
    //     $params['data'] =$code->access_code;
    //     $params['level'] = 'H';
    //     $params['size'] = 2;
    //     $params['savename'] = FCPATH.'assets/access_code/'.$data['image_name'];
    //     $this->ciqrcode->generate($params);
    //   } 
    //   $data['main_content']='library/book_details/access_code';
    //   $this->load->view('inc/template',$data);
    // }

    public function generate_code($id){
   
      $book_details = $this->library_model->get_infoIdByBookDetails($id);
       // $booksCount = $book_details->total_numberof_copies;
      $sName = $this->settings->getSetting('school_short_name');

      foreach ($book_details as $key => $val) {
        $len = strlen((string)$val->id);
        $digits = 'LB';
        for ($i = 6 - $len;$i > 0; $i--) { 
          $digits .= '0';
        }
        $digits .= $val->id;
        $a_code[] = strtoupper($sName).$digits;
        $copiesId[] = $val->id;
      }
      return $this->library_model->update_access_CodeId($a_code,$copiesId);
      // $result = $this->library_model->update_access_CodeId($a_code,$copiesId);
      // if ($result) {
      //   redirect('library_controller/access_code_view/'.$id);
      // }
    }

  
    public function searchqrCode(){
      $bookid = $this->input->post('bookid');
      $book_details = $this->library_model->getbooksqrCodeDetailsAll($bookid);
      print_r($book_details); die();
     // print($book_details);
    }

    public function access_code_insert(){
      $result= $this->library_model->access_code_updatedby_aId();
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Update Succesfully.');
        redirect('library_controller/book_details');
      }else{
        $this->session->set_flashdata('flashError', 'No changes ');
        redirect('library_controller/book_details');
      }
    }

    public function add_more_copies($bookId){
      $result= $this->library_model->add_more_copiesofbooksId($bookId);
      if ($result) {
        $lbcId = $this->library_model->get_barcode_details($bookId);
        $this->library_model->insert_access_number_forprint($lbcId);
        $this->session->set_flashdata('flashSuccess', 'Book(s) Succesfully Added. Also added to the cart.');
        redirect('library_controller/bookd_details_details/'.$bookId);
        // redirect('library_controller/generate_code/'.$bookId);
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong. ');
        redirect('library_controller/bookd_details_details/'.$bookId);
      }
    }

    public function clear_all_cart_data(){
      echo $this->library_model->clearAll_card_data();
    } 
// Transation
  // public function transaction(){
    // $data['lc_cards'] = $this->session->userdata('more_cards');
    // $libraries = $this->library_model->get_libraries_details();
    // $count = count($libraries);
    // $lbrId = $this->session->userdata('libraries');
    // if (empty($lbrId)) {
    //   $this->session->set_userdata('libraries',$libraries[0]['id']);
    // }
  
    // if ($count > 1) {
    //   $data['libraries'] = $libraries;
    // }
    // $data['main_content']='library/transaction/index';
    // $this->load->view('inc/template',$data);
  // }

  public function return_book(){
    $data['title_name'] = 'Return';
    $data['all_names'] = $this->library_model->get_staff_student_names();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/transaction/return_book_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/transaction/return_book_mobile';
    }else{
      $data['main_content']='library/transaction/return_book';    	
    }
    $this->load->view('inc/template',$data);
  }

  public function issue_book(){
    $data['title_name'] = 'Borrow';
    $data['all_names'] = $this->library_model->get_staff_student_names();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/transaction/issue_book_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/transaction/issue_book_mobile';
    }else{
      $data['main_content']='library/transaction/issue_book';     	
    }
    $this->load->view('inc/template',$data);
  }

  public function serach_books(){
    $book_accessid = $this->input->post('book_accessid');
    $bType = json_decode($this->input->post('bType'));
    $result = $this->library_model->get_books_details($book_accessid,$bType);
    if (empty($result)) {
      return 0;
    }
   echo json_encode($result);
  }

  public function serach_books_byaccessId(){
    $book_accessid = $this->input->post('book_accessid');
    if (empty($book_accessid)) {
      echo json_encode(0);
    }
    $result = $this->library_model->get_books_detailsbyaccessId($book_accessid);
    if (empty($result)) {
      return 0;
    }
   echo json_encode($result);  
  }

  // public function get_searchbyname(){
  //   $card_number = $this->input->post('card_number');
  //   $result = $this->library_model->get_card_numberwisedata($card_number);
  //   if (empty($result)) {
  //     return 0;
  //   }
  //  echo json_encode($result);  
  // }

  private function isValidHexadecimal($input) {
    return ctype_xdigit($input);
  }

  private function __disambiguateAccessId($inputId) {
    $sName = $this->settings->getSetting('school_short_name');

    if(strpos($inputId, 'student_url_qr_code')){
      return $inputId;
    }
    if (strpos($inputId, strtoupper($sName)) === false) {
        if ($this->isValidHexadecimal($inputId)) {
            $accessId = hex2bin($inputId);
        } else {
            $accessId = null;
        }
    } else {
        $accessId = $inputId;
    }

    return $accessId;
  }

  public function detailsofbooks_fine_page(){
    $accessId = $this->__disambiguateAccessId($this->input->post('accessId'));
    $data['accessId'] = $accessId;
    $result = $this->library_model->get_libraryid_details_fine_view($accessId);
    
    echo json_encode($result);
  }

  public function detailsofbooks(){    
    $selectedLbId =   $this->session->userdata('libraries');
    $accessId = $this->__disambiguateAccessId($this->input->post('accessId'));
    $rDate = $this->input->post('rDate');
    $data['accessId'] = $accessId;
    // $result = $this->library_model->get_libraryid_details($accessId);
    $result = $this->library_model->get_libraryid_details($accessId, $selectedLbId);
    if ($result == 2) {
      echo json_encode($result);
      return false;
    }
    $count = 0;
    foreach ($result['lbr'] as $key => $val) {
      if (!empty($val->id)) {
        $count++;
      }
    }

    $books_type = $this->settings->getSetting('books_type');
    $template = '';
    foreach ($result['lbr'] as $key =>  &$res) {
      $issueDate = $res->issue_date;
      $cDate = $rDate;
      if ($rDate==0) {
        $cDate = date('Y-m-d');
      }
     // $due_date = $res->due_date;
      $dayCount = $res->hold_period;
      $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
      $ts1 = strtotime($cDate);
      $ts2 = strtotime($due_date);
      $datediff = $ts1 - $ts2;
      $days_exceeded=($datediff / (60*60*24));
      $perday_fine=$res->fine_per_day *  $days_exceeded;

      if ($days_exceeded > 0) {
        $days = $days_exceeded;
      }else{
        $days = 0;
      }
      if ($perday_fine > 0) {
        $fine = $perday_fine;
      }else{
        $fine = 0;
      }

      $res->remaining =   $res->num_books - $count;
      $res->num_books = $res->num_books;
      $res->booktitle = $res->book_title;
      $res->issue_date =date('d-m-Y', strtotime($res->issue_date)) ;
      $res->due_date = date('d-m-Y',strtotime($due_date));
      $res->days = $days;
      $res->fine = $fine;
      $booType = json_decode($res->book_type);
      foreach ($books_type as $key => $val) {
        if ($val->value == $res->bType) {
          $res->bType = $val->name;
        }
      }
    }
    echo json_encode($result);
  }

  public function serach_libraray_access($accessId=""){
    $accessId =$this->input->post('access_number');
    $data['accessId'] = $accessId;
   // $binaccessId =  hex2bin($accessId);

   //  if (!empty($binaccessId) ) {
   //    $a_id =  $binaccessId;
   //  }else{
   //    $a_id = $accessId;
   //  }
    $result = $this->library_model->get_libraryid_details($accessId);
    echo json_encode($result);
  }

 


  public function insert_issue_book(){
    $accessId =$this->input->post('accessId');
    $book_accessId =$this->input->post('book_accessId');
    $issue_date =$this->input->post('issue_date');
    echo $this->library_model->insert_issuebook_details($accessId,$book_accessId,$issue_date);
  }

  public function send_notification_for_issue_books(){
    $accessId =$this->input->post('accessId');
    $book_accessId =$this->input->post('book_accessId');

    $library_enable_notification = $this->settings->getSetting('library_enable_notification');

    if ($library_enable_notification) {
      $school_name = $this->settings->getSetting('school_name');
      $input_array = array(
        'mode' => 'notification', 
        'title' => $school_name, 
        'source' => 'Library UI',
        'is_unicode' => '0',
        'visible' => 1,
        'send_to' => 'Both',
        'class_send_to' => '',
        'batch_class' => '',
        'acad_year_id' =>  $this->acad_year->getAcadYearID(),
      );

      $get_type = $this->library_model->get_type_of_accessId($accessId);
      if ($get_type->stake_holder_type == 'staff' ) {
          $result =  $this->library_model->sent_sms_for_issue_books_staff($accessId,$book_accessId);
          $message_template = $this->library_model->get_messageformSMSTable('Library','Staff Books Issued');
          $book_title = '';
          $staffIds =[];
          foreach ($result as $key => $val) {
            $book_title .= $val->book_title;
            array_push($staffIds, $val->staffId);
          }
          $message = 'Library book '.$book_title.' is issued. Ensure to return it within the due date.';
          if (!empty($message_template)) {
            $message = str_replace('%%book_title%%', $book_title, $message_template); 
          }
          $input_array['staff_ids'] = array_unique($staffIds);
          $input_array['message'] = $message;

      }else if ($get_type->stake_holder_type == 'student'){
        // if ($message['parent'] == true) {
        $result =  $this->library_model->sent_sms_for_issue_books_student($accessId,$book_accessId);
        $message_template = $this->library_model->get_messageformSMSTable('Library','Student Books Issued');
        $book_title = '';
        $stdIds =[];
        $stdName='';
        foreach ($result as $key => $val) {
          $book_title .= $val->book_title;
          $stdName .=$val->stdName;       
          array_push($stdIds, $val->stdId);
        }
        $message = 'Library book '.$book_title.' is issued to '.$stdName.'. Ensure to return it within the due date.';
        if (!empty($message_template)) {
          $message = str_replace('%%book_title%%', $book_title, $message_template); 
          $message = str_replace('%%stdName%%', $stdName, $message); 
        }
        $input_array['student_ids'] = $stdIds;
        // $input_array['student_url'] = 'library_controller/borrowed_books_history_all/'.$stdId.'/'.'student';
        $input_array['message'] = $message; 
      }
        $response = sendText($input_array);
        echo json_encode($response);
    }
  }

  private function _return_notification_library_books($lbr_card_id,$rId){
    $get_type = $this->library_model->get_type_of_accessId($lbr_card_id);
    $school_name = $this->settings->getSetting('school_name');
    $input_array = array(
      'mode' => 'notification', 
      'title' => $school_name, 
      'source' => 'Library UI',
      'is_unicode' => '0',
      'visible' => 1,
      'send_to' => 'Both',
      'class_send_to' => '',
      'batch_class' => '',
      'acad_year_id' =>  $this->acad_year->getAcadYearID(),
    );

    if ($get_type->stake_holder_type == 'staff' ) {
      $result =  $this->library_model->sent_sms_for_return_books_staff($rId);
      $message_template = $this->library_model->get_messageformSMSTable('Library','Staff Books return');
      $book_title = '';
      $staffIds =[];
      foreach ($result as $key => $val) {
        $book_title .= $val->book_title;
        array_push($staffIds, $val->staffId);
      }
      $message = 'Library book '.$book_title.' is returned.';
      if (!empty($message_template)) {
        $message = str_replace('%%book_title%%', $book_title, $message_template); 
      }     
      $input_array['staff_ids'] = array_unique($staffIds);
      // $input_array['staff_url'] = 'library_controller/card_view';
      $input_array['message'] = $message;
    }else if($get_type->stake_holder_type == 'student'){
      $result =  $this->library_model->sent_sms_for_return_books_student($rId);
      $message_template = $this->library_model->get_messageformSMSTable('Library','Student Books return');
      $book_title = '';
      $stdId = '';
      $stdIds =[];
      $stdName ='';
      foreach ($result as $key => $val) {
        $book_title .= $val->book_title;
        array_push($stdIds, $val->stdId);
        $stdName .=$val->stdName;
        $stdId .= $val->stdId;
      }
      $message = 'Library book '.$book_title.' is returned by '.$stdName;
      if (!empty($message_template)) {
        $message = str_replace('%%book_title%%', $book_title, $message_template); 
        $message = str_replace('%%stdName%%', $stdName, $message); 
      }
      $input_array['student_ids'] = array_unique($stdIds);
      // public function borrowed_books_history_all($staff_student_id, $userType){

      // $input_array['student_url'] = 'library_controller/borrowed_books_history_all/'.$stdId.'/'.'student';
      $input_array['message'] = $message;
    }
    $response = sendText($input_array);
    return $response;
  }
  public function return_book_bylibId() {
    $returnId = $this->input->post('returnId');
    $rId = [];
    $fAmount = [];
    foreach ($returnId as $key => $val) {
        $exp = explode('_', $val);
        array_push($rId, $exp[0]);
        array_push($fAmount, $exp[1]);
    }
    $fine_amount = $this->input->post('fine_amount');
    $lbr_card_id = $this->input->post('lbr_card_id');

    $result = $this->library_model->update_issuebook_details($rId);
    $result = 1; // Assuming you set $result to 1 for testing purposes

    if ($result) {
        $id_code = $this->_fine_transaction_library($lbr_card_id);
        if (!empty($id_code)) {
            $insert = $this->library_model->insert_fine_transacation($returnId, $id_code->sCode, $fAmount);

            $library_enable_notification = $this->settings->getSetting('library_enable_notification');
            $message = ''; // Define $message variable

            if ($library_enable_notification) {
                $response = $this->_return_notification_library_books($lbr_card_id, $rId);
                if ($response['success'] != '') {
                    $message = $response['success'];
                }
                if ($response['error'] != '') {
                    $message = $response['error'];
                }
            }

            $this->session->set_flashdata('flashSuccess', 'Return Successfully. ' . $message);
            redirect('library_controller');
        }
    } else {
        $this->session->set_flashdata('flashError', 'Something went wrong');
        redirect('library_controller');
    }
  }


  public function return_goto_collection(){
    $returnId = $this->input->post('returnId');
    $rId = [];    
    $fAmount = [];
    foreach ($returnId as $key => $val) {
      $exp = explode('_', $val);
      array_push($rId, $exp[0]);
      array_push($fAmount, $exp[1]);
    }  
    $lbr_card_id = $this->input->post('lbr_card_id');
    $result = $this->library_model->update_issuebook_details($rId);
    if ($result) {
     $id_code = $this->_fine_transaction_library($lbr_card_id);
    if (!empty($id_code)) {
      $this->library_model->insert_fine_transacation($returnId,$id_code->sCode,$fAmount);
      $get_type = $this->library_model->get_type_of_accessId($lbr_card_id);
      if (!empty($get_type)) {
        $library_enable_notification = $this->settings->getSetting('library_enable_notification');
        if ($library_enable_notification) {
          $response = $this->_return_notification_library_books($lbr_card_id, $rId);
          if($response['success'] != '') {
            $message = $response['success'];
          }
          if($response['error'] != '') {
            $message = $response['error'];
          }
         echo $message;
        }else{
          echo 1;
        }
      }else{
        echo 0;
      }
     
     }
    }
  }

  private function _fine_transaction_library($lbr_card_id){
   return $this->library_model->get_library_transcationbyReturnId($lbr_card_id);
  }

  public function generate_staff_qr_codes(){
    $data['main_content']='library/qr_code/staff_qr_code';
    $this->load->view('inc/template',$data); 
  }

  public function generate_student_qr_codes(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='library/qr_code/student_qr_code';
    $this->load->view('inc/template',$data); 
  }

  public function generate_url_qr_codes(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='library/qr_code/student_url_qr_code';
    $this->load->view('inc/template',$data); 
}

  public function generate_student_photos(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='library/qr_code/student_photo';
    $this->load->view('inc/template',$data); 
  }

  public function get_all_staff_student(){

    $type = $this->input->post('type');
    if ($type == "staff") {
      $result = $this->library_model->get_staff_all();
      $staff = array_chunk($result, 6);
      $template = "";
      foreach ($staff as $value) {
        $template .= "<tr>";
        foreach ($value as $res) { 
          $template .= "<td>".$res->name." <input type='hidden' name='stafId[]' class='staffId' value=".$res->staff_id."></td>";  
        }
        $template .= "</tr>";
      }
    }
    print($template);
  }

  public function get_all_student_student(){
    $sectionId = $this->input->post('sectionId');
    $result = $this->library_model->get_student_all($sectionId);
    $std = array_chunk($result, 6);
    $template = "";
    foreach ($std as $value) {
      $template .= "<tr>";
      foreach ($value as $res) {
        $template .= "<td>".$res->name."<input type='checkbox' checked style='float:right' name='stdId[]' class='stdId' value=".$res->stdId.">".'<br>'.$res->cls_sec." </td>";  
      }
      $template .= "</tr>";
    }
    print($template);
  }

  public function get_all_parent_of_student(){
    $sectionId = $this->input->post('sectionId');
    $result = $this->library_model->get_parent_of_student_all($sectionId);
    $std = array_chunk($result, 6);
    $template = "";
    foreach ($std as $value) {
      $template .= "<tr>";
      foreach ($value as $res) {
        $template .= "<td>".$res->name.'<br>'.$res->cls_sec." <input type='hidden' name='pId[]' class='pId' value=".$res->pId."></td>";  
      }
      $template .= "</tr>";
    }
    print($template);
  }

  public function generate_staff_qr_code(){
    $staffId = $this->input->post('staffId');
    $sName = $this->settings->getSetting('school_short_name');
    foreach ($staffId as $key => $id) {
      $len = strlen((string)$id);
      $digits = 'ST';
      for ($i = 6 - $len;$i > 0; $i--) { 
        $digits .= '0';
      }
      $s_short_name = $sName;
      $digits .= $id;
      $qrCode[] = strtoupper($s_short_name).$digits;
    }
    $result = $this->library_model->insert_qrCodestaff($qrCode,$staffId);
    if ($result){
      echo 1;
    }else{
      echo 0;
    }
  }

  public function generate_staff_qr_code_view(){
    $staffId = $this->input->post('staffId');
    $data['s_qr_code'] = $this->library_model->get_s_qrCodeDetails($staffId);
    foreach ($data['s_qr_code'] as $code) {
      // $data['image_name'] = bin2hex( $code->qr_code).'-'.'Mohan'.'-'.'1A'.'.png';
      $data['image_name'] = $code->name.'.png';
      $params['data'] = bin2hex($code->qr_code);
      $params['level'] = 'H';
      $params['size'] = 4;
      $params['savename'] = FCPATH.'assets/staff_qr_codes/'.$data['image_name'];
      $this->ciqrcode->generate($params);
    } 
  }

  public function generate_student_qr_code(){
    $stdId = $this->input->post('stdId');
    $sName = $this->settings->getSetting('school_short_name');
    foreach ($stdId as $key => $id) {
      $len = strlen((string)$id);
      $digits = 'SD';
      for ($i = 6 - $len;$i > 0; $i--) { 
        $digits .= '0';
      }
      $digits .= $id;
      $qrCode[] = strtoupper($sName).$digits;
    }
    $result = $this->library_model->insert_qrCodestudent($qrCode,$stdId);
    if ($result) {
      echo 1;
    }else{
      echo 0;
    }
  }

  public function generate_student_url_code() {
    $stdId = $this->input->post('stdId');
    $encodeArry = [];
    foreach ($stdId as $key => $id) {
      $guid = $this->qr_code_url_guid();
      $encodeArry[$id] = $guid;
    }
    $result = $this->library_model->insert_student_url_qr_code($encodeArry,$stdId);
    if ($result) {
      echo 1;
    }else{
      echo 0;
    }
    
  }

  private function qr_code_url_guid(){

    $schoolcode = substr(CONFIG_ENV['school_sub_domain'],0,5);
    mt_srand((double)microtime()*10000);//optional for php 4.2.0 and up.
    $charid = strtoupper(md5(uniqid(rand(), true)));
    $hyphen = '-'; //chr(45);// "-"
    $uuid = substr($charid, 0, 4).$hyphen
        .substr($charid, 8, 4).$hyphen
        .substr($charid,12, 4).$hyphen
        .substr($charid,16, 6).$hyphen
        .$schoolcode;
    return $uuid;
}


  public function generate_parent_qr_codes(){
    $data['classes'] = $this->library_model->get_all_class();
    $data['main_content']='library/qr_code/parent_qr_codes';
    $this->load->view('inc/template',$data); 
  }


  public function generate_parent_qr_code(){
    $pId = $this->input->post('pId');
    $sName = $this->settings->getSetting('school_short_name');
    foreach ($pId as $key => $id) {
      $len = strlen((string)$id);
      $digits = 'P';
      for ($i = 6 - $len;$i > 0; $i--) { 
        $digits .= '0';
      }
      $digits .= $id;
      $qrCode[] = strtoupper($sName).$digits;
    }
    $result = $this->library_model->insert_qrCodeparent($qrCode,$pId);
    if ($result) {
      echo 1;
    }else{
      echo 0;
    }
  }
  // public function generate_student_qr_code_view($id_code){
  //   ob_clean();
  //   header("Content-Type: image/png");
  //   $params['data'] = bin2hex($id_code);
  //   $params['level'] = 'H';
  //   $params['size'] = 4;
  //   $this->ciqrcode->generate($params);

  // }


  public function generate_student_qr_code_view(){
    $stdId = $this->input->post('stdId');
    $data['st_qr_code'] = $this->library_model->get_stu_qrCodeDetails($stdId);
    foreach ($data['st_qr_code'] as $code) {
      // $data['image_name'] = bin2hex( $code->qr_code).'-'.'Mohan'.'-'.'1A'.'.png';
      $data['image_name'] = $code->name.' - '.$code->cls_sec.'.png';
      $params['data'] = bin2hex($code->iCode);
      $params['level'] = 'H';
      $params['size'] = 4;
      // $params['savename'] = FCPATH.'assets/student_qr_codes/'.$data['image_name'];
      $clsFolder = $code->cls_sec;
      $path = FCPATH.'assets/student_qr_codes/'.$clsFolder.'/';
      if (!file_exists ($path)){
        mkdir($path,0777,true);
      }
      $params['savename'] = $path.'/'.$data['image_name'];
      $this->ciqrcode->generate($params);
    } 
  }

  public function generate_student_photos_download(){
      $this->load->library('zip');
      $stdId = $this->input->post('stdId');
      $data['st_qr_code'] = $this->library_model->get_stu_qrCodeDetails($stdId);
      $this->load->library('zip');    
    
      foreach ($data['st_qr_code'] as $code) {
        if ($code->picture_url !='') {
          $adm = str_replace('/',' ',$code->admission_no);
          $url = $this->filemanager->getFilePath($code->picture_url);
          $ext = pathinfo($url, PATHINFO_EXTENSION);
          $name = $code->name.' - '.$adm.'.'.$ext;
          $data = file_get_contents($url);
          $this->zip->add_data($name, $data);
          $this->zip->archive(FCPATH.'assets/student_qr_codes/my_backup.zip');
        }
      } 
      $this->zip->download('my_backup.zip');        
  }

  public function card_view(){
    $data['staff_student_Id'] = $this->authorization->getAvatarStakeHolderId();
    $data['userType'] ='staff';

    $site_url = site_url();
    $data['tiles'] = array(
      [
        'title' => 'Books',
        'sub_title' => 'Books',
	      'icon' => 'svg_icons/books.svg',
        'url' => $site_url.'library_controller/books_seraching_staff_student/'.$data['userType'],
        'permission' => 1
      ],
      [
        'title' => 'Cards',
        'sub_title' => 'Cards',
	      'icon' => 'svg_icons/assigncard.svg',
        'url' => $site_url.'library_controller/lbr_card_details/'.$data['staff_student_Id'].'/'.$data['userType'],
        'permission' => 1
      ],
      [
        'title' => 'Transactions',
        'sub_title' => 'Transactions',
	      'icon' => 'svg_icons/transaction.svg',
        'url' => $site_url.'library_controller/borrowed_books_history_all/'.$data['staff_student_Id'].'/'.$data['userType'],
        'permission' => 1
      ]
    );

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/staff_student/index_staff_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/staff_student/index_staff_mobile';
    }else{
      $data['main_content']='library/staff_student/index_staff_desktop';     	
    }
    $this->load->view('inc/template',$data); 
  }

  public function card_view_student(){
    $data['staff_student_Id'] = $this->parent_model->getStudentIdOfLoggedInParent();
    $data['userType'] ='student';

    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/staff_student/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/staff_student/index_mobile';
    }else{
      $data['main_content']='library/staff_student/index';     	
    }
    $this->load->view('inc/template',$data); 
  }


  public function lbr_card_details_student(){ 
    $data['lbr_staff'] = $this->library_model->get_libr_cards_staff($loggedInStaffId);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'library/staff_student/mobile/lbr_cards_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'library/staff_student/mobile/lbr_cards';
    }else{
      $data['main_content']    = 'library/staff_student/desktop/lbr_cards';      	
    }   
    $this->load->view('inc/template',$data); 
  }

  public function lbr_card_details($staff_student_id, $userType){    
    $data['userType'] = $userType;
    $data['staff_student_id'] = $staff_student_id;
    $data['lbr_staff'] = $this->library_model->get_libr_cards_staff($staff_student_id,$userType);
    // echo "<pre>"; print_r($data['lbr_staff']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'library/staff_student/mobile/lbr_cards_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'library/staff_student/mobile/lbr_cards';
    }else{
      $data['main_content']    = 'library/staff_student/desktop/lbr_cards';      	
    }
    $this->load->view('inc/template',$data); 
  }

  public function borrowed_books_history_byCardId($staff_student_id, $lbrId, $userType){
    $data['userType'] = $userType;
    $data['staff_student_id'] = $staff_student_id;
    $data['lbr_borrowed'] = $this->library_model->get_libr_books_cardWise($lbrId, $userType);
    // echo "<pre>"; print_r($data['lbr_borrowed']); die();
    if ($this->mobile_detect->isMobile()) { 
      $data['main_content']='library/staff_student/mobile/library_card_history';
    } else { 
      $data['main_content']='library/staff_student/desktop/library_card_history';
    }  
    $this->load->view('inc/template',$data); 
  }


  public function borrowed_books_history_all($staff_student_id, $userType){
    $get_lbr_cards = $this->library_model->get_libr_cards_staff($staff_student_id,$userType);

    $lbrIds =[];
    foreach ($get_lbr_cards as $key => $value) {
      array_push($lbrIds, $value->id);
    }    
    $data['userType'] = $userType;
    $data['staff_student_id'] = $staff_student_id;
    if (!empty($lbrIds)) {
      $data['lbr_borrowed'] = $this->library_model->get_libr_books_all_tx($lbrIds, $userType);
    }
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'library/staff_student/mobile/library_card_history_all_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']    = 'library/staff_student/mobile/library_card_history_all';
    }else{
      $data['main_content']    = 'library/staff_student/desktop/library_card_history_all';      	
    } 
    $this->load->view('inc/template',$data); 
  }

  public function borrowed_books_staff(){
    $loggedInStaffId = $this->authorization->getAvatarStakeHolderId();
    $data['lbr_staff_borrowed'] = $this->library_model->get_libr_books_staff_all($loggedInStaffId);
    //echo "<pre>"; print_r($data['lbr_staff_borrowed']); die();
    if ($this->mobile_detect->isMobile()) { 
      $data['main_content']='library/staff_student/mobile/borrowed_books';
    } else { 
      $data['main_content']='library/staff_student/desktop/borrowed_books';
    }  
    $this->load->view('inc/template',$data); 
  }

  public function books_seraching_staff_student($userType){
    $data['userType'] = $userType;
    $this->load->library('Mobile_Detect');    
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/staff_student/mobile/books_serach_staff_student_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/staff_student/mobile/books_serach_staff_student';
    }else{
      $data['main_content']='library/staff_student/desktop/books_serach_staff_student';      	
    }
    $this->load->view('inc/template',$data); 
  }
  
 
  private function __duplicate_booksfound($book_title, $remarks, $source, $author, $publisher, $book_arrival, $published_year, $price, $subject_category, $book_category){

    if (!empty($book_title)) {
      $this->db->like('book_title', $book_title);
      $this->db->like('source', $source);
      $this->db->like('author', $author);
      $this->db->like('publisher_name', $publisher);
      $this->db->like('yearof_publishing', $published_year);
      $this->db->like('subject', $subject_category);
      $res = $this->db->get('library_books')->row();

      if (empty($res)) {
        //Books dont match
        return 0;
      } else {
        return 1;
      }

      //   if (!empty($res->book_title)) {
      //     if ($res->book_title == $book_title) {
      //       $found = 1;
      //     }
      //   }else{
      //      $found = 0;
      //   }
      //   return $found;
      }
      return 0;
    }

    private function  __marshalbookType($book_type){
      if (!empty($book_type)) {

        $books_type = $this->settings->getSetting('books_type');
        foreach ($bookType as  $val) {
          if ($val->name == $book_type) {
            $found = $val->value;
          }
        }
        return $found;
      }
     
    }

   //submit books data from a excel import
    public function importBooksData () {
      $fromId = $this->input->post('from_id');
      $toId = $this->input->post('to_id');

      for ($id = $fromId; $id <= $toId; $id++) {
        $rawData = $this->db->select('*')->from('library_books_vinayaka_raw_data')->where('id',$id)->get()->row();
        if (empty($rawData)) continue;
        $result = new stdClass();
        //Marshal data
        $result->book_type = 1;
        if($rawData->book_type != '') {
          $result->book_type = $this->__marshalbookType($rawData->book_type);
        }
        $result->category = $rawData->category;
        $result->language = $rawData->language;
        $result->author = $rawData->author;
        $result->yearof_publishing = $rawData->year_of_publishing;
        $result->publisher_name = $rawData->publisher;
        $result->book_title = $rawData->book_title;
        $result->series = $rawData->series;
        $result->edition = $rawData->edition;
        $result->isbn = $rawData->isbn;
        $result->pages = $rawData->pages;
        $result->call_number = $rawData->call_number;
        $result->supplier = $rawData->supplier;
        $result->bill_no_date = $rawData->bill_no_date;
        $result->acc_no = $rawData->acc_no;
        $result->subject = $rawData->subject;
        $result->location_book = $rawData->location;
        $result->source = $rawData->source;

        $this->db->trans_start();
        $book_insert_id = $this->library_model->insert_import_data($result);

        $book_copies = new stdClass();
        $book_copies->book_id = $book_insert_id;
        $book_copies->access_code = null;
        $book_copies->costof_book = $rawData->price;
        $book_copies->date_of_accession = $rawData->date_of_possession;

        $res = $this->library_model->insert_import_book_copies($book_copies);
        $this->db->trans_complete();
        if ($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          echo "Failed to add book ID: ".$rawData->id." book: ".$rawData->book_title;die();
          continue;
        }

        $this->db->trans_commit();

        // $book_copies->access_code = $this->generate_code($book_insert_id);

        /*$result_data =  $this->library_model->bulk_insert_northhill_books_data($result);
        if ($result_data) {
         $res =  $this->generate_code($result_data);
        }
        $isBookDuplicate = $this->__duplicate_booksfound($result->book_name, $result->remarks, $result->source, $result->author, $result->publisher, $result->book_arrival, $result->published_year, $result->price, $result->subject_category, $result->book_category);
        if ($isBookDuplicate == 0) {
          $bookType = $this->__marshalbookType($result->book_type);
          $result->book_type = $bookType;
          $result->contains = $result->media_type;
          $result->category = $result->subject_category;
          $result->yearof_publishing = $result->published_year;
          $result->publisher_name = $result->publisher;
          $result->series = null;
          $result->volume = null;
          $result->subject = $result->subject_category;
          $result->location_book = null;
          $result->book_title = $result->book_name;
          $result->access_code = $result->barcode;
          $result->costof_book = (empty($result->price))?null:$result->price;
          $result->date_of_accession = $result->book_arrival;
          $result_data =  $this->library_model->insert_import_booksdata($result);
        } else {
          $result->access_code = $result->barcode;
          $result->date_of_accession = $result->book_arrival;
          $result->costof_book = $result->price;
          $result_data =  $this->library_model->update_import_booksdata($result);
        }*/
      }
      $this->session->set_flashdata('flashSuccess', 'Imported from '.$fromId.' to '.$toId);
      redirect('csv/lbr_books');
    }


    public function serachby_isbn(){
      $isbn_number = $this->input->post('isbn_number');
      $books = $this->library_model->get_librar_databyisbn($isbn_number);
      $this->_serachby_book($books);
    }

    public function serachby_book(){
      $books_title = $this->input->post('books_title');
      $books = $this->library_model->get_librar_databybook($books_title);
      $this->_serachby_book($books);
    }

    public function serachby_author(){
      $book_author = $this->input->post('book_author');
      $books = $this->library_model->get_librar_databyauthor($book_author);
      $this->_serachby_book($books);
    }

    public function serachby_p_name(){
      $publisher_name = $this->input->post('publisher_name');
      $books = $this->library_model->get_librar_databypublisher($publisher_name);
      $this->_serachby_book($books);
    }

    public function new_arrivals(){
      $new_arrivals_date = $this->settings->getSetting('new_arrivals_date');
      if ($new_arrivals_date) {
        $books = $this->library_model->get_new_arrivals($new_arrivals_date);
        $this->_serachby_book($books);
      }else{
        return false;
      }
      
    }

    public function serachby_book_accessNumber(){
      $accessId = $this->input->post('accessId');
      $books = $this->library_model->get_librar_books_databyId($accessId);   
      $this->_serachby_book($books);
    }

    public function books_search_view($userType){
      $books_title = $this->input->post('books_title');
      $book_author = $this->input->post('book_author');
      $publisher_name = $this->input->post('publisher_name');
      $data['userType']= $userType;
      $books_list = $this->library_model->get_filter_serachby_bookslist($books_title,$book_author,$publisher_name);
      $lbr_cards_master = $this->library_model->get_lbrCards_master_details($data['userType']);
      if (empty($books_list)) {
        $this->session->set_flashdata('flashInfo', 'Data not found');
        redirect('library_controller/books_seraching_staff_student/'.$userType);
      }
      // $books_type = $this->settings->getSetting('books_type');
      foreach ($books_list as $key => &$res) {
        $found = 0;
        foreach ($lbr_cards_master as $key => $val) {
          if ($val == $res->book_type) {
            $found = 1;
            break;
          }
        }
        if ($found) {
            if ($res->bCopies >= 1) {
              $res->available = 'Available';
            }else{
              $res->available = 'Not Available';
            }
        }else{
          $res->available = 'Reference only';
        }
      }
      $data['books_list'] = $books_list;
      $data['book_cat'] = $this->settings->getSetting('books_type');
        if ($this->mobile_detect->isTablet()) {
          $data['main_content']   = 'library/staff_student/mobile/books_search_view_tablet';
        }else{
          $data['main_content']    = 'library/staff_student/mobile/books_search_view';      	
        }

      $this->load->view('inc/template',$data); 
    }

    public function display_newly_add_books(){
      $books = $this->library_model->default_newly_added_librar_books();

      $this->_serachby_book($books); 
    }
    private function _serachby_book($books){
      $permitAddlibrary = $this->authorization->isAuthorized('LIBRARY.VIEW_EDIT_DELETE');
      if (empty($books)) {
        return false;
      }
      $books_type = $this->settings->getSetting('books_type');
      foreach ($books as $key => &$res) {
        foreach ($books_type as $key => $val) {
          if ($val->value == $res->book_type) {
            $res->bType = $val->name;
          }
        }
      }

      $html = '';
      $i = 1;
      $tCopies = '';
      $html .= '<table id="customers2" class="table table-bordered">
        <thead>
          <tr>
            <th style="width:2%">#</th>
            <th style="width:20%">Title</th>
            <th style="width:20%">Sub Title</th>
            <th style="width:5%">Type</th>
            <th style="width:10%">Author</th>
            <th style="width:15%">Publisher</th>
            <th style="width:8%"># of copies</th>';
            //<th style="width:12%">Reserve Books</th>';
            if ($permitAddlibrary) {
              $html.= '<th style="width:20%">Action</th>';       
            }

            $html.= '</tr>
        </thead><tbody>';
      foreach ($books as $key=>$book) {
       
        $html .='<tr>';
        $html .='<td>'.$i++.'</td>';
        $html .='<td>'.$book->book_title.'</td>';
        $html .='<td>'.$book->b_sub_title.'</td>';
        $html .= '<td>' . (isset($book->bType) ? $book->bType : 'N/A') . '</td>';
        $html .='<td>'.$book->author.'</td>';
        $html .='<td>'.$book->publisher_name.'</td>';

        // $html .='<td>'.!empty($book->nCopies).' ('.$book->status.')'.'</td>';
        $bCopies = 0;
        $tbookCopies = 0;
        if (isset($book->bCopies)) {
          $bCopies = $book->bCopies;
        }
        if (isset($book->tbookCopies)) {
          $tbookCopies = $book->tbookCopies;
        }
        $html .='<td>'.$bCopies.' / '.$tbookCopies.'</td>';
          if ($permitAddlibrary) {
          //    $html .='<td> 
          // <a href='.site_url('library_controller/bookd_details_edit/'.$book->id).' class="btn btn-warning " data-placement="top" data-toggle="tooltip" data-original-title="Edit"><i class="fa fa-edit"></i></a>
          // <a onclick="delete_books_confirm_box('.$book->id.')" class="btn btn-danger " data-placement="top" data-toggle="tooltip" data-original-title="Delete"><i class="fa fa-trash-o"></i></a>
          // <a href='.site_url('library_controller/access_code_view/'.$book->id).' class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="View book copies"><i class="fa fa-file"></i></a>
          // </td>';
          //    $html .='<td> 
          // <a onclick="bootbox_reserved()" class="btn btn-primary">Reserved</a>
          // </td>';
          $html .='<td> 
          <a href='.site_url('library_controller/bookd_details_details/'.$book->id).' class="btn btn-primary">Details</a>
          <a data-toggle="modal" data-target="#summary" onclick="get_books_map_copies('.$book->id.')" id="map'.$book->id.'" class="btn btn-primary">View / Map</a>
          <a data-toggle="modal" data-target="#view_transction" style="margin-top:10px;margin-left:25px" onclick="get_transcation_report('.$book->id.')" id="map'.$book->id.'" class="btn btn-primary">View Transactions</a>          
          </td>';
        }
      }
      $html .= '</tr></tbody></table>';
      print($html);
    }
    

    public function bookd_details_details($booksId){
      $data['booksId'] = $booksId;
      $data['book_details'] = $this->library_model->get_qrCodeDetails($booksId);
      // echo "<pre>"; print_r($data['book_details']); die();
      $data['main_content']='library/book_details/books_details';
      $this->load->view('inc/template',$data);   
    }

    public function daily_transaction(){
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/reports/daily_transaction_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/reports/daily_transaction_mobile';
      }else{
        $data['main_content']='library/reports/daily_transaction';    	
      }

      
      $this->load->view('inc/template_fee',$data); 
    }

    public function daily_transaction_report(){
      $from_date = $_POST['from_date'];
      $to_date = $_POST['to_date'];
      $selectType = $_POST['selectType'];
      $result = $this->library_model->get_transactionfor_lbr_daywise($from_date,$to_date,$selectType);
      echo json_encode($result);
    }

    public function get_transcation_report(){
      $bookId = $_POST['bookId'];
      // echo "<pre>"; print_r($bookId); die();
      $result = $this->library_model->get_transcation_report($bookId);
      echo json_encode($result);
    }



    public function print_add_cart(){
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/individual_qr_print_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/individual_qr_print_mobile';
      }else{
        $data['main_content']='library/individual_qr_print';     	
      }
      $this->load->view('inc/template_fee',$data); 
    }

    public function student_qr_codes(){
      $data['main_content']='library/student_qr_code';       
      $this->load->view('inc/template_fee',$data);
    }

    public function generate_student_qr_codes_list(){
      $input = $this->input->post();
      $student_qr_codes = $this->library_model->get_student_qr_codes_list();
      foreach ($student_qr_codes as $key => $val) {
        $qrCode[] = $val->identification_code;
      }
      $data['print_cart'] = $qrCode;
      $data['copies'] = $input['no_of_copies'];
      $data['per_row'] = $input['per_row'];
      $data['main_content']='library/student_qr_code';       
      $this->load->view('inc/template_fee',$data);
    }
    public function add_to_cart_for_print(){
      $lbcId = $this->input->post('lbcId');
      echo $this->library_model->insert_access_number_forprint($lbcId);
    }

    public function lbr_fine_amount($lc_card_access = ''){
      $data['access_code'] = $lc_card_access;
      $data['all_names'] = $this->library_model->get_staff_student_names();
      $data['payment_modes'] = $this->settings->getSetting('lbr_payment_modes');
      // $data['payment_modes'] = $feeConfig['allowed_payment_modes'];
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/fine_amount_collection_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/fine_amount_collection_mobile';
      }else{
        $data['main_content']='library/fine_amount_collection';     	
      }

      
      $this->load->view('inc/template',$data);
    }

    private function _get_identification_code($accessId){
      return $this->library_model->get_identification_code_fine($accessId);
    }

    public function finehistory_accessWise(){
      $accessId = $this->__disambiguateAccessId($this->input->post('accessId'));
      $limit =  $this->input->post('limit');
      $data['accessId'] = $accessId;
      $id_code = $this->_get_identification_code($data['accessId']);
      $result = $this->library_model->get_lbr_fine_details($id_code,$limit);
      echo json_encode($result);
    }

    public function library_fine_insert(){
      $accessId = $this->__disambiguateAccessId($this->input->post('access_number'));
      $data['accessId'] = $accessId;
      $id_code = $this->_get_identification_code($data['accessId']);
      $result = $this->library_model->insert_fine_amountwith_id_code($id_code);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Fine amount succesfully');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      redirect('library_controller');
    }

    public function fine_due_list_all(){
      // $data['due_list'] = $this->library_model->get_staff_duelist_fine();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/reports/fine_due_list_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/reports/fine_due_list_mobile';
      }else{
        $data['main_content']='library/reports/fine_due_list';     	
      }
      $this->load->view('inc/template_fee',$data); 
    }

    public function due_list_all(){
      // $data['due_list'] = $this->library_model->get_staff_not_retur_list();
      // echo "<pre>"; print_r($data['due_list']); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/reports/due_list_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/reports/due_list_mobile';
      }else{
        $data['main_content']='library/reports/due_list';     	
      }
      $this->load->view('inc/template_fee',$data); 
    }

    public function get_due_list_userwise(){
      $holder_type = $this->input->post('holder_type');
      switch ($holder_type) {
        case 'SD':
          $classSection = $this->input->post('classSection');
          $result = $this->library_model->get_studet_duelist($classSection);
          break;
        case 'ST':
          $result = $this->library_model->get_staff_duelist_fine();
          break;
      }
      echo json_encode($result);
    }

    public function get_due_list_not_returning_userwise() {
      $holder_type = $this->input->post('holder_type');
      $due_cross = $this->input->post('due_cross');
      switch ($holder_type) {
        case 'SD':
          $classSection = $this->input->post('classSection');
          $result = $this->library_model->get_studet_not_return_list($due_cross, $classSection, 0);
          break;
        case 'ST':
          $result = $this->library_model->get_staff_not_retur_list($due_cross,0,0);
          break;
        case 'BOOK':
          $book_accessid = $this->input->post('book_accessid');
          $result = $this->library_model->get_staff_not_retur_list($due_cross,0,$book_accessid);
          if (empty($result)) {
            $result = $this->library_model->get_studet_not_return_list($due_cross,0,$book_accessid);
          }
          break;
      }
      $borrowed = [];
      foreach ($result as $key => $value) {
        $borrowed[$value->s_name][] = $value;
      }
      echo json_encode($borrowed);
    }

    public function lbr_bulk_circulation(){
      $data['classes'] = $this->library_model->get_classandsection_detils(); 
      //echo "<pre>"; print_r($data['classes']); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/transaction/bulk_circulation_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/transaction/bulk_circulation_mobile';
      }else{
        $data['main_content']='library/transaction/bulk_circulation';     	
      }
      $this->load->view('inc/template',$data); 
    }

    public function get_studentClassSectionwise(){
      $cls_name = explode('_', $this->input->post('cls_name')); 
      $result = $this->library_model->get_classwise_student_list($cls_name);
      echo json_encode($result);
    }

    private function _lbr_books_due_date_cal($result){
      if ($result->status == 'Return') {
        $issueDate = $result->issue_date;
        $cDate = date('Y-m-d');
        $dayCount = $result->hold_period;
        $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
        $ts1 = strtotime($cDate);
        $ts2 = strtotime($due_date);
        $datediff = $ts1 - $ts2;
        $days_exceeded=($datediff / (60*60*24));
        $perday_fine=$result->fine_per_day *  $days_exceeded;

        if ($days_exceeded > 0) {
          $days = $days_exceeded;
        }else{
          $days = 0;
        }
        if ($perday_fine > 0) {
          $fine = $perday_fine;
        }else{
          $fine = 0;
        }
        $result->due_date = date('d-m-Y',strtotime($due_date));
        $result->days = $days;
        $result->fine = $fine;
      }else{
        $result->due_date = '';
        $result->days = '';
        $result->fine = '';
      }
      return $result; 
    }
    public function books_serach_bulk(){
      $book_accessid = $this->input->post('book_accessid');
      $lbrcardId = $this->input->post('lbrcardId');
      $result = $this->library_model->get_data_bulk_books_serach($book_accessid,$lbrcardId);
      if (empty($result)) {
        $status = 2;
      } else {
        $status = $this->_lbr_books_due_date_cal($result);
      }
      echo json_encode($status);
    }

    public function get_lc_id_by_stdid(){
      $stdId = $this->input->post('stdId');
      $result = $this->library_model->get_student_lc_idBy_iCode($stdId);
      echo json_encode($result);
    }

    public function get_library_accessIdbyLbrId(){
      $lbrId = $this->input->post('lbrId');
      $result = $this->library_model->get_student_accessId_idBy_iCode($lbrId);
      echo json_encode($result);
    }
    public function insert_library_transacation_bulk(){
      echo $this->library_model->issue_transcation_table_insert();
      
    }
    private function _get_library_transcation_id($book_accessId){
      return $this->library_model->get_library_transcation_id($book_accessId);
    }

    public function serach_return_books_access_wise(){
      $accessId = $_POST['accessId'];
      $result = $this->library_model->get_book_accessId_return_books($accessId);
    }

    public function summary_report(){
      $data['books_summary'] = $this->library_model->get_librar_books_summary();
      // echo "<pre>"; print_r($data['books_summary']); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/reports/summary_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/reports/summary_mobile';
      }else{
        $data['main_content']='library/reports/summary';     	
      }
      $this->load->view('inc/template',$data); 
    }

    public function history_student_staff(){
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/reports/history_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/reports/history_mobile';
      }else{
        $data['main_content']='library/reports/history';    	
      }
      $this->load->view('inc/template_fee',$data); 
    }

    public function get_class_all(){
      $result = $this->library_model->get_class_section_all();
      echo json_encode($result);
    }

    public function get_class_section_wise_std_data(){
      $cls_section = $this->input->post('cls_section');
      list($class_id,$sectionId) = explode('_',$cls_section);
      $result = $this->library_model->get_class_section_studetn_data($class_id,$sectionId);
      echo json_encode($result);
    }

    public function get_user_selection_data(){
      $user_selection = $this->input->post('user_selection');
      list($sId,$type) = explode('_',$user_selection);
      $result = $this->library_model->get_user_selection_libraryCard($sId,$type);
      echo json_encode($result);
    }

    public function get_staff_all_lbr(){
      $result = $this->library_model->get_staff_all_library();
      echo json_encode($result);
    }

    public function get_all_data_by_lbcId(){

      // $lbcId = $this->input->post('lbcId');
      $userId = $this->input->post('userId');
      // list($lbId,$type) = explode('_',$lbcId);
      list($uId,$types) = explode('_',$userId);
      // $result = $this->library_model->fetch_lbrIdwise_data($lbId,$types,$uId);
      $result = $this->library_model->fetch_lbrIdwise_data($types,$uId);
      echo json_encode($result);

    }

    public function history_of_lbrusers($sId, $type){
      $data['lbr_details'] = $this->library_model->get_all_lbr_details_byId_Type($sId,$type);
      // echo "<pre>"; print_r($data['lbr_details']); die();
      
      $data['main_content']='library/reports/lbr_history';
      $this->load->view('inc/template',$data); 
    }



    // public function _student_lbr_history($sId){
    // }

    public function _staff_lbr_history($sId){
      $result = $this->library_model->get_all_lbr_details_bystaff($sId);
    }

    public function damage_lost_books(){
      $data['all_names'] = $this->library_model->get_staff_student_names();
      $data['payment_modes'] = $this->settings->getSetting('lbr_payment_modes');
      // echo "<pre>"; print_r($data['payment_modes']); die();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content']='library/transaction/damage_lost_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content']='library/transaction/damage_lost_mobile';
      }else{
        $data['main_content']='library/transaction/damage_lost';     	
      }
      $this->load->view('inc/template',$data);
    }

    public function get_return_book_damagefine_collection(){
      $accessId =  $this->input->post('accessId'); 
      $accessId = $this->__disambiguateAccessId($this->input->post('accessId'));
      $data['accessId'] = $accessId;
      $idCode = $this->library_model->get_identification_code_fine($accessId);
      $result = $this->library_model->library_return_books_damage_collections($idCode);      
      echo json_encode($result);
    }

    public function library_damage_book_insert(){
      // $input = $this->input->post();
      // echo "<pre>"; print_r($input); die();

      $transId = $this->input->post('transId');
      $damge_books_price = $this->input->post('damge_books_price');
      $rId = [];    
      $fAmount = [];
      foreach ($transId as $key => $val) {
        $exp = explode('_', $val);
        array_push($rId, $exp[0]);
        array_push($fAmount, $exp[1]);
      }

      $bPrice = [];
      foreach ($damge_books_price as $key => $val) {
        $exp = explode('_', $val);
        array_push($bPrice, $exp[1]);
      }
      $result = $this->library_model->update_issue_damage_book_details($rId,$fAmount,$bPrice);
      
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Damage / lost books succesful return');
        redirect('library_controller');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
        redirect('library_controller');
      }
    }

    public function stock_check(){
      $libraries = $this->library_model->get_libraries_details();
      $lbrId = $this->session->userdata('libraries');   
      if (empty($lbrId) && !empty($libraries)) {
        $lbrId = $this->session->set_userdata('libraries',$libraries[0]['id']);
      }
      $data['stock_data'] = $this->library_model->get_stock_check_data($lbrId);
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'library/transaction/stock_check_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'library/transaction/stock_check_mobile';
      }else{
        $data['main_content'] = 'library/transaction/stock_check';     	
      }
      $this->load->view('inc/template',$data);
    }

    public function libCheckInReport(){
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'library/transaction/stock_check_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'library/transaction/stock_check_mobile';
      }else{
        $data['main_content'] = 'library/transaction/checkinReport';     	
      }
      $this->load->view('inc/template',$data);

    }
    public function new_stock_check(){
      $data['AvatarId'] = $this->authorization->getAvatarId();
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'library/transaction/new_stock_check_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'library/transaction/new_stock_check_mobile';
      }else{
        $data['main_content'] = 'library/transaction/new_stock_check';    	
      }
      $this->load->view('inc/template',$data);
    }

    public function save_stock_check(){
      $result = $this->library_model->save_stock_check();
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'The stock check is created successfuly');
        redirect('library_controller/stock_check');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
        redirect('library_controller/new_stock_check');
      }
    }

    public function start_checking($id){
      $data['id'] = $id; 
      $data['name'] = $this->library_model->get_check_name($id);
      $data['last_five_records'] = $this->library_model->last_five_records_lsc($id);
      if(!empty($data['last_five_records'])){
        foreach($data['last_five_records'] as &$row){
          $row['book_title'] = str_replace("'","`",$row['book_title']);
          $row['book_title'] = str_replace('"','',$row['book_title']);
          $row['book_title'] = trim($row['book_title']);
          $row['checked_on'] = date('d-m-Y H:i a', strtotime($row['checked_on']));
       }
      }
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'library/transaction/start_checking_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'library/transaction/start_checking_mobile';
      }else{
        $data['main_content'] = 'library/transaction/start_checking';     	
      }
    
      $this->load->view('inc/template',$data);
    }

    public function book_check_in(){
      $access_code = $_POST['access_code'];
      $check_id = $_POST['check_id'];
      $status = $_POST['status'];
      $missing_remark=$_POST['missing_remark'];
      $checked_by = $this->authorization->getAvatarId();
      if($status === 1)
        $status = 'Verified';
      if($status === 0)
        $status = "Discarded";
      $result = $this->library_model->book_stock_update($access_code, $check_id, $status, $checked_by,$missing_remark);
      $data = $this->library_model->getBookDetails1($access_code);
      echo json_encode(array('result'=>$result, 'data'=>$data));
    }

    public function complete_checking(){
      $result = $this->library_model->complete_checking($_POST['id']);
      echo $result;
    }

    public function stock_report($id){
      // $data['stock_report_data'] = $this->library_model->stock_report($id);
      // $data['missing_stock_report_data'] = $this->library_model->missing_stock_report($id);
      // echo '<pre>';print_r( $data['missing_stock_report_data']);die();
      $data['name'] = $this->library_model->get_check_name($id);
      $data['check_id'] = $id;
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'library/transaction/stock_check_report_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'library/transaction/stock_check_report_mobile';
      }else{
        $data['main_content'] = 'library/transaction/stock_check_report';
      }
      $this->load->view('inc/template',$data);
    }
    public function get_stock_check_report_chunkIds(){
      $check_id = $_POST['check_id'];
      $result = $this->library_model->stock_check_report_ids($check_id);
      $stockCheck_ids = array_chunk($result, 1000);
      echo json_encode($stockCheck_ids);
    }
    public function get_stock_check_report_data(){
      $stockCheckIds = $_POST['stockCheckIds'];
      $result = $this->library_model->stock_report_data($stockCheckIds);
      echo json_encode($result);

    }

    public function missing_report($id){
      $data['check_id'] = $id;
      $data['name'] = $this->library_model->get_check_name($id);
      $data['main_content'] = 'library/transaction/stock_check_missing_book_report';
      $this->load->view('inc/template',$data);
    }

    public function get_stock_check_missing_books_report_data(){
      $result = $this->library_model->stock_report_missing_books_data();
      echo json_encode($result);
    }
    
    public function getBookDetails(){
      
      $result = $this->library_model->getBookDetails($_POST['access_code'], $_POST['check_id']);
      print_r($result);
    }

    public function report_missing(){
      $result = $this->library_model->report_missing($_POST['check_id'], $_POST['access_code'],$_POST['missing_remark']);
      echo $result;
    }

    public function complete_checking_request(){
      $id = $_POST['id'];
      $result = $this->library_model->no_of_unchecked_books($id);
      echo $result;

    }

    public function libraries(){
      $data['libraries'] = $this->library_model->get_libraries_details();
      $data['main_content'] = 'library/libraries';
      $this->load->view('inc/template',$data);
    }

    public function add_libraries(){
      $result = $this->library_model->add_libraries_name();
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Created successfuly');
        redirect('library_controller/libraries');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong');
        redirect('library_controller/libraries');
      }
    }

     public function delete_libraries($id){
      $result = $this->library_model->delete_libraries_name($id);
      if ($result) {
        $this->session->set_flashdata('flashSuccess', 'Deleted successfuly');
        redirect('library_controller/libraries');
      }else{
        $this->session->set_flashdata('flashError', 'Something went wrong/Library is used');
        redirect('library_controller/libraries');
      }
    }

    public function set_sessions_in_librires(){
      $this->session->set_userdata('libraries', $_POST['libraries']);
    }

    public function get_copies_of_books(){
      $bookId = $_POST['bookId'];
      $result = $this->library_model->get_copies_map_entery($bookId);
      echo json_encode($result);
    }

    public function update_accees_number_maping(){
      $access_code = $_POST['access_code'];
      echo $this->library_model->update_access_number_to_lb_copies($access_code);
    }

    public function print_qr_code_books(){
      $data['running_num'] = $this->library_model->get_last_number();
      $data['main_content']='library/individual_qr_print';
      $this->load->view('inc/template',$data); 
    }

    public function insert_generate_last_digit(){
      $this->load->library('Zend');
      //load in folder Zend
      $this->zend->load('Zend/Barcode');

      $input = $this->input->post();
      if (empty($input['to'])) {
        redirect('library_controller/print_qr_code_books');
      }
      // $result = $this->library_model->get_last_number_to_generate($lastId);    
      for($i=$input['from']; $i <= $input['to'] ; $i++){
        $qrCode[] = $input['format'].sprintf("%".''."02d",$i);
      }
      $lastId = $this->library_model->insert_last_running_number();
      $data['running_num'] = $this->library_model->get_last_number();
      $data['print_cart'] = $qrCode;
      $data['copies'] = $input['no_of_copies'];
      $data['per_row'] = $input['per_row'];
      // $data['rack_number'] = $input['rack_number'];
      $data['type'] = $input['type'];
      $data['top_name'] = $input['top_name'];
      // if ($this->mobile_detect->isTablet()) {
      //   $data['main_content']   = 'helptext/about_tablet';
      // }else if($this->mobile_detect->isMobile()){
      //   $data['main_content']    = 'helptext/about_mobile';
      // }else{
      //   $data['main_content']    = 'helptext/about_desktop';      	
      // }

      $data['main_content']='library/individual_qr_print_new';
      $this->load->view('inc/template',$data);
  }

  public function fine_daily_transaction(){
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/reports/fine_daily_tx_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/reports/fine_daily_tx_mobile';
    }else{
      $data['main_content']='library/reports/fine_daily_tx';    	
    }
    $this->load->view('inc/template_fee',$data);
  }

  public function disabled_books_report(){
    // $data['disabled_books'] = $this->library_model->disabled_books_report();
    //    //echo "<pre>"; print_r($data['disabled_books']); die();
   if ($this->mobile_detect->isTablet()) {
      $data['main_content']='library/reports/disabled_books_report';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content']='library/reports/disabled_books_report';
    }else{
      $data['main_content']='library/reports/disabled_books_report';      
    }
    $this->load->view('inc/template',$data);
  }

  public function disabled_books_get_report()
  {
      $disabled_books = $this->library_model->disabled_books_report();
     //echo "<pre>"; print_r($result['disabled_books']); die();
      echo json_encode($disabled_books);

  }


  public function fine_daily_tx_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->library_model->get_fine_daily_tx_data($from_date, $to_date);
    $lbr_payment_modes = $this->settings->getSetting('lbr_payment_modes');
    foreach ($result as $key => &$val) {
      foreach ($lbr_payment_modes as $key => $mode) {
        if ($mode->value == $val->payment_type) {
          $val->pType = $mode->name;
        }
      }
    }
    echo json_encode($result);
  }
  
  public function borrow_return(){
    $data['all_names'] = $this->library_model->get_staff_student_names();
    $data['main_content']='library/transaction/borrow_return';
    $this->load->view('inc/template',$data);
  }


  public function return_book_by_id(){
    $lbId = $_POST['lbId'];
    $return_date = $_POST['return_date'];
    echo $this->library_model->update_issuebook_detailsbyId($lbId, $return_date);
  }

  public function renewal_issuebook_details(){
    $lbId = $_POST['lbId'];
    $return_date = $_POST['return_date'];
    $issue_date = date('d-m-Y');
   
    $result= $this->library_model->renewal_issuebook_details($lbId, $return_date, $issue_date);
   
    echo json_encode($result);
  }

  public function borrow_return_goto_collection(){
    $returnId = $this->input->post('returnId');
    $return_date = $this->input->post('return_date');
    $rId = [];    
    $fAmount = [];
    foreach ($returnId as $key => $val) {
      $exp = explode('_', $val);
      array_push($rId, $exp[0]);
      array_push($fAmount, $exp[1]);
    }  
    $lbr_card_id = $this->input->post('lbr_card_id');
    $result = $this->library_model->update_borrow_returnbook_details($rId, $return_date);
    if ($result) {
     $id_code = $this->_fine_transaction_library($lbr_card_id);
    if (!empty($id_code)) {
      $this->library_model->insert_fine_transacation($returnId,$id_code->sCode,$fAmount);
      $get_type = $this->library_model->get_type_of_accessId($lbr_card_id);
      if (!empty($get_type)) {
        $library_enable_notification = $this->settings->getSetting('library_enable_notification');
        if ($library_enable_notification) {
          $response = $this->_return_notification_library_books($lbr_card_id, $rId);
          if($response['success'] != '') {
            $message = $response['success'];
          }
          if($response['error'] != '') {
            $message = $response['error'];
          }
         echo $message;
        }else{
          echo 1;
        }
      }else{
        echo 0;
      }
     
     }
    }
  }

  public function check_access_number_exit_in_db_value(){
    $value = $_POST['value'];
    echo $this->library_model->check_access_number_exit_in_db_value($value);
  }

  public function get_library_borrow_details() {
    $book_access_number = $_POST['book_access_number'];
    echo $this->library_model->get_library_borrow_detailsby_access_number($book_access_number);
  }

  public function detailsof_rfid_books(){    
    $selectedLbId =   $this->session->userdata('libraries');
    $accessId = $this->input->post('accessId');
    $rDate = $this->input->post('rDate');
    $data['accessId'] = $accessId;

    // echo "<pre>"; print_r($accessId);die();
    
    // $result = $this->library_model->get_libraryid_details($accessId);
    $result = $this->library_model->get_libraryid_rfid_details($accessId, $selectedLbId);
    if ($result == 2) {
      echo json_encode($result);
      return false;
    }
    $count = 0;
    foreach ($result['lbr'] as $key => $val) {
      if (!empty($val->id)) {
        $count++;
      }
    }

    $books_type = $this->settings->getSetting('books_type');
    $template = '';
    foreach ($result['lbr'] as $key =>  &$res) {
      $issueDate = $res->issue_date;
      $cDate = $rDate;
      if ($rDate==0) {
        $cDate = date('Y-m-d');
      }
     // $due_date = $res->due_date;
      $dayCount = $res->hold_period;
      $due_date = date('Y-m-d',strtotime($issueDate) + (24*3600*$dayCount));
      $ts1 = strtotime($cDate);
      $ts2 = strtotime($due_date);
      $datediff = $ts1 - $ts2;
      $days_exceeded=($datediff / (60*60*24));
      $perday_fine=$res->fine_per_day *  $days_exceeded;

      if ($days_exceeded > 0) {
        $days = $days_exceeded;
      }else{
        $days = 0;
      }
      if ($perday_fine > 0) {
        $fine = $perday_fine;
      }else{
        $fine = 0;
      }

      $res->remaining =   $res->num_books - $count;
      $res->num_books = $res->num_books;
      $res->booktitle = $res->book_title;
      $res->issue_date =date('d-m-Y', strtotime($res->issue_date)) ;
      $res->due_date = date('d-m-Y',strtotime($due_date));
      $res->days = $days;
      $res->fine = $fine;
      $booType = json_decode($res->book_type);
      foreach ($books_type as $key => $val) {
        if ($val->value == $res->bType) {
          $res->bType = $val->name;
        }
      }
    }
    echo json_encode($result);
  }

  public function get_library_access_code_damage_lost(){
    $access_code = $_POST['access_code'];
    $success = $this->library_model->get_library_access_code_damage_lost($access_code);
    echo json_encode($success);
  }

   public function damage_lost_books_report() {
    $data['main_content']    = 'library/reports/damage_lost_books_report';
    $this->load->view('inc/template', $data);
  }

  public function damage_lost_report(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $selectType = $_POST['selectType'];
    $result = $this->library_model->get_damage_lost_report($from_date,$to_date,$selectType);
    // echo "<pre>"; print_r($result); die();
    
    echo json_encode($result);
  }

  public function insert_csv_library_data(){
    $result = $this->library_model->insert_csv_library_data();
    echo json_encode($result);
    
}

  private function _createTableLibraryCsv($tableName, $headers) {
    $this->load->dbforge();
    $this->dbforge->drop_table($tableName, TRUE);
    $fields = $this->_makeFields($headers);
    $this->dbforge->add_key('id', TRUE);
    $this->dbforge->add_field($fields);
    $this->dbforge->create_table($tableName);
  }

  private function _makeFields($headers){

    $fields = array(
      'id' => array(
              'type' => 'INT',
              'constraint' => 11,
              'auto_increment' => TRUE,
              'primary' => TRUE
        ),
      );
    foreach ($headers as $key) {
      if(!array_key_exists($key, $fields)) {
        $fields[$key] = array(
                  'type' => 'TEXT',
                  'null' => True
          );
      }
    }
    return $fields;
  }

public function upload_excel_to_erp(){
  $insertedIds= $this->input->post('insertedIds');
  $group_by = $this->input->post('group_by');
  $db_table = $this->input->post('db_table');
  foreach ($insertedIds as $key => $insertedId) {
    $result = $this->library_model->upload_excel_to_erp($insertedId, $group_by,$db_table);
    if($result){
      echo $insertedId;
    }else{
      echo false;
    }
    
  }
}

public function save_export_books_filters(){
  $result = $this->library_model->save_export_books_filters($_POST);
  if($result){
    echo true;
  }else{
    echo false;
  }

}
public function get_saved_filter_data(){
  $id=$this->input->post('filter_id');
  $result = $this->library_model->get_saved_filter_data($id);
  echo json_encode($result);
}

public function remove_mutiple_lib_cards(){
  $lib_card_id = $this->input->post("id");
  $result = $this->library_model->remove_mutiple_lib_cards($lib_card_id);
  echo json_encode($result);

}

public function getRfid_DetailsRange(){
  $userType = $this->input->post("userType");
  $fromdate = $this->input->post("fromdate");
  $toDate = $this->input->post("todate");
  $get_user_details = $this->library_model->getRfid_DetailsRange($userType,$fromdate,$toDate);
  echo json_encode($get_user_details);

}
public function manualCheckout(){
  $id = $this->input->post("id");
  $update_checkout = $this->library_model->manualCheckout($id);
  echo json_encode($update_checkout);
}

} ?>