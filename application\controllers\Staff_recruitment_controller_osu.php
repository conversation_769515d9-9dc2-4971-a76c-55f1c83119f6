<?php 
// Career form implementaion
class Staff_recruitment_controller_osu extends CI_Controller
{
    function __construct() {
       parent::__construct();
       $this->load->model('Staff_recruitment_model_osu');
       $this->load->library('filemanager');
       $this->load->library('payment_application');
       $this->config->load('form_elements');
       $this->load->model('email_model');
       $this->load->helper('email_helper');
       $this->load->model('communication/emails_model');
    } 
    public function add_career_details_new() {
        $result=$this->Staff_recruitment_model_osu->add_career_details_new($_POST,$this->s3FileUpload($_FILES['resume'], 'staff_recruitment'));
        $mail_status=$this->Staff_recruitment_model_osu->is_mail_sent($_POST['admission_id']);
        if($mail_status->sent_mail != '1'){
            // mail to the staff notifying that a candidate has entered first page of the application form.

            $email_template = $this->email_model->get_email_template_to_send_visitor('staff recruitment form page 1');
            $email_data= $this->Staff_recruitment_model_osu->get_email_data($_POST['admission_id']);
            $emailBody =  $email_template->content;
            
            $emailBody = str_replace('%%name%%', $email_data->candidate_name, $emailBody);
            $emailBody = str_replace('%%position%%',$email_data->designation, $emailBody);
            $emailBody = str_replace('%%phone%%',$email_data->phone_number, $emailBody);
            $emailBody = str_replace('%%schoolname%%',$this->settings->getSetting('school_name'), $emailBody);
            $emailBody = str_replace('%%mail%%',$email_data->email, $emailBody);

            $files_array = array();
            if($email_data->resume != '') {
            array_push($files_array, array('name' => 'resume ', 'path' => $email_data->resume));
            }
            $files_string = '';
            if(!empty($files_array)) {
            $files_string = json_encode($files_array);
            }

            $email_master_data = array(
                'subject' => $email_template->email_subject,
                'body' => $emailBody,
                'source' => 'Advance HR Staff Recruitment Personal page',
                'sent_by' => 0,
                'recievers' => "Staff",
                'from_email' => $email_template->registered_email,
                'files' => empty($files_string) ? '' : json_encode($files_string) ,
                'acad_year_id' => $this->acad_year->getAcadYearID(),
                'visible' => 1,
                'sender_list'=>trim($email_template->members_email) == '' ? 'No Member Email Found' : $email_template->members_email,
                'sending_status' => 'Completed'
            );
              
            $email_master_id = $this->emails_model->saveEmail($email_master_data);

            if(! empty(trim($email_template->members_email))){
                $memberEmail_staff = [];
                $this->load->model('Birthday_Notifications_Model');
                $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
    
                $email_data_staff = [];
                if(!empty($members_data)){
                  foreach ($members_data as $key => $val) {
                      if(empty($val->stf_email))
                          continue;
                    if(!empty($val->stf_email))
                        $memberEmail_staff[] = $val->stf_email;
    
                      $email_obj = new stdClass();
                      $email_obj->stakeholder_id = $val->staff_id;
                      $email_obj->avatar_type = $val->avatar_type;
                      $email_obj->email = $val->stf_email;
                      $email_data_staff[] = $email_obj;
                    }
                }
                $this->emails_model->save_sending_email_data($email_data_staff,$email_master_id);
    
                sendEmail($emailBody, $email_template->email_subject,$email_master_id, $memberEmail_staff,$email_template->registered_email,json_decode($files_string));
            }
            //---------------------------------------------------------------------------------------------------------------------
            // mail to the candidate to continue the application form.
            $email_template2 = $this->email_model->get_email_template_to_send_visitor('staff recruitment continue email');
            $emailBody2 =  $email_template2->content;
            $encoded_id=($_POST['admission_id']*3952)-3299;

            $emailBody2 = str_replace('%%schoolname%%',$this->settings->getSetting('school_name'), $emailBody2);
            $emailBody2 = str_replace('%%position%%',$email_data->designation, $emailBody2);
            $emailBody2 = str_replace('%%href%%', base_url().'Staff_recruitment_controller_osu/add_recruitment_new/' .$_POST['course_select'] .'/'. $encoded_id  , $emailBody2);
            $memberEmail_candidate[] = $email_data->email;

            $email_master_data = array(
                'subject' => $email_template2->email_subject,
                'body' => $emailBody2,
                'source' => 'Advance HR Staff Recruitment Personal page to candidate',
                'sent_by' => 0,
                'recievers' => "candidate",
                'from_email' => $email_template2->registered_email,
                'files' => null,
                'acad_year_id' => $this->acad_year->getAcadYearID(),
                'visible' => 1,
                'sender_list'=>$email_data->email,
                'sending_status' => 'Completed'
            );
              
            $email_master_id_candidate = $this->emails_model->saveEmail($email_master_data);

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = 0;
            $email_obj->avatar_type =0;
            $email_obj->email =$email_data->email;
            $email_data_candidate[] = $email_obj;
            $this->emails_model->save_sending_email_data($email_data_candidate,$email_master_id_candidate);


            sendEmail($emailBody2, $email_template2->email_subject,$email_master_id_candidate, $memberEmail_candidate,$email_template2->registered_email,'');
            $this->Staff_recruitment_model_osu->update_first_mail_status($_POST['admission_id']);
        } 
        echo "not sent";
        echo $result;
    }

    public function send_otp() {
		$input = $this->input->post();
    	$otp = rand(100000,999999);
        // echo "<pre>"; print_r($input); die();
    	$smsint = $this->settings->getSetting('smsintergration');
    	$msg = 'This is your one-time password:' . $otp . '-NextElement';
    	$from_name = $this->settings->getSetting('school_name');


		if (filter_var($input['contact'], FILTER_VALIDATE_EMAIL ) ) {
        	$emailId = $input['contact'];
            $get_email_template=$this->Staff_recruitment_model_osu->get_mail_verification_template();
            
            // echo "<pre>"; print_r($get_email_template); die();
            $memberEmail[] = $emailId;
            
            $get_email_template->content = str_replace('%%otp%%',$otp,$get_email_template->content);

            $email_master_data = array(
                'subject' => $get_email_template->email_subject,
                'body' => $get_email_template->content,
                'source' => 'Advance HR Staff Recruitment Personal page OTP to candidate',
                'sent_by' => 0,
                'recievers' => "candidate",
                'from_email' => $get_email_template->registered_email,
                'files' => null,
                'acad_year_id' => $this->acad_year->getAcadYearID(),
                'visible' => 1,
                'sender_list'=>$emailId,
                'sending_status' => 'Completed'
            );
              
            $email_master_id_candidate = $this->emails_model->saveEmail($email_master_data);

            $email_obj = new stdClass();
            $email_obj->stakeholder_id = 0;
            $email_obj->avatar_type =0;
            $email_obj->email =$emailId;
            $email_data_candidate[] = $email_obj;
            $this->emails_model->save_sending_email_data($email_data_candidate,$email_master_id_candidate);


            $res = sendEmail($get_email_template->content, $get_email_template->email_subject,$email_master_id_candidate, $memberEmail,$get_email_template->registered_email,'');
            
			if($res) {
				$this->Staff_recruitment_model_osu->insert_otp($input, $otp);
				echo json_encode(['status' => 'ok','msg' => 'Email Sent!']);
			} else {
				echo json_encode(['status' => 'error','msg' => 'Unable to send Email please try again!' ]);
			}
		 	
        }else{
    	 	$content =  urlencode(''.$msg.'');
			$get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$input['contact'].'&sender='.$smsint->sender.'&message='.$content;
			$check_returned = $this->curl->simple_get($get_url);
			if(!empty($check_returned)) {

				$check_returned = json_decode($check_returned);

				if($check_returned->status == 'OK') {
					$this->Staff_recruitment_model_osu->insert_otp($input, $otp);
					echo json_encode(['status' => 'ok','msg' => 'SMS Sent!']);
				} else {
					echo json_encode(['status' => 'error','msg' => 'Unable to send SMS please try again!' ]);
				}
			} else {
				echo json_encode(['status' => 'error','msg' => 'Unable to send SMS please try again!' ]);
			}
        }
	}

    public function verifyOTP(){
        $otp=$this->Staff_recruitment_model_osu->get_opt($_POST['admission_id']);
        if($otp->otp_code == $_POST['otpValue']){
            echo $this->Staff_recruitment_model_osu->update_otp_status($_POST['contact_type'],$_POST['admission_id'],$_POST['contact_main']);
        }else{
            echo 0;
        }
    }


    private function s3FileUpload($file, $folder_name = 'staff_recruitment'){
        if ($file['tmp_name'] == '' || $file['name'] == '') {
            return ['status' => 'empty', 'file_name' => ''];
        }
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
    }

    public function personal_details_2() {
        $result=$this->Staff_recruitment_model_osu->personal_details_2($_POST,$this->s3FileUpload($_FILES['photo'], 'staff_recruitment'));
        echo $result;
    }

    public function thank_you_page() {
        $data['main_content'] = 'thank_you_page_view_career';
        $this->load->view('staff_recruitment/staff_template/template', $data);

    }

    public function add_recruitment_new($job_title='', $id='') {
        if($id !=''){
            $data['id']=($id+3299)/3952;
        }else{
            $data['id']= $this->Staff_recruitment_model_osu->getRowId();
        }
        $final_submit=$this->Staff_recruitment_model_osu->final_submit_status($data['id']);
        if(!empty($final_submit) && $final_submit->final_submit == '1'){
            $data['main_content'] = 'thank_you_page_view_career';
            $this->load->view('staff_recruitment/staff_template/template', $data);
            return;
        }
        if($id !=''){
            $data['candidate_data']=$this->Staff_recruitment_model_osu->getCandidateData($data['id']);
        }
        // echo "<pre>"; print_r( $data['candidate_data']); die();
        $data['postings'] = $this->Staff_recruitment_model_osu->getOpenedStaffPostings();
        foreach ($data['postings'] as $posting) {
            $posting->designation = ucwords(strtolower($posting->designation));
        }
        $data['captcha'] =$this->settings->getSetting('staff_recruitment_captcha_site_key');
        $data['captcha_verification'] =$this->settings->getSetting('staff_recruitment_captcha_verification');
        $data['yesno_array']= $this->settings->getSetting('advanced_hr_custom_yesno_fields');
        $data['text_array']= $this->settings->getSetting('advanced_hr_custom_text_fields');
        // $data['academic_data']=$this->Staff_recruitment_model_osu->getAcademicDetails();
        $data['job_title'] = $job_title;
        $data['main_content'] = 'staff_recruitment_osu/add_recruitment_osu';
        $this->load->view('staff_recruitment/staff_template/template', $data);

    }

    public function get_recrutable_staffs_id(){
        $createdfrom_date = $_POST['createdfrom_date'];
        $createdto_date = $_POST['createdto_date'];
        $candidate_status = $_POST['candidate_status'];
        $job_title = $_POST['job_title'];

        $staffs_id= $this->Staff_recruitment_model_osu->get_recrutable_staffs_id($createdfrom_date,$createdto_date,$candidate_status,$job_title);
        echo json_encode($staffs_id);
    }
    public function candidate_status_update(){
        $status=$_POST['status'];
        $id=$_POST['id'];

        $candidate_updated= $this->Staff_recruitment_model_osu->candidate_status_update($status,$id);
    }

    // public function get_recrutable_staffs_first(){
    
    //     $createdfrom_date = '';
    //     $createdto_date = '';
        
    //     $recrutments =  $this->Staff_recruitment_model_osu->get_recrutable_staffs($createdfrom_date,$createdto_date);
    //     echo json_encode($recrutments);
    // }

    public function get_data_approved_position(){
        $data['approved_position'] = $this->Staff_recruitment_model_osu->get_data_approved_position();
        echo json_encode($data);
    }

    public function get_job_name(){
        $data= $this->Staff_recruitment_model_osu->get_job_name($_POST['job_title_id']);
        echo json_encode($data);
    }

    public function add_data_approved_position(){
        // echo "<pre>"; print_r($_POST); die();
        $position_id = $_POST['position_id'];
        $position_code = $_POST['position_code'];
        $department_id = $_POST['department_id'];
        $approved_on = date('Y-m-d');
        $status = "Approved";
        // $filled_by_id = $_POST['filled_by_id'];
        $success = $this->Staff_recruitment_model_osu->add_data_approved_position($position_id,$department_id,$approved_on,$status,$position_code,$filled_by_id);
        echo json_encode($success);
    }

    public function get_job_openings(){
        $data['job_openings']= $this->Staff_recruitment_model_osu->get_job_openings();
        // echo "<pre>"; print_r($data['job_openings'] ); die();
        echo json_encode($data);
    }

    public function add_career_education_details(){
        // echo "<pre>"; print_r($_POST);die();
        $this->Staff_recruitment_model_osu->add_career_education_details($_POST);
        redirect('Staff_recruitment_controller_osu/add_recruitment_osu');
    } 

    public function get_career_education_details(){
        $new_row_id =$_POST['new_row_id'];
        $data['education_details']=$this->Staff_recruitment_model_osu->get_career_education_details($new_row_id);
        echo json_encode($data);
    }

    public function add_additional_academic_info(){
        $result = $this->Staff_recruitment_model_osu->add_additional_academic_info($_POST,$this->s3FileUpload($_FILES['docs'], 'staff_recruitment'));
        echo json_encode($result);
    }

    public function add_exp_info(){
        // echo "<pre>"; print_r($_POST);
        // echo "<pre>:::::::"; print_r($this->s3FileUpload($_FILES['docs'], 'staff_recruitment'));die();
        $result = $this->Staff_recruitment_model_osu->add_exp_info($_POST,$this->s3FileUpload($_FILES['docs'], 'staff_recruitment'));
        echo json_encode($result);
    }

    public function remove_information(){
        $result = $this->Staff_recruitment_model_osu->remove_information($_POST);
        echo $result;
    } 

    public function remove_experience_information(){
        $result = $this->Staff_recruitment_model_osu->remove_experience_information($_POST);
        echo $result;
    } 
    public function remove_reference_information(){
        $result = $this->Staff_recruitment_model_osu->remove_reference_information($_POST);
        echo $result;
    } 

    public function other_information() {
        
        //    echo "<pre> david:"; print_r($_POST);
           if(! empty($_FILES['video'])){
               $status= $this->Staff_recruitment_model_osu->other_information($_POST,$this->s3FileUpload($_FILES['video']));
           }else{
            $status= $this->Staff_recruitment_model_osu->other_information($_POST,'');
           }
        echo $status;
    }

    public function finalSubmit(){
        $this->Staff_recruitment_model_osu->final_submit_done($_POST['id']);
        $email_template = $this->email_model->get_email_template_to_send_visitor('staff recruitment form');
        $email_data= $this->Staff_recruitment_model_osu->get_email_data($_POST['id']);
        $emailBody =  $email_template->content;
        
        $emailBody = str_replace('%%name%%', $email_data->candidate_name, $emailBody);
        $emailBody = str_replace('%%position%%',$email_data->designation, $emailBody);
        $emailBody = str_replace('%%date%%',$email_data->created_on, $emailBody);
        $emailBody = str_replace('%%mail%%',$email_data->email, $emailBody);

        $files_array = array();
        if($email_data->resume != '') {
        array_push($files_array, array('name' => 'resume ', 'path' => $email_data->resume));
        }
        $files_string = '';
        if(!empty($files_array)) {
        $files_string = json_encode($files_array);
        }
        
        $email_master_data = array(
            'subject' => $email_template->email_subject,
            'body' => $emailBody,
            'source' => 'Advance HR Staff Recruitment final submit',
            'sent_by' => 0,
            'recievers' => "Staff",
            'from_email' => $email_template->registered_email,
            'files' => empty($files_string) ? '' : json_encode($files_string) ,
            'acad_year_id' => $this->acad_year->getAcadYearID(),
            'visible' => 1,
            'sender_list'=>trim($email_template->members_email) == '' ? 'No Member Email Found' : $email_template->members_email,
            'sending_status' => 'Completed'
        );
          
        $email_master_id = $this->emails_model->saveEmail($email_master_data);

        if(!empty(trim($email_template->members_email))){
            $this->load->model('Birthday_Notifications_Model');
            $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
            
            $memberEmail = [];
            $email_data_staff = [];
            if(!empty($members_data)){
              foreach ($members_data as $key => $val) {
                  if(empty($val->stf_email))
                      continue;
                if(!empty($val->stf_email))
                    $memberEmail[] = $val->stf_email;
    
                  $email_obj = new stdClass();
                  $email_obj->stakeholder_id = $val->staff_id;
                  $email_obj->avatar_type = $val->avatar_type;
                  $email_obj->email = $val->stf_email;
                  $email_data_staff[] = $email_obj;
                }
            }
            $this->emails_model->save_sending_email_data($email_data_staff,$email_master_id);
    
            echo sendEmail($emailBody, $email_template->email_subject,$email_master_id, $memberEmail,$email_template->registered_email,json_decode($files_string));
        }
        echo 1;
    }

    public function reffer_data() {
        // echo "<pre>"; print_r($_POST); die();
        $result=$this->Staff_recruitment_model_osu->reffer_data($_POST);
        echo $result;

    }

    public function staff_recruitment_settings(){
        $data['main_content'] = 'staff_recruitment/staff_recruitment_settings';
        $this->load->view('inc/template',$data);
    }

    public function introPage(){
        $data['postings'] = $this->Staff_recruitment_model_osu->getOpenedStaffPostings();
        $data['total_heads']=$this->Staff_recruitment_model_osu->getTotalHeads();

        if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'staff_recruitment/intro_page_mobile';
          }else{
            $data['main_content'] = 'staff_recruitment/intro_page';   	
          }
        $this->load->view('staff_recruitment/staff_template/template',$data); 
    }

    public function add_edit_references(){
        $result = $this->Staff_recruitment_model_osu->add_edit_references($_POST);
        echo $result;
    }
    public function previewData(){
        // echo "<pre>"; print_r($_POST); die();
        $data =$this->Staff_recruitment_model_osu->previewData($_POST['admission_id']);
        echo json_encode($data);
    }

    public function get_career_experience_details(){
        $new_row_id =$_POST['new_row_id'];
        $data['experience_details']=$this->Staff_recruitment_model_osu->get_career_experience_details($new_row_id);
        echo json_encode($data);
    }

    public function get_reference_details(){
        $new_row_id =$_POST['new_row_id'];
        $data['reff_details']=$this->Staff_recruitment_model_osu->get_reference_details($new_row_id);
        echo json_encode($data);
    }
    
    public function check_contact_verification(){
        $data=$this->Staff_recruitment_model_osu->check_contact_verification($_POST['admission_id']);
        // echo "<pre>"; print_r($data); die();
        
        if($data->mobile_verified=='1' || $data->mail_verified=='1') {
            echo "1";
        }else{
            echo "0";
        }
    }

}   
?>