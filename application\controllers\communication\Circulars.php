<?php

class Circulars extends CI_Controller {
    private $yearId;
    private $tileName;
	function __construct()
	{
     	parent::__construct();
      	if (!$this->ion_auth->logged_in()) {
        	redirect('auth/login', 'refresh');
		}
		if(!$this->authorization->isAuthorized('CIRCULARV2.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$this->yearId = $this->acad_year->getAcadYearId();
		$this->load->model('class_section');
		$this->load->model('communication/circular_model', 'circular');
		$this->load->library('filemanager');
		$this->tileName = $this->settings->getSetting('circular_tile_name');
		if($this->tileName == '') {
			$this->tileName = 'Circulars';
		}
		$this->templates = [
	      ["name" => "Blank Template", "value" => ""],
		  ["name" => "Meeting Schedule", "value" => "<h3>MEETING SCHEDULE</h3><br>Dear Madam / Sir,<br>Greetings from <i>School Name</i>!<br><br>You are invited for a<br>Meeting Session: <i>At School / Online Link</i><br>Date: <i>25.5.2022</i><br>Day: <i>Tuesday</i><br>Time at: <i>10:45am / 3:45pm</i><br>With: <i>Teacher's Name</i><br><br>Warm Regards,<br><i>Teacher's Name</i><br>Grade: <i>3B</i><br>By: <i>Coordinator / Exams Officer / Admin Head / Transport Supervisor / Accountant / Principal</i><br><i>School Name</i><br>"],
		  ["name" => "Sports Teacher Report","value" => "<h2>SPORTS TEACHER REPORT - SPORTING ATHLETICS DETAILS</h1>Dear Madam / Sir,<br><br>
		  Greetings from <i>School Name</i>!<br><br>Date / Day: -----------------<br><br>Please be informed of Student ---------------------<br>Sporting Event / Competition: Athletics / School Band Team / School Marchpast Team / Basketball / Football / Cricket / Skating / Swimming<br><br>Sporting Athletic Report: Not carried the Sporting Accessory Kit to attend Class / Unwell & not participated in the Class<br><br>To participate in the Event / Competition<br><br>Request you to support your child to address and resolve the concern to ensure better, active participation of your child to learn the enriching Sporting Athletics Skillsets.<br><br>Warm Regards,<br><i>Teacher name</i><br>Physical Education Facilitator,<br><i>School Name</i><br>"],
		  ["name"=>"Complete School Uniform Status", "value"=>"<h3>GRADE TEACHER REPORT -COMPLETE SCHOOL UNIFORM STATUS</h3>Dear Madam / Sir,<br><br>Greetings from <i>School Name</i>!<br><br>Please be informed of Student ---------------------<br>Date / Day:<br><br>Complete School Uniform Status into School Today: ID Card / Pair of Complete black coloured Shoes / Pair of grey coloured Socks / School maroon coloured T-Shirt with School Logo / School grey coloured shorts / School grey coloured track pants / School blue coloured denim jeans / School grey coloured Hoodie with School Logo / Neatly Tied up Hair with COMPLETE Black Coloured Hair Accessory ONLY<br><br>Request you to support your child to walk into School smartly dressed in Complete School Uniform.<br><br>Warm Regards,<br><i>Teacher Name</i><br>Grade Facilitator, Grade: 6A<br><i>School Name</i>"],
		  ["name"=>"School reporting time status","value"=>"<h3>GRADE TEACHER REPORT - MY SCHOOL REPORTING TIME STATUS</h3>Dear Madam / Sir,<br>Greetings from <i>School Name</i>!<br><br>Cognitio Student Says: My Regular School Starts at 8:00AM Every Morning.<br><br>Date / Day:<br>Request you to support your child to walk into school BEFORE TIME in accordance with<br>“I do not want to be LATE TO MY SCHOOL; My Regular School Starts at 8:00AM Every Morning.”<br><br>Warm Regards,<br><i>Teacher Name</i><br>By: Grade Facilitator, Grade: <i>Grade/Section</i><br><i>School Name</i>"],
		  ["name"=>"Self Hygiene Status","value"=>"<h3>GRADE TEACHER REPORT - MY SELF HYGIENE STATUS</h3>Dear Madam / Sir,<br>Greetings from <i>School Name</i>!<br><br>Date / Day:<br><br>My Self Hygiene Requirement List: Hand Sanitizer / Hand Napkin / Snack Table Napkin / Tissues / Water Bottle / Thermos Flask<br><br>Request you to support your child in BRINGING in the above listed into School for the Hygiene of Self in view of “MY SELF HYGIENE - Hygiene Safety of Self and Others.”<br>
		  <br>Warm Regards,<br><i>Teacher name</i><br>By: Grade Facilitator, Grade: <i>Grade/Section</i><br><i>School Name</i><br>"],
		  ["name"=>"Classroom Stationary & Subject book status","value"=>"<h3>SUBJECT TEACHER REPORT - CLASSROOM STATIONERY & SUBJECT BOOK STATUS</h3>Dear Madam / Sir,<br>Greetings from <i>School Name</i>!<br><br>Date / Day-----------------<br><br>Classroom Stationery / <br><br>Subject: Hindi /<br><br>Subject Book Name: Learner Book / Activity Skill Builder Book / Classwork & Homework Record / Assessment Record / Activity Book / Question Bank Book / Lab Coat for Science Lab -Biology / Lab Coat for Science Lab -Chemistry / Lab Coat for Science Lab -Physics<br><br>Request you to support your child in bringing in the above-mentioned Learning Accessories into Class every day for Enhanced, Enriched & Enjoyable Learning Process.<br><br>Warm Regards,<br><i>Teacher Name</i><br>By: Science Lab Facilitator / Math Facilitator<br><i>School Name</i>"],
		  ["name"=>"Absence Status from Subject Classes and Related Cambridge Board Examinations","value"=>"<h3>SUBJECT TEACHER REPORT - CLASSROOM STATIONERY & SUBJECT BOOK STATUS</h3>Dear Madam / Sir,<br>Greetings from <i>School Name</i>!<br><br>Date / Day-----------------<br><br>Please be informed of your Child ABSENCE to Class TODAY<br><br>Subjects:<br>1.English /<br>2.Mathematics /<br>3.ICT -Information & Communication Technology /<br>4.Computer Science /<br>5.Science – EVM /<br>6.Science -Biology /<br>7.Science -Chemistry /<br>8.Science -Physics /<br>9.Hindi as a Second Language /<br>10.French Foreign Language /<br>11.Commerce -Accounting /<br>12.Commerce -Business / Business Studies /<br>13.Commerce -Economics /<br>14.Art & Design /<br>15.Science -Psychology /<br><br>Also Request you to ensure that your child ABSENCE to school stands MONITORED AND RESTRICTED by you, in order to help your child towards experiencing Enhanced, Enriched & Enjoyable Learning Process.<br><br>Furthermore, this could also WITHHOLD & DELAY THE STUDENT COMPLETION OF THE COURSE & SCHOOL APPROVAL TOWARDS TAKING UP THE CAMBRIDGE BOARD EXAMINATION.<br><br>Warm Regards,<br><i>Teacher Name</i><br>Grades: <i>Grade Name</i><br>By: English Facilitator / Exams Officer / Principal<br><i>School Name</i>"],
		  ['name'=>'Weekly Update',"value"=>"<h3>Weekly Update</h3><br><p>Dear Parents,</p><p>Please find below the topics that were covered during the week.</p><table><thead><tr><th>Day</th><th>Topic</th></tr></thead><tbody><tr><td>Monday</td><td></td></tr><tr><td>Tuesday</td><td></td></tr><tr><td>Wednesday</td><td></td></tr><tr><td>Thursday</td><td></td></tr><tr><td>Friday</td><td></td></tr></tbody></table><p><br></p><p>Assignments for the week</p><br><p>Warm Regards,</p><p>Ms.________________________________________</p><p> </p><style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}</style>"],
		  ['name'=>'Classroom Chronicles', 'value' => '<style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}</style><table><tr><td colspan="2"  style="text-align:center"><strong>DAILY CLASS ROOM CHRONICLE</strong></td></tr><tr><td width=50%>Schedule</td><td ></td></tr><tr><td >Assembly</td><td></td></tr><tr><td >Breakfast</td><td ></td></tr><tr><td >Period 1</td><td ></td></tr><tr><td >Period 2</td><td ></td></tr><tr><td >Short Break</td><td ></td></tr><tr><td >Period 3</td><td ></td></tr><tr><td >Period 4</td><td ></td></tr><tr><td >Period 5</td><td ></td></tr><tr><td >Lunch Break</td><td ></td></tr><tr><td >Period 6</td><td ></td></tr><tr><td >Period 7</td><td ></td></tr><tr><td >Period 8</td><td ></td></tr><tr><td >Dispersal</td><td ></td></tr><tr><td >Homework 1</td><td ></td></tr><tr><td >Homework 2</td><td ></td></tr><tr><td >Homework 3</td><td ></td></tr><tr><td >Homework 4</td><td ></td></tr><tr><td >Additonal Paritculars</td><td ></td></tr></table>'],
		  ["name" => "Principal's Dashboard", "value" => "<style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}</style><table class='table table-bordered'><tr><td>S No </td><td>Subject</td><td>Classwork</td><td>Homework</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td></tr></table>"]
		  
	    ];
	}

	public function list() {
		$data['permitAddCircular'] = $this->authorization->isAuthorized('CIRCULARV2.CREATE');
		$data['is_approver'] = $this->authorization->isAuthorized('CIRCULARV2.APPROVER');
		$data['permitViewAll'] = $this->authorization->isAuthorized('CIRCULARV2.VIEW_ALL');
		$data['permit_un_publish'] = $this->authorization->isAuthorized('CIRCULARV2.UNPUBLISH');
		$data['tileName'] = $this->tileName;
		$data['avatarId'] = $this->authorization->getAvatarId();
		$data['stakeHolderId'] = $this->authorization->getAvatarStakeHolderId();
		$resource_config = $this->settings->getSetting('resources');
		if (($resource_config)) {
			$data['resource_size'] = $resource_config->resource_size;
		} else {
			$data['resource_size'] = "10MB";
		}
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/circular/tablet_list';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/circular/mobile_list';
        }else{
			$data['main_content'] = 'communication/circular/desktop_list';    	
        }
        $this->load->view('inc/template', $data);
	}

	public function getCircularList() {
		$avatarId = $this->authorization->getAvatarId();
		$type = $_POST['type'];
	    $from_date = date('Y-m-d', strtotime($_POST['from_date']));
	    $to_date = date('Y-m-d', strtotime($_POST['to_date']));
	    if($this->mobile_detect->isMobile()) {
	    	$data['circular_list'] = $this->circular->getMobileCircularList($avatarId, $from_date, $to_date, $type);
	    } else {
	    	$data['circular_list'] = $this->circular->getCircularList($avatarId, $from_date, $to_date, $type);
	    }

	    echo json_encode($data);
	}

	public function getCircularView() {
		$circular_id = $_POST['circular_id'];
		$data = $this->circular->getCircularData($circular_id);
		echo json_encode($data);
	}

	public function getSingleCircularData() {
		$circular_id = $_POST['circular_id'];
		$circular = $this->circular->getSingleCircularData($circular_id);
		echo json_encode($circular);
	}

	public function index(){
		$fromDate = date('Y-m-d', strtotime("-2 day"));
	    $toDate = date('Y-m-d'); 
	    $this->__handleIndex($fromDate, $toDate, 'mine');
	}

	//Landing function for Circular index page if used filters
  	public function filterIndex() {
	    $displayType = $this->input->post('displayType');
	    $fromDate = date('Y-m-d', strtotime($this->input->post('from_date')));
	    $toDate = date('Y-m-d', strtotime($this->input->post('to_date')));
	    //echo $fromDate;die();
	    $this->__handleIndex($fromDate, $toDate, $displayType);
	}

  	private function __handleIndex($fromDate, $toDate, $displayType) {
	    $avatarId = $this->authorization->getAvatarId();
	    $viewPrivilege = $this->authorization->isAuthorized('CIRCULARV2.VIEW_ALL');
	    if (!$viewPrivilege)
	      $displayType = 'mine'; //When all viewprivilege is not there, then you can view only your data.

	  	$data['is_approver'] = $this->authorization->isAuthorized('CIRCULARV2.APPROVER');
	    $data['circular_list'] = $this->circular->getCircularList($avatarId, $fromDate, $toDate, $displayType);
	    $data['avatarId'] = $avatarId;
	    $data['avatarname'] = $this->authorization->getAvatarFriendlyName();
	    $data['permitViewAll'] = $viewPrivilege;
	    $data['permitAddCircular'] = $this->authorization->isAuthorized('CIRCULARV2.CREATE');
	    $data['displayType'] = $displayType;
	    $data['selected_from_date'] = $fromDate;
	    $data['selected_to_date'] = $toDate;
	    $data['tileName'] = $this->tileName;
	    $data['main_content'] = 'communication/circular/index';
        $this->load->view('inc/template', $data); 
  	}

  	public function getCircularData() {
  		$circular_id = $_POST['circular_id'];
  		echo json_encode($this->circular->getCircularData($circular_id));
  	}

	public function getCircularById() {
  		$circular_id = $_POST['circular_id'];
  		echo json_encode($this->circular->getCircularById($circular_id));
  	}  	

  	public function update_circular() {

		$circular_id =$_POST['id'];
		$circular_body =$_POST['body'];
		$title_edit_class = $_POST['title_edit_class'];
		echo $this->circular->updateCircular($circular_id,$circular_body,$title_edit_class);
	}

  	public function changeStatus() {
  		$circular_id = $_POST['circular_id'];
  		$is_visible = $_POST['is_visible'];
  		echo $this->circular->changeStatus($circular_id, $is_visible);
  	}

	public function create_circular() {
		if(!$this->authorization->isAuthorized('CIRCULARV2.CREATE')) {
			redirect('dashboard', 'refresh');
		}
		$resource_config = $this->settings->getSetting('resources');
		if (($resource_config)) {
			$data['resource_size'] = $resource_config->resource_size;
		} else {
			$data['resource_size'] = "10MB";
		}
		
		$data['templates'] = $this->templates;
		$data['classes'] = $this->class_section->getAllClassess();
		$data['batches'] = $this->class_section->get_batches();
		$data['class_section'] = $this->class_section->getAllClassSections(1);
		$this->load->model('staff/Staff_Model');
		$data['staff_details'] = $this->Staff_Model->getAll_Staff();
		$data['circularv2_sendto_mode'] = $this->settings->getSetting('circularv2_sendto_mode');
		$data['categories'] = $this->circular->getCircularCategories();
		$data['tileName'] = $this->tileName;
		$this->load->model('communication/texting_model', 'texting_model');
		$data['groups'] = $this->texting_model->getTextingGroups();
		// echo "<pre>"; print_r($data); die();

		$data['staff_type'] = $this->settings->getSetting("staff_type");

		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/circular/create_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/circular/create_mobile';
        }else{
			$data['main_content'] = 'communication/circular/create';   	
        }
        $this->load->view('inc/template', $data);
	}

	public function getCircularTitles() {
		$circulars = $this->circular->getCircularTitles($this->yearId);
		echo json_encode($circulars);
	}

	public function getTemplate() {
		$templateName = $this->input->post('templateName');
    	echo json_encode($this->__getTemplateByName($templateName));
	}

	private function __getTemplateByName ($templateName) {
	    $templateValue = '';
	    foreach ($this->templates as $temp) {
	      if ($temp['name'] == $templateName) {
	        $templateValue = $temp['value'];
	        break;
	      }
	    }
	    return $templateValue;
	}

	private function __s3FileUpload($file) {
	    if($file['tmp_name'] == '' || $file['name'] == '') {
	      return ['status' => 'empty', 'file_name' => ''];
	    }        
	    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'circulars');
	}

	public function Kolkata_datetime(){
		$timezone = new DateTimeZone("Asia/Kolkata" );
		$date = new DateTime();
		$date->setTimezone($timezone );
		$dtobj = $date->format('Y-m-d H:i:s');
		return $dtobj;
	}

	public function update_circular_status() {
		$master_id = $_POST['master_id'];
		echo $this->circular->update_circular_status($master_id);
	}

	private function saveNotificationEmails($circular_id) {
		$circular = $this->circular->getCircularDetails($circular_id);
		if($circular->mode == 'circular_email') {

			$from_email = $this->settings->getSetting('circularv2_from_email');
			
			$subject = $circular->category.':'.$circular->title;
			$email_master_data = array(
				'subject' => $subject,
				'body' => $circular->body,
				'source' => 'Circular',
				'sent_by' => $this->authorization->getAvatarId(),
				'recievers' => $circular->recievers,
				'from_email' => $from_email,
				'files' => $circular->file_path,
				'acad_year_id' => $this->yearId,
				'visible' => 1,
				'sender_list' => NULL,
				'sending_status' => 'Completed'
			);
			$this->load->model('communication/emails_model');
			$email_master_id = $this->emails_model->saveEmail($email_master_data);

			$sent_data = $this->circular->getCircularSentTo($circular_id);
			$this->load->model('communication/emails_model');
			$this->load->helper('email_helper');
			$this->emails_model->save_sending_data($sent_data, $email_master_id);
			$email_ids = [];
			foreach ($sent_data as $key => $sent) {
				if($sent->email != '' && $sent->email != null) {
					array_push($email_ids, $sent->email);
				}
			}
			$email_response = sendEmail($circular->body, $subject, $email_master_id, $email_ids, $from_email, json_decode($circular->file_path));
		}

		$stdIds = $this->circular->getCircularRcieverStdIds($circular_id);
		$stfIds = $this->circular->getCircularRcieverStaffIds($circular_id);
		$notification = "New Circular with title '" .  $circular->title . "' posted under category '" . $circular->category."'";
		$this->__sendNotifications($stdIds, $stfIds, $notification);
	}

	private function callBulkNotificationSender($master_id, $type) {
		$return_url = site_url('Callback_Controller/bulkNotifications');
		$curl = curl_init();

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];

        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_bulknotification_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "master_id=".$master_id."&type=".$type."&return_url=".$return_url,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          return 0;
        } else {
          return 1;
        }
	}

	private function __sendNotifications($std_ids, $staff_ids, $content) {
		$school_name = $this->settings->getSetting('school_name');
		$input_array = array(
			'mode' => 'notification', 
			'title' => $school_name, 
			'message' => $content, 
			'source' => 'Circular',
			'student_url' => site_url('parent/Circular_inbox'),
			'staff_url' => site_url('staff/Circular_view'),
			'visible' => 1,
			'send_to' => 'Both',
			'acad_year_id' => $this->acad_year->getAcadYearId()
		);
		if(!empty($std_ids)) {
			$input_array['student_ids'] = $std_ids;
		}

		if(!empty($staff_ids)) {
			$input_array['staff_ids'] = $staff_ids;
		}
		$this->load->helper('texting_helper');
		sendText($input_array);
	}

	public function getCircularDetail() {
		$circular_id = $_POST['circular_id'];
		$data['circular'] = $this->circular->getCircularData($circular_id);
		$data['recievers'] = $this->circular->getCircularRecievers($circular_id, $data['circular']->acad_year_id);
		echo json_encode($data);
	}

	public function report() {
		$data['tileName'] = $this->tileName;
        if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/circular/report_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/circular/report_mobile';
        }else{
			$data['main_content'] = 'communication/circular/report';    	
        }

		
        $this->load->view('inc/template', $data);
	}

	public function generate_report(){
	    $from_date = $_POST['from_date'];
	    $to_date = $_POST['to_date'];
	    $result = $this->circular->getCircularData_report($from_date, $to_date);
	    echo json_encode($result);
	}

	public function view_circular_data($cmid){    
	    $data['circular'] = $this->circulars_model->getCircularDataById($cmid);
	    // echo "<pre>"; print_r($data['circular']); die();
	    $avatarId = $data['circular']->created_by;
	    $data['tileName'] = $this->tileName;
	    $data['user'] = $this->db->select('friendly_name')
	                    ->where('id',$avatarId)
	                    ->get('avatar')->row_array()['friendly_name'];

	    $data['main_content'] = 'communication/circular/details_report';
	    $this->load->view('inc/template', $data);  
	}

	public function viewCircularDetails() {
		$circular_id = $_POST['circular_id'];
		$data['tileName'] = $this->tileName;
		$data['circular'] = $this->circular->getCircularData($circular_id);
		$data['recievers'] = $this->circular->getCircularRecievers($circular_id, $data['circular']->acad_year_id);
		// echo "<pre>"; print_r($data); die();
		if ($this->mobile_detect->isTablet()) {
			$data['main_content'] = 'communication/circular/details_report_tablet';
        }else if($this->mobile_detect->isMobile()){
			$data['main_content'] = 'communication/circular/details_report_mobile';
        }else{
			$data['main_content'] = 'communication/circular/details_report';
        }
		$this->load->view('inc/template', $data); 
	}

	public function circular_resend(){
		$data['admis_type'] = $this->settings->getSetting('admission_type');
		$data['classes'] = $this->class_section->getAllClassess();
		$data['main_content'] = 'communication/circular/circular_resend';
		$this->load->view('inc/template', $data);
	}

	public function get_class_wise_student_lsit(){
		$classId = $this->input->post('classId');
		$result = $this->circular->get_class_wise_student_data($classId);
		echo json_encode($result);
	}

	public function get_circular_preview_data(){
		$this->load->model('communication/texting_model', 'texting_model');
		$input = $this->input->post();
		$students = array();
		if(!empty($input['student_ids'])) {
			$students = $this->texting_model->getStudents($input['student_ids'], 'preferred');
			$parents = $this->texting_model->getStudents($input['student_ids'], 'Both');
			$students = array_merge($students, $parents);
			usort($students, function($a, $b) { return strcmp($a->Name, $b->Name); });
		}
		echo json_encode($students);
	}

	public function save_resend_circular(){
		$input = $this->input->post();
		$result = $this->circular->insert_resend_circular_data();
		if($result['status'] == 1){
			if(!empty($result['data'])){
				$this->load->helper('texting_helper');
				$dataTemplate = [
					'mode'        => 'notification',
					'title'       => 'Circulars',
					'message'     => 'Your circulars has been updated. Please check the app for more details.',
					'source'      => 'Resend Circulars',
					'student_ids' => $result['data'],
				];
				sendText($dataTemplate);
				echo 1;
			}else{
				echo $result['status'];
			}
		}else{
			echo 0;
		}
	}

	public function save_circular_beta() {
		$input = $this->input->post();
		$files_string = $input['attachment'];

		list($category, $approval_required) = explode("_", $input['category']);
		$is_approved = ($approval_required == 1)?0:1;
		$visible = ($approval_required == 1)?0:1;
		$reciever_arr = [];
		if(isset($input['staff_ids'])){
			$reciever_arr[] = 'Staffs';
		}
		if(isset($input['student_ids'])){
			$reciever_arr[] = 'Students';
		}
		if(isset($input['section_ids'])){
			$class_sections = $this->circular->getClassSectionNames($input['section_ids']);
			$reciever_arr[] = implode(', ', $class_sections);
		}
		$recievers = implode(" & ", $reciever_arr);

		$circular_number = 0;
		$circular_receipt_id = $this->settings->getSetting('circular_receipt_number_set');
		if (!empty($circular_receipt_id)) {
			$receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id', $circular_receipt_id)->get()->row();
			$this->load->library('fee_library');
			if (!empty($receipt_book)) {
				// getting circular number
				$circular_number = $this->fee_library->receipt_format_get_update($receipt_book);

				// updating circular number
				$this->db->where('id', $circular_receipt_id);
				$this->db->update('feev2_receipt_book', array('running_number' => $receipt_book->running_number + 1));
			}
		}
		$sender_list = [];
		$decodeUserObj = json_decode($input['userObj'],true);
		$userArry = array_filter($decodeUserObj, 'is_array');
		$type = '';
		foreach ($userArry as $key => $val) {
			if (is_array($val)) {
				if($val['avatar_type'] == 1){
					$type = '2';
					$sender_list['students'] = [
						'send_to' => 'Both',
						'send_to_type' => $input['send_to_type'],
						'ids' => $val['stakeholder_id']
					];
				}
				if($val['avatar_type'] == 4){
					$type = '4';
					$sender_list['staff'] = [
						'ids' =>  $val['stakeholder_id']
					];
				}
			}
		}
		$sent_data = array_map(function($singArry) {
			return (object) $singArry;
		}, $userArry);

		$master_data = array(
			'title' => $input['title'],
			'body' => $input['body'],
			'category' => $category,
			'sent_by' => $this->authorization->getAvatarId(),
			'recievers' => $recievers,
			'mode' =>  $input['communication_type'],
			'file_path' => ($files_string=='')?NULL:$files_string,
			'acad_year_id' => $this->yearId,
			'visible' => $visible,
			'is_approved' => $is_approved,
			'circular_number' => $circular_number,
			'is_file_path_json' => 1,
			'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
			'sending_status' => 'Initiated',
			'sent_on' => $this->Kolkata_datetime(),
			'from_email' => $this->input->post('fromemail')
		);
		
		$this->db->trans_start();
		$master_id = $this->circular->saveCircular($master_data);
		$chunked_array = array_chunk($sent_data, 500);
		if(!empty($master_id)){
			$this->send_notification_for_approver($category, $input['title']);
			foreach ($chunked_array as $key => $value) {
				$this->circular->save_sending_user_data($value, $master_id);
			}
		}
		$this->db->trans_complete();
		if($this->db->trans_status()) {
			$chunked_sending = array_chunk($sent_data, 100);
			echo json_encode(array('master_id'=>$master_id, 'send_data'=> $chunked_sending,'fromemail'=> $input['fromemail']));
		}
	}

	public function send_notification_for_approver($category, $title){
		$approvers_details_for_category = $this->circular->get_approvers_details_for_category($category);
		// echo "<pre>";print_r($approvers_details_for_category);die();
		if(!empty($approvers_details_for_category['staff_details'])){
			$school_name = $this->settings->getSetting('school_name');
			$this->load->model('communication/texting_model', 'texting_model');
			$notification = "Dear staff member, You have '.$title.' circular to approve. Please check your school app for more details. Thank you!";
			$credits = $this->texting_model->_calculateCredits($notification, 0);
			$text_master = array(
				'title' => $category,
				'message' => $notification,
				'sent_by' => $this->authorization->getAvatarId(),
				'reciever' => $approvers_details_for_category['require_approval_by_list'],
				'acad_year_id' => $this->acad_year->getAcadYearId(),
				'source' => 'Circular Approval',
				'text_count' => 0,
				'visible' => 1,
				'mode' => 'notification',
				'sms_credits' => $credits,
				'is_unicode' => 0,
				'sender_list' => NULL,
				'sending_status' => 'Initiated'
			);
			$texting_master_id = $this->texting_model->save_texts($text_master);

			$url = site_url('communication/circulars/list');
			$textingData = [];
			foreach($approvers_details_for_category['staff_details'] as $staff){
				$textingData[] = array(
					'texting_master_id' => $texting_master_id,
					'stakeholder_id' => $staff->staff_id,
					'mobile_no' => $staff->staff_mobile_no,
					'mode' => 1,
					'status' => ($staff->tokenState == 0 )? 'No Token' : 'Sent',
					'avatar_type' => 4,
					'is_read' => 0,
					'user_id' => $staff->user_id,
					'token' => $staff->user_token,
				);
			}
			$token_data = $this->texting_model->save_notifications($textingData);
			$this->load->helper('notification_helper');
			$notification_status = commonNotifications($token_data, $school_name, $notification, $url);
		}
	}

	public function send_circular_email_notification(){
		$this->load->model('communication/texting_model', 'texting_model');
		$chunked_array = json_decode($this->input->post('chunk_data'), true);
    	$circular_id = $this->input->post('master_id');
		$circular_data = $this->circular->getCircularDetails($circular_id);
		if($circular_data->is_approved == 1){
			$texting_master_id = $circular_data->texting_master_id;
			if(empty($texting_master_id)){
				$school_name = $this->settings->getSetting('school_name');
				$notification = "New Circular with title '" .  $circular_data->title . "' posted under category '" . $circular_data->category."'";
				$credits = $this->texting_model->_calculateCredits($notification, 0);
				$text_master = array(
					'title' => $school_name,
					'message' => $notification,
					'sent_by' => $this->authorization->getAvatarId(),
					'reciever' => $circular_data->recievers,
					'acad_year_id' => $this->acad_year->getAcadYearId(),
					'source' => 'Circular',
					'text_count' => 0,
					'visible' => 1,
					'mode' => 'notification',
					'sms_credits' => $credits,
					'is_unicode' => 0,
					'sender_list' => NULL,
					'sending_status' => 'Initiated'
				);
				$texting_master_id = $this->texting_model->save_texts($text_master);
				$this->circular->addTextingId($circular_id, $texting_master_id);

			}
			
			if(!empty($texting_master_id)){
				$notification = "New Circular with title '" .  $circular_data->title . "' posted under category '" . $circular_data->category."'";
				$school_name = $this->settings->getSetting('school_name');
				$this->texting_model->updateTextCount($texting_master_id, count($chunked_array));
				$url = '';
				$textingData = [];
				foreach ($chunked_array as $key => $val) {
					if($val['avatar_type'] == 1){
						$url = site_url('parent/Circular_inbox');
					}
					if($val['avatar_type'] == 4){
						$url = site_url('staff/Circular_view');
					}
					if(!empty($val['user_id'])){
						$textingData[] = array(
							'texting_master_id' => $texting_master_id,
							'stakeholder_id' => $val['stakeholder_id'],
							'mobile_no' => $val['mobile_no'],
							'mode' => 1,
							'status' => ($val['tokenState'] == 0 )? 'No Token' : 'Sent',
							'avatar_type' => $val['avatar_type'],
							'is_read' => 0,
							'user_id' => $val['user_id'],
							'token' => $val['token'],
						);
					}
				}
				$token_data = $this->texting_model->save_notifications($textingData);
				$this->load->helper('notification_helper');
				$notification_status = commonNotifications($token_data, $school_name, $notification, $url);
			}

			if($circular_data->mode == 'circular_email'){
				$email_master_id = $circular_data->email_master_id;
				$subject = $circular_data->category.':'.$circular_data->title;
				$from_email = $this->input->post('fromemail');
				if ($from_email == ''){
					$from_email = $this->settings->getSetting('circularv2_from_email');
				}
				if(empty($email_master_id)){
					
					$email_master_data = array(
						'subject' => $subject,
						'body' => $circular_data->body,
						'source' => 'Circular',
						'sent_by' => $this->authorization->getAvatarId(),
						'recievers' => $circular_data->recievers,
						'from_email' => $from_email,
						'files' => ($circular_data->file_path=='')?NULL:$circular_data->file_path,
						'acad_year_id' => $this->yearId,
						'visible' => 1,
						'sender_list' => empty($circular_data->sender_list)?NULL:$circular_data->sender_list,
						'sending_status' => 'Completed'
					);
					$this->load->model('communication/emails_model');
					$email_master_id = $this->emails_model->saveEmail($email_master_data);
					$this->circular->addEmailId($circular_id, $email_master_id);
				}
				if(!empty($email_master_id)){
					$this->load->model('communication/emails_model');
					$this->load->helper('email_helper');
					$json_string_email = json_encode($chunked_array);
					$email_info_object = json_decode($json_string_email);
					$this->emails_model->save_sending_email_data($email_info_object, $email_master_id);
					$email_ids = [];
					foreach ($chunked_array as $key => $value) {
						if($value['email'] != '' && $value['email'] != null) {
							array_push($email_ids, $value['email']);
						}
					}
					$email_response = sendEmail($circular_data->body, $subject, $email_master_id, $email_ids, $from_email, json_decode($circular_data->file_path));
				}
			}
			echo 1;
		}else{
			echo 0;
		}
	}

	public function update_circular_completed_status() {
		$master_id = $_POST['master_id'];
		echo $this->circular->update_circular_completed_status($master_id);
	}

	public function get_approvel_circular_data(){
		$rejected_approved_by = $this->authorization->getAvatarStakeHolderId();
		$this->load->model('communication/texting_model', 'texting_model');
		$circular_id = $_POST['circular_id'];
		$appoved_reject_comments=$_POST['appoved_reject_comments'];
		$approval_status = isset($_POST['status'])?$_POST['status']:1;
		$status = $this->circular->approveCircular($circular_id, $approval_status,$appoved_reject_comments,$rejected_approved_by);
		if($status){
			if($approval_status == 2){
				echo 2;
				exit();
			}
			$result = $this->circular->get_circular_data_by_id($circular_id);
			$fromemail = $this->circular->get_circular_from_email_by_id($circular_id);
			$chunked_array = [];
			if(!empty($result)){
				$sent_data = [];
				foreach ($result as $key => $val) {
					if(!empty($val->temp_data)){
						$sent_data[] = json_decode($val->temp_data,true);
					}
				}
				$chunked_array = array_chunk($sent_data, 100);
			}
			echo json_encode(array('from_email'=>$fromemail->from_email, 'chunk_data'=> $chunked_array));
			// echo json_encode($chunked_array);
		}else{
			echo 0;
		}
	}

	public function getCircularsNotSentDetail(){
		$input = $this->input->post();
		if(!isset($input) || empty($input) || $input['id'] <= 0 || $input['id'] == ''){
			echo 0;
			return;
		}

		$result = $this->circular->getCircularsNotSentDetail($input);
		echo json_encode($result);
	}

	public function resendDetails(){
		$input = $this->input->post();
		if(!isset($input) || empty($input) || $input['email_master_id'] <= 0 || $input['email_master_id'] == ''){
			echo 0;
			return;
		}

		$circular_data = $this->circular->get_emails_to_resend($input['email_master_id']);
		// echo "<pre>";print_r($circular_data);die();
		if(!empty($circular_data)){
			$email_master_id = $circular_data['email_master_id'];
			$subject = $circular_data['subject'];
			$from_email = $circular_data['from_email'];
			if ($from_email == ''){
				$from_email = $this->settings->getSetting('circularv2_from_email');
			}
			$this->load->model('communication/emails_model');
			$this->load->helper('email_helper');
			$email_ids = $circular_data['email_ids'];
			$email_response = sendEmail($circular_data['body'], $subject, $email_master_id, $email_ids, $from_email, json_decode($circular_data['file_path']));
			$this->circular->update_attempts($input['email_master_id']);
			echo 1;
			return;
		}else{
			echo 0;
			return;
		}
	}
}