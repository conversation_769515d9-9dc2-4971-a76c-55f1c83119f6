<?php

class Emails extends CI_Controller {
    private $yearId;
    private $templates;
	function __construct()
	{
     	parent::__construct();
      	if (!$this->ion_auth->logged_in()) {
        	redirect('auth/login', 'refresh');
		}
		if(!$this->authorization->isModuleEnabled('EMAILS') || !$this->authorization->isAuthorized('EMAILS.MODULE')) {
			redirect('dashboard', 'refresh');
		}
		$this->yearId = $this->acad_year->getAcadYearId();
		$this->load->model('class_section');
		$this->load->model('communication/circular_model', 'circular');
		$this->load->model('communication/emails_model', 'emails');
		$this->load->library('filemanager');
		$this->templates = [
	      ["name" => "Blank Template", "value" => ""],
	      ["name" => "Food", "value" => "<h1>Food Report</h1><table><tbody><tr><td><span style='font-weight: bold;'><br></span></td><td><span style='font-weight: bold;'>Completed</span></td><td><span style='font-weight: bold;'>Not Completed</span></td><td><span style='font-weight: bold;'>Enjoyed</span></td></tr><tr><td><span style='font-weight: bold;'>SNACK</span></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr><tr><td><span style='font-weight: bold;'>LUNCH</span></td><td><br></td><td><br></td><td><br></td></tr><tr><td><br></td><td><br></td><td><br></td><td><br></td></tr></tbody></table><p><br></p><style>table {  font-family: arial, sans-serif;width:60%;  border-collapse: collapse;}td, th {  border: 1px solid #dddddd;  text-align: left;  padding: 8px;}tr:nth-child(even) {  background-color: #dddddd;}</style>"]
	    ];
	}

	public function index(){
		$fromDate = date('Y-m-d', strtotime("-7 day"));
	    $toDate = date('Y-m-d'); 
	    $this->__handleIndex($fromDate, $toDate, 'mine','');
	}

	public function filterIndex() {
	    $displayType = $this->input->post('displayType');
		$source = $this->input->post('source');
	    $fromDate = date('Y-m-d', strtotime($this->input->post('from_date')));
	    $toDate = date('Y-m-d', strtotime($this->input->post('to_date')));
	    //echo $fromDate;die();
	    $this->__handleIndex($fromDate, $toDate, $displayType,$source);
	}

	private function __handleIndex($fromDate, $toDate, $displayType,$source) {
	    $avatarId = $this->authorization->getAvatarId();
	    $viewPrivilege = $this->authorization->isAuthorized('EMAILS.VIEW_ALL');
	    if (!$viewPrivilege)
	      $displayType = 'mine'; //When all viewprivilege is not there, then you can view only your data.

	    $data['email_list'] = $this->emails->getEmailList($avatarId, $fromDate, $toDate, $displayType,$source);
	    $data['avatarId'] = $avatarId;
	    $data['avatarname'] = $this->authorization->getAvatarFriendlyName();
	    $data['permitViewAll'] = $viewPrivilege;
	    $data['permitSendEmail'] = $this->authorization->isAuthorized('EMAILS.SEND');
	    $data['displayType'] = $displayType;
		$data['source'] = $source;
	    $data['selected_from_date'] = $fromDate;
	    $data['selected_to_date'] = $toDate;
		$data['source_filters'] = $this->emails->get_sources_from_email_master();
	    $data['main_content'] = 'communication/emails/index';
        $this->load->view('inc/template', $data); 
  	}

  	public function create_email() {
  		if(!$this->authorization->isAuthorized('EMAILS.SEND')) {
			redirect('dashboard', 'refresh');
		}
		$data['templates'] = $this->templates;
		$from_emails = $this->settings->getSetting('from_emails');
		$data['from_emails'] = [];
		if($from_emails) {
			$data['from_emails'] = $from_emails;
		}
		$data['classes'] = $this->class_section->getAllClassess();
		$data['batches'] = $this->class_section->get_batches();
		$data['class_section'] = $this->class_section->getAllClassSections();
		$this->load->model('staff/Staff_Model');
		$data['staff_details'] = $this->Staff_Model->getAll_Staff();
		$this->load->model('communication/texting_model', 'texting_model');
		// echo "<pre>"; print_r($data); die();
		$data['groups'] = $this->texting_model->getTextingGroups();
		$data['staff_type'] = $this->settings->getSetting("staff_type");
		$data['main_content'] = 'communication/emails/create';
        $this->load->view('inc/template', $data);
  	}

  	private function __s3FileUpload($file) {
	    if($file['tmp_name'] == '' || $file['name'] == '') {
	      return ['status' => 'empty', 'file_name' => ''];
	    }        
	    return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'emails');
	}

  	public function save_email() {
		$input = $this->input->post();
		$files_string = $input['attachment'];
		$group_data = array();
		$group_data['students'] = array();
		$group_data['class_section'] = array();
		$group_data['staff'] = array();
		$reciever_arr = [];
		$recievers = '';
		$student_ids = [];
		$staff_ids = [];
		$sender_list = [];
		$this->load->model('communication/texting_model', 'texting_model');
		if(!empty($input['student_ids'])) {
			$reciever_arr[] = 'Students';
			$group_data['students'] = array_unique($input['student_ids']);
			$student_ids = $group_data['students'];
			$sender_list['students'] = [
				'send_to' => 'Both',
				'ids' => $student_ids
			];
		}

		if(!empty($input['staff_ids'])) {
			$reciever_arr[] = 'Staffs';
			$group_data['staff'] = $input['staff_ids'];
			$staff_ids = $input['staff_ids'];
		}
		if(!empty($input['section_ids'])) {
			$class_sections = $this->circular->getClassSectionNames($input['section_ids']);
			$reciever_arr[] = implode(', ', $class_sections);
			$group_data['class_section'] = $input['section_ids'];
			$sec_std = $this->texting_model->getStudentsBySections($input['section_ids'], $input['send_to_class'], $this->yearId, $input['class_batch']);
			if(!empty($sec_std)) {
				foreach($sec_std as $k => $s) {
					$student_ids[] = $s->std_id;
				}
			}
		}

		$management_ids = $this->settings->getSetting('management_staff_ids');
		if($management_ids) {
			foreach ($management_ids as $man) {
				if(!in_array($man->id, $staff_ids))
					$staff_ids[] = $man->id;
			}
		}

		if(!empty($staff_ids)) {
			$sender_list['staff'] = [
				'ids' => $staff_ids
			];
		}

		if($input['group_name'] != '') {
			$group_json = json_encode($group_data);
			$this->texting_model->saveGroup($input['group_name'], $group_json);
		}

		$recievers = implode(" & ", $reciever_arr);

		$master_data = array(
			'subject' => $input['subject'],
			'body' => $input['body'],
			'source' => 'Email UI',
			'sent_by' => $this->authorization->getAvatarId(),
			'recievers' => $recievers,
			'from_email' => $input['from_email'],
			'files' => ($files_string=='')?NULL:$files_string,
			'acad_year_id' => $this->yearId,
			'visible' => 1,
			'sender_list' => empty($sender_list)?NULL:json_encode($sender_list),
			'sending_status' => 'Initiated'
		);

		$master_id = $this->emails->saveEmail($master_data);

		$texting_master_id = 0;
		if($master_id) {
			$notification = "New Email with subject '" .  $input['subject'] . " in your inbox";
			$school_name = $this->settings->getSetting('school_name');
			$text_master = array(
				'title' => $school_name,
				'message' => $notification,
				'sent_by' => $this->authorization->getAvatarId(),
				'reciever' => $recievers,
				'acad_year_id' => $this->acad_year->getAcadYearId(),
				'source' => 'Email',
				'text_count' => 0,
				'visible' => 1,
				'mode' => 'notification',
				'sms_credits' => 0,
				'is_unicode' => 0,
				'sender_list' => NULL,
				'sending_status' => 'Completed'
			);
			$texting_master_id = $this->texting_model->save_texts($text_master);

			$this->emails->addTextingId($master_id, $texting_master_id);
		}
		echo json_encode(array('master_id' => $master_id, 'texting_master_id' => $texting_master_id, 'student_ids' => $student_ids, 'staff_ids' => $staff_ids));
  	}

  	public function update_email_status() {
		$master_id = $_POST['master_id'];
		echo $this->emails->update_email_status($master_id);
	}

	public function email_sender() {
		$email_master_id = $_POST['master_id'];
		$texting_master_id = $_POST['texting_master_id'];
		$stakeholder_ids = $_POST['stakeholder_ids'];
		$type = $_POST['type'];
		$this->load->model('communication/texting_model', 'texting_model');

		$sent_data = array();
		if($type === 'student') {
			$sent_data = $this->texting_model->getStudents($stakeholder_ids, 'Both');
		} else if($type === 'staff') {
			$sent_data = $this->texting_model->getStaff($stakeholder_ids);
		}
		$status = $this->emails->save_sending_data($sent_data, $email_master_id);
		if($status) {
			$this->load->helper('email_helper');
			$email = $this->emails->getEmailInfo($email_master_id);
			$email_ids = [];
			foreach ($sent_data as $key => $sent) {
				if($sent->email != '' && $sent->email != null) {
					array_push($email_ids, $sent->email);
				}
			}
			$email_response = sendEmail($email->body, $email->subject, $email_master_id, $email_ids, $email->from_email, json_decode($email->files));
			$this->load->helper('texting_helper');
			$text_data = array(
				'sending_info' => $sent_data,
				'texting_master_id' => $texting_master_id,
				'type' => $type,
				'student_url' => site_url('dashboard'),
				'staff_url' => site_url('dashboard')
			);
			$text_response = sendTextMessageByData($text_data);
		}
		echo 1;
	}

	public function getEmailDetail() {
		$email_id = $_POST['email_id'];
		$data['email'] = $this->emails->getEmailData($email_id);
		$data['recievers'] = $this->emails->getCircularRecievers($email_id, $data['email']->acad_year_id);
		echo json_encode($data);
	}

}