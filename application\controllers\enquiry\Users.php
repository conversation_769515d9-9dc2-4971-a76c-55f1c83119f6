<?php

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Class Fee
 * @property Ion_auth|Ion_auth_model $ion_auth        The ION Auth spark
 * @property CI_Form_validation      $form_validation The form validation library
 */
class Users extends CI_Controller {

	public function __construct() {		
    parent::__construct();
    $this->load->model('enquiry_model');
    $this->load->model('email_model');
    $this->load->helper('texting_helper');
    $this->config->load('form_elements');
	}

  public function index($source = '',$referred_by=''){
    $acad_years = [];
    $data['source'] = 'Website';
    if($source != '') {
      $data['source'] = $source;
    }
    $data['referred_by'] = $referred_by;

    if ($source == 'referral' && !empty($referred_by)) {
      $key = "1a2b3c4d5e6f7g8h9i0j"; 
      $method = "AES-128-CTR";
      $ivLength = openssl_cipher_iv_length($method);
      $iv = substr(hash('sha256', $key), 0, $ivLength);
      $decodedData = base64_decode($referred_by); 
      $data['referred_by'] = openssl_decrypt($decodedData, $method, $key, 0, $iv);
      $data['referred_by'] = str_replace("`",'', $data['referred_by']);
  }
    //Get the current year
    $acad_year = new stdClass();
    $acad_year->acad_year_id = $this->settings->getSetting('academic_year_id');
    $acad_year->acad_year_name = $this->acad_year->getAcadYearById($acad_year->acad_year_id);
    $acad_year->selected = '';
    $acad_years[] = $acad_year;

    //Get the promotion year
    $prom_acad_year = new stdClass();
    $prom_acad_year->acad_year_id = $this->settings->getSetting('promotion_academic_year_id');
    $prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($prom_acad_year->acad_year_id);
    $prom_acad_year->selected = 'selected';
    $acad_years[] = $prom_acad_year;

    //Get the double promotion year
    $d_prom_acad_year = new stdClass();
    $d_prom_acad_year->acad_year_id = $this->settings->getSetting('double_promotion_academic_year_id');
    $d_prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($d_prom_acad_year->acad_year_id);
    $d_prom_acad_year->selected = '';
    $acad_years[] = $d_prom_acad_year;

    //Get default enquiry year
    $default_acad_year_id = $this->settings->getSetting('default_enquiry_year_id');
    if ($default_acad_year_id == '' || $default_acad_year_id == null) {
      //Do Nothing
    } else {
      $acad_year->selected = ($default_acad_year_id == $acad_year->acad_year_id) ? 'selected' : '';
      $prom_acad_year->selected = ($default_acad_year_id == $prom_acad_year->acad_year_id) ? 'selected' : '';
      $d_prom_acad_year->selected = ($default_acad_year_id == $d_prom_acad_year->acad_year_id) ? 'selected' : '';
    }

    $data['acad_years'] = $acad_years;
    $data['default_acad_year_id'] = $default_acad_year_id;

    $data['header_instruction'] = $this->settings->getSetting('enquiry_header_instruction',0);
    $data['dob_instruction'] = $this->settings->getSetting('enquiry_dob_instruction',0);
    $required_fields = $this->enquiry_model->get_enquiry_required_fields();
    $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
    $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    $data['know_aboutUs'] = json_decode($this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us'));
    $data['enquiry_additional_coaching'] = json_decode($this->settings->getSetting('enquiry_additional_coaching'));
    $data['enquiry_combination'] = json_decode($this->settings->getSetting('enquiry_combination'));
    $data['boards'] = $this->settings->getSetting('board');
    $data['otp_verification'] =$this->settings->getSetting('enquiry_mobile_number_otp_verification');
    $data['captcha_code_verification'] =$this->settings->getSetting('enquiry_captcha_code_verification');
    $data['check_registered_mobile_number'] = $this->settings->getSetting('enquiry_check_registered_mobile_number');
    $data['captcha_site_key'] =$this->settings->getSetting('enquiry_captcha_site_key');
    $data['enquiry_form_text'] = $this->settings->getSetting('enquiry_form_text');

    $caste = $this->enquiry_model->load_caste();
    $Category = $this->settings->getSetting('category');
    $categoryOptions=[];
        if(!empty($caste) && !empty($Category)){
          foreach ($caste as $key => $value) {
              foreach ($Category as $k => $v) {
              if(!array_key_exists($value->category,$categoryOptions) && $value->category == $v){
                  $object = new stdClass();
                  $object->category = ucwords($value->category);
                  $object->value = $k;
                  $categoryOptions[$value->category]=$object;
              }
              }
          }
        }

        $casteOptions = [];
        if(!empty($caste)){
            foreach ($caste as $key => $value) {
                if (!array_key_exists($value->caste, $casteOptions)) {
                    $object = new stdClass();
                    $object->category = $value->category;
                    $object->caste = ucwords($value->caste);
                    $casteOptions[$value->caste] = $object;
                }
            }
        }
        $subCasteOptions = [];
        foreach ($caste as $key => $value) {
            if (!array_key_exists($value->sub_caste, $subCasteOptions)) {
                $object = new stdClass();
                $object->caste = $value->caste;
                $object->sub_caste = ucwords($value->sub_caste);
                $subCasteOptions[$value->sub_caste] = $object;
            }
        }

        $data['caste'] = $caste;
        $data['categoryOptions'] = $categoryOptions;
        $data['casteOptions'] = $casteOptions;
        $data['subCasteOptions']=$subCasteOptions;

        if(!empty($data['caste'])){
            $student_caste_present_in_db=true;
        }else{
            $student_caste_present_in_db = false;
        }

        $data['student_caste_present_in_db']=$student_caste_present_in_db;
    $enquiry_page_type = $this->settings->getSetting('enquiry_page_type');
    $data['master_table_class_names'] = $this->enquiry_model->get_class_names_from_master();
    if ($enquiry_page_type == 'enquiry_horizontal_type') {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'enquiry/enquiry_form_iisb';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'enquiry/enquiry_form_iisb';
      }else{
        $data['main_content'] = 'enquiry/enquiry_form_iisb';       
      }
    } else if ($enquiry_page_type == 'enquiry_mlps') {
        $data['main_content'] = 'enquiry/enquiry_form_mlps';       
    } else if ($enquiry_page_type == 'enquiry_pncc') {
      $data['main_content'] = 'enquiry/enquiry_pncc';       
    }else if ($enquiry_page_type == 'enquiry_jspuc') {
      $data['main_content'] = 'enquiry/enquiry_form_jspuc';       
    }else if ($enquiry_page_type == 'enquiry_yashasvi') {
      $data['main_content'] = 'enquiry/enquiry_form_yashasvi';
    }else if($enquiry_page_type == 'manchesterglobal'){
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'enquiry/enquiry_form_manchesterglobal_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'enquiry/enquiry_form_manchesterglobal_mobile';
      }else{
        $data['main_content'] = 'enquiry/enquiry_form_manchesterglobal';
      }
    } else{
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'enquiry/enquiry_form_tablet';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'enquiry/enquiry_form_mobile';
      }else{
        $data['main_content'] = 'enquiry/enquiry_form';       
      }
    }
    
    $this->load->view('enquiry/inc/template', $data);
  }


   public function contact_us_iisb($source = ''){
    $acad_years = [];
    $data['source'] = 'Contact Us Form';
    if($source != '') {
      $data['source'] = $source;
    }
    //Get the current year
    $acad_year = new stdClass();
    $acad_year->acad_year_id = $this->settings->getSetting('academic_year_id');
    $acad_year->acad_year_name = $this->acad_year->getAcadYearById($acad_year->acad_year_id);
    $acad_year->selected = '';
    $acad_years[] = $acad_year;

    //Get the promotion year
    $prom_acad_year = new stdClass();
    $prom_acad_year->acad_year_id = $this->settings->getSetting('promotion_academic_year_id');
    $prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($prom_acad_year->acad_year_id);
    $prom_acad_year->selected = 'selected';
    $acad_years[] = $prom_acad_year;

    //Get the double promotion year
    $d_prom_acad_year = new stdClass();
    $d_prom_acad_year->acad_year_id = $this->settings->getSetting('double_promotion_academic_year_id');
    $d_prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($d_prom_acad_year->acad_year_id);
    $d_prom_acad_year->selected = '';
    $acad_years[] = $d_prom_acad_year;

    //Get default enquiry year
    $default_acad_year_id = $this->settings->getSetting('default_enquiry_year_id');
    if ($default_acad_year_id == '' || $default_acad_year_id == null) {
      //Do Nothing
    } else {
      $acad_year->selected = ($default_acad_year_id == $acad_year->acad_year_id) ? 'selected' : '';
      $prom_acad_year->selected = ($default_acad_year_id == $prom_acad_year->acad_year_id) ? 'selected' : '';
      $d_prom_acad_year->selected = ($default_acad_year_id == $d_prom_acad_year->acad_year_id) ? 'selected' : '';
    }

    $data['acad_years'] = $acad_years;
    $data['default_acad_year_id'] = $default_acad_year_id;

    $data['header_instruction'] = $this->settings->getSetting('enquiry_header_instruction',0);
    $data['dob_instruction'] = $this->settings->getSetting('enquiry_dob_instruction',0);
    $required_fields = $this->enquiry_model->get_enquiry_required_fields();
    $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
    $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    $data['know_aboutUs'] = json_decode($this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us'));
    $data['enquiry_additional_coaching'] = json_decode($this->settings->getSetting('enquiry_additional_coaching'));
    $data['enquiry_combination'] = json_decode($this->settings->getSetting('enquiry_combination'));
    $data['boards'] = $this->settings->getSetting('board');
    $enquiry_page_type = $this->settings->getSetting('enquiry_page_type');
    if ($enquiry_page_type == 'enquiry_horizontal_type') {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'enquiry/enquiry_index_iisb_contact';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'enquiry/enquiry_index_iisb_contact';
      }else{
        $data['main_content'] = 'enquiry/enquiry_index_iisb_contact';       
      }
    }
    
    $this->load->view('enquiry/inc/template', $data);
  }

  public function iisb_landing_form($source = ''){
    $acad_years = [];
    $data['source'] = 'Landing page';
    if($source != '') {
      $data['source'] = $source;
    }
    //Get the current year
    $acad_year = new stdClass();
    $acad_year->acad_year_id = $this->settings->getSetting('academic_year_id');
    $acad_year->acad_year_name = $this->acad_year->getAcadYearById($acad_year->acad_year_id);
    $acad_year->selected = '';
    $acad_years[] = $acad_year;

    //Get the promotion year
    $prom_acad_year = new stdClass();
    $prom_acad_year->acad_year_id = $this->settings->getSetting('promotion_academic_year_id');
    $prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($prom_acad_year->acad_year_id);
    $prom_acad_year->selected = 'selected';
    $acad_years[] = $prom_acad_year;

    //Get the double promotion year
    $d_prom_acad_year = new stdClass();
    $d_prom_acad_year->acad_year_id = $this->settings->getSetting('double_promotion_academic_year_id');
    $d_prom_acad_year->acad_year_name = $this->acad_year->getAcadYearById($d_prom_acad_year->acad_year_id);
    $d_prom_acad_year->selected = '';
    $acad_years[] = $d_prom_acad_year;

    //Get default enquiry year
    $default_acad_year_id = $this->settings->getSetting('default_enquiry_year_id');
    if ($default_acad_year_id == '' || $default_acad_year_id == null) {
      //Do Nothing
    } else {
      $acad_year->selected = ($default_acad_year_id == $acad_year->acad_year_id) ? 'selected' : '';
      $prom_acad_year->selected = ($default_acad_year_id == $prom_acad_year->acad_year_id) ? 'selected' : '';
      $d_prom_acad_year->selected = ($default_acad_year_id == $d_prom_acad_year->acad_year_id) ? 'selected' : '';
    }

    $data['acad_years'] = $acad_years;
    $data['default_acad_year_id'] = $default_acad_year_id;

    $data['header_instruction'] = $this->settings->getSetting('enquiry_header_instruction',0);
    $data['dob_instruction'] = $this->settings->getSetting('enquiry_dob_instruction',0);
    $required_fields = $this->enquiry_model->get_enquiry_required_fields();
    $data['required_fields'] = $this->__construct_name_wise_required($required_fields);
    $data['disabled_fields'] = $this->enquiry_model->get_enquiry_disabled_fields();
    $data['know_aboutUs'] = json_decode($this->settings->getSetting('enquiry_how_did_you_get_to_know_about_us'));
    $data['enquiry_additional_coaching'] = json_decode($this->settings->getSetting('enquiry_additional_coaching'));
    $data['enquiry_combination'] = json_decode($this->settings->getSetting('enquiry_combination'));
    $data['boards'] = $this->settings->getSetting('board');
    $enquiry_page_type = $this->settings->getSetting('enquiry_page_type');
    if ($enquiry_page_type == 'enquiry_horizontal_type') {
      if ($this->mobile_detect->isTablet()) {
        $data['main_content'] = 'enquiry/enquiry_landing_form';
      }else if($this->mobile_detect->isMobile()){
        $data['main_content'] = 'enquiry/enquiry_landing_form';
      }else{
        $data['main_content'] = 'enquiry/enquiry_landing_form';       
      }
    }
    
    $this->load->view('enquiry/inc/template', $data);
  }

  private function __construct_name_wise_required($requiredData){
      $fields = $this->db->list_fields('enquiry');
      $rData = [];
      foreach ($fields as $key => $val) {
          if (in_array($val, $requiredData)) {
              $rData[$val] = array('font' =>'TRUE', 'required' =>'required');
          }else{
              $rData[$val] = array('font' =>'', 'required' =>'');
          }
      }
      return $rData;
  }

  public function get_class_sections(){
    $acadYearId = $_POST['acadYearId'];
    if(!isset($_POST['acadYearId'])){
      echo json_encode(array());
    }
    $result =  $this->enquiry_model->getClassByAcadYear_new($acadYearId);
    echo json_encode($result);
  }

  public function get_class_sections_validation(){
    $acadYearId = $_POST['acadYearId'];
    $dob = $_POST['dob'];
    $result =  $this->enquiry_model->get_validate_ClassByAcadYear($acadYearId,$dob);
    echo json_encode($result);
  }

  private function _enquiry_sms($number, $message){
      $input_arr = array();
      $input_arr['source'] = 'Enquiry';
      $input_arr['message'] = $message;
      $input_arr['custom_numbers'] = (array)$number;
      $input_arr['mode'] = 'sms';
      $response = sendText($input_arr, 'External');
      if($response['success'] != '') {
        $status = 1;
      } else {
        $status = 0;
      }
      return $status;
  }

  public function get_enquiry_record(){
    $get_enq_data =  $this->enquiry_model->get_enquiry_record();
    if($get_enq_data == 1){
      echo 1;
    }else{
      echo 0;
    }
  }

  public function submit_enquiry(){
    $emails = $this->settings->getSetting('email_settings_enquiry',0);
    $email_template = $this->email_model->get_all_templatebyassigned();
    $members = [];
    array_push($members, $this->input->post('email'));
    if($this->input->post('father_email_id') && trim($this->input->post('father_email_id')) != ''){
      array_push($members, $this->input->post('father_email_id'));
    }
    if($this->input->post('mother_email_id') && trim($this->input->post('mother_email_id')) != ''){
      array_push($members, $this->input->post('mother_email_id'));
    }
    
    $result = $this->enquiry_model->insert_enquiry_data();
    if ($result) {
      $this->enquiry_model->update_receipt_enquiry_wise($result);
      
      $enquiry_number = $this->enquiry_model->get_enquiry_number_by_id($result);
      $acad_year_id = $this->settings->getSetting('academic_year_id');
      $enquiry_data = $this->enquiry_model->get_enquiry_full_data($result);
      if (!empty($email_template)) {
        $memberEmail = [];
        $emailBody =  $email_template->content;
        $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
        if(!empty($enquiry_data)){
          $emailBody = str_replace('%%parent_name%%',$enquiry_data->parent_name, $emailBody);
          $emailBody = str_replace('%%student_name%%',$enquiry_data->student_name, $emailBody);
          $emailBody = str_replace('%%class_name%%',$enquiry_data->class_name, $emailBody);
        }
        $this->load->model('communication/emails_model');
        $email_data = [];
        
        foreach ($members as $key => $val) {
          if(empty($val)){
            continue;
          }
          if(!empty($val)){
            $memberEmail[] = $val;
          }
          $email_obj = new stdClass();
          $email_obj->stakeholder_id = 0;
          $email_obj->avatar_type = 2;
          $email_obj->email = $val;
          $email_data[] = $email_obj;
        }
        if(!empty(trim($email_template->members_email))){
          $this->load->model('Birthday_Notifications_Model');
          $members_data = $this->Birthday_Notifications_Model->membersDataForBirthdayInfo($email_template->members_email);
          if(!empty($members_data)){
            foreach ($members_data as $key => $val) {
                if(empty($val->stf_email))
                    continue;
                if(!empty($val->stf_email))
                    $memberEmail[] = $val->stf_email;
                $email_obj = new stdClass();
                $email_obj->stakeholder_id = $val->staff_id;
                $email_obj->avatar_type = $val->avatar_type;
                $email_obj->email = $val->stf_email;
                $email_data[] = $email_obj;
            }
          }
        }
        $senderList = implode(', ', $memberEmail);
        $email_master_data = array(
          'subject' => $email_template->email_subject,
          'body' => $emailBody,
          'source' => 'Enquiry Form Filed By The Parent',
          'sent_by' => 0,
          'recievers' => "Parents",
          'from_email' => $email_template->registered_email,
          'files' => NULL,
          'acad_year_id' => isset($_POST['academic_year']) ? $_POST['academic_year'] : $acad_year_id,
          'visible' => 1,
          'sending_status' => 'Completed',
          'sender_list' => $senderList
        );
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
        if(!empty($memberEmail)){
          $this->load->helper('email_helper');
          sendEmail($emailBody, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, []);
        }
      }
      $this->_email_to_staff($enquiry_number,$enquiry_data);
      $message = $this->settings->getSetting('enquiry_sms',0);
      if (!empty($message)) {
        $message = str_replace('%%enquiry_number%%',$enquiry_number, $message);
        $this->_enquiry_sms($this->input->post('mobile_number'), $message);
      }
    }
    redirect('enquiries-success/'.$result);
  }



public function submit_enquiry_contact(){

    $emails = $this->settings->getSetting('email_settings_enquiry',0);
    $email_template = $this->email_model->get_all_templatebyassigned();
    $members = [];
    if (!empty($email_template)) {
      $members = explode(',', $email_template->members_email);
    }
    array_push($members, $this->input->post('email'));
    
    $memberEmail = [];
    foreach ($members as $key => $val) {
      $memberEmail[]['email'] = $val;
    }
    $result = $this->enquiry_model->insert_enquiry_data_contactus_form();
    if ($result) {
      $enquiry_number = $this->enquiry_model->get_enquiry_number_by_id($result);
      $parent_name = $this->enquiry_model->get_enquiry_parent_name_id($result);

      if (!empty($email_template)) {
        $emailBody =  $email_template->content;
        $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
        $emailBody = str_replace('%%parent_name%%',$parent_name, $emailBody);
        $this->__send_email($emailBody, $email_template->email_subject, $memberEmail, $email_template->registered_email);
      }
    }
    redirect('iisb_contact_form_success');
  }


  public function submit_enquiry_landingform(){

    $emails = $this->settings->getSetting('email_settings_enquiry',0);
    $email_template = $this->email_model->get_all_templatebyassigned();
    $members = [];
    if (!empty($email_template)) {
      $members = explode(',', $email_template->members_email);
    }
    array_push($members, $this->input->post('email'));
    
    $memberEmail = [];
    foreach ($members as $key => $val) {
      $memberEmail[]['email'] = $val;
    }
    $result = $this->enquiry_model->insert_enquiry_data_contactus_form();
    if ($result) {
      $enquiry_number = $this->enquiry_model->get_enquiry_number_by_id($result);
      $parent_name = $this->enquiry_model->get_enquiry_parent_name_id($result);
      if (!empty($email_template)) {
        $emailBody =  $email_template->content;
        $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
        $emailBody = str_replace('%%parent_name%%',$parent_name, $emailBody);
        $this->__send_email($emailBody, $email_template->email_subject, $memberEmail, $email_template->registered_email);
      }
    }
    redirect('iisb_landing_form_success');
  }



  private function _email_to_staff($enquiry_number,$enquiry_data){
    $this->load->model('communication/emails_model');
    $email_template = $this->enquiry_model->get_enquiry_email_staff_template_byId();
    if(empty($email_template) || empty($email_template->staffdetails)){
      return;
    }
    
    $memberEmail = [];
    $email_data = [];
    if (!empty($email_template) && !empty($email_template->staffdetails)) {
      foreach ($email_template->staffdetails as $key => $val) {
        if(empty($val->email))
            continue;
        if(!empty($val->email))
            $memberEmail[] = $val->email;
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $val->staff_id;
        $email_obj->avatar_type = $val->avatar_type;
        $email_obj->email = $val->email;
        $email_data[] = $email_obj;
      }
    }
    
    $emailBody =  $email_template->content;
    $emailBody = str_replace('%%enquiry_number%%',$enquiry_number, $emailBody);
    if(!empty($enquiry_data)){
        $emailBody = str_replace('%%parent_name%%',$enquiry_data->parent_name, $emailBody);
        $emailBody = str_replace('%%student_name%%',$enquiry_data->student_name, $emailBody);
        $emailBody = str_replace('%%class_name%%',$enquiry_data->class_name, $emailBody);
    }
    $email_master_data = array(
      'subject' => $email_template->email_subject,
      'body' => $emailBody,
      'source' => 'Enquiry Form Filed By The Parent Sent To Staff Members',
      'sent_by' => 0,
      'recievers' => "Staffs",
      'from_email' => $email_template->registered_email,
      'files' => '',
      'acad_year_id' =>isset($_POST['academic_year']) ? $_POST['academic_year'] : '',
      'visible' => 1,
      'sending_status' => 'Completed',
    );
    $email_master_id = $this->emails_model->saveEmail($email_master_data);
    if(!empty($email_data)){
      $this->emails_model->save_sending_email_data($email_data, $email_master_id);
    }
    if(!empty($memberEmail)){
      $this->load->helper('email_helper');
      return sendEmail($emailBody, $email_template->email_subject, $email_master_id, $memberEmail, $email_template->registered_email, []);
    } else {
      return;
    }
  }

  public function enquiry_form_message($enquiry_id){
    $data['enquiry_number'] = $this->enquiry_model->get_enquiry_number_by_id($enquiry_id);
    // $school_short_name = $this->settings->getSetting('school_short_name');
    // $data['main_content']    = 'enquiry/message/'.$school_short_name;
    // if (file_exists(realpath(APPPATH . '/views/enquiry/message/' . $school_short_name.'.php'))) {
    //   $data['main_content']    = 'enquiry/message/'.$school_short_name;
    // }else{
    //   $data['main_content']    = 'enquiry/message/sucess_message';
    // }
    $enquiry_page_type = $this->settings->getSetting('enquiry_page_type');
    switch ($enquiry_page_type) {
      case 'enquiry_mlps':
      case 'enquiry_pncc': 
      case 'enquiry_jspuc':
      case 'enquiry_yashasvi':
        $data['main_content'] = 'enquiry/message/enquiry_form_message_landing';
        break;
      case 'enquiry_horizontal_type':
        $data['main_content'] = 'enquiry/message/sucess_message';       
        break;
      case 'enquiry_sahitya':
        $data['main_content'] = 'enquiry/message/sahitya';       
        break;
      default:
        $data['main_content'] = 'enquiry/message/sucess_message';       
        break;
    }
    if($enquiry_page_type == 'enquiry_pvv'){
      $this->load->view('enquiry/inc/template_pvv', $data);
    }else{
      $this->load->view('enquiry/inc/template', $data);
    }
    
  }

  public function enquiry_form_message_contact(){
    $data['main_content']    = 'enquiry/message/enquiry_form_message_contact';
    $this->load->view('enquiry/inc/template', $data);
  }

  public function enquiry_form_message_landing(){
    $thank_you_url = $this->settings->getSetting('enquiry_school_thankyou_url');

    if ($thank_you_url) {
      header("Location: $thank_you_url");
    } else {
      $data['main_content']    = 'enquiry/message/enquiry_form_message_landing';
      $this->load->view('enquiry/inc/template', $data);
    }
  }

  public function sendOTP() {

		$input = $this->input->post();
    	$otp = rand(100000,999999);
    	$email = $this->settings->getSetting('admission_email_based_otp');
    	$smsint = $this->settings->getSetting('smsintergration');
    	// $msg = $otp. ' is your One Time Password for verification as an applicant for registration-NXTSMS';
      $msg = $otp. ' is your One Time Password for verification as an applicant for registration-Nextelement';
    	$content =  urlencode(''.$msg.'');
			// $get_url = 'http://'.$smsint->url.'?method=sms&api_key='.$smsint->api_key.'&to='.$input['mobileNumber'].'&sender='.$smsint->sender.'&message='.$content;

      $get_url = 'https://'.$smsint->url.'?apikey='.$smsint->api_key.'&senderid='.$smsint->sender.'&number='.$input['mobileNumber'].'&message='.$content;
			$check_returned = $this->curl->simple_get($get_url);

			if(!empty($check_returned)) {
			  $check_returned = json_decode($check_returned);

        if($check_returned->status == 'OK') {
          $this->enquiry_model->insertOTP($input, $otp);
          echo json_encode(['status' => 'ok','msg' => 'SMS Sent!']);
        } else {
          echo json_encode(['status' => 'error','msg' => 'Unable to send SMS please try again!' ]);
        }

      } else {
        echo json_encode(['status' => 'error','msg' => 'Unable to send SMS please try again!' ]);
      }
    	//temporarily commnting old sms sending code
	}

  public function check_registerd_number(){
    if(isset($_POST['mobileNumber'])){
      echo $this->enquiry_model->check_registerd_number($_POST['mobileNumber']);
      return;
    }
    echo 0;
  }

  public function verify_otp(){
    if(isset($_POST['otp'])){
      echo $this->enquiry_model->verify_otp($_POST['mobileNumber'],$_POST['otp']);
    }else{
      echo 0;
    }
  }

  public function validate_date_of_birth(){
    $result = $this->enquiry_model->validate_date_of_birth($_POST['dob'],$_POST['class_id']);
    echo json_encode($result);
  }

  public function delete_otp(){
    echo $this->enquiry_model->delete_otp($_POST['mobileNumber']);
  }


}