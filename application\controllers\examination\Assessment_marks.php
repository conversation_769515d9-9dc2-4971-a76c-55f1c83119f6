<?php

defined('BASEPATH') OR exit('No direct script access allowed');
require_once APPPATH . '/libraries/jpgraph/src/jpgraph.php';
require_once APPPATH . '/libraries/jpgraph/src/jpgraph_bar.php';

class Assessment_marks extends CI_Controller {

  private $yearId;
	public function __construct() {
		parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->library('filemanager');
    $this->load->model('examination/Assessment_model','assessment_model');   
    $this->load->model('examination/Assessment_marks_model','assessment_marks');
    
  }

  public function index($class_id=1) {
    $class = $this->input->post('classSectionId');
    if(!empty($class)) {
      $class_id = $class;
    }
    if($class == 0 && $class != '') {
      $class_id = $class;
    }
    $data['classSelected'] = $class_id;
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $staffId = $this->authorization->getAvatarStakeHolderId();
    // $data['classList'] = $this->assessment_model->getClassess();
    $data['classList'] = $this->assessment_marks->getClassess($data['is_exam_admin'], $staffId);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/marks/assessment_marks_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/marks/assessment_marks_mobile';
    }else{
      $data['main_content'] = 'examination/marks/assessment_marks';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function get_portions() {
    $assessment_id = $_POST['assessment_id'];
    $portion = $this->assessment_model->getPortion($assessment_id);
    echo json_encode($portion);
  }

  public function reset_regen_status() {
    $template_id = $_POST['template_id'];
    $status = $this->assessment_marks->reset_regen_status($template_id);
    echo json_encode($status);
  }

  public function get_generated_html() {
    $student_card_id = $this->input->post('student_card_id');
    $data['gen_html'] = $this->assessment_marks->get_generated_html($student_card_id);
    echo json_encode($data);
  }

  public function getMarksStatus(){
    $assId = $_POST['assId'];
    $classId = $_POST['classId'];
    $subjectList = $this->assessment_model->getSubjectSections($assId);
    $sections = $this->assessment_model->getSectionsByClass($classId);
    // $sections = $this->assessment_model->getAssessmentSections($assId,$classId);
    // echo "<pre>"; print_r($data['subjectList']);
    // echo "<pre>"; print_r($data['sections']);die();
    $statusArr = array();
    foreach ($subjectList as $subKey => $sub) {
      $statObj = new stdClass();
      $statObj->name = $sub->name;
      // $statObj->sId = $sec['id'];
      // $statObj->sName = $sec['name'];
      $statObj->sections = array();
      foreach ($sections as $secKey => $sec) {
        $stat = $this->assessment_model->checkMarksStatus($sub->assEid, $sec->sectionId);
        if(empty($stat)){
          $statObj->sections[$sec->sectionName] = 0;
        }
        else{
          if($stat->lockedCount) {
            $statObj->sections[$sec->sectionName] = 2;
          } else {
            $statObj->sections[$sec->sectionName] = 1;
          }
        }
      }
      array_push($statusArr, $statObj);
    }
    
    $htmlStr = '';
    $i = 1;
    foreach ($statusArr as $key => $value) {
      $htmlStr .= '<tr><td>'.$value->name.'</td>';
      foreach ($value->sections as $k => $val) {
        if($val == 0)
          $color = 'background-color:#7B807E;';
        else if($val == 1)
          $color = 'background-color:#F89D0C;';
        else if($val == 2)
          $color = 'background-color:#0A761F;';
        $htmlStr .= '<td><h5 style="text-align:center;padding:3px;color:white;'.$color.'">'.$k.'</h5></td>';
      }
      $htmlStr .= '</tr>';
    }
    echo $htmlStr;
  }

  //Ajax call
  public function getAssessmentDataForMarksEntry() {
    $classId = $_POST['classId'];
    $data['assessments'] = $this->assessment_model->getAssessments($classId);
    $staff = $this->staffcache->getStaffCache();
    $staffId = 0;
    if(!empty($staff)) $staffId = $staff->staffId;
    $showAll = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    if($showAll) {
      $staffId = 0;
    }
    $permitted = $this->assessment_marks->permittedAssessments($classId, $staffId);
    $assessments = array();
    foreach ($data['assessments'] as $key => $assessment) {
      if(in_array($assessment->id, $permitted)) {
        $sub = $this->assessment_model->getsubAdded($assessment->id);
        $assessments[] = array(
          'id' => $assessment->id,
          'short_name' => $assessment->short_name,
          'long_name' => $assessment->long_name,
          'ass_type' => $assessment->ass_type,
          'subAdded' => $sub
        );
      }
    }
    echo json_encode($assessments);
  }

  public function marks_entry($assessment_id, $class_id, $subject_id=0, $section_id=0) {
    $data['class_id'] = $class_id;
    $data['assessment_id'] = $assessment_id;
    $data['subject_id'] = $subject_id;
    $data['section_id'] = $section_id;
    $current_section = $section_id;
    if(isset($_POST['subject_id'])) {
      $data['subject_id'] = $_POST['subject_id'];
    } else {
      $data['subject_id'] = $this->session->userdata('subject_id');
      $this->session->unset_userdata('subject_id');
    }
    if($data['subject_id'] == null || $data['subject_id'] == 0) {
      $data['subject_id'] = [];
    }
    if(isset($_POST['section_id'])) {
      $data['section_id'] = $_POST['section_id'];
      $current_section = $_POST['section_id'];
    }
    $data['assessment'] = $this->assessment_model->getAssessmentName($assessment_id);
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['sections'] = $this->assessment_marks->getPermittedSections($assessment_id, $data['is_exam_admin']);
    if($current_section == 0) {
      $current_section = (empty($data['sections']))?0:$data['sections'][0]->id;
    }
    $data['groups'] = $this->assessment_marks->getPermittedSubjects($assessment_id, $data['is_exam_admin'], $current_section);
    $data['com_count'] = 5;
    $com_count = $this->settings->getSetting('marks_entry_components_count');
    if($com_count) {
      $data['com_count'] = $com_count;
    }
    $data['class'] = $this->assessment_marks->getClassData($class_id);
    // echo "<pre>"; print_r($data); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/marks_entry/marks_entry_v2_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/marks_entry/marks_entry_v2_mobile';
    }else{
      $data['main_content'] = 'examination/marks_entry/marks_entry_v2';  	
    }
    $this->load->view('inc/template', $data);
  }

  public function getPermittedGroups() {
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $groups = $this->assessment_marks->getPermittedSubjects($assessment_id, $is_exam_admin, $section_id);
    echo json_encode($groups);
  }

  public function unlockEntityMarksEntry() {
    $entities_id = $_POST['entities_id'];
    echo ($this->assessment_marks->unlockEntityMarksEntry($entities_id));
  }

  public function add_subject_remarks() {
    $data = $this->input->post();
    $data['subject_id'] = explode(",", $data['subject_id']);
    $data['group'] = $this->assessment_marks->getGroupData($data['subject_id']);
    $data['assessment'] = $this->assessment_model->getAssessmentName($data['assessment_id']);
    $data['section'] = $this->assessment_marks->getSectionDetails($data['section_id']);
    $data['students'] = $this->assessment_marks->getSectionStudents($data['section_id']);
    $subject_remarks = $this->assessment_marks->getSubjectRemarks($data['assessment_id'], $data['subject_id']);
    // echo "<pre>"; print_r($subject_remarks);
    foreach ($data['students'] as $key => $std) {
      $data['students'][$key]->remarks = array();
      foreach ($data['subject_id'] as $subId) {
        $data['students'][$key]->remarks[$subId] = '';
        // $data['students'][$key]->subject_remarks = '';
        if(array_key_exists($std->student_id.'_'.$subId, $subject_remarks)) {
          $data['students'][$key]->remarks[$subId] = $subject_remarks[$std->student_id.'_'.$subId];
        }
      }
    }

    $data['remarks_list'] = array();
    if($data['assessment']->remarks_group_id) {
      $data['remarks_list'] = $this->assessment_marks->getRemarksList($data['assessment']->remarks_group_id);
    }
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/marks_entry/subject_remarks';
    $this->load->view('inc/template', $data);
  }

  public function save_marks() {
    $input = $this->input->post();
    $status = $this->assessment_marks->saveMarks();
    $this->session->set_flashdata('flashSuccess', 'Marks added successfully');
    redirect('examination/assessment_marks/marks_entry/'.$input["ass_id"].'/'.$input["class_id"]);
  }

  public function insert_marks() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $status = $this->assessment_marks->insertMarks();
    $this->session->set_flashdata('flashSuccess', 'Marks added successfully');
    $this->session->set_userdata('subject_id', $input["subject_id"]);
    redirect('examination/assessment_marks/marks_entry/'.$input["ass_id"].'/'.$input["class_id"].'/0/'.$input["section_id"]);
    // redirect('examination/assessment_marks/marks_entry/'.$input["ass_id"].'/'.$input["class_id"].'/'.$input["subject_id"].'/'.$input["section_id"]);
  }

  public function saveRemarks() {
    $data = $this->input->post();
    // echo "<pre>"; print_r($data); die();
    $status = $this->assessment_marks->saveRemarks();
    $this->session->set_userdata('subject_id', $data["subject_id"]);
    redirect('examination/assessment_marks/marks_entry/'.$data['assessment_id'].'/'.$data['class_id'].'/0/'.$data['section_id']);
    // redirect('examination/assessment_marks/marks_entry/'.$data['assessment_id'].'/'.$data['class_id'].'/'.$data['subject_id'].'/'.$data['section_id']);
  }

  public function getPermittedEntities() {
    $group_id = $_POST['group_id'];
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['entities'] = $this->assessment_marks->getPermittedEntitiesList($assessment_id, $section_id, $group_id, $is_exam_admin);
    $elective_group_id = 0;
    // $elective_group_id = $this->assessment_marks->getElective($group_id);
    // $assessment_entities_ids = array();
    // foreach ($data['entities'] as $key => $ent) {
    //   array_push($assessment_entities_ids, $ent->assessment_entities_id);
    // }
    // $data['status'] = $this->assessment_marks->getMarksEntryStatus($assessment_entities_ids, $elective_group_id, $group_id, $section_id);
    $data['status'] = $this->assessment_marks->getMarksEntryStatus($assessment_id, $group_id, $section_id);
    $data['remarks_status'] = $this->assessment_marks->getRemarksEntryStatus($assessment_id, $group_id, $elective_group_id, $section_id);
    // echo "<pre>"; print_r($data['remarks_status']); die();
    echo json_encode($data);
  }

  public function marksEntry($assId, $class_id){
    $data['class_id'] = $class_id;
    $data['ass_id'] = $assId;
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    $data['sections'] = $this->assessment_model->getSectionsByClass($class_id);
    $data['groups'] = $this->assessment_model->getGroupsByAssId($assId);
    $data['com_count'] = 5;
    $com_count = $this->settings->getSetting('marks_entry_components_count');
    if($com_count) {
      $data['com_count'] = $com_count;
    }
    $class = $this->assessment_model->getClassName($class_id);
    $data['className'] = $class->className;
    // echo "<pre>"; print_r($data); die();

    $data['main_content'] = 'examination/marks_entry/marks_entry';
    $this->load->view('inc/template', $data);
  }

  public function getEntities() {
    $group_id = $_POST['group_id'];
    $assessment_id = $_POST['assessment_id'];
    $section_id = $_POST['section_id'];
    $entities = $this->assessment_marks->getPermittedEntitiesList($assessment_id, $section_id, [$group_id]);
    echo json_encode($entities);
  }

  public function add_marks() {
    $input = $this->input->post();
    $data['subject_id'] = $input['group_id'];
    $data['section_id'] = $input['section_id'];
    $data['class_id'] = $input['class_id'];
    $data['ass_id'] = $input['assessment_id'];
    // echo '<pre>'; print_r($data); die();
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['assessment'] = $this->assessment_model->getAssessmentName($data['ass_id']);
    $data['group'] = $this->assessment_marks->getGroupData($input['group_id']);
    $entities = $this->assessment_marks->getMarksEntities($input['assessment_id'], $input['entities'], $input['section_id'], $data['is_exam_admin']);
    $data['section'] = $this->assessment_marks->getSectionDetails($input['section_id']);
    $class = $this->assessment_model->getClassName($data['class_id']);
    $data['className'] = $class->className;
    $data['staffList'] = $this->assessment_model->getStaff();
    $data['group_id'] = $input['group_id'];
    $aeIds = array();
    foreach ($entities as $key => $entity) {
      $aeIds[] = $entity->aeId;
      $entity->formula = '';
      $entity->grades = json_decode($entity->grades);
      if($entity->ass_type == 'Derived') {
        $derived = json_decode($entity->derived_formula);
        $entity->formula = $derived->formula->name;
      }
      unset($entity->derived_formula);
      $data['entities'][$entity->aeId] = $entity;
    }
      $list = $this->assessment_marks->getStudentMarksData($input['group_id'], $input['section_id'], $aeIds);
    // if($data['group']->is_elective) {
    // $list = $this->assessment_marks->getElectiveStudentMarksList($input['group_id'], $input['section_id'], $aeIds);
    // } else {
    //   $list = $this->assessment_marks->getStudentMarksList($input['section_id'], $aeIds);
    // }
    // echo "<pre>"; print_r($list); die();
    // $subject_remarks = $this->assessment_marks->getSubjectRemarks($data['ass_id'], $data['group_id']);
    // foreach ($list['list'] as $key => $std) {
    //   $std->subject_remarks = '';
    //   if(array_key_exists($std->student_id, $subject_remarks)) {
    //     $std->subject_remarks = $subject_remarks[$std->student_id];
    //   }
    // }
    // $data['remarks_list'] = array();
    // if($data['assessment']->remarks_group_id) {
    //   $data['remarks_list'] = $this->assessment_marks->getRemarksList($data['assessment']->remarks_group_id);
    // }
    $data['students'] = $list['list'];
    $data['isFreshEntry'] = $list['isFreshEntry'];
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/marks_entry/add_marks';
    $this->load->view('inc/template', $data);
  }

  public function marks() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $data['class_id'] = $input['class_id'];
    $data['ass_id'] = $input['assessment_id'];
    $data['assessment'] = $this->assessment_model->getAssessmentName($data['ass_id']);
    $data['group'] = $this->assessment_marks->getGroupData($input['group_id']);
    $entities = $this->assessment_marks->getMarksEntities($input['assessment_id'], $input['entities'], $input['section_id']);
    $class = $this->assessment_model->getClassName($data['class_id']);
    $data['className'] = $class->className;
    $data['staffList'] = $this->assessment_model->getStaff();
    $data['group_id'] = $input['group_id'];
    $aeIds = array();
    foreach ($entities as $key => $entity) {
      $aeIds[] = $entity->aeId;
      $entity->formula = '';
      $entity->grades = json_decode($entity->grades);
      if($entity->ass_type == 'Derived') {
        $derived = json_decode($entity->derived_formula);
        $entity->formula = $derived->formula->name;
      }
      unset($entity->derived_formula);
      $data['entities'][$entity->aeId] = $entity;
    }
    if($data['group']->is_elective) {
      $list = $this->assessment_marks->getElectiveStudentMarksList($input['group_id'], $input['section_id'], $aeIds);
    } else {
      $list = $this->assessment_marks->getStudentMarksList($input['section_id'], $aeIds);
    }
    $subject_remarks = $this->assessment_marks->getSubjectRemarks($data['ass_id'], $data['group_id']);
    foreach ($list['list'] as $key => $std) {
      $std->subject_remarks = '';
      if(array_key_exists($std->student_id, $subject_remarks)) {
        $std->subject_remarks = $subject_remarks[$std->student_id];
      }
    }
    $data['remarks_list'] = array();
    if($data['assessment']->remarks_group_id) {
      $data['remarks_list'] = $this->assessment_marks->getRemarksList($data['assessment']->remarks_group_id);
    }
    $data['students'] = $list['list'];
    $data['isFreshEntry'] = $list['isFreshEntry'];
    // echo "<pre>"; print_r($data); die();
    $data['main_content'] = 'examination/marks_entry/marks';
    $this->load->view('inc/template', $data);
  }

  public function saveMarks() {
    $input = $this->input->post();
    $status = $this->assessment_marks->saveMarks();
    $this->session->set_flashdata('flashSuccess', 'Marks added successfully');
    redirect('examination/assessment_marks/marksEntry/'.$input["ass_id"].'/'.$input["class_id"]);
    // echo '<pre>'; print_r($input); die();
  }

  public function unlockMarksStatus() {
    $aeIds = $_POST['aeIds'];
    echo ($this->assessment_marks->unlockMarksStatus($aeIds));
  }

   public function addStudentMarks($assId, $class_id){
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    if(empty($data['assessment'])) {
      redirect('examination/assessment_marks');
    }
    $data['subjectList'] = $this->assessment_model->getSubjectSections($assId);
    // $data['sections'] = $this->assessment_model->getAssessmentSections($assId,$class_id);
    $data['sections'] = $this->assessment_model->getSectionsByClass($class_id);
    $class = $this->assessment_model->getClassName($class_id);
    $data['className'] = $class->className;
    
    $staff = $this->staffcache->getStaffCache();
    $data['class_id'] = $class_id;
    $data['staffList'] = $this->assessment_model->getStaff();
    $data['staffId'] = 0;
    if(!empty($staff)){
      $data['staffId'] = $staff->staffId;
    }

    $data['exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    if($data['exam_admin']) {
      $data['sectionAccess'] = $this->assessment_marks->getReadPermission($assId, $data['sections']);
    } else {
      $data['sectionAccess'] = $this->assessment_marks->getPermissionForStaff($assId, $data['staffId'], $class_id);
    }

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/marks/marks_entry_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/marks/marks_entry_mobile';
    }else{
      $data['main_content'] = 'examination/marks/marks_entry';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function adding_marks($assId, $class_id){
    $data['assessment'] = $this->assessment_model->getAssessmentName($assId);
    if(empty($data['assessment'])) {
      redirect('examination/assessment_marks');
    }
    $subjectList = $this->assessment_model->getSubjectsAndPermissions($assId);
    $data['subjectList'] = [];
    foreach ($subjectList as $key => $value) {
      
    }
    // echo "<pre>"; print_r($data['subjectList']); die();
    // $data['sections'] = $this->assessment_model->getAssessmentSections($assId,$class_id);
    $data['sections'] = $this->assessment_model->getSectionsByClass($class_id);
    $class = $this->assessment_model->getClassName($class_id);
    $data['className'] = $class->className;
    
    $staff = $this->staffcache->getStaffCache();
    $data['class_id'] = $class_id;
    $data['staffList'] = $this->assessment_model->getStaff();
    $data['staffId'] = 0;
    if(!empty($staff)){
      $data['staffId'] = $staff->staffId;
    }

    $data['exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    if($data['exam_admin']) {
      $data['sectionAccess'] = $this->assessment_marks->getReadPermission($assId, $data['sections']);
    } else {
      $data['sectionAccess'] = $this->assessment_marks->getPermissionForStaff($assId, $data['staffId'], $class_id);
    }

    $data['main_content'] = 'examination/marks_entry/adding_marks';
    $this->load->view('inc/template', $data);
  }

  public function getStudentListAndMarksBySection() {
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    } else {
      $assId = $_POST['assId'];
      $assEid = $_POST['assEid'];
      $entityId = $_POST['entityId'];
      $classId = $_POST['classId'];
      $section = $_POST['section'];
      $sectionId = $_POST['sectionId'];
      $className = $_POST['className'];
      $data['entityName'] = $this->assessment_model->getEntityName($entityId);
      $data['isFreshEntry'] = $this->assessment_marks->isFreshMarksEntry($assEid,$sectionId, $classId);
      $data['isElective'] = $this->assessment_marks->isElective($entityId, $classId);
      $electiveStd = array();
      $isElective = 0;
      if(!empty($data['isElective'])) {
        $electiveStd = $this->assessment_marks->getEleStudents($data['isElective']->id, $data['isElective']->elective_group_id);
        $isElective = 1;
      }
          
      if ($data['isFreshEntry']) {
        $data['sList'] = $this->assessment_marks->getStudentList($classId,$sectionId,$electiveStd, $isElective);
      }
      else {
        $data['sList'] = $this->assessment_marks->getStudentListWithMarks($classId,$sectionId, $assEid, $electiveStd, $isElective);
      }
      
      echo json_encode($data);
    }
  }

  public function submitStudentMarks(){
    $assId = $this->input->post('assId');
    $classId = $this->input->post('class_id');
    
    $result = $this->assessment_marks->saveStudentMarks();

    if ($result){
      $this->session->set_flashdata('flashSuccess', 'Marks added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/addStudentMarks/'.$assId.'/'.$classId); 
  }

  public function submitStudentAssMarks(){
    $assId = $this->input->post('assId');
    $classId = $this->input->post('class_id');
    
    $result = $this->assessment_marks->insertStudentMarks();

    if ($result){
      $this->session->set_flashdata('flashSuccess', 'Marks added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/addStudentMarks/'.$assId.'/'.$classId); 
  }

  public function unlockMarksEntry(){
    echo json_encode($this->assessment_marks->unlockMarksEntry($_POST['assEid']));
  }

  public function getMarksHistory(){
    $data = $this->assessment_marks->getMarksHistory($_POST['id']);
    $html_str = '';
    if(!empty($data)){
      $i = 1;
      foreach ($data as $key => $value) {
        $html_str .= '<tr>';
        $html_str .= '<td>'.$i++.'</td>';
        $html_str .= '<td>'.$value->staffName.'</td>';
        $html_str .= '<td>'.$value->action.'</td></tr>';
      }
    }
    echo $html_str;
  }

  public function marksCards($class_id = 1){
    $selected_class = $this->input->post('classId');

    if (empty($selected_class)) {
      $selected_class = $class_id;
    }

    $data['isPublisher'] = $this->authorization->isAuthorized('EXAMINATION.PUBLISH_MARKS_CARDS');
    $isSchoolAdmin = $this->authorization->isAuthorized('EXAMINATION.VERIFY_MARKS_CARDS');
    $isPrincipal = $this->authorization->isAuthorized('EXAMINATION.VERIFY_REMARKS');
    $data['classList'] = $this->assessment_model->getClassess();
    $data['selected_class'] = $selected_class;
    if ($selected_class == -1 || empty($selected_class)) {
      //Do Nothing
    } else {
      $data['marksCards'] = $this->assessment_marks->getMarksCards($selected_class);//get all the marks cards
      $flag = 1;
      if($isSchoolAdmin || $isPrincipal) {
        $flag = 0;
      }
      foreach ($data['marksCards'] as $key => $value) {
        $temp = array();
        $arr = json_decode($value->assessments);
        $assIds = array();
        foreach ($arr as $k => $val) {
          array_push($temp, $val->name);
          array_push($assIds, $val->id);
        }
        $value->assessments = implode(",", $temp);
        if(!empty($assIds)) {
          $sections = $this->assessment_model->getSections(implode(",", $assIds), $flag);
        } else {
          $sections = $this->assessment_model->getSectionsByClass($value->class_id);
        }
        $value->showMore = 0;
        if(!empty($sections)){
          $value->showMore = 1;
        }
      }
    }

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/marks_cards/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/marks_cards/index_mobile';
    }else{
      $data['main_content'] = 'examination/marks_cards/index';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function deleteMarksCard() {
    $card_id = $_POST['card_id'];
    echo $this->assessment_marks->deleteMarksCard($card_id);
  }

  public function marksCardDetails($cardId){
    $data['marksCard'] = $this->assessment_marks->getMarksCard($cardId);
    if(empty($data['marksCard'])) {
      redirect('examination/assessment_marks/marksCards');
    }
    $isSchoolAdmin = $this->authorization->isAuthorized('EXAMINATION.VERIFY_MARKS_CARDS');
    $isPrincipal = $this->authorization->isAuthorized('EXAMINATION.VERIFY_REMARKS');
    $flag=1;
    if($isSchoolAdmin || $isPrincipal) {
      $flag = 0;
    }

    $arr = json_decode($data['marksCard']->assessments);
    $assIds = array();
    foreach ($arr as $k => $val) {
      array_push($assIds, $val->id);
    }

    if(!empty($assIds)) {
      $data['sections'] = $this->assessment_model->getSections(implode(",", $assIds), $flag);
    } else {
      $data['sections'] = $this->assessment_model->getSectionsByClass($data['marksCard']->classId);
    }

    // $data['sections'] = $this->assessment_model->getSections(implode(",", $assIds), $flag);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/marks_cards/more_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/marks_cards/more_mobile';
    }else{
      $data['main_content'] = 'examination/marks_cards/more';     
    }
    $this->load->view('inc/template', $data);
  }

  public function generate_ranking_form($marks_card_template_id, $class_id) {
    $data['students_data'] = $this->assessment_marks->get_students_data_with_rank($marks_card_template_id, $class_id);
    $data['assessments'] = $this->assessment_marks->get_assessments_for_ranking($class_id);
    
    $data['main_content'] = 'examination/generate_ranking/index';
    $this->load->view('inc/template', $data);
  }

  public function generate_ranking() {
    $marks_card_template_id = $_POST['marks_card_template_id'];
    $class_id = $_POST['class_id'];
    $this->assessment_marks->generate_ranking($_POST['entities']);
    redirect("examination/assessment_marks/generate_ranking_form/$marks_card_template_id/$class_id");
  }


  public function createMarksCard(){
    $data['classList'] = $this->assessment_model->getClassess();
    $data['gradingSystems'] = $this->assessment_marks->getGradingSystems();
    $data['main_content'] = 'examination/marks_cards/create_markscard';
    $this->load->view('inc/template', $data);
  }

  public function students($tempId, $sectionId){
    $data['tempId'] = $tempId;
    $data['class'] = $this->assessment_model->getClassAndClassTeacher($sectionId);
    if(empty($data['class'])) {
      redirect('examination/assessment_marks/marksCards');
    }
    $data['marksCard'] = $this->assessment_marks->getMarksCardById($tempId);
    $data['students'] = $this->assessment_marks->getSecStudents($sectionId,$tempId);
    // echo "<pre>"; print_r($data['students']); die();
    foreach ($data['students'] as $key => $value) {
      if($value->ack_status == 0 || $value->ack_status == '') {
        $value->ack_status = 'Not Seen';
      } else {
        $value->ack_status = 'Seen';
      }
    }

    $data['sectionId'] = $sectionId;
    $data['add_remarks'] = 0;
    $data['verify_remarks'] = 0;
    $data['generate_marksCard'] = 0;
    $data['regenerate_marksCrad'] = 0;
    $data['view_marksCard'] = 0;
    $data['permitted'] = 0;
    $data['is_super_admin'] = $this->authorization->isSuperAdmin();
    $data['sectionIdIfCT'] = $this->assessment_marks->getSectionIdIfClassTeacher();
    if($this->authorization->isAuthorized('EXAMINATION.ADD_REMARKS_CT')) {
      $data['add_remarks'] = 1;
      if(!in_array($sectionId, $data['sectionIdIfCT']))
        $data['add_remarks'] = 0;
    }
    if($this->authorization->isAuthorized('EXAMINATION.VERIFY_REMARKS')) {
      $data['verify_remarks'] = 1;
    }
    if($this->authorization->isAuthorized('EXAMINATION.VERIFY_MARKS_CARDS')) {
      $data['generate_marksCard'] = 1;
    }
    if($this->authorization->isAuthorized('EXAMINATION.VIEW_MARKS_CARD_CT')) {
      $data['view_marksCard'] = 1;
      if(!in_array($sectionId, $data['sectionIdIfCT']))
        $data['view_marksCard'] = 0;
    }
    if($this->authorization->isAuthorized('EXAMINATION.VIEW_MARKS_CARD_ALL')) {
      $data['view_marksCard'] = 1;
    }
    if($this->authorization->isAuthorized('EXAMINATION.ADD_REMARKS_ALL')) {
      $data['add_remarks'] = 1;
    }
    if($this->authorization->isAuthorized('EXAMINATION.REGENERATE_MARKSCARDS')) {
      $data['regenerate_marksCrad'] = 1;
    }
    if($this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN')) {
      $data['view_marksCard'] = 1;
      $data['add_remarks'] = 1;
    }
    $data['class'] = $this->assessment_model->getClassAndClassTeacher($sectionId);
    $data['assessments'] = $this->assessment_model->get_assessments_class_wise($data['class']->id, 'Manual');
    if($data['marksCard']->generation_type == 1) {
      $data['main_content'] = 'examination/consolidation/upload_marks_cards';
      if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) {
        $data['main_content'] = 'examination/consolidation/upload_marks_cardsMobTab';
      }
    } else {
      $data['main_content'] = 'examination/consolidation/bulk_generation';
      if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) {
        $data['main_content'] = 'examination/consolidation/bulk_generationMobTab';
      }
    }

    $this->load->view('inc/template', $data);
  }

  public function get_assessments_by_type() {
    $input= $this->input->post();
    echo json_encode($this->assessment_model->get_assessments_class_wise($input['class_id'], $input['ass_type']));
  }

  public function getMarksCardLinks() {
    $tempId = $this->input->post('tempId');
    $class_section = $this->input->post('class_section');
    $sectionId = $this->input->post('sectionId');
    $dirname = FCPATH.'assets/marks_card_zip/'.$class_section;
    if(is_dir($dirname)){
      // unlink(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip');
      array_map('unlink', glob("$dirname/*.*"));
      rmdir($dirname);
    }
    mkdir(FCPATH.'assets/marks_card_zip/'.$class_section, 0755, true);
    $cards = $this->assessment_marks->getSectionMarksCards($tempId, $sectionId);
    echo json_encode($cards);
  }

  public function addToZip() {
    $card = $_POST['card'];
    $cards_length = $_POST['cards_length'];
    $class_section = $_POST['class_section'];
    $path = FCPATH.'assets/marks_card_zip/'.$class_section;
    $this->load->helper('file');
    if ($card['pdf_link'] !='') {
      $url = $this->filemanager->getFilePath($card['pdf_link']);
      $ext = pathinfo($url, PATHINFO_EXTENSION);
      $name = $card['stdName'].'_'.$card['stdId'].'.'.$ext;
      $data = file_get_contents($url);
      write_file($path.'/'.$name, $data);
      // $this->zip->add_data($name, $data);
    }
    echo 1;
    // echo json_encode($card);
  }

  public function downloadZipFile() {
    $class_section = $_POST['class_section'];
    $dir_path = FCPATH.'assets/marks_card_zip/'.$class_section;
    $this->load->library('zip');
    $this->zip->read_dir($dir_path, FALSE);
    // $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip');
    $this->zip->download($class_section.'.zip');
    echo 1;
  }

  public function zipReportCards() {
    $report_card_ids = $_POST['report_card_ids'];
    $class_section = $this->input->post('class_section');
    $sectionId = $this->input->post('sectionId');
    $cards = $this->assessment_marks->getMarksCardsByIds($report_card_ids);
    $this->load->library('zip'); 
    if(file_exists(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip')){
      unlink(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip');
    }
  
    if(!empty($cards)) {
//We moved from AWS to Wasabi around this time. The old data stayed in AWS.
$date1 = new DateTime('2021-05-31');
      foreach ($cards as $card) {
        if ($card->pdf_link !='') {
//We moved from AWS to Wasabi around this time. The old data stayed in AWS.
$date2 = new DateTime($card->created_date);
if ($date2 < $date1) {
  $url = $this->filemanager->getOldFilePath($card->pdf_link);
} else {
  $url = $this->filemanager->getFilePath($card->pdf_link);
}

          
          $ext = pathinfo($url, PATHINFO_EXTENSION);
          $name = $card->stdName.'_'.$card->stdId.'.'.$ext;
          $data = file_get_contents($url);
          $this->zip->add_data($name, $data);
          $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip');
        }
      }
      $this->zip->download($class_section.'.zip');
    } else {
      $this->session->set_flashdata('flashError', 'Marks cards not generated yet!!!');
    }
    
    redirect('examination/assessment_marks/students/'.$tempId.'/'.$sectionId);
  }

  public function marckCardZip(){
      $tempId = $this->input->post('tempId');
      $class_section = $this->input->post('class_section');
      $sectionId = $this->input->post('sectionId');
      $cards = $this->assessment_marks->getSectionMarksCards($tempId, $sectionId);
      $this->load->library('zip'); 
      if(file_exists(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip')){
          unlink(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip');
      }   
    
      if(!empty($cards)) {
        foreach ($cards as $card) {
          if ($card->pdf_link !='') {
            $url = $this->filemanager->getFilePath($card->pdf_link);
            $ext = pathinfo($url, PATHINFO_EXTENSION);
            $name = $card->stdName.'_'.$card->stdId.'.'.$ext;
            $data = file_get_contents($url);
            $this->zip->add_data($name, $data);
            $this->zip->archive(FCPATH.'assets/marks_card_zip/'.$class_section.'.zip');
          }
        }
        $this->zip->download($class_section.'.zip');
      } else {
        $this->session->set_flashdata('flashError', 'Marks cards not generated yet!!!');
      }
      
      redirect('examination/assessment_marks/students/'.$tempId.'/'.$sectionId);
  }

  public function uploadMarksCardManually() {
    $input = $this->input->post();
    $file = $_FILES['file'];
    if($file['tmp_name'] == '' || $file['name'] == '') {
      echo 0;
    } else {
      $path = $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'marks_cards');
      $status = $this->assessment_marks->updateManualMarksCard($input['student_id'], $input['template_id'], $path['file_name'], 1);
      echo $status;
    }
  }

  public function lockRemarksEntry(){
    echo $this->assessment_marks->lockRemarksEntry();
  }

  public function publishMarksCard(){
    echo $this->assessment_marks->publishMarskCard();
  }

  public function marksCardTemplate($cardId){
    $data['marksCard'] = $this->assessment_marks->getMarksCard($cardId);
    if(empty($data['marksCard'])) {
      redirect('examination/assessment_marks/marksCards');
    }
    $data['assessments'] = $this->assessment_marks->getAssmtDetails($data['marksCard']->class_id);
    $data['main_content'] = 'examination/marks_cards/markscard_template';
    $this->load->view('inc/template', $data);
  }

  public function marksCardXML($cardId) {
    $data['marksCard'] = $this->assessment_marks->getMarksCard($cardId);
    if(empty($data['marksCard'])) {
      redirect('examination/assessment_marks/marksCards');
    }
    $data['main_content'] = 'examination/marks_cards/markscard_xml';
    $this->load->view('inc/template', $data);
  }

  public function template_creation() {
    $data['classes'] = $this->assessment_marks->getClassess(1, 0);
    if($this->mobile_detect->isMobile() || $this->mobile_detect->isTablet()) { 
      $data['main_content'] = 'examination/marks_cards/template_generator_mobile';
    } else {
      $data['main_content'] = 'examination/marks_cards/template_generator';
    }
    
    $this->load->view('inc/template', $data);
  }

  public function generate_template() {
    //echo "<pre>"; print_r($this->input->post()); die();
    $post = $_FILES['xml'];
    $xml = simplexml_load_file($post['tmp_name']);
    $data = array();
    $data['assessments'] = array();
    foreach ($xml->assessments as $key => $value) {
      array_push($data['assessments'], (String)$value->name);
    }
    $data['subjects'] = $this->assessment_marks->getClassMappings($_POST['class_id']);
    $data['master_subjects'] = $this->assessment_marks->getClassMasterSubjects($_POST['class_id']);
    echo json_encode($data);
  }

  public function checkTemplate(){
    //echo "<pre>"; print_r(); die();
    $input = $_FILES['xml'];
    if ($input['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(array('error' => 'Error uploading XML file'));
        exit; 
    }
    $xml = @simplexml_load_file($input['tmp_name']);

    if ($xml === false) {
        echo json_encode(array('error' => 'Error loading XML file'));
        exit; 
    }

    $assArray = array();
    foreach ($xml->assessments as $key => $value) {
        array_push($assArray, $value->name);
    }
    
    echo json_encode($assArray);
}


  public function createMarksCardTemplate($cardId){
    $status = $this->assessment_marks->addMarksTemplate($cardId);
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Template added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/marksCardDetails/'.$cardId);
  }

  public function editMarksCardTemplate() {
    $tempId = $this->input->post('tempId');
    $template = $this->input->post('template');
    $status = $this->assessment_marks->updateTemplate($template, $tempId);
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Template updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/marksCardDetails/'.$tempId);
  }

  public function save_and_return_sample_html() {
    $tempId = $this->input->post('tempId');
    $template = $this->input->post('template');
    $class_id = $this->input->post('classId');
    $status = $this->assessment_marks->updateTemplate($template, $tempId);
    //Wait for 3 second for the read and write servers to sync
    sleep(2);

    //Select a random student id and generate html
    $stdId = $this->assessment_marks->get_random_student_id($class_id);
    $ret_val = $this->_generate_marks_card_html($stdId, $tempId);
    $html = $ret_val['html'];
    
    $data = array();
    $data['gen_html'] = $html;
    $data['template_id'] = $tempId;
    $data['std_id'] = $stdId;
    echo json_encode($data);
  }

  public function save_and_return_sample_dummy_html() {
    $tempId = $this->input->post('tempId');
    $template = $this->input->post('template');
    $class_id = $this->input->post('classId');
    $status = $this->assessment_marks->updateTemplate($template, $tempId);
    //Wait for 3 second for the read and write servers to sync
    sleep(2);

    //Select a random student id and generate html
    $stdId = $this->assessment_marks->get_random_student_id($class_id);
    $ret_val = $this->_generate_marks_card_html($stdId, $tempId, 'dummy_marks');
    $html = $ret_val['html'];

    $data = array();
    $data['gen_html'] = $html;
    $data['template_id'] = $tempId;
    $data['std_id'] = $stdId;
    echo json_encode($data);
  }

  public function generate_pdf_of_random_student() {
    $template_id = $this->input->post('template_id');
    $std_id = $this->input->post('std_id');

    //Generate PDF
    $ret_val = $this->_generate_marks_card_html($std_id, $template_id);
    $html = $ret_val['html'];
    $pdf_path = $this->testPost($std_id, $template_id, '2', $html);

    $data = array();
    $data['pdf_relative_path'] = $pdf_path;
    $data['pdf_path'] = $this->config->item('s3_base_url') . '/' . $pdf_path;
    echo json_encode($data);
  }

  public function generate_pdf_of_random_student_dummy() {
    $template_id = $this->input->post('template_id');
    $std_id = $this->input->post('std_id');

    //Generate PDF
    $ret_val = $this->_generate_marks_card_html($std_id, $template_id, 'dummy_marks');
    $html = $ret_val['html'];
    $pdf_path = $this->testPost($std_id, $template_id, '2', $html);

    $data = array();
    $data['pdf_relative_path'] = $pdf_path;
    $data['pdf_path'] = $this->config->item('s3_base_url') . '/' . $pdf_path;
    echo json_encode($data);
  }

  public function check_pdf_generated() {
    echo json_encode($this->assessment_marks->check_pdf_generated($_POST['pdf_path']));
  }

  public function editMarksCardXML() {
    //echo "<pre>"; print_r($this->input->post()); die();
    $tempId = $this->input->post('tempId');
    $status = $this->assessment_marks->updateXMLJSON();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'XML/JSON updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/marksCardXML/'.$tempId);
  }

  public function getSubjectsByAssessmentId(){
    $assId = array($_POST['assId']);
    $subType = $_POST['subjectType'];
    $data = $this->assessment_marks->getSubjectsByAssessment($assId, $subType);
    echo json_encode($data);
  }

  public function ackReport($cardId, $classId){
    $data['classData'] = $this->assessment_model->getClassName($classId);
    if(empty($data['classData'])) {
      redirect('examination/assessment_marks/marksCards');
    }
    $data['sections'] = $this->assessment_marks->getSectionsFromClass($classId);
    $data['cardId'] = $cardId;
    $data['main_content'] = 'examination/marks_cards/ack_report';
    $this->load->view('inc/template', $data);
  }

  public function getAckReport(){
    echo json_encode($this->assessment_marks->getAckReport());
  }

  public function remarksReport($cardId, $classId){
    $data['classData'] = $this->assessment_model->getClassName($classId);
    if(empty($data['classData'])) {
      redirect('examination/assessment_marks/marksCards');
    }
    $data['sections'] = $this->assessment_marks->getSectionsFromClass($classId);
    $data['cardId'] = $cardId;
    $data['main_content'] = 'examination/marks_cards/remarks_report';
    $this->load->view('inc/template', $data);
  }

  public function getRemarksReport(){
    $sectionId = $_POST['sectionId'];
    $data['remarks'] = $this->assessment_marks->getRemarksReport();
    $data['classTeacher'] = $this->assessment_model->getClassTeacherBySectionId($sectionId);
    echo json_encode($data);
  }

  public function submitRemarks($tempId){
    $classId = $this->input->post('classId');
    $stdId = $this->input->post('next');
    $type = $this->input->post('type');
    $status = $this->assessment_marks->insertRemarks($tempId);
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Remarks Added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/studentRemarks/'.$tempId.'/'.$stdId.'/'.$type);
  }

  public function getAssessmentsByClassId(){
    $classId = $_POST['classId'];
    echo json_encode($this->assessment_marks->getAssmtDetails($classId));//get all
  }

  public function saveMarksCard(){
    $status = $this->assessment_marks->saveMarksCard();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Template added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/marksCards/');
  }

  public function studentRemarks($tempId, $stdId, $type){
    $data['marksCard'] = $this->assessment_marks->getMarksCardById($tempId);
    $assessments = json_decode($data['marksCard']->assessments);
    $data['class'] = $this->db->select('id,class_name')->where('id', $data['marksCard']->class_id)->get('class')->row();
    $data['stdData'] = $this->assessment_model->getStdDetails($stdId);
    $data['observations'] = $this->assessment_marks->getStdObservations($stdId);
    $data['remarks'] = $this->assessment_marks->getRemarks($tempId, $stdId);
    $data['prev'] = $this->assessment_marks->getAdjacentStudents($stdId, 'prev');
    $data['next'] = $this->assessment_marks->getAdjacentStudents($stdId, 'next');
    $data['remarks_list'] = $this->assessment_marks->getReportRemarksList();
    $assArr = array();
    $assIds = array();
    foreach ($assessments as $key => $value) {
      array_push($assIds, $value->id);
    }
    $assessments = $this->assessment_marks->sortAssessments($assIds);
    $data['assessments'] = $assessments;
    $data['marks_data'] = $this->assessment_marks->getReportCardMarksData($assIds, $stdId);

    foreach ($assessments as $key => $value) {
      $assArr[$value->name] = $this->assessment_marks->getReportBystdId($value->id, $stdId);
    }

    $data['subjects'] = $this->assessment_marks->getSortedSubjects($data['class']->id);

    $mData = $this->_formMarksStructure($assArr);
    
    $data['marksArr'] = $mData['marksArr'];
    $data['assIdArr'] = $mData['assIdArr'];
    $data['grades_added'] = $mData['grades_added'];
    $remarkStatus = $this->assessment_marks->checkVerified($tempId, $stdId);
    $data['rStatus'] = '0';
    if(!empty($remarkStatus))
      $data['rStatus'] = $remarkStatus->status;
    if($type == 'verify') {
      $data['type'] = 'verify';
    }
    else
      $data['type'] = 'add';
    
    $picture_types = $this->settings->getSetting('markscard_pictures');
    $data['picture_types'] = array();
    if(!empty($picture_types)) {
      if(!empty($data['remarks'])) {
        $pictures = json_decode($data['remarks']->pictures);
        $pics = array();
        $remarks = array();
        if(!empty($pictures)) {
          foreach ($pictures as $key => $val) {
            $pics[$val->mapping] = $val->path;
            $remarks[$val->mapping] = isset($val->remarks)?$val->remarks:'';
          }
        }
        // echo "<pre>"; print_r($pictures); die();
        foreach ($picture_types as $mapping => $pic_name) {
          $path = '';
          $remark = '';
          if(array_key_exists($mapping, $pics)) {
            $path = $pics[$mapping];
            $remark = $remarks[$mapping];
          }
          if(isset($pics))
          $data['picture_types'][$mapping] = array(
            'name' => $pic_name,
            'path' => $path,
            'remarks' => $remark
          );
        }
      }
    }
    // echo "<pre>"; print_r($data['picture_types']); die();
    $data['main_content'] = 'examination/consolidation/remarks_page';
    $this->load->view('inc/template', $data);
  }

  public function s3FileUpload($tmp_name, $name) {
      if($tmp_name == '' || $name == '') {
        return ['status' => 'empty', 'file_name' => ''];
      }        
      return $this->filemanager->uploadFile($tmp_name,$name,'marks_card_pictures');
  }

  public function saveMarksCardPictures() {
    $input = $this->input->post();
    // echo '<pre>'; print_r($_POST); 
    // echo '<pre>'; print_r($_FILES['pictures']); die();
    $pictures = array();
    $files = $_FILES['pictures'];
    if(!empty($_FILES['pictures'])) {
      foreach ($input['mapping'] as $k => $mapping) {
        $file = $this->s3FileUpload($files['tmp_name'][$k], $files['name'][$k]);
        $file_name = '';
        if($file['file_name'] != '') {
          $file_name = $file['file_name'];
        }
        $pictures[] = array(
          'mapping' => $mapping,
          'path' => $file['file_name'],
          'remarks' => $input[$mapping]
        );
      }
    }
    // echo "<pre>"; print_r($pictures);die();
    if(!empty($pictures)) {
      $this->assessment_marks->addPictures($pictures, $input['marks_card_id'], $input['stdId']);
    }
    redirect('examination/assessment_marks/studentRemarks/'.$input['marks_card_id'].'/'.$input['stdId'].'/'.$input['type']);
  }

  private function _formMarksStructure($assArr) {
    $assArray = array();
    $data['grades_added'] = true;
    $data['assIdArr'] = array();
    foreach ($assArr as $key => $value) {
      array_push($data['assIdArr'], $key);
      foreach ($value as $k => $val) {
        if(!array_key_exists($val->aemId, $assArray)){
          $assArray[$val->aemId] = array();
        }
        if($val->marks == ' - ' || $val->marks == '-1.00') {
          $val->percentage = ' - ';
          $val->grade = ' - ';
          $val->total_marks = ' - ';
        } else {
          $val->percentage = 0;
          if($val->total_marks > 0)
            $val->percentage = round(($val->marks / $val->total_marks) * 100);
          $val->grade = $this->assessment_model->__calculateGradeNew($val->percentage, $val->aemId, 'component');
        }

        if(is_null($val->eGSId) || is_null($val->gGSId)) {
          $data['grades_added'] = false;
        }

        $assArray[$val->aemId][$key] = array(
          'marks' => $val->marks,
          'percentage' => $val->percentage,
          'grade' => $val->grade,
          'tmarks' => $val->total_marks,
          'name' => $val->name
        );
      }
    }
    $data['marksArr'] = $assArray;
    return $data;
  }

  public function getMergedAssessments(){
    $assName = $_POST['assName'];
    $stdId = $_POST['stdId'];
    $stdData = $this->assessment_model->getStdDetails($stdId);
    $assessments = $this->assessment_marks->getMarksDetails($assName);
    $assArr = array();
    $assIds = array();
    foreach ($assessments as $id => $name) {
      array_push($assIds, $id);
    }
    $assessments = $this->assessment_marks->sortAssessments($assIds);

    foreach ($assessments as $key => $value) {
      $assArr[$value->name] = $this->assessment_marks->getReportBystdId($value->id, $stdId);
    }

    $mData = $this->_formMarksStructure($assArr);

    $subjects = $this->assessment_marks->getSortedSubjects($stdData->classId);

    $assIdArr = $mData['assIdArr'];
    $assArray = $mData['marksArr'];

    $htmlStr = '<thead><tr><th></th>';
    foreach ($assIdArr as $key => $value) {
      $htmlStr .= '<th colspan="2">'.$value.'</th>';
    } 
    $htmlStr .= '</tr></thead><thead><tr><th>Subjects</th>';
    foreach ($assIdArr as $key => $value) {
      $htmlStr .= '<th>Marks</th><th>Max</th>';
    }
    $htmlStr .= '</tr></thead><tbody>';
    $i = 1;
    foreach ($subjects as $in => $sub) {
      if(!array_key_exists($sub->id, $assArray)) {
        continue;
      }
      $htmlStr .= '<tr><td>'.$sub->name.'</td>';
      $index=''; 
      foreach ($assIdArr as $key => $value) {
        $index=$value;
        
        if(!array_key_exists($value, $assArray[$sub->id])) {
          $subMarks = ' - ';
          $total = ' - ';
        } else {
          $subMarks = $assArray[$sub->id][$value]['marks'];
          if($subMarks == -1.00) 
            $subMarks = 'AB';
          $total = $assArray[$sub->id][$value]['tmarks'];
        }
        $htmlStr .= '<td>'.$subMarks.'</td><td>'. $total .'</td>';
      }
    }
    $htmlStr .= '</tbody>';

    echo $htmlStr;
  }

  public function unlockRemarks(){
    $tempId = $_POST['tempId'];
    $stdId = $_POST['stdId'];
    echo $this->assessment_marks->unlockRemarks($tempId, $stdId);
  }

  public function testingMarksCard(){
    $tempId = $_POST['tempId'];
    $stdId = $_POST['stdId'];
    $type = $_POST['type'];
    $ret_val = $this->_generate_marks_card_html($stdId, $tempId);
    $html = $ret_val['html'];
    
    $does_errors_exist = $ret_val['does_errors_exist'];
    $pdf_path = $this->testPost($stdId, $tempId, $type, $html);
    $this->assessment_marks->update_html_in_table($stdId, $tempId, $html, $does_errors_exist);
    $status = ($pdf_path == '0') ? 0 : 1;

    $data = array();
    $data['pdf_relative_path'] = $pdf_path;
    $data['pdf_path'] = $this->filemanager->getFilePath($pdf_path);

    echo json_encode($data);
  }

  private function _generate_marks_card_html($stdId, $tempId, $generate_type = "") {
    $stdData = $this->assessment_model->getStdDetails($stdId);
    $stdHealthData = $this->assessment_model->getStdHealthDetails($stdId);
    $stdrunningHTWTData = $this->assessment_model->getStdrunninghltdata($stdId);
    // echo '<pre>';print_r($stdData);die();

    //Config
    $string_to_use_for_tbd = 'NA';
    if ($this->settings->getSetting('examination_string_to_use_for_tbd_in_report_card')) {
      $string_to_use_for_tbd = $this->settings->getSetting('examination_string_to_use_for_tbd_in_report_card');
    }

    $stdAddress = $this->assessment_marks->getStdAddress($stdId);
    $parent = $this->assessment_marks->getStdParentNames($stdId);
    $father = '';
    $father_mobile_no = '';
    $mother = '';
    $mother_mobile_no = '';
    $guardian = '';
    $guardian_mobile_no = '';
    $format_for_absent = "AB";
    if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }

    foreach ($parent as $key => $value) {
      if($value->relation_type == 'Father') {
        $father = $value->name;
        $father_mobile_no = $value->mobile_no;
      } else if($value->relation_type == 'Mother') {
        $mother = $value->name;
        $mother_mobile_no = $value->mobile_no;
      } else {
        $guardian = $value->name;
        $guardian_mobile_no = $value->mobile_no;
      }
    }

    $principal = $this->assessment_marks->getPrincipalObject($stdData->classId);
    $template_background = $this->assessment_marks->gettemplate_BG($tempId);
    $data['marksCard'] = $this->assessment_marks->getMarksCardById($tempId);
    $assessments = json_decode($data['marksCard']->assessments);
    $template = $data['marksCard']->template_content;
    $assIds = array();
    foreach ($assessments as $key => $value) {
      array_push($assIds, $value->id);
    }

    //changes to avoid getting double marks entry for electives
    $stdArray = $this->assessment_marks->getStudentReportCardMarks($assIds, $stdId, $stdData->class_section_id, $generate_type);
    //  echo '<pre>';print_r($stdArray);die();
    // $stdArray = $this->assessment_marks->getStdArrayData($assIds, $stdId);

    // $classTeacher = $this->assessment_marks->getClassTeacher($stdId);
    $classTeachers = $this->assessment_marks->getClassTeachers($stdData->class_section_id);
    $student_house = '';
    $combination = '';
    
    if($stdData->student_house) {
      $student_house = $stdData->student_house;
    }
    if($stdData->combination) {
      $combination = $stdData->combination;
    }
    $template = str_replace('%%student_name%%',$stdData->stdName, $template);
    $template = str_replace('%%class_name%%',$stdData->class_name, $template);
    $template = str_replace('%%section_name%%',$stdData->section_name, $template);
    $template = str_replace('%%sts_number%%',$stdData->sts_number, $template);
    $template = str_replace('%%registration_no%%',$stdData->registration_no, $template);
    $template = str_replace('%%std_present_address%%',$stdAddress['present_address'], $template);
    $template = str_replace('%%std_permanent_address%%',$stdAddress['permanent_address'], $template);
    $template = str_replace('%%house%%',strtoupper($student_house), $template);
    $template = str_replace('%%combination%%',$combination, $template);

    $template = str_replace('%%height%%',(isset($stdHealthData->height) ? $stdHealthData->height : ''), $template);
    $template = str_replace('%%weight%%',(isset($stdHealthData->weight) ? $stdHealthData->weight: ''), $template);
    $template = str_replace('%%blood_group%%',(isset($stdHealthData->blood_group) ? $stdHealthData->blood_group : ''), $template);
    $template = str_replace('%%runningheight%%',(isset($stdrunningHTWTData->height_in_cm) ? $stdrunningHTWTData->height_in_cm : ''), $template);
    $template = str_replace('%%runningweight%%',(isset($stdrunningHTWTData->weight_in_kg) ? $stdrunningHTWTData->weight_in_kg: ''), $template);


    if($stdData->picture_url != null || $stdData->picture_url != '') {
      $url = $this->filemanager->getFilePath($stdData->picture_url);
      $img = '<img src="'.$url.'" alt="photo" width="80" height="90" style="position:relative;top:0px;left:0px;">';
      $img1 = '<img src="'.$url.'" alt="photo" width="140" height="160" style="position:relative;top:0px;left:0px;">';
      $template = str_replace('%%student_photo%%', $img, $template);
      $template = str_replace('%%ourschool_student_photo%%', $img1, $template);
    } else {
      $template = str_replace('%%student_photo%%','', $template);
      $template = str_replace('%%ourschool_student_photo%%', '', $template);
    }
    $rem = $this->assessment_marks->getRemarks($tempId, $stdId);
    foreach ($stdArray as $key => $sub) {
      $long_grade = '';
      $long_gGrade = '';
      $gradePoint = '';
      $groupGradePoint = '';
      $subject_remarks = $sub->subject_remarks;
        $marks = $sub->marks;
        $grade = '';
        //$graph_grades='';
        // $gMarks = $sub->gMarks;
        $percentage = '';
        $entNotRoundedMarks = $sub->marks;
        $grpNotRoundedMarks = isset($sub->gMarks)?$sub->gMarks:'';
        $entRoundedtoOne = '';
        $entNotRoundedMarks = '';
        $digit = $this->settings->getSetting('examination_marks_rounding_digits');
        if($digit == '') {
          $digit = 0;
        }
        if($sub->marks === 'not_added') {
          $grade = 'NA';
          $marks = 'NA';
          $long_grade = 'NA';
          $percentage = 'NA';
          $entRoundedtoOne = 'NA';
          $entNotRoundedMarks = 'NA';
          //$graph_grades = 'NA';
        } else if($sub->marks === 'not_elected' || $sub->marks == -3){
          $grade = $string_to_use_for_tbd;
          $marks = $string_to_use_for_tbd;
          $long_grade = $string_to_use_for_tbd;
          $percentage = $string_to_use_for_tbd;
          $entRoundedtoOne = $string_to_use_for_tbd;
          $entNotRoundedMarks = $string_to_use_for_tbd;
          //$graph_grades = $string_to_use_for_tbd;
        } else if($sub->marks == -2){
          $grade = '';
          $marks = '';
          $long_grade = '';
          $percentage = '';
          $entRoundedtoOne = '';
          $entNotRoundedMarks = '';
          //$graph_grades = '';
        } else if($sub->marks == -5){
          $grade = 'Ex';
          $marks = 'Ex';
          $long_grade = 'Ex';
          $percentage = 'Ex';
          $entRoundedtoOne = 'Ex';
          $entNotRoundedMarks = 'Ex';
          //$graph_grades = 'Ex';
        } else {
          $entNotRoundedMarks = $marks;
          $entRoundedtoOne = round($marks, 1);
          $marks = round($marks);
          if($sub->evaluation_type == 'marks') {
            $percentage = 0;
// #2 => In other place also '$grade_percentage' is pre initialized as zero i.e. at line 1534
            $grade_percentage = 0;
            if($sub->total_marks > 0) {
              $percentage = round(($sub->marks / $sub->total_marks) * 100, $digit);
              $grade_percentage = round(($sub->marks / $sub->total_marks) * 100, $digit);
            }
            $grade = $this->assessment_model->__calculateGradeNew($grade_percentage, $sub->aemId, 'component', 'short_name');//get component level grading system
            $long_grade = $this->assessment_model->__calculateGradeNew($grade_percentage, $sub->aemId, 'component', 'long_name');//get component level grading system
            $gradePoint = $this->assessment_model->__calculateGradeNew($grade_percentage, $sub->aemId, 'component', 'grade_point');//get component level grade point
          }
            // } else {
          //   $graph_grades = $this->assessment_model->calculate_grades_for_graph($sub->grade, $sub->aemId);
           
          // }
        }
        
        $gGrade = '';
        $long_gGrade = '';
        $gMarks = '';
        $gPercentage = '';
        $grpRoundedtoOne = '';
        $grpNotRoundedMarks = '';
        //$graph_grades='';
        if(isset($sub->gMarks) && ($sub->gMarks === 'not_added' || $sub->gMarks === ' - ')) {
          $gGrade = 'NA';
          $long_gGrade = 'NA';
          $gMarks = 'NA';
          $gPercentage = 'NA';
          $grpRoundedtoOne = 'NA';
          $grpNotRoundedMarks = 'NA';
          //$graph_grades = 'NA';
        } else if(isset($sub->gMarks) && ($sub->gMarks === 'not_elected' || $sub->gMarks == -3)) {
          if($sub->is_elective) {
            $grade = 'NA';
            $marks = 'NA';
            $long_grade = 'NA';
            $percentage = 'NA';
            $entRoundedtoOne = 'NA';
            $entNotRoundedMarks = 'NA';
            //$graph_grades = 'NA';
          }
          $gGrade = 'NA';
          $long_gGrade = 'NA';
          $gMarks = 'NA';
          $gPercentage = 'NA';
          $grpRoundedtoOne = 'NA';
          $grpNotRoundedMarks = 'NA';
          //$graph_grades = 'NA';
        } else {
          $grpNotRoundedMarks = isset($sub->gMarks)?$sub->gMarks:'';
          $grpRoundedtoOne = isset($sub->gMarks)?round($sub->gMarks, 1):'';
          $gMarks = isset($sub->gMarks)?round($sub->gMarks):'';
          if($sub->evaluation_type == 'marks') {
            $gPercentage = 0;
// #3 => same as #2
            $grade_gPercentage= 0;
            if($sub->gtotal > 0) {
              if (isset($sub->gMarks)) {
                $gPercentage = round(($sub->gMarks / $sub->gtotal) * 100, $digit);
                $grade_gPercentage = round(($sub->gMarks / $sub->gtotal) * 100, $digit);
              } else {
                $gPercentage = 0;
                $grade_gPercentage = 0;
              }
            }
            $gGrade = $this->assessment_model->__calculateGradeNew($grade_gPercentage, $sub->gid, 'group', 'short_name');//get group level grading system
            $long_gGrade = $this->assessment_model->__calculateGradeNew($grade_gPercentage, $sub->gid, 'group', 'long_name');//get group level grading system
            $groupGradePoint = $this->assessment_model->__calculateGradeNew($grade_gPercentage, $sub->gid, 'group', 'grade_point');//get group level grade point
            
          // } else {
          //   $graph_grades = $this->assessment_model->calculate_grades_for_graph($sub->grade, $sub->aemId);
           
          }
        }
        
        // $graph_grade = new stdClass();
        // $graph_grade->long_name = '';
        // $graph_grade->grade_point = '';

        if($sub->marks == '-1.00') {
          $marks = $format_for_absent;
          $grade = $format_for_absent;
          $long_grade = $format_for_absent;
          $entRoundedtoOne = $format_for_absent;
          $entNotRoundedMarks = $format_for_absent;
          $gradePoint = $format_for_absent;
          $percentage = $format_for_absent;
        }
        if(isset($sub->gMarks) && $sub->gMarks == '-1.00') {
          $gMarks = $format_for_absent;
          $gGrade = $format_for_absent;
          $long_gGrade = $format_for_absent;
          $grpNotRoundedMarks =$format_for_absent;
          $grpRoundedtoOne = $format_for_absent;
          $groupGradePoint = $format_for_absent;
          $gPercentage = $format_for_absent;
        }

        if($sub->marks == '-5.00') {
          $marks = 'Ex';
          $grade = 'Ex';
          $long_grade = 'Ex';
          $entRoundedtoOne = 'Ex';
          $entNotRoundedMarks = 'Ex';
          $gradePoint = 'Ex';
          $percentage = 'Ex';
        }
       
        if(isset($sub->gMarks) &&  $sub->gMarks == '-5.00') {
          $gMarks = 'Ex';
          $gGrade = 'Ex';
          $long_gGrade = 'Ex';
          $grpNotRoundedMarks = 'Ex';
          $grpRoundedtoOne = 'Ex';
          $groupGradePoint = 'Ex';
          $gPercentage = 'Ex';
        } 

        
        if($sub->is_elective == 1 &&  $sub->marks == '-5.00') {
          $gMarks = 'Ex';
          $gGrade = 'Ex';
          $long_gGrade = 'Ex';
          $grpNotRoundedMarks = 'Ex';
          $grpRoundedtoOne = 'Ex';
          $groupGradePoint = 'Ex';
          $gPercentage = 'Ex';
        } 

       
        
        

        //entity marks
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_total',floatval($sub->total_marks), $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_marks',$marks, $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_percentage',$percentage, $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_class_average',$sub->class_average, $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_class_highest',$sub->class_highest, $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_section_average',isset($sub->section_average) ? $sub->section_average : '-1', $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_section_highest',isset($sub->section_highest) ? $sub->section_highest : '-1', $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_section_rank',(isset($sub->entity_section_rank)?$sub->entity_section_rank: '-1'), $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_fullgrade_rank',(isset($sub->entity_grade_rank)?$sub->entity_grade_rank: '-1'), $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_in_words', $this->numberTowords($sub->marks), $template);

        //group marks
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_total',floatval($sub->gtotal), $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_marks',$gMarks, $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_percentage',$gPercentage, $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_in_words',$this->numberTowords($gMarks), $template);
        
        //group elective marks
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_total',floatval($sub->gtotal), $template);
        
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_marks',$gMarks, $template);
        //$template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_name', isset($sub->name) ? $sub->name : "NA" , $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_percentage',$gPercentage, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_in_words',$this->numberTowords($gMarks), $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_class_average',$sub->class_average, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_class_highest',$sub->class_highest, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_section_average',isset($sub->section_average) ? $sub->section_average : '-1', $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_section_rank',(isset($sub->entity_section_rank)?$sub->entity_section_rank: '-1'), $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_fullgrade_rank',(isset($sub->entity_grade_rank)?$sub->entity_grade_rank: '-1'), $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_section_highest',isset($sub->section_highest) ? $sub->section_highest : '-1', $template);

        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_rounded_class_highest',round($sub->class_highest,0), $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_rounded_section_highest',round(isset($sub->section_highest) ? $sub->section_highest : '-1',0), $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_rounded_class_highest',round($sub->class_highest,0), $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_rounded_section_highest',round(isset($sub->section_highest) ? $sub->section_highest : '-1',0), $template);

        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_not_rounded_class_highest',$sub->class_highest, $template);
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_not_rounded_section_highest',isset($sub->section_highest) ? $sub->section_highest : '-1', $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_not_rounded_class_highest',$sub->class_highest, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_not_rounded_section_highest',isset($sub->section_highest) ? $sub->section_highest : '-1', $template);

        //entity elective marks
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_name',$sub->name, $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_total',floatval($sub->total_marks), $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_marks',$marks, $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_percentage',$percentage, $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_class_average',$sub->class_average, $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_class_highest',$sub->class_highest, $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_section_average',isset($sub->section_average) ? $sub->section_average : '-1', $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_section_highest',isset($sub->section_highest) ? $sub->section_highest : '-1', $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_section_rank',(isset($sub->entity_section_rank)?$sub->entity_section_rank: '-1'), $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_fullgrade_rank',(isset($sub->entity_grade_rank)?$sub->entity_grade_rank: '-1'), $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_in_words',$this->numberTowords($sub->entityElective), $template);

        if($sub->evaluation_type == 'marks') {
          $template = str_replace($sub->assName.'_a_'.$sub->eString.'_grade',$grade, $template);
          $template = str_replace($sub->assName.'_a_'.$sub->eString.'_long_grade',$long_grade, $template);
          $template = str_replace($sub->assName.'_a_'.$sub->eString.'_grading_point',$gradePoint, $template);

          $template = str_replace($sub->assName.'_g_'.$sub->gString.'_grade',$gGrade, $template);
          $template = str_replace($sub->assName.'_g_'.$sub->gString.'_long_grade',$long_gGrade, $template);
          $template = str_replace($sub->assName.'_g_'.$sub->eString.'_grading_point',$groupGradePoint, $template);

          $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_grade',$gGrade, $template);
          $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_long_grade',$long_gGrade, $template);
          $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_grading_point',$groupGradePoint, $template);

          $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_grade',$grade, $template);
          $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_long_grade',$long_grade, $template);
          $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_grading_point',$gradePoint, $template);
        } else {
          
          $grade = $sub->grade;
          
          if($grade == 'TBD') {
            $grade = '';
          } else if($grade == $format_for_absent) {
            $grade = $format_for_absent;
          }else if($grade == 'Ex') {
            $grade = 'Ex';
          }
          $template = str_replace($sub->assName.'_a_'.$sub->eString.'_grade',$grade, $template);
          $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_grade',$grade, $template);
          $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_grade',$grade, $template);
          //$template = str_replace($sub->assName . '_a_' . $sub->eString . '_grading_point', $graph_grades[0]['grade_point'], $template);
          //$template = str_replace($sub->assName . '_e_' . $sub->elMapString . '_grading_point', $graph_grades[0]['grade_point'], $template);
          //$template = str_replace($sub->assName . '_ae_' . $sub->entityElective . '_grading_point', $graph_grades[0]['grade_point'], $template);
        }

        //elective Name
        //$replacement = !empty($sub->eleName) ? $sub->eleName : 'NA';
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_name', $sub->eleName, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_display_name',$sub->eleDisplayName, $template);

        //subject code
        $template = str_replace($sub->assName.'_g_'.$sub->elMapString.'_subject_code',$sub->subject_code, $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_subject_code',$sub->subject_code, $template);

        //not rounded marks
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_not_rounded_marks', (($entNotRoundedMarks=='NA' || $entNotRoundedMarks == $format_for_absent || $entNotRoundedMarks=='Ex')?$entNotRoundedMarks:floatval($entNotRoundedMarks)), $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_not_rounded_marks',(($grpNotRoundedMarks=='NA' || $grpNotRoundedMarks == $format_for_absent)?$grpNotRoundedMarks:floatval($grpNotRoundedMarks)), $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_not_rounded_marks',(($grpNotRoundedMarks=='NA' || $grpNotRoundedMarks == $format_for_absent)?$grpNotRoundedMarks:floatval($grpNotRoundedMarks)), $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_not_rounded_marks',(($entNotRoundedMarks=='NA' || $entNotRoundedMarks == $format_for_absent)?$entNotRoundedMarks:floatval($entNotRoundedMarks)), $template);

        //rounded to One marks
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_rounded_to_one_marks',$entRoundedtoOne, $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_rounded_to_one_marks',$grpRoundedtoOne, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_rounded_to_one_marks',$grpRoundedtoOne, $template);
        $template = str_replace($sub->assName.'_ae_'.$sub->entityElective.'_rounded_to_one_marks',$entRoundedtoOne, $template);

        //replacing subject ids
        $template = str_replace($sub->assName.'_a_'.$sub->eString.'_entityId',$sub->aemId, $template);
        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_groupId',$sub->gid, $template);

        $template = str_replace($sub->assName.'_g_'.$sub->gString.'_subject_remarks',$subject_remarks, $template);
        $template = str_replace($sub->assName.'_e_'.$sub->elMapString.'_subject_remarks',$subject_remarks, $template);
    }
    
    if(!empty($rem)) {
      $remarks = trim(preg_replace('/<p><br><\/p>/', '', $rem->remarks));
      $template = str_replace('%%remarks%%',$remarks, $template);
      $template = str_replace('%%specific_participation%%',$rem->additional_remarks, $template);
    } else {
      $this->assessment_marks->dummyRemarks($stdId, $tempId);
    }

    $template = str_replace('%%class_teacher%%',$classTeachers['class_teacher'] ? $classTeachers['class_teacher'] : '-' , $template);

    if (!empty($classTeachers['staff_signature'])){
      $fullpath = $this->filemanager->getFilePath($classTeachers['staff_signature']);
      $template = str_replace('%%class_teacher_signature%%', $fullpath, $template);
    }else {
      $template = str_replace('%%class_teacher_signature%%','', $template);
    }
    $template = str_replace('%%assistance_class_teacher%%',$classTeachers['assistant_class_teacher'], $template);
    $template = str_replace('%%class_teacher_woi%%',$classTeachers['class_teacher_woi'], $template);
    $template = str_replace('%%assistance_class_teacher_woi%%',$classTeachers['assistant_class_teacher_woi'], $template);
    $template = str_replace('%%date%%',date('d-m-Y'), $template);
    $template = str_replace('%%parent%%',$father, $template);
    $template = str_replace('%%father%%',$father, $template);
    $template = str_replace('%%mother%%',$mother, $template);
    $template = str_replace('%%father_mobile_no%%',$father_mobile_no, $template);
    $template = str_replace('%%mother_mobile_no%%',$mother_mobile_no, $template);
    // $template = str_replace('%%principal%%',$principal->principal_name, $template); // shreyas code changes
    $template = str_replace('%%principal%%',isset($principal->principal_name) ? $principal->principal_name : '', $template); // Solution for: [KGA] | [2024-07-24 13:26:37] | unknown | Trying to get property of non-object (/home/<USER>/oxygenv2/application/controllers/examination/Assessment_marks.php:1684) | (Notice:8) | *************:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

   
    
    if (!empty($principal->principal_signature)){
      $principath = $this->filemanager->getFilePath($principal->principal_signature);
      $template = str_replace('%%principal_signature%%', $principath, $template);
    }else {
      $template = str_replace('%%principal_signature%%','', $template);
    }

    if (!empty($template_background->template_background)){
      $temp_data = json_decode($template_background->template_background);
      $templateIMG = isset($temp_data->template_background_0) ? $this->filemanager->getFilePath($temp_data->template_background_0) : '';
      $template = str_replace('%%template_background%%', $templateIMG, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_1 = json_decode($template_background->template_background, true);
      $templateIMG1 = isset($temp_data_1['template_background_1']) ? $this->filemanager->getFilePath($temp_data_1['template_background_1']) : '';
      $template = str_replace('%%template_background_1%%', $templateIMG1, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_2 = json_decode($template_background->template_background, true);
      $templateIMG2 = isset($temp_data_2['template_background_2']) ? $this->filemanager->getFilePath($temp_data_2['template_background_2']) : '';
      $template = str_replace('%%template_background_2%%', $templateIMG2, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_3 = json_decode($template_background->template_background, true);
      $templateIMG3 = isset($temp_data_3['template_background_3']) ? $this->filemanager->getFilePath($temp_data_3['template_background_3']) : '';
      $template = str_replace('%%template_background_3%%', $templateIMG3, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_4 = json_decode($template_background->template_background, true);
      $templateIMG4 = isset($temp_data_4['template_background_4']) ? $this->filemanager->getFilePath($temp_data_4['template_background_4']) : '';
      $template = str_replace('%%template_background_4%%', $templateIMG4, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_5 = json_decode($template_background->template_background, true);
      $templateIMG5 = isset($temp_data_5['template_background_5']) ? $this->filemanager->getFilePath($temp_data_5['template_background_5']) : '';
      $template = str_replace('%%template_background_5%%', $templateIMG5, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_6 = json_decode($template_background->template_background, true);
      $templateIMG6 = isset($temp_data_6['template_background_6']) ? $this->filemanager->getFilePath($temp_data_6['template_background_6']) : '';
      $template = str_replace('%%template_background_6%%', $templateIMG6, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_7 = json_decode($template_background->template_background, true);
      $templateIMG7 = isset($temp_data_7['template_background_7']) ? $this->filemanager->getFilePath($temp_data_7['template_background_7']) : '';
      $template = str_replace('%%template_background_7%%', $templateIMG7, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_8 = json_decode($template_background->template_background, true);
      $templateIMG8 = isset($temp_data_8['template_background_8']) ? $this->filemanager->getFilePath($temp_data_8['template_background_8']) : '';
      $template = str_replace('%%template_background_8%%', $templateIMG8, $template);
    }
    if (!empty($template_background->template_background)){
      $temp_data_9 = json_decode($template_background->template_background, true);
      $templateIMG9 = isset($temp_data_9['template_background_9']) ? $this->filemanager->getFilePath($temp_data_9['template_background_9']) : '';
      $template = str_replace('%%template_background_9%%', $templateIMG9, $template);
    }
    
   
   
    

   

    $template = str_replace('%%roll_no%%',$stdData->rollNo, $template);
    $template = str_replace('%%alpha_roll_no%%',$stdData->alpha_rollnum, $template);
    $template = str_replace('%%enrollment_number%%',$stdData->enrollment_number, $template);
    $template = str_replace('%%admission_no%%',$stdData->admission_no, $template);
    $template = str_replace('%%dob%%',$stdData->dob, $template);

    $template = str_replace('%%gender_female%%',$stdData->gender_female, $template);
    $template = str_replace('%%gender_girl%%',$stdData->gender_girl, $template);
    $template = str_replace('%%gender%%',$stdData->gender, $template);

    if(!empty($rem)) {
      $pictures = json_decode($rem->pictures);
      if(!empty($pictures)) {
        foreach ($pictures as $key => $pic) {
          $pic_remark = (isset($pic->remarks))?$pic->remarks:'';
          $path = $this->filemanager->getFilePath($pic->path);
          $template = str_replace('%%'.$pic->mapping.'%%', $path, $template);
          $template = str_replace('%%'.$pic->mapping.'_remarks%%', $pic_remark, $template);
        }
      }
    }

    $html = $template;
    
    //logic to get special grading system if required
    preg_match_all("/##.*?##/", $html, $matched);
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $matchStr) {
        $matchStr = trim($matchStr, '#');
        $replaceStr = $this->_getReplaceString($matchStr);
        $html = str_replace('##'.$matchStr.'##',$replaceStr, $html);
      }
    }

    $matched = [];
    preg_match_all("/!\*.*?!\*/", $html, $matched);
    $html = $this->_fillAttendance($html, $matched, $stdId,$stdData->class_section_id);

    //For prev remarks subjects
    $matched = [];
    preg_match_all("/&&&.*?&&&/", $html, $matched);
    $html = $this->_add_prev_remarks($html, $matched, $symbol='&$', $stdId);

//For adding computed field into report cards // Format:: &@&computed_8&@&
    $matched = [];
    preg_match_all("/&@&.*?&@&/", $html, $matched);
    $html = $this->__get_and_replace_computed_fields($html, $matched, $symbol='&@&', $stdId);

    //For calculating Average across subjects
    $matched = [];
    preg_match_all("/&\*.*?&\*/", $html, $matched);
    $html = $this->_calculateAverage($html, $matched, $symbol='&*');

    $matched = [];
    preg_match_all("/!!.*?!!/", $html, $matched); 
    $value = $this->_calculatTotals($html, $matched);
    $totals = $value['totals'];
    $html = $value['html'];
    foreach ($totals as $name => $tot) {
      if($tot['score'] == 'NA') {
        $grading = array(
          'short_name' => 'NA',
          'long_name' => 'NA',
          'grade_point' => 'NA'
        );
        $html = str_replace('!!'.$name.'_in_words!!', 'NA', $html);
        $html = str_replace('!!'.$name.'_percentage!!','', $html);
      } else if($tot['score'] == $format_for_absent) {
        $grading = array(
          'short_name' => $format_for_absent,
          'long_name' => $format_for_absent,
          'grade_point' => $format_for_absent,
        );
        $html = str_replace('!!'.$name.'_in_words!!', 'Absent', $html);
        $html = str_replace('!!'.$name.'_percentage!!','', $html);
      } else if($tot['score'] == 'Ex') {
        $grading = array(
          'short_name' => 'Ex',
          'long_name' => 'Ex',
          'grade_point' => 'Ex'
        );
        $html = str_replace('!!'.$name.'_in_words!!', 'Exempted', $html);
        $html = str_replace('!!'.$name.'_percentage!!','', $html);
      } else {
        $percent = round(($tot['score'] / $tot['max']) * 100);
        $html = str_replace('!!'.$name.'_in_words!!', $this->numberTowords($tot['score']), $html);
        $html = str_replace('!!'.$name.'_percentage!!',$percent, $html);
        $grading = $this->assessment_marks->getGrade($tot['grading_scale_id'], $percent); // Grading scale id used here
      }
      // $gGrade = $this->assessment_model->__calculateGradeNew($percent, $tot['groupId'], 'group', 'short_name');//get group level grading system
      // $long_gGrade = $this->assessment_model->__calculateGradeNew($percent, $tot['groupId'], 'group', 'long_name');//get group level grading system
      // $groupGradePoint = $this->assessment_model->__calculateGradeNew($percent, $tot['groupId'], 'group', 'grade_point');//get group level grade point
      if(!empty($grading)) {
        $html = str_replace('!!'.$name.'_grade!!',$grading['short_name'], $html);
        $html = str_replace('!!'.$name.'_long_grade!!',$grading['long_name'], $html);
        $html = str_replace('!!'.$name.'_grading_point!!',$grading['grade_point'], $html);
      }
    }

    //divine custom grade calculation
    $matched = [];
    preg_match_all("/\*@.*?\*@/", $html, $matched);
    $html = $this->_calculatDivineGrade($html, $matched, $symbol='*@');

    //Skalvi Custom Grade Calculation
    //logic to get special grading system if required
    $matched = [];
    preg_match_all("/@@.*?@@/", $html, $matched);
    $html = $this->_calculatGradingTotalsSkalvi($html, $matched, $symbol='@@');

    //logic to get special grading system if required
    $matched = [];
    preg_match_all("/\*\*.*?\*\*/", $html, $matched);
    $html = $this->_calculatGradingTotalsSkalvi($html, $matched, $symbol='**');

    //logic to get special grading system if required
    $matched = [];
    preg_match_all("/--.*?--/", $html, $matched);
    $html = $this->_calculatGradingTotalsSkalvi($html, $matched, $symbol='--');

    //logic to get special grading system if required
    $matched = [];
    preg_match_all("/\^\^.*?\^\^/", $html, $matched);
    $html = $this->_calculatGradingTotalsSkalvi($html, $matched, $symbol='^^');
// die();

    //Code to add up/down buttons
    $matched = [];
    preg_match_all("/&&.*?&&/", $html, $matched);
    $html = $this->_calculateHigherLower($html, $matched, $symbol='&&');

  //Code to generate a graph
  //@&Literacy, 5, numeracy, 4, theme, 3@&
  $matched = [];
  preg_match_all("/@&.*?@&/", $html, $matched);
  $html = $this->_getChartImage($html, $matched, $symbol='@&', $stdData->stdName);

  //logic to find PUC final class - distinction or first class, or fail
    $matched = [];
    preg_match_all("/\~\~.*?\~\~/", $html, $matched);
    // echo '<pre>matched';print_r($matched);die();
    $html = $this->_calculatGradingTotalsPUC($html, $matched, $symbol='~');

    $html = str_replace('%%remarks%%','', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_total</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_marks</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_percentage</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_name</', '>-<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_grade</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_rounded_to_one_marks</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_not_rounded_marks</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_long_grade</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_grading_point</', '>NA<', $html);
    $html = preg_replace('/>(.*?)_%%(.*?)%%_in_words</', '>NA<', $html);

    $does_errors_exist = 0;
    if ($this->_contains('%%', $html)) {
      $does_errors_exist = 1;
    }

    $this->assessment_marks->update_html_in_table($stdId, $tempId, $html, $does_errors_exist);
    // die();

    return array('html' => $html, 'does_errors_exist' => $does_errors_exist);
  }

  private function numberTowords($number) {
    if(!is_numeric($number) || $number == -3) return 'NA';
    if($number == -1) return 'Absent';
    if($number == -5) return 'Exempted';
    if($number < 0) return '';
    $number_after_decimal = round($number - ($num = floor($number)), 2) * 100;
    // Check if there is any number after decimal
    $amt_hundred = null;
    $count_length = strlen($num);
    $x = 0;
    $string = array();
    $change_words = array(0 => '', 1 => 'One', 2 => 'Two',
     3 => 'Three', 4 => 'Four', 5 => 'Five', 6 => 'Six',
     7 => 'Seven', 8 => 'Eight', 9 => 'Nine',
     10 => 'Ten', 11 => 'Eleven', 12 => 'Twelve',
     13 => 'Thirteen', 14 => 'Fourteen', 15 => 'Fifteen',
     16 => 'Sixteen', 17 => 'Seventeen', 18 => 'Eighteen',
     19 => 'Nineteen', 20 => 'Twenty', 30 => 'Thirty',
     40 => 'Forty', 50 => 'Fifty', 60 => 'Sixty',
     70 => 'Seventy', 80 => 'Eighty', 90 => 'Ninety');
    $here_digits = array('', 'Hundred','Thousand','Lakh', 'Crore');
    while( $x < $count_length ) {
       $get_divider = ($x == 2) ? 10 : 100;
       $amount = floor($num % $get_divider);
       $num = floor($num / $get_divider);
       $x += $get_divider == 10 ? 1 : 2;
       if ($amount) {
         $add_plural = (($counter = count($string)) && $amount > 9) ? 's' : null;
         $amt_hundred = ($counter == 1 && $string[0]) ? ' and ' : null;
         $string [] = ($amount < 21) ? $change_words[$amount].' '. $here_digits[$counter]. $add_plural.' 
         '.$amt_hundred:$change_words[floor($amount / 10) * 10].' '.$change_words[$amount % 10]. ' 
         '.$here_digits[$counter].$add_plural.' '.$amt_hundred;
         }else $string[] = null;
       }
    $implode_to_Rupees = implode('', array_reverse($string));
    $get_decimal = ($number_after_decimal > 0) ? " (.) " . ($change_words[$number_after_decimal / 10] . " " . $change_words[$number_after_decimal % 10]) : '';
    return ($implode_to_Rupees ? $implode_to_Rupees . ' ' : '') . $get_decimal;
  }

  private function _fillAttendance($html, $matched, $stdId,$class_section_id) { 
 
    $matches = array_unique($matched[0]);
    foreach ($matches as $matchStr) {
      $matchStr = trim($matchStr, '!*');
      $match_data = explode("_", $matchStr);
      $subject_id = $match_data[0];
      $from_date = $match_data[1];
      $to_date = $match_data[2];
      // list($subject_id, $from_date, $to_date) = explode("_", $matchStr);
      $type = 'subject_wise';
      if(isset($match_data[3])) {
        $type = $match_data[3];
      }
      // echo '<pre>';print_r($matchStr);die();
      if($type == 'subject-wise') {
        $attendance = $this->assessment_marks->getAttendanceData($subject_id, $from_date, $to_date, $stdId);
      } else if ($type == 'subject-wise-elective') {
        $attendance = $this->assessment_marks->getAttendanceDataElective($subject_id, $from_date, $to_date, $stdId);
      } else if($type == 'day-wise') {
        $attendance = $this->assessment_marks->getAttendanceDataDayWise($from_date, $to_date, $stdId);
      } else if($type == 'day-wise-all') {
        $attendance = $this->assessment_marks->getAttendanceDataDayWiseAll($from_date, $to_date, $stdId);
      } else if ($type == 'day-wise-v1') {
        $attendance = $this->assessment_marks->getAttendanceDataDayWise_v1($from_date, $to_date, $stdId);
      } else if ($type == 'day-wise-v2') {
        $attendance = $this->assessment_marks->getAttendanceDataDayWise_v2($from_date, $to_date, $stdId,$class_section_id);
      }
     
      // Defensive: ensure $attendance is an object with required properties
      if (!is_object($attendance)) {
        $attendance = new stdClass();
      }
      if (!property_exists($attendance, 'present')) {
        $attendance->present = 0;
      }
      if (!property_exists($attendance, 'absent')) {
        $attendance->absent = 0;
      }
      if (!property_exists($attendance, 'count_late')) {
        $attendance->count_late = 0;
      }
      if(empty($attendance)) {
        $present = '';
        $total = '';
        $count_late = '';
        $percentage = 'NA';
      } else {
        $present = $attendance->present;
        $total = $attendance->present + $attendance->absent;
        $count_late = $attendance->count_late;
        $digit = $this->settings->getSetting('examination_marks_rounding_digits');
        if($digit == '') {
          $digit = 0;
        }
        if ($total > 0)
          $percentage = round(($present / $total) * 100, $digit);
        else
          $percentage = 'NA';
      }
      $html = str_replace('!*'.$matchStr.'!*_present', $present, $html);
      $html = str_replace('!*'.$matchStr.'!*_total', $total, $html);
      $html = str_replace('!*'.$matchStr.'!*_late', $count_late, $html);
      $html = str_replace('!*'.$matchStr.'!*_percentage', $percentage, $html);
    }
    return $html;
  }

  private function _calculatTotals($html, $matched) {
    $totalArr = [];
    $format_for_absent = "AB";
if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $matchStr) {
        $matchStr = trim($matchStr, '!');
        $dont_add = 0;
        if(substr($matchStr, -1) == '*') {
          $dont_add = 1;
        }
        if(preg_match("/^marks/", $matchStr)) {
          list($prefix, $totalIndex, $mString) = explode("#", $matchStr);
          $complete = explode("/", $mString);
          $marks = explode("+", $complete[0]);
          $totals = explode("+", $complete[1]);
          if(isset($complete[2])) { // In html template, in the preg match string grading system id is not proper OR preg string does not match the format
            $grading_scale_id = trim($complete[2], '*');
          } else {
            $grading_scale_id = 1;
          }
          $score = 0;
          $is_not_applicable = 0;
          $absent_count = 0;
          $na_count = 0;
          $total_subjects = 0;
          foreach ($marks as $mark) {
            $total_subjects++;
            if(is_numeric($mark)) {
              $score += $mark;
            } else if($mark == 'NA'){
              $is_not_applicable = 1;
              $na_count++;
            } else if($mark == $format_for_absent) {
              $absent_count++;
            }
          }
          $max = 0;
          foreach ($totals as $tot) {
            if(is_numeric($tot)) {
              $max += $tot;
            }
          }
          if(!array_key_exists($totalIndex, $totalArr)) {
            $totalArr[$totalIndex] = array();
          }
          if($dont_add) {
            $html = str_replace('!!'.$matchStr.'!!','', $html);
          } else {
            if($score == 0 && ($total_subjects == $na_count || $total_subjects == $absent_count)) {
              $score = ($total_subjects == $absent_count)? $format_for_absent:'NA';
              $html = str_replace('!!'.$matchStr.'!!',$score, $html);
              $html = str_replace('!!'.$totalIndex.'_percentage!!',$score, $html);
            } else {
              $html = str_replace('!!'.$matchStr.'!!',$score, $html);
              if ($max === 0) {
                $percent = 'ERR-DZ';
              } else {
                $percent = round(($score / $max) * 100, 2);
              }
              $html = str_replace('!!'.$totalIndex.'_percentage!!',$percent, $html);
            }
          }
          
          $totalArr[$totalIndex]['score'] = $score;
          $totalArr[$totalIndex]['max'] = $max;
          $totalArr[$totalIndex]['grading_scale_id'] = $grading_scale_id;
        }
      }
    }
    return array('html' => $html, 'totals' => $totalArr);
  }

  private function __get_and_replace_computed_fields($html, $matched, $symbol, $std_id) {
    $format_for_absent = "AB";
    if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $match_str) {
        $match_str_full = trim($match_str, $symbol);
        $computed_arr = explode("$", $match_str_full);
        $mapping_string = $computed_arr[0];
        $show_mgp = isset($computed_arr[1]) ? $computed_arr[1] : 'marks';
        $grading_system_id = isset($computed_arr[2]) ? $computed_arr[2] : '';
          $result= $this->assessment_marks->get_computed_field_result($std_id, $mapping_string, $show_mgp, $grading_system_id);
          if($result['status'] == 'success') {
            $res_marks = $result['result'];
            if($result['result'] == -1){
              $res_marks=$format_for_absent;
            }else if($result['result'] == -2){
              $res_marks="TBD";
            }else if($result['result'] == -3){
              $res_marks="NA";
            }
            
            switch ($show_mgp) {
             
              case 'marks':
                $html = str_replace($match_str, $res_marks, $html);
                break;
              case 'grade':
                $html = str_replace($match_str, $result['grade'], $html);
                break;
              case 'percentage':
                $html = str_replace($match_str, $result['percentage'], $html);
                break;
              case 'total':
                $html = str_replace($match_str, $result['total'], $html);
                break;
              case 'ceil':
                $html = str_replace($match_str, ceil($result['result']), $html);
                break;
              case 'floor':
                $html = str_replace($match_str, floor($result['result']), $html);
                break;
              case 'round0':
                $html = str_replace($match_str, round($result['result'], 0), $html);
                break;
              case 'round1':
                $html = str_replace($match_str, round($result['result'], 1), $html);
                break;
              case 'round2':
                $html = str_replace($match_str, round($result['result'], 2), $html);
                break;
              case 'percentage_round0':
                $html = str_replace($match_str, round($result['percentage'], 0), $html);
                break;
              case 'percentage_round1':
                $html = str_replace($match_str, round($result['percentage'], 1), $html);
                break;
              case 'percentage_round2':
                $html = str_replace($match_str, round($result['percentage'], 2), $html);
                break;

              }
          }

      }
    }


     return $html;
  }

  private function _add_prev_remarks($html, $matched, $symbol, $std_id) {
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $match_str) {
        $match_str_full = trim($match_str, $symbol);
        $tem_id_arr = explode("_", $match_str_full);
        $template_id = $tem_id_arr[1];
        $remarks= $this->assessment_marks->get_prev_remarks($std_id, $template_id);

        $html = str_replace($match_str, $remarks, $html);
      }
    }
     return $html;
  }

  private function _calculateAverage($html, $matched, $symbol) {
    $digit = $this->settings->getSetting('examination_marks_rounding_digits');
    if($digit == '') {
      $digit = 0;
    }
    $format_for_absent = "AB";
if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $match_str) {
        $match_str_full = trim($match_str, $symbol);
        $marks_str_temp = explode("/", $match_str_full);
        $marks_str_numbers = $marks_str_temp[0];
        $rounding_parameter = isset($marks_str_temp[1]) ? $marks_str_temp[1] : 'not_rounded';
        $marks_array = explode("$$", $marks_str_numbers);
        $total = 0;
        $na_count = 0;
        $absent_count = 0;
        $total_subjects = 0;
        foreach ($marks_array as $mark) {
          $total_subjects++;
          if(is_numeric($mark)) {
            $total += $mark;
          } else if($mark == 'NA'){
            $na_count++;
          } else if($mark == $format_for_absent) {
            $absent_count++;
          }
        }
        $avg = 0;
        if($total == 0 && ($total_subjects == $na_count || $total_subjects == $absent_count)) {
          $avg = ($total_subjects == $absent_count)? $format_for_absent:'NA';
        } else {
          if ($total_subjects > 0) {
            switch ($rounding_parameter) {
              case 'not_rounded':
                //Not rounded defaults to 2 digit rounding
                $avg = round($total / $total_subjects, 2);
                break;
              case '2_round':
                $avg = round($total / $total_subjects, 2);
                break;
              case '1_round':
                $avg = round($total / $total_subjects, 1);
                break;
              case 'round':
                $avg = round($total / $total_subjects);
                break;
              case 'roundup':
                $avg = ceil($total / $total_subjects);
                break;
              case 'rounddown':
                $avg = floor($total / $total_subjects);
                break;
              default:
                //Not rounded defaults to 2 digit rounding
                $avg = 'INV_ROUND_OPTION - 2_round, not_rounded, 1_round, round, roundup, rounddown';
                break;
              }
          } else {
            $avg = 'ERR-DZ';
          }
  
        }
        $html = str_replace('&*'.$match_str_full.'&*',$avg, $html);
      }
    }
    return $html;
  }

  private function _calculateHigherLower($html, $matched, $symbol) {
    $format_for_absent = "AB";
if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $match_str) {
        $match_str = trim($match_str, $symbol);
     
        //Separate out Grading scheme ID with marks & totals
        $input_array = explode("$$", $match_str);
        // echo '<pre>';print_r($input_array);die();
        $last_marks = $input_array[0];
        $current_marks = $input_array[1];
        $width_perc = $input_array[2];

        if ($last_marks == $format_for_absent || $current_marks == $format_for_absent) {
          $html = str_replace('&&'.$match_str.'&&'," ", $html);
        }

        if ($last_marks < $current_marks) {
          $html = str_replace('&&'.$match_str.'&&',"<img src='/home/<USER>/silicon/assets/up_arrow.png' width='$width_perc%'>", $html);
        } else if ($last_marks > $current_marks) {
          $html = str_replace('&&'.$match_str.'&&',"<img src='/home/<USER>/silicon/assets/down_arrow.png' width='$width_perc%'>", $html);
        } else  {
          $html = str_replace('&&'.$match_str.'&&',"<img src='/home/<USER>/silicon/assets/equal_arrow.png' width='$width_perc%'>", $html);
        }
      }
    }
    return $html;
  }

  private function _calculatGradingTotalsPUC($html, $matched, $symbol) {
    $fail_percentage_in_settings = $this->settings->getSetting('examination_fail_percentage');
    if (empty($fail_percentage)) {
      $fail_percentage = 0;
    }

    $format_for_absent = "AB";
if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }

    $totalArr = [];
    $grade = '';
    $no_of_failed_subjects = 0;
    $total_score = 0;
    $total_totals = 0;
    $match_str = '';
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $match_str) {
        $no_of_failed_subjects = 0;
        $match_str = trim($match_str, $symbol);
        //Separate out Grading scheme ID with marks & totals
        $mark_total_grade = explode("$$", $match_str);
        $mark_total = $mark_total_grade[0];
        $grade_scale_id = $mark_total_grade[1];

        if (!isset($mark_total_grade) || !isset($grade_scale_id)) {
          break;
        }
        
        $subjects = explode("+", $mark_total);
        foreach ($subjects as $sub) {
          $sub_details = explode("/", $sub);
          if (!isset($sub_details[0]) || !isset($sub_details[1])) {
            $grade = 'invalid';
            break;
          }
          $sub_mark = $sub_details[0];
          $sub_total = $sub_details[1];
          if (isset($sub_details[2]))
            $fail_percentage = $sub_details[2];
          else
            $fail_percentage = $fail_percentage_in_settings;

          if ($sub_mark == 'NA') {
            //No Action if so
            continue;
          } else if ($sub_mark == $format_for_absent) {
            //If the student is absent in one of the exams, consider as fail!
            $no_of_failed_subjects ++;
            continue;
          }

          //Add total_totals
          if(is_numeric($sub_total)) {
            $total_totals = $total_totals + $sub_total;
          }

          //Add sub total
          if(is_numeric($sub_mark)) {
            $total_score = $total_score + $sub_mark;
            $perc = 0;
            if ($sub_total != 0) {
              $perc = $sub_mark / $sub_total * 100;
            }
            if ($perc < $fail_percentage) {
              $no_of_failed_subjects ++;
            }
          }
        }
      if ($no_of_failed_subjects == 0) {
        if ($total_totals != 0) {
          $total_perc = $total_score / $total_totals * 100 * 1.0;
          //Get the Grade description
          if (is_numeric($grade_scale_id)) {
            $grading = $this->assessment_marks->getGrade($grade_scale_id, $total_perc);
          } else {
            $grading['long_name'] = 'Invalid';
          }
        } else {
          $grading['long_name'] = 'Invalid';
        }
      } else {
        //Get the Fail grade!
        $grading = $this->assessment_marks->getGrade($grade_scale_id, 0);
      }

      $html = str_replace('~~' . $match_str . '~~', $grading['long_name'], $html);
    }
  }
    return $html;
  }

  private function _getDisplayGradeSkalvi($grades) {
    $format_for_absent = "AB";
if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }
    $grades_values = array(
      'A+' => 5,
      'A' => 4,
      'B' => 3,
      'C' => 2,
      'D' => 1,
      'E' => 0
    );

    $g_count = count($grades);

    $grade_points = 0;
    $na_count = 0;
    $absent_count = 0;
    // echo '<pre>';print_r($grades);
    foreach ($grades as $grade) {
      if(array_key_exists($grade, $grades_values)) {
        $grade_points += $grades_values[$grade];
      } else if($grade == 'NA' || $this->_contains('%%',$grade)) {
        //Manjukiran 14-Oct-2022: The second condition is used in case the component string is not resolved. This happens when marks are not entered at all and in case of elective components. In the future, we need to flag this as an error.
        $na_count++;
      } else if($grade == $format_for_absent) {
        $absent_count++;
      }
    }

    if($g_count == $na_count) {
      return 'NA';
    }
    if($g_count == $absent_count) {
      return $format_for_absent;
    }

    $g_count = $g_count - $na_count;

    /*if($g_count == 2) {
      $g1 = array_key_exists($grades[0], $grades_values)?$grades_values[$grades[0]]:0;
      $g2 = array_key_exists($grades[1], $grades_values)?$grades_values[$grades[1]]:0;
      if($g1 > $g2) return $grades[0];
      return $grades[1];
    }*/

    if($g_count == 10) {
      if($grade_points >= 46) {
        return 'A+';
      }
      if($grade_points >= 36) {
        return 'A';
      }
      if($grade_points >= 26) {
        return 'B';
      }
      if($grade_points >= 16) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 9) {
        if($grade_points >= 41) {
          return 'A+';
        }
        if($grade_points >= 32) {
          return 'A';
        }
        if($grade_points >= 23) {
          return 'B';
        }
        if($grade_points >= 14) {
          return 'C';
        }
        return 'D';
    } else if($g_count == 8) {
      if($grade_points >= 36) {
        return 'A+';
      }
      if($grade_points >= 28) {
        return 'A';
      }
      if($grade_points >= 21) {
        return 'B';
      }
      if($grade_points >= 13) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 7) {
      if($grade_points >= 32) {
        return 'A+';
      }
      if($grade_points >= 25) {
        return 'A';
      }
      if($grade_points >= 18) {
        return 'B';
      }
      if($grade_points >= 11) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 6) {
      if($grade_points >= 27) {
        return 'A+';
      }
      if($grade_points >= 21) {
        return 'A';
      }
      if($grade_points >= 15) {
        return 'B';
      }
      if($grade_points >= 9) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 5) {
      if($grade_points >= 23) {
        return 'A+';
      }
      if($grade_points >= 18) {
        return 'A';
      }
      if($grade_points >= 13) {
        return 'B';
      }
      if($grade_points >= 8) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 4) {
      if($grade_points >= 18) {
        return 'A+';
      }
      if($grade_points >= 14) {
        return 'A';
      }
      if($grade_points >= 10) {
        return 'B';
      }
      if($grade_points >= 6) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 3) {
      if($grade_points >= 14) {
        return 'A+';
      }
      if($grade_points >= 11) {
        return 'A';
      }
      if($grade_points >= 8) {
        return 'B';
      }
      if($grade_points >= 5) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 2) {
      if($grade_points >= 9) {
        return 'A+';
      }
      if($grade_points >= 7) {
        return 'A';
      }
      if($grade_points >= 5) {
        return 'B';
      }
      if($grade_points >= 3) {
        return 'C';
      }
      return 'D';
    } else if($g_count == 1) {
      if($grade_points == 5) {
        return 'A+';
      }
      if($grade_points == 4) {
        return 'A';
      }
      if($grade_points == 3) {
        return 'B';
      }
      if($grade_points == 2) {
        return 'C';
      }
      return 'D';
    }
  }

  // returns true if $needle is a substring of $haystack
  private function _contains($needle, $haystack) {
      return strpos($haystack, $needle) !== false;
  }

  private function _getChartImage($html, $matched, $symbol, $std_name) {
    // echo "<pre>"; print_r($matched); 
    $format_for_absent = "AB";
    if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
          $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
        }
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $matchStr) {
        $matchStr = trim($matchStr, $symbol);
        $components = explode("#", $matchStr);

        $grades = explode(",", $components[0]);
       
        $width = isset($components[1]) ? $components[1] : '700';
        $height = isset($components[2]) ? $components[2] : '300';
        $yMax = isset($components[3]) ? $components[3] : '3';
        $intervals = isset($components[4]) ? $components[4] : '1';
        $bgColor = isset($components[5]) ? $components[5] : '';

        $yAxis = [];
        $xAxis = [];
        $i = 0;
        foreach ($grades as $g) {
          
          if ($i++ % 2 == 0)
            $xAxis[] = $g;
          else {
            switch ($g) {
              case $format_for_absent:
              case 'NA':
                $yAxis[] = '0';
                break;
                
              default:
                $yAxis[] = $g;
            }
          }
        }
        
       
        $base64_str = $this->_makeChartImage($yAxis, $xAxis, $width, $height, $yMax, $std_name,$bgColor,$intervals);
        $html = str_replace($symbol.$matchStr.$symbol,$base64_str, $html);
      }
    }
    return $html;
  }

  private function _makeChartImage($datay, $datax, $width, $height, $yMax, $std_name,$bgColor,$intervals) {
    $color = $bgColor ? "#$bgColor" : "#4E78A7EB";

      $formattedLabels = array_map(function($label) {
        return wordwrap($label, 15, "\n", true); 
    }, $datax);
     $datxa = [
        'type' => 'bar',
        'data' => [
            'labels' => $formattedLabels,
            'datasets' => [[
                'label' => '', // Empty string to prevent "undefined"
                'data' => $datay,
                'backgroundColor' => $color,
                'barThickness' => 30,
            ]]
        ],
        'options' => [
            'scales' => [
                'yAxes' => [[
                    'ticks' => [
                        'beginAtZero' => true,
                        'min' => 0,
                        'max' => (int)$yMax, 
                        'stepSize' => $intervals
                    ]
                ]]
            ],
            'plugins' => [
                'legend' => [
                    'display' => false // Hide the legend
                ]
            ]
        ]
    ];

    $chartJson = json_encode($datxa);
    $chartUrl = "https://quickchart.io/chart?c=" . urlencode($chartJson);
    $imageData = file_get_contents($chartUrl);
    $base64 = base64_encode($imageData);
    return '<img src="data:image/png;base64,' . $base64 . '" style="width:100%; height:auto;" />';
}

// private function _makeChartImage($datay, $datax, $width, $height, $yMax, $std_name) {
//   $chartData = implode(',', $datay);
// $encodedLabels = implode('|', array_map('urlencode', $datax));

// $chartUrl = "https://chart.googleapis.com/chart?" .
//     "cht=bvs&" . // Chart type: vertical bar chart
//     "chs={$width}x{$height}&" . // Chart size
//     "chd=t:{$chartData}&" . // Chart data
//     "chxl=0:|{$encodedLabels}&" . // X-axis labels
//     "chxt=x,y&" . // Enable X and Y axes
//     "chxr=1,0,{$yMax}"; // Y-axis range
//       echo "<pre>"; print_r($chartUrl); 
//   // Use cURL to fetch the image
//   $ch = curl_init();
//   curl_setopt($ch, CURLOPT_URL, $chartUrl);
//   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
//   $imageData = curl_exec($ch);
//   echo "<pre>"; print_r($imageData); die();
//   curl_close($ch);

//   // Check if the image was successfully retrieved

//   // Convert image data to Base64
//   $base64 = base64_encode($imageData);

//   // Return the <img> tag with the Base64 image
//   return '<img src="data:image/png;base64,' . $base64 . '" style="width:100%; height:auto;" />';
// }



//   private function _makeChartImage($datay, $datax, $width, $height, $yMax, $std_name) {

//    //$this->load->library('jpgraph');


//     $graph = new Graph($width, $height);

//     $graph->SetScale('textlin');
    
//     // Set shadow
//     $graph->SetShadow();
    
//     // Adjust the margin
//     $graph->SetMargin(40,30,20,40);
    
//     // Create the bar plot
//     $bplot = new BarPlot($datay);
    
//     // Add the plot to the graph
//     $graph->Add($bplot);
    
//     // Setup X-axis
//     $graph->xaxis->SetTickLabels($datax);
    
//     // Setup Y-axis
//     $graph->yaxis->scale->SetAutoMin(0);
//     $graph->yaxis->scale->SetAutoMax($yMax);
//     $graph->yaxis->SetTickPositions(range(0, $yMax, 1));
//     $graph->yaxis->SetLabelFormat('%1.1f');
//     $graph->yaxis->scale->ticks->Set(1);
    
//     // Title
//     $graph->title->Set("Bar Graph for $std_name");

//     // Capture the image output
//     ob_start();
//     $graph->Stroke(_IMG_HANDLER);
//     $img = $graph->img->img;
//     imagepng($img);
//     $imageData = ob_get_contents();
//     ob_end_clean();
    
    
//     // Generate a unique file name
//     $file_name = 'new' . rand(1, 1000) . rand(1, 10000) . $std_name . '.png';
    
//     // Encode the image to base64
//     $base64 = base64_encode($imageData);
   
    
//     return '<img src="data:image/png;base64,' . $base64 . '" />';
// }

// private function _makeChartImage($datay, $datax, $width, $height, $yMax, $std_name) {
//   // echo "<pre>"; print_r($datay);
//   // echo "<pre>"; print_r($datax);
//   // echo "<pre>"; print_r($width);
//   // echo "<pre>"; print_r($height);
//   // echo "<pre>"; print_r($yMax);
//   // echo "<pre>"; print_r($std_name); die();
//   // Ensure all values in $datay and $datax are numeric
//   $datay = array_map('floatval', $datay);
//   $datax = array_map('floatval', $datax); // If numeric X-axis labels are required

//   // Create the graph
//   $graph = new Graph($width, $height);
//   $graph->SetScale('textlin');
  
//   // Add a drop shadow
//   $graph->SetShadow();
  
//   // Adjust the margin a bit to make more room for titles
//   $graph->SetMargin(40, 30, 20, 40);
  
//   // Create a bar plot
//   $bplot = new BarPlot($datay);
  
//   // Add the plot to the graph
//   $graph->Add($bplot);
  
  
//   $graph->xaxis->SetTickLabels($datax);
//   $graph->yaxis->scale->setAutoMin(0);
//   $graph->yaxis->scale->setAutoMax($yMax);
//   $graph->yaxis->SetLabelFormat('%1.1f');
//   $graph->yaxis->scale->ticks->Set(1);
  
  
//   $file_name = 'new' . rand(1, 1000) . rand(1, 10000) . $std_name . '.png';
//   $img = $graph->Stroke($file_name);

//   $imagedata = file_get_contents($file_name);
//   $base64 = base64_encode($imagedata);

//   return '<img src="data:image/png;base64,' . $base64 . '" />';
// }



  private function _calculatGradingTotalsSkalvi($html, $matched, $symbol) {
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $matchStr) {
        $matchStr = trim($matchStr, $symbol);
        $grades = explode(",", $matchStr);
        $display_grade = $this->_getDisplayGradeSkalvi($grades);
        $html = str_replace($symbol.$matchStr.$symbol,$display_grade, $html);
      }
    }
    return $html;
  }

  private function _calculatDivineGrade($html, $matched, $symbol) {
    $format_for_absent = "AB";
if ($this->settings->getSetting('examination_report_card_word_for_absent')) {
      $format_for_absent = $this->settings->getSetting('examination_report_card_word_for_absent');
    }
    $grades = array(
      '5' => 'A+',
      '4' => 'A',
      '3' => 'B',
      '2' => 'C',
      '1' => 'D',
      'AB' => $format_for_absent,
      'NA' => 'NA'
    );
    foreach ($matched as $i => $match) {
      foreach ($match as $key => $matchStr) {
        $marks = trim($matchStr, $symbol);
        $grade = isset($grades[$marks])?$grades[$marks]:'';
        $html = str_replace($symbol.$marks.$symbol,$grade, $html);
      }
    }
    return $html;
  }


  public function testPost($stdId, $tempId, $type, $html) {
        $school = CONFIG_ENV['main_folder'];
        $path = $school.'/marks_cards/'.uniqid().'-'.time().".pdf";

        $bucket = $this->config->item('s3_bucket');
        $fullPath = $this->filemanager->getFilePath($path);

        $html = str_replace('%%qr_code%%',$fullPath, $html);
        $path = $this->assessment_marks->replacePathIfExists($stdId, $tempId, $path, $type);
        /*if($school == 'demoschool') {
          $path = $this->assessment_marks->replacePathIfExists($stdId, $tempId, $path, $type);
        } else {
          $status = $this->assessment_marks->updateMarksCardNew($stdId, $tempId, $path, $type);
        }*/
        $page = 'portrait';
        $page_size = 'a4';
        $pageSetting = $this->settings->getSetting('marks_card_page_size');
        $sizeArray = ["a4", "a3", "a5", "letter", "legal"]; 
        if ($pageSetting) {
          if(in_array($pageSetting, $sizeArray)) {
            $page_size = $pageSetting;
          }
        }

        $curl = curl_init();

        $html = str_replace('+', '%252B', $html);
        $postData = urlencode($html);

        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        $return_url = site_url().'Callback_Controller/updatePdfLinkStatus';
        
        $jobServerExam =  CONFIG_ENV['job_server_exam_pdfgen_uri'];
        $use_alternate_job_server_ip = trim($this->settings->getSetting('examination_use_alternate_job_server_ip'));
        if(strlen($use_alternate_job_server_ip)) {
          $jobServerExam = 'http://'. $use_alternate_job_server_ip .'/index.php/api/Pdfgenerator';
        }
        $postArray = array(
          CURLOPT_URL => $jobServerExam,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_USERPWD => $username . ":" . $password,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
          CURLOPT_HTTPHEADER => array(
              "Accept: application/json",
              "Cache-Control: no-cache",
              "Content-Type: application/x-www-form-urlencoded",
              "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
          ),
        );
        curl_setopt_array($curl, $postArray);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          // echo 0;
          return 0;
        } else {
          // $this->db->where('pdf_link',$path);
          // echo 1;
          return $path;
        // echo $response;
        }
    }

  public function __computeSpecialGradeFor11and12($entityId, $groupId, $theoryPercentage, $groupPercentage){
    $failGrade = $this->assessment_model->__calculateGradeNew(0, $entityId, 'component', 'short_name');
    $theoryGrade = $this->assessment_model->__calculateGradeNew($theoryPercentage, $entityId, 'component', 'short_name');
    if($theoryGrade == $failGrade)
      return $theoryGrade;
    return $this->assessment_model->__calculateGradeNew($groupPercentage, $groupId, 'group', 'short_name');//return groupGrade
  }

  public function _getReplaceString($matchStr){
    $matchStr = trim($matchStr, '$');
    $strings = explode("$", $matchStr);
    $grade = '-';
    $functionName = $strings[1];
    if($strings[4] == 'NA' || $strings[5] == 'NA')
      return 'NA';
    
    switch ($functionName) {
      case 'computeSpecialGradeFor11and12':
        $grade = $this->__computeSpecialGradeFor11and12($strings[2], $strings[3], $strings[4], $strings[5]); //entityId, groupId, entityPercentage, groupPercentage
        break;
      
      default:
        break;
    }
    return $grade;
  }

  public function downloadMarksCard($id, $name){
    $name = str_replace('-', ' ', urldecode($name));
    $link = $this->assessment_marks->downloadMarksCard($id);

    //We moved from AWS to Wasabi around this time. The old data stayed in AWS.
		$date1 = new DateTime('2021-05-31');
    $created_date= $this->assessment_marks->get_created_date_of_marksCArd($id);
		$date2 = new DateTime($created_date);
		if ($date2 < $date1) {
			$url = $this->filemanager->getOldFilePath($link);
		} else {
			$url = $this->filemanager->getFilePath($link);
		}

    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($name.'.pdf', $data, TRUE);
  }

  public function subject_remarks() {
    $data['subject_remarks'] = $this->assessment_marks->getAllSubjectRemarks();

    // echo '<pre>'; print_r($data); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/subject_remarks/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/subject_remarks/index_mobile';
    }else{
      $data['main_content'] = 'examination/subject_remarks/index';    	
    }
    $this->load->view('inc/template', $data);
  }

  public function addSubjectRemarks() {
    $status = $this->assessment_marks->addSubjectRemarks();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/subject_remarks');
  }

  public function editSubjectRemarks() {
    $input = $this->input->post();
    $status = $this->assessment_marks->updateSubjectRemarks();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/subject_remarks');
    // echo "<pre>"; print_r($input); die();
  }

  public function deleteRemarks() {
    $status = $this->assessment_marks->deleteSubjectRemarks();
    echo json_encode($status);
      }

  public function addMoreSubjectRemarks() {
    $input = $this->input->post();
    $status = $this->assessment_marks->addMoreSubjectRemarks();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'added successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/subject_remarks');
    // echo "<pre>"; print_r($input); die();
  }

  public function marks_entry_status() {
    $data['is_exam_admin'] = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $staffId = $this->authorization->getAvatarStakeHolderId();
    $data['classList'] = $this->assessment_marks->getClassess($data['is_exam_admin'], $staffId);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/marks_entry/entry_status_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/marks_entry/entry_status_mobile';
    }else{
      $data['main_content'] = 'examination/marks_entry/entry_status';
    }
    $this->load->view('inc/template', $data);
  }

  public function getAssessments() {
    $classId = $_POST['class_id'];
    $this->load->model('examination/Assessment_reports_model','assessment_reports');
    $assessments = $this->assessment_reports->getClsAssessments_new($classId, 'Manual');
    echo json_encode($assessments);
  }

  public function getMarksEntryStatus() {
    $class_id = $_POST['class_id'];
    $assessment_id = $_POST['assessment_id'];
    $is_exam_admin = $this->authorization->isAuthorized('EXAMINATION.EXAM_ADMIN');
    $data['entities'] = $this->assessment_marks->getPermittedSubjectStatus($assessment_id, $class_id, $is_exam_admin);
    $data['sections'] = $this->assessment_marks->getSections($class_id);
    $data['status'] = array();
    foreach ($data['entities'] as $group_id => $ent) {
      if(!empty($ent['entities'])) {
        $assessment_entities_ids = array_keys($ent['entities']);
        $data['status'][$group_id] = $this->assessment_marks->getMarksEntryStatusClassWise($assessment_entities_ids, $ent['elective_id'], $group_id, $class_id);
      }
    }
    echo json_encode($data);
    // echo '<pre>'; print_r($data); die();
  }

        public function changePublish() {
    $status = $this->assessment_marks->changePublishStatus();
    echo $status;
  }

  public function changeMultiPublish() {
    $status = $this->assessment_marks->changeMultiPublishStatus();
    echo $status;
  }

  public function generate_marks() {
    $data['classList'] = $this->assessment_model->getClassess();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/generate_marks/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/generate_marks/index_mobile';
    }else{
      $data['main_content'] = 'examination/generate_marks/index';
    }    
    
    $this->load->view('inc/template', $data);
  }

  public function get_student_marks_in_derived_assessment() {
    $assId= $_POST['assessment_id'];
    $sectionId= $_POST['section_id'];
    $data['assessments'] = $this->assessment_model->getAssessmentName($assId);

    $assIds = json_decode($data['assessments']->formula);
    $assArr = array();
    if(!empty($assIds)){
      foreach ($assIds->assessments as $key => $value) {
        array_push($assArr, $value->id);
      }
    }
    array_push($assArr, $assId);

    $status = $this->assessment_model->get_student_derived_assessments_marks($sectionId, $assArr, $_POST['entity_id']);
    $merg= ucwords($assIds->merg_algorithm->name);
    echo json_encode(array($status, $merg));
  }

  public function get_student_marks() {
    
    $assId= $_POST['assessment_id'];
    // $classId= $_POST['class_id'];
    // echo "<pre>"; echo print_r($_POST); die();
    $sectionId= $_POST['section_id'];
    // $data['assessments'] = $this->assessment_model->getAssessmentName($assId);
   
    
      $status = $this->assessment_model->get_student_assessments_marks($sectionId, $assId, $_POST['entity_id']);
      $data['formula'] = $this->assessment_model->get_subjects_derived_formula($_POST['entity_id']);
    echo json_encode(array($status, json_decode($data['formula'])->formula));
  }

  public function get_student_section_and_class_ranks() {
    $assId= $_POST['assessment_id'];
    $sectionId= $_POST['section_id'];

    $status = $this->assessment_model->get_student_section_and_class_ranks($sectionId, $assId, $_POST['entity_id']);
    // echo '<pre>';print_r($status);die();
    echo json_encode($status);
  }

  public function get_student_marks_details() {
    $assessment_id= $_POST['assessment_id'];
    $student_id= $_POST['student_id'];
    $class_section_id= $_POST['class_section_id'];
    $class_id= $_POST['class_id'];
    $status = $this->assessment_model->get_student_marks_details($assessment_id, $student_id, $class_section_id, $class_id);
    echo json_encode($status);
  }

  public function edit_marks_of_a_student() {
    echo json_encode($this->assessment_model->edit_marks_of_a_student());
  }

  public function delete_marks_of_a_student() {
    echo json_encode($this->assessment_model->delete_marks_of_a_student());
  }

  public function derived_fields() {
    $staffId = $this->authorization->getAvatarStakeHolderId();
    $data['classList'] = $this->assessment_marks->getClassess(1, $staffId);
    $data['gradingSystem'] = $this->assessment_marks->getGradingSystem();
    $data['derived_fields'] = $this->assessment_marks->get_derived_fields('all');
    // echo '<pre>'; print_r($data['classList']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'examination/consolidation/derived_fields_view_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'examination/consolidation/derived_fields_view_mobile';
    }else{
      $data['main_content'] = 'examination/consolidation/derived_fields_view';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_assessment_class_wise() {
    echo json_encode($this->assessment_marks->get_assessment_class_wise($this->input->post()));
  }

  public function get_subjects_class_wise() {
    echo json_encode($this->assessment_marks->get_subjects_class_wise($this->input->post()));
  }
  
  public function create_derived_fields() {
    // echo '<pre>'; print_r($this->input->post()); die();
    echo json_encode($this->assessment_marks->create_derived_fields($this->input->post()));
  }

  public function get_full_details_of_derived_field() {
    echo json_encode($this->assessment_marks->get_full_details_of_derived_field($this->input->post()));
  }

  public function get_computed_fields_class_wise() {
    $class_id= $this->input->post('filtered_class');
    echo json_encode($this->assessment_marks->get_derived_fields($class_id));
  }

  // public function rgenerate_computed_fields() {
  //   $acfm_id= $this->input->post('acfm_id');
  //   echo json_encode($this->assessment_marks->rgenerate_computed_fields($acfm_id));
  // }

  public function rgenerate_computed_fields_v2() {
    $acfm_id= $this->input->post('acfm_id');
    echo json_encode($this->assessment_marks->rgenerate_computed_fields_v2($acfm_id));
  }

  public function calback_function_to_regenerate_details() {
    $data= $this->input->post('data');
    $acfm_id= $this->input->post('acfm_id');
    // echo '<pre>'; print_r($data); die();
    $status= $this->assessment_marks->calback_function_to_regenerate_details($data, $acfm_id);
    echo $status;
  }
  
  public function mass_generation_function() {
    $filtered_class= $this->input->post('filtered_class');
    echo json_encode($this->assessment_marks->mass_generation_function($filtered_class));
  }

  public function check_unique_mapping_string() {
    $mapping_string= $this->input->post('mapping_string');
    echo json_encode($this->assessment_marks->check_unique_mapping_string($mapping_string));
  }

  public function get_class_wise_computed_fields() {
    $selected_class_id= $this->input->post('selected_class_id');
    echo json_encode($this->assessment_marks->get_class_wise_computed_fields($selected_class_id));
  }

  public function get_computed_data_by_id() {
    $acfm_id= $this->input->post('acfm_id');
    $data= $this->assessment_marks->get_computed_data_by_id($acfm_id);
    
    $json_subject= json_decode($data->subjects);
    // echo '<pre>'; print_r($json_subject); die();
    if(array_key_exists('computed_fields', $json_subject)) {
      $computed_fields= $json_subject->computed_fields;
      $cfIdsArr= array();
      if(!empty($computed_fields)) {
        foreach ($computed_fields as $key => $value) {
          $cfIdsArr[]= $value->cf_id;
        }
        $json_subject->computed_fields= $this->assessment_marks->get_computed_fields_by_ids($cfIdsArr);
      }
    }
    unset($data->subjects);
    echo json_encode(['computed_data' => $data, 'subjects' => $json_subject]);
  }

  public function delete_whole_data() {
    $acfm_id= $this->input->post('acfm_id');
    echo json_encode($this->assessment_marks->delete_whole_data($acfm_id));
  }

  public function fetch_and_regenerate_derived_subject() {
    echo json_encode($this->assessment_marks->fetch_and_regenerate_derived_subject($this->input->post()));
  }

  public function fetch_and_regenerate_derived_subjects_in_derived_assessments() {
    echo json_encode($this->assessment_marks->fetch_and_regenerate_derived_subjects_in_derived_assessments($this->input->post()));
  }

  public function fetch_and_regenerate_ccomputed_fields() {
    echo json_encode($this->assessment_marks->fetch_and_regenerate_ccomputed_fields($this->input->post()));
  }

  public function regenerate_average_high_and_rank() {
    echo json_encode($this->assessment_marks->regenerate_average_high_and_rank($this->input->post()));
  }

 public function update_template(){
    $status  = $this->assessment_marks->update_template();
    if ($status){
      $this->session->set_flashdata('flashSuccess', 'Template Updated successfully');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    } 
    redirect('examination/assessment_marks/marksCards');
 }

public function get_all_ids_path() {
  $url_paths_arr= [];
  foreach($_POST['ids'] as $key => $val) {
    $link = $this->assessment_marks->downloadMarksCard($val);

    //We moved from AWS to Wasabi around this time. The old data stayed in AWS.
    $date1 = new DateTime('2021-05-31');
    $created_date= $this->assessment_marks->get_created_date_of_marksCArd($val);
    $date2 = new DateTime($created_date);
    if ($date2 < $date1) {
      $url = $this->filemanager->getOldFilePath($link);
    } else {
      $url = $this->filemanager->getFilePath($link);
    }
    $url_paths= new stdClass();
    $url_paths->url= $url;
    $url_paths->filename= $_POST['names'][$key]. '.pdf';
    $url_paths_arr[]= $url_paths;
  }

  echo json_encode($url_paths_arr);
}

public function add_remarks_studentwise($marksCard_id,$class_id){
  $data = $this->input->post();
  $data['template_id']=$marksCard_id;
  $data['class_id']=$class_id;
  $data['sectionlist'] = $this->assessment_marks->get_section_class_for_remarks($class_id);
  $data['get_remarks']=$this->assessment_marks->get_default_remarks();
  if ($this->mobile_detect->isMobile()) {
    $data['main_content'] = 'examination/remarks_entry/add_remarksv2_studentwise_mobile';
  }else{
    $data['main_content'] = 'examination/remarks_entry/add_remarksv2_studentwise';     
  }
  $this->load->view('inc/template', $data);
}

public function get_remarks_students(){
  $templId=$this->input->post('template_id');
  $class_id=$this->input->post('class_id');
  $section_id=$this->input->post('section_id');
  $data['marksCard'] = $this->assessment_marks->getMarksCardById($templId);
  $data['students'] = $this->assessment_marks->getSecStudents($section_id,$templId);
  echo json_encode($data);
}

public function save_remark_studentwise(){
  $result = $this->assessment_marks->save_remark_studentwise();
    echo json_encode($result);
  }

  public function selected_remarks(){
    $result = $this->assessment_marks->selected_remarks();
    echo json_encode($result);
  }

  public function upload_remark_studentwise(){
    $file_path = $_FILES['upload_csv']['tmp_name'];
    $csv_marks_arr = [];
    $this->load->library('csvimport');
    if ($this->csvimport->get_array($file_path)) {
      $csv_marks_arr = $this->csvimport->get_array($file_path);
    }
    echo json_encode($csv_marks_arr);
  }

  function getAllComputefFieldsClassWise() {
    $result = $this->assessment_marks->getAllComputefFieldsClassWise();
    echo json_encode($result);
}
  
// function getStudentsSubjectWiseMarks(){
//   echo "<pre>"; print_r($this->input->post()); die();
//   $result = $this->assessment_marks->getStudentsSubjectWiseMarks();
//   echo json_encode("Hello");
// } 


}