<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON> Pacchhipulusu
 *          <EMAIL>
 *
 * Created:  16 January 2022
 *
 * Description: Controller for Staff Tasks Basket.
 *
 * Requirements: PHP5 or above
 *
 */

class Staff_mytasksv2 extends CI_Controller {
    function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }
      if (!$this->authorization->isModuleEnabled('STAFF_TASKS_BASKET')) {
        redirect('dashboard', 'refresh');
      }
      $this->load->model('stb_v2/Staff_mytasksv2_model', 'task_model');
      $this->CI = &get_instance();
      $this->load->library('filemanager');
    }

    public function mytasks_v2(){
      $resourcesData = $this->settings->getSetting('resources');
      $fileSize = '5MB';
      $fileType = ['jpg', 'jpeg', 'png'];
      if(!empty($resourcesData) && isset($resourcesData->resource_size)){
        $fileSize = $resourcesData->resource_size ? $resourcesData->resource_size : '5MB';
      }
      if(!empty($resourcesData) && isset($resourcesData->allowed_file_types)){
        $fileType = $resourcesData->allowed_file_types ? $resourcesData->allowed_file_types : ['jpg', 'jpeg', 'png'];
      }
      $data['fileSize'] = $fileSize;
      $data['fileTypes'] = $fileType;
      $data['staff_list'] = $this->task_model->getStaffList();
      $data['basket_list'] = $this->task_model->getBasketList();
      $data['background_image'] = $this->task_model->getBackgroundImage();
      $data['teams_details'] = $this->task_model->getAllStaffTeam();
      $data['board_details'] = $this->task_model->getAllBoards();
      $data['taskListCount'] = $this->task_model->getTaskListCount();
      $data['selected_view'] = 'board_view';
      $data['title'] = 'Manage My Tasks';
      if($this->mobile_detect->isMobile()){
        $data['main_content']    = 'stb/index_mobile.php';
      }else if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'stb/manage_mytask_v2/index_tablet.php';
      }else{
        $data['main_content']    = 'stb/manage_mytask_v2/index_desktop.php';
      }
      $this->load->view('inc_v2/template', $data);
    }

    public function weekly_mytasks(){
      // $data['staff_list'] = $this->task_model->getStaffList();
      // $data['basket_list'] = $this->task_model->getBasketList();
      // $data['background_image'] = $this->task_model->getBackgroundImage();
      // $data['teams_details'] = $this->task_model->getAllStaffTeam();
      // $data['mytasklists'] = $this->task_model->getAllMyTaskLists(); 
      $data['board_details'] = $this->task_model->getAllBoards();
      // $data['unassigned_tasks'] = $this->task_model->get_all_unscheduled_tasks();
      $data['selected_view'] = 'week_view';
      $data['title'] = 'Manage My Tasks';
      if($this->mobile_detect->isTablet()){
        $data['main_content']    = 'stb/manage_weekly_mytasks/index_desktop.php';
      }else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'stb/index_mobile.php';
      }else{
        $data['main_content']    = 'stb/manage_weekly_mytasks/index_desktop.php';
      }
      $this->load->view('inc_v2/template', $data);
    }

    // public function mytasks_simple_calendar_view(){
    //   $data['task_types'] = $this->task_model->get_all_task_types();
    //   $data['title'] = 'Manage My Tasks';
    //   if($this->mobile_detect->isTablet()){
    //     $data['main_content']    = 'stb/manage_weekly_mytasks/index_tablet_simple_calendar_view.php';
    //   }else if ($this->mobile_detect->isMobile()) {
    //     $data['main_content']    = 'stb/index_mobile.php';
    //   }else{
    //     $data['main_content']    = 'stb/manage_weekly_mytasks/index_desktop_simple_calendar_view.php';
    //   }
    //   $this->load->view('inc_v2/template', $data);
    // }

    public function getAllTaskTypes(){
      $result = $this->task_model->get_all_task_types();
      echo json_encode($result);
    }

    public function add_to_basket(){
    $data = $this->task_model->add_to_basket($_POST['basket_name']);
    echo json_encode($data);
  }

    public function delegated_tasks(){
      $data['taskListCount'] = $this->task_model->getTaskListCount();
      $data['selected_view'] = 'delegated_to_me_view';
      $data['background_image'] = $this->task_model->getBackgroundImage();
      $data['title'] = 'Manage Delegated Tasks';
      if($this->mobile_detect->isTablet()){
        $data['main_content']    = 'stb/manage_delegated_tasks/index_tablet.php';
      }else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'stb/index_mobile.php';
      }else{
        $data['main_content']    = 'stb/manage_delegated_tasks/index_desktop.php';
      }
      $this->load->view('inc_v2/template', $data);
    }

    public function get_delegated_tasklists(){
      // $input = $this->input->post();
      $result = $this->task_model->get_delegated_tasklists();
      echo json_encode($result);
    }

    public function get_delegated_tasks_to_you(){
      $list_id = $this->input->get('list_id');
      $result = $this->task_model->get_delegated_tasks_to_you($list_id);
      echo json_encode($result);
    }

    public function addNewBoard(){
      $result = $this->task_model->addNewBoard($_POST);
      echo json_encode($result);
    }

    public function get_all_unscheduled_tasks(){
      $input = $this->input->post();
      $result = $this->task_model->get_all_unscheduled_tasks($input);
      echo json_encode($result);
    }

    public function getAllMyTaskLists(){
      $input = $this->input->post();
      $result = $this->task_model->getAllMyTaskLists($input);
      echo json_encode($result);
    }

    public function getallmylists() {
      $input = $this->input->post();
      $result = $this->task_model->getallmylists($input);
      echo json_encode($result);
    }
    
    public function add_new_tasklist(){
      $result = $this->task_model->add_new_tasklist();
      echo json_encode($result);
    }

    public function change_list_color(){
      $input = $this->input->post();
      $result = $this->task_model->change_list_color($input);
      echo json_encode($result);
    }

    public function add_update_background(){
    $input = $this->input->post();
    $result = $this->task_model->add_user_background_img($input['background_img_path']);

    echo json_encode($result);
  }

  public function delete_list(){
    $input = $this->input->post();
    $result = $this->task_model->delete_list($input);
    echo json_encode($result);
  }

  public function editmylist(){
    $input = $this->input->post();
    $result = $this->task_model->editmylist($input);
    echo json_encode($result);
  }

  public function add_mytask(){
    $result = $this->task_model->add_mytask($_POST);
    echo json_encode($result);
  }

  public function get_all_open_delegated_tasks_of_all_tasklists(){
    $result = $this->task_model->get_all_open_delegated_tasks_of_all_tasklists($_POST['board_id'], $_POST['start_date'], $_POST['end_date']);
    echo json_encode($result);
  }

  public function getallopenmytasks(){
    $list_id = $this->input->get('list_id');
    $result = $this->task_model->getallopenmytasks($list_id);
    echo json_encode($result);
  }

  public function getalldelegatedmytasks(){
    $list_id = $this->input->get('list_id');
    $result = $this->task_model->getalldelegatedmytasks($list_id);
    echo json_encode($result);
  }

  public function getallcompletedmytasks(){
    $list_id = $this->input->get('list_id');
    $result = $this->task_model->getallcompletedmytasks($list_id);
    echo json_encode($result);
  }

  public function updateTaskCompletion(){
    $input = $this->input->post();
    $result = $this->task_model->updateTaskCompletion($input);
    echo json_encode($result);
  }

  public function delete_task(){
    $input = $this->input->post();
    $result = $this->task_model->delete_task($input);
    echo json_encode($result);
  }

  public function delete_delegated_task(){
    $input = $this->input->post();
    $result = $this->task_model->delete_delegated_task($input);
    echo json_encode($result);
  }

  public function add_comment(){
      $input = $this->input->post();

      $details = $this->task_model->get_detials_for_notification($input['task_id']);

      $this->load->helper('texting_helper');
      $notify_array = array();
      $notify_array['staff_ids'] = $details['staff_ids'];
      $notify_array['title'] = 'Task Update';
      $notify_array['message'] = 'New comment by '. $details['staff_name'] .' on '. $details['task_name'];
      $notify_array['mode'] = 'notification';
      $notify_array['source'] = 'Staff Task Basket';
      sendText($notify_array);
      
      $result = $this->task_model->add_comment($input);
      
      echo json_encode($result);
  }

  function update_list_tasks(){
    $input = $this->input->post();
    $result = $this->task_model->update_list_tasks($input);
    echo json_encode($result);
  }

  public function update_priority(){
    $input = $this->input->post();
    $result = $this->task_model->update_priority($input);
    echo json_encode($result);
  }

  public function update_task_type(){
    $input = $this->input->post();
    $result = $this->task_model->update_task_type($input);
    echo json_encode($result);
  }

  public function delegate_task(){
    $input = $this->input->post();

    $staff_ids = [];
    if($input['primary_staff_id'] != null && $input['primary_staff_id'] != ''){
      $staff_ids = [$input['primary_staff_id']];
    }
    
    if(!empty($input['secondary_staff_list'])){
      foreach($input['secondary_staff_list'] as $staff){
        $staff_ids[] = $staff;
      }
    }

    $task_name = $input['task_name'];
    $assigned_by = $this->task_model->get_staff_name();

    $this->load->helper('texting_helper');
    $notify_array = array();
    $notify_array['staff_ids'] = $staff_ids;
    $notify_array['title'] = 'Task Delegated';
    $notify_array['message'] = $task_name. ' has been delegated to you by '.$assigned_by;
    $notify_array['mode'] = 'notification';
    $notify_array['source'] = 'Staff Task Basket';
    sendText($notify_array);

    $result = $this->task_model->delegate_task($input);
    echo json_encode($result);
  }

  public function editmytask(){
    $input = $this->input->post();
    $result = $this->task_model->editmytask($input);
    echo json_encode($result);
  }

  public function editmytaskdesc(){
    $input = $this->input->post();
    $result = $this->task_model->editmytaskdesc($input);
    echo json_encode($result);
  }

  public function update_task_timings(){
    $input = $this->input->post();
    $result = $this->task_model->update_task_timings($input);
    echo json_encode($result);
  }

  public function update_task_timings_calendar() {
    $input = $this->input->post();
    $result = $this->task_model->update_task_timings_calendar($input);
    echo json_encode($result);
  }

  public function remove_due_date(){
    $input = $this->input->post();
    $result = $this->task_model->remove_due_date($input);
    echo json_encode($result);
  }

  public function add_attachment(){
    $input = $this->input->post();
    $result = $this->task_model->add_attachment($input, $input['attached_file_path'], $input['attached_file_name']);
    echo json_encode($result);
  }

  public function delete_attachment(){
    $input = $this->input->post();
    $result = $this->task_model->delete_attachment($input);
    echo json_encode($result);
  }

  public function getallmytasksbydates(){
    $input=$this->input->post();
    $result = $this->task_model->getallmytasksbydates($input);
    echo json_encode($result);
  }

  public function getAllComments(){
    $input=$this->input->post();
    $result = $this->task_model->getAllComments($input);
    echo json_encode($result);
  }

  public function update_calendar_task_details(){
    $input=$this->input->post();
    $result = $this->task_model->update_calendar_task_details($input);
    echo json_encode($result);
  }

  public function saveTaskNotes(){
    $input=$this->input->post();
    $result = $this->task_model->saveTaskNotes($input);
    echo json_encode($result);
  }

  public function delete_calendar_task(){
    $input=$this->input->post();
    $result = $this->task_model->delete_calendar_task($input);
    echo json_encode($result);
  }
}
