<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if (!function_exists('render_search_buttons')) {
  function render_search_buttons()
  {
    $html = '
        <div class="panel-controls" id="exportButtons" style="display:none; float:right; margin-bottom:12px;">
          <div class="search-box">
            <i class="bi bi-search"></i>
            <input type="text" class="input-search" id="table-search" placeholder="Search">
          </div>
        </div>';

    return $html;
  }
}
// Export Btns On top
if (!function_exists('render_report_buttons2')) {
  function render_report_buttons2($printFunction2 = 'printProfile', $excelFunction2 = 'exportToExcel_daily')
  {
    $CI = &get_instance(); // Needed to access CodeIgniter instance
    $printIcon = $CI->load->view('svg_icons/print.svg', [], true);
    $excelIcon = $CI->load->view('svg_icons/excel.svg', [], true);

    $html = '
      <div class="panel-controls" id="exportButtons2" style="display:none; float:right;">
          <button id="expbtns" class="btn btn-outline-primary me-2" onclick="' . $printFunction2 . '()">
              <span class="icon">' . $printIcon . '</span> Print All
          </button>
          <button id="expbtns" class="btn btn-outline-primary" onclick="' . $excelFunction2 . '()">
              <span class="icon">' . $excelIcon . '</span> Export All
          </button>
      </div>
      ';

    return $html;
  }
}

// Progress Bar
if (!function_exists('progress_bar')) {
  function progress_bar()
  {

    $html = '
         <div class="progress" style="display:none;">
            <div class="progress-bar" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
          </div>';

    return $html;
  }
}
//All  Filters Dropdown 
if (!function_exists('filters_dropdown')) {
  function filters_dropdown()
  {

    $html = '<div class="flex-grow-1">
                    <span class="fw-bold custom-left-margin" style="font-size: 20px;font-family: Inter;font-size: 17px;font-style: normal;font-weight: 600; margin-left:20px;">Filters</span>
                    <i class="bi bi-chevron-up" id="filterToggleIcon" style="font-size: 14px; margin-left: 3px; cursor: pointer;" onclick="toggleFilters()"></i>
                 </div>';

    return $html;
  }
}
// No Data Message Image and Content
if (!function_exists('no_data_message')) {
  function no_data_message()
  {

    $svg_content = file_get_contents(APPPATH . "views/svg_icons/no_data.svg");
    $html = '<div class="no-data-state" style="display: none;">
                  <div class="text-center" style="margin-top: 15px;">
                    <div style="display: inline-block;">
                      ' . $svg_content . '
                    </div>
                    <h3 style="margin-top: 25px;font-size: 28px;color: #161327;font-weight: 600;">No Data Available</h3>
                    <p style="margin-top: 10px; font-size: 16px; color: #161327;">There is no available data to show.</p>
                    <p style="margin-top: -5px; font-size: 16px; color: #161327;">Please choose different filters and try again.</p>
                  </div>
                </div>';

    return $html;
  }
}
// Saved Report Filters also call the filters dropdown
if (!function_exists('render_saved_report_filters')) {
  function render_saved_report_filters()
  {
    $CI = &get_instance();

    if (!$CI->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) {
      return '';
    }

    $CI->load->helper('reports_datatable');
    $filtersDropdown = filters_dropdown();

    $style = '
    <style>
      .menu-button {
        display: inline-flex;
        padding: 9px;
        align-items: center;
        gap: 8px;
        border-radius: 4px;
        border: 1px solid #DEDCDF;
        background: #FFF;
        color: #161327;
        font-size: 20px;
        cursor: pointer;
        justify-content: center;
        height: 40px;
        width: 40px;
      }

      .menu-button i {
        color: #161327;
      }

      .menu-button.active {
        padding: 10px;
        border: 1px solid #CEC3F8;
        background: #EFECFD;
        box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
        color: #623CE7;
      }

      .menu-button.active i {
        color: #623CE7;
      }

      .menu-dropdown {
        position: absolute;
        top: 50px;
        right: 0;
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 8px 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        min-width: 180px;
      }

      .menu-dropdown button {
        background: none;
        border: none;
        width: 100%;
        padding: 10px 16px;
        text-align: left;
        font-size: 14px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
      }

      .menu-dropdown button:hover {
        background-color: #f5f5f5;
      }

      .report-row {
        display: flex;
        align-items: center;
        gap: 15px;
      }
      .menu-dropdown i {
        color: #161327;
      }

    </style>';

    $html = '
    <div class="row">
      <div class="col-md-12">
        <div class="d-flex justify-content-end align-items-center" style="gap: 10px;">
          ' . $filtersDropdown . '
          <p for="filter_types" class="me-2 mb-0" style="color: #161327; font-family: Inter; font-size: 14px; font-style: normal; font-weight: 500; line-height: 120%;">Saved Reports</p>
          <select name="" id="filter_types" class="form-control grow-select me-2" onchange="selectFilters()" style="flex: 1; max-width: 250px;">
            <option value="">Select Report</option>
          </select>
          <div class="menu-wrapper position-relative">
            <button class="menu-button btn btn-light" onclick="toggleMenu()" type="button" style="margin-right: 36px;">
              <i class="bi bi-three-dots"></i>
            </button>
            <div class="menu-dropdown custom-dropdown" id="filterMenu" style="display:none">
              <button class="dropdown-item" onclick="saveFilter()">
                <i class="bi bi-save"></i> Save Report
              </button>
              <button class="dropdown-item" onclick="saveFilter()">
                <i class="bi bi-file-plus"></i> Save Report as
              </button>
              <button class="dropdown-item" onclick="updateFilter()">
                <i class="bi bi-pencil-square"></i> Update Report
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>';

    return $style . $html;
  }
}
