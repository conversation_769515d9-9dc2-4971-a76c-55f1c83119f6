<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

if (!function_exists('sendText')) {
        // Internal meaning Send SMS When Session is Available
        // External meaning Send SMS When Session is Not Available
	function sendText($input=[], $callFrom = 'Internal') {
		$CI =& get_instance();
                $CI->load->model('communication/texting_model');
                $CI->load->helper('notification_helper');
                $CI->load->helper('sms_v2_helper');
                $response = array();
                $response['success'] = '';
                $response['error'] = '';

                if(!isset($input['mode'])) {
                        $response['error'] = '<li>mode field is required.</li>';
                        return $response;
                }

                if($input['mode'] == 'notification' || $input['mode'] == 'notification_sms') {
                        $click_action = $CI->settings->getSetting('push_notification_activity_name');
                        $push_notiification_key = $CI->settings->getSetting('push_notification_key');

                        if($click_action == '' ) {
                                $response['error'] = '<li>click_action for notification is not added.</li>';
                                return $response;
                        }
                        if($push_notiification_key == '' ) {
                                $response['error'] = '<li>server key for notification is not added.</li>';
                                return $response;
                        }
                }

                //optional fields
                $sent_by = 1;
                if($callFrom == 'Internal'){
                        if(!isset($input['avatar_id'])) {
                                $sent_by = $CI->authorization->getAvatarId();
                        } else {
                                $sent_by = $input['avatar_id'];
                        }
                        if(!$sent_by) {
                                $sent_by = 1;
                        }
                } else {
                        $sent_by = 1;
                }
                $send_to = 'Both';

                $acad_year_id = 0;
                if($callFrom == 'Internal') {
                        $acad_year_id = $CI->acad_year->getAcadYearId();
                        if(!$acad_year_id) {
                                $acad_year_id = 0;
                        }
                } else {
                        $acad_year_id = $CI->settings->getSetting('academic_year_id');
                }

                $class_send_to = 'Both';
                $batch_class = 'all';
                $is_unicode = 0;
                $visible = 1;
                $student_url = site_url('dashboard');
                $staff_url = site_url('dashboard');

                
                //required fields
                if(!isset($input['title'])) {
                	$input['title'] = $CI->settings->getSetting('school_name');
                }

                if(!isset($input['message'])) {
                	$response['error'] = '<li>message field is required.</li>';
                	return $response;
                }

                if(!isset($input['source'])) {
                	$response['error'] = '<li>source field is required.</li>';
                	return $response;
                }

                if(isset($input['send_to'])) {
                	$send_to = $input['send_to'];
                }

                if(isset($input['class_send_to'])) {
                	$class_send_to = $input['class_send_to'];
                }

                if(isset($input['acad_year_id'])) {
                	$acad_year_id = $input['acad_year_id'];
                }

                if(isset($input['is_unicode'])) {
                	$is_unicode = $input['is_unicode'];
                }

                if(isset($input['visible'])) {
                	$visible = $input['visible'];
                }

                if(isset($input['student_url'])) {
                	$student_url = $input['student_url'];
                }

                if(isset($input['staff_url'])) {
                	$staff_url = $input['staff_url'];
                }

                if(!isset($input['student_ids']) && !isset($input['class_section_ids']) && !isset($input['staff_ids']) && !isset($input['custom_numbers'])) {
                	$response['error'] = '<li>Recipient ids are required.</li>';
                	return $response;
                }

                $recievers_array = array();
                $reciever_data = array();
                $response_count = array(
                        'total_students' => 0,
                        'total_staff' => 0,
                        'total_custom' => 0,
                        'std_notifications' => 0,
                        'std_sms' => 0,
                        'staff_sms' => 0,
                        'staff_notification' => 0
                );
                if(isset($input['student_ids']) && !empty($input['student_ids'])) {
                	$reciever_data = $CI->texting_model->getStudents($input['student_ids'], $send_to, $callFrom);
                	if(!empty($reciever_data)) {
                		$recievers_array[] = 'Students';
                                $response_count['total_students'] = count($reciever_data);
                	}
                }


                if(isset($input['class_section_ids']) && !empty($input['class_section_ids'])) {
                	$sectionStudents = $CI->texting_model->getStudentsBySections($input['class_section_ids'], $class_send_to, $acad_year_id, $batch_class, $callFrom);
                	if(!empty($sectionStudents)) {
                		$class_sections = $CI->texting_model->getClassSectionNames($input['class_section_ids']);
                		$recievers_array[] = implode(", ", $class_sections);
                		$reciever_data = array_merge($reciever_data, $sectionStudents);
                                $response_count['total_students'] += count($sectionStudents);
                	}
                }

                if(isset($input['staff_ids']) && !empty($input['staff_ids'])) {
                	$staffs = $CI->texting_model->getStaff($input['staff_ids']);
                	if(!empty($staffs)) {
                		$recievers_array[] = 'Staff';
                		$reciever_data = array_merge($reciever_data, $staffs);
                                $response_count['total_staff'] = count($staffs);
                	}
                }

                if(isset($input['custom_numbers']) && !empty($input['custom_numbers'])) {
                	$customs =  $CI->texting_model->getCustomNumbers($input['custom_numbers']);
                	if(!empty($customs)) {
                		$recievers_array[] = 'Custom Numbers';
                		$reciever_data = array_merge($reciever_data, $customs);
                                $response_count['total_custom'] = count($customs);
                	}
                }

                // echo "<pre>"; print_r($reciever_data); die();

                if(empty($recievers_array)) {
                	$response['error'] = '<li>Failed to get recievers</li>';
                	return $response;
                }
                $recievers = implode(" & ", $recievers_array);

                $text_count = count($reciever_data);
		$credits = $CI->texting_model->_calculateCredits($input['message'], $is_unicode);
		

		//texting_master data
		$masterData = array(
			'title' => $input['title'],
			'message' => $input['message'],
			'sent_by' => $sent_by,
			'reciever' => $recievers,
			'acad_year_id' => $acad_year_id,
			'source' => $input['source'],
			'text_count' => $text_count,
			'visible' => $visible,
			'mode' => $input['mode'],
			'sms_credits' => $credits,
			'is_unicode' => $is_unicode
		);

		$textData = $CI->texting_model->saveTexting($masterData, $reciever_data);

                $sent_count = $CI->texting_model->getTextSentCount($textData['master_id']);
                if(!empty($sent_count)) {
                        $response_count['std_notifications'] = ($sent_count->std_notifications)?$sent_count->std_notifications:0;
                        $response_count['staff_notifications'] = ($sent_count->staff_notifications)?$sent_count->staff_notifications:0;
                        $response_count['std_sms'] = ($sent_count->std_sms)?$sent_count->std_sms:0;
                        $response_count['staff_sms'] = ($sent_count->staff_sms)?$sent_count->staff_sms:0;
                        if($send_to == 'Both') {
                                $response_count['std_notifications'] = round($response_count['std_notifications']/2);
                                $response_count['std_sms'] = round($response_count['std_sms']/2);
                        }
                }

		$error = '';
		$success = '';
		$is_ntfn_exist = 0;
		$sms_number_exist = 0;

		if(!empty($textData['student_tokens'])) {
			$is_ntfn_exist = 1;
			$std_status = studentNotifications($textData['student_tokens'], $input['title'], $input['message'], $student_url);
			if($std_status == -2) {
                                $error .= '<li>Cannot send notification from localhost.</li>';
                        } else if($std_status == -3) {
                                $error .= '<li>Notification is in TEST mode please contact Admin.</li>';
                        } else if($std_status) {
				$success .= '<li>Successfully sent notifications to students.</li>';
			} else {
				$error .= '<li>Failed to send notifications to students<br></li>';
			}
		}
		if(!empty($textData['staff_tokens'])) {
			$is_ntfn_exist = 1;
			$staff_status = staffNotifications($textData['staff_tokens'], $input['title'], $input['message'], $staff_url);
			if($staff_status == -2) {
                                $error .= '<li>Cannot send notification from localhost.<br></li>';
                        } else if($staff_status == -3) {
                                $error .= '<li>Notification is in TEST mode please contact Admin.</li>';
                        } else if($staff_status) {
				$success .= '<li>Successfully sent notifications to staff.<br></li>';
			} else {
				$error .= '<li>Failed to send notifications to staff<br></li>';
			}
		}
                $sms_credits = '';
		if(!empty($textData['sms_data'])) {
			$sms_number_exist = 1;
			if($CI->texting_model->checkCredits($credits)) {
				$sms_status = sendTextSms($textData['sms_data'], $input['message'], $textData['master_id'], $is_unicode);
				if($sms_status == -2) {
					$error .= '<li>Cannot send sms from localhost.<br></li>';
				} else if($sms_status == -3) {
                                        $error .= '<li>Sms is in TEST mode please contact Admin.</li>';
                                } else if($sms_status) {
					$success .= '<li>Sms sent successfully.</li>';
				} else {
					$error .= '<li>Failed to send sms.</li>';
				}
			} else {
                                $error .= '<li>Sms not sent - Credits not available</li>';
				$sms_credits = '<li>Sms not sent - Credits not available</li>';
			}
		}

		if($input['mode'] == 'notification' && $is_ntfn_exist == 0) {
			$error .= 'Notification not sent - No Tokens.</li>';
		}

		if($input['mode'] == 'sms' && $sms_number_exist == 0) {
			$error .= 'Sms not sent - Numbers not found.</li>';
		}

		return array('success' => $success, 'error' => $error, 'sms_credits' => $sms_credits, 'count' => $response_count);
		// echo "<pre>"; print_r($textData); die();
	}
}


if (!function_exists('sendUniqueText')) {
        // Internal meaning Send SMS When Session is Available
        // External meaning Send SMS When Session is Not Available
        function sendUniqueText($input=[], $callFrom = 'Internal') {
                $CI =& get_instance();
                $CI->load->model('communication/texting_model');
                $CI->load->helper('notification_helper');
                $CI->load->helper('sms_v2_helper');
                $response = array();
                $response['success'] = '';
                $response['error'] = '';
                $response['warning'] = '';
                $warning = "";

                if(!isset($input['mode'])) {
                        $response['error'] = 'mode field is required.';
                        return $response;
                }

                if($input['mode'] == 'notification' || $input['mode'] == 'notification_sms') {
                        $click_action = $CI->settings->getSetting('push_notification_activity_name');
                        $push_notiification_key = $CI->settings->getSetting('push_notification_key');

                        if($click_action == '' ) {
                                $response['error'] = 'click_action for notification is not added.';
                                return $response;
                        }
                        if($push_notiification_key == '' ) {
                                $response['error'] = 'server key for notification is not added.';
                                return $response;
                        }
                }

                //optional fields
                $sent_by = 1;
                if($callFrom == 'Internal'){
                        $sent_by = $CI->authorization->getAvatarId();
                        if(!$sent_by) {
                                $sent_by = 1;
                        }
                } else {
                        $sent_by = 1;
                }
                $send_to = 'Father';

                $acad_year_id = 0;
                if($callFrom == 'Internal'){
                        $acad_year_id = $CI->acad_year->getAcadYearId();
                        if(!$acad_year_id) {
                                $acad_year_id = 0;
                        }
                } else {
                        $acad_year_id = $CI->settings->getSetting('academic_year_id');
                }
                $is_unicode = 0;
                $visible = 1;
                $student_url = site_url('parent_controller/texts');
                $staff_url = site_url('communication/texting/staff_texts');

                //required fields

                if(!isset($input['title'])) {
                        $input['title'] = $CI->settings->getSetting('school_name');
                }

                if(!isset($input['source'])) {
                        $response['error'] = 'source field is required.';
                        return $response;
                }

                if(isset($input['send_to'])) {
                        $send_to = $input['send_to'];
                }

                if(isset($input['acad_year_id'])) {
                        $acad_year_id = $input['acad_year_id'];
                }

                if(isset($input['is_unicode'])) {
                        $is_unicode = $input['is_unicode'];
                }

                if(isset($input['visible'])) {
                        $visible = $input['visible'];
                }

                if(isset($input['student_url'])) {
                        $student_url = $input['student_url'];
                }

                if(isset($input['staff_url'])) {
                        $staff_url = $input['staff_url'];
                }

                if(!isset($input['student_id_messages']) && !isset($input['staff_id_messages'])) {
                        $response['error'] =  'Recipient ids are required.';
                        return $response;
                }

                $recievers_array = array();
                $msg_data = array();
                $id_messages = array();
                if(isset($input['student_id_messages'])) {
                        $std_ids = array_keys($input['student_id_messages']);
                        $std_data = $CI->texting_model->getStudents($std_ids, $send_to, $callFrom);
                        if(!empty($std_data)) {
                                $recievers_array[] = 'Students';
                                foreach ($std_data as $key => $std) {
                                        $msg_data[$std->id.'_'.$std->avatar_type] = $std;
                                        $id_messages[$std->id.'_'.$std->avatar_type] = $input['student_id_messages'][$std->std_id];
                                }
                        }
                }

                if(isset($input['staff_id_messages'])) {
                        $staff_ids = array_keys($input['staff_id_messages']);
                        $staffs = $CI->texting_model->getStaff($staff_ids);
                        if(!empty($staffs)) {
                                $recievers_array[] = 'Staff';
                                foreach ($staffs as $key => $stf) {
                                        $msg_data[$stf->id.'_'.$stf->avatar_type] = $stf;
                                        $id_messages[$stf->id.'_'.$stf->avatar_type] = $input['staff_id_messages'][$stf->id];
                                }
                        }
                }

                // echo "<pre>"; print_r($msg_data); 
                // echo "<pre>"; print_r($id_messages); 

                if(empty($recievers_array)) {
                        $response['error'] = 'Failed to get recievers';
                        return $response;
                }
                $recievers = implode(" & ", $recievers_array);

                // $text_count = count($reciever_data);
                // $credits = ($CI->texting_model->_calculateCredits($input['message'], $is_unicode));
                

                //texting_master data
                $masterData = array(
                        'title' => $input['title'],
                        'sent_by' => $sent_by,
                        'reciever' => $recievers,
                        'acad_year_id' => $acad_year_id,
                        'source' => $input['source'],
                        'visible' => $visible,
                        'mode' => $input['mode'],
                        'is_unicode' => $is_unicode
                );

                $textData = $CI->texting_model->saveUniqueTexting($masterData, $msg_data, $id_messages);

                // echo "<pre>"; print_r($textData);die();

                $error = '';
                $success = '';
                $is_ntfn_exist = 0;
                $sms_number_exist = 0;
                if(!empty($textData['student_tokens'])) {
                        $is_ntfn_exist = 1;
                        $std_status = uniqueStudentNotifications($textData['student_tokens'], $input['title'], $student_url);
                        if($std_status == -2) {
                                $error .= 'Cannot send notification from localhost.<br>';
                        } else if($std_status) {
                                $success .= 'Successfully sent notification to students.<br>';
                        } else {
                                $error .= 'Failed to send notification to students<br>';
                        }
                }
                if(!empty($textData['staff_tokens'])) {
                        $is_ntfn_exist = 1;
                        $staff_status = uniqueStaffNotifications($textData['staff_tokens'], $input['title'], $staff_url);
                        if($staff_status == -2) {
                                $error .= 'Cannot send notification from localhost.<br>';
                        } else if($staff_status) {
                                $success .= 'Successfully sent notification to staff.<br>';
                        } else {
                                $error .= 'Failed to send notification to staff<br>';
                        }
                }
                if(!empty($textData['sms_data'])) {
                        $sms_number_exist = 1;
                        $sms_status = sendUniqueTextSms($textData['sms_data'], $textData['master_ids'], $is_unicode);
                        if($sms_status == -2) {
                                $error .= 'Cannot send sms from localhost.<br>';
                        } else if($sms_status == -1) {
                                $success .= 'Sms not sent - Credits not available<br>';
                        } else if($sms_status) {
                                $success .= 'Sms sent successfully.';
                        } else {
                                $error .= 'Failed to send sms.<br>';
                        }
                }

                if($input['mode'] == 'notification' && $is_ntfn_exist == 0) {
                        $warning = 'Notification not sent - No Tokens.';
                }

                if($input['mode'] == 'sms' && $sms_number_exist == 0) {
                        $error .= 'Sms not sent - Numbers not found.';
                }

                return array('success' => $success, 'error' => $error, 'warning' => $warning);
                // echo "<pre>"; print_r($textData); die();
        }
}

if (!function_exists('checkCredits')) {
        //message
        //sending_count - number of students/staff
        //type - student/staff/parent
        //sending_to - Incase of student only - both/''
        //is_unicode - if the message is a unicode message then 1 else 0
        function checkCredits($message, $sending_count, $type='student', $sending_to='both', $is_unicode=0) {
                $CI =& get_instance();
                $CI->load->model('communication/texting_model');

                //gives the number of credits this message will use
                $msg_credits = $CI->texting_model->_calculateCredits($message, $is_unicode);

                $members = $sending_count;
                if($type === 'student' && $sending_to === 'both') {
                        $members = 2 * $sending_count;
                        //double the members if we need to send sms for both the parent
                }

                //required credits - single message credits multiplied by total number of messages to send
                $required_credits = $msg_credits * $members;

                //get available credits for the school
                $available_credits = $CI->texting_model->getRemainingSMSCredits();

                if($available_credits >= $required_credits) {
                        return 1;//credits available
                }
                return 0;//not available
        }
}

if(!function_exists('sendTextMessageByData')) {
        //sending_info - Complete data with staff,parent
        //texting_master_id - master id 
        function sendTextMessageByData($input = []) {
                $CI =& get_instance();
                $response = array(
                        'success' => '',
                        'error' => '',
                );
                if(empty($input)) {
                        $response['error'] .= '<li>No Data</li>';
                        return $response;
                }
                $sending_info = $input['sending_info'];
                $texting_master_id = $input['texting_master_id'];
                $type = $input['type'];

                if(empty($sending_info)) {
                        $response['error'] .= '<li>No Data</li>';
                        return $response;
                }

                $is_unicode = 0;
                if(isset($input['is_unicode'])) {
                        $is_unicode = $input['is_unicode'];
                }

                $CI->load->model('communication/texting_model');
                $master_data = $CI->texting_model->getTextingMaster($texting_master_id);

                if(empty($master_data)) {
                        $response['error'] .= '<li>No Data</li>';
                        return $response;
                }

                $CI->texting_model->updateTextCount($texting_master_id, count($sending_info));

                $url = site_url('dashboard');
                if($type == 'staff' && isset($inpu['staff_url'])) {
                        $url = $inpu['staff_url'];
                }

                if($type == 'student' && isset($inpu['student_url'])) {
                        $url = $inpu['student_url'];
                }                

                $message = $master_data->message;
                $title = $master_data->title;
                $master_mode = $master_data->mode;
                $mode = 1;
                if($master_mode == 'sms') {
                        $mode = 2;
                }
                $sms_save = [];
                $sms_send = [];
                $notification_save = [];
                $notification_send = [];
                if($master_mode === 'notification') {
                        foreach ($sending_info as $key => $val) {
                                $subData = array(
                                        'texting_master_id' => $texting_master_id,
                                        'stakeholder_id' => $val->id,
                                        'mobile_no' => $val->mobile_no,
                                        'mode' => 1,
                                        'status' => 'Sent',
                                        'avatar_type' => $val->avatar_type,
                                        'is_read' => 0,
                                        'user_id' => $val->user_id,
                                        'token' => $val->token
                                );
                                if($val->tokenState == 0) {
                                  $subData['status'] = 'No Token';
                                } else {
                                        $notification_send[] = $subData;
                                }
                                $notification_save[] = $subData;
                        }
                } else if ($master_mode === 'sms') {
                        foreach ($sending_info as $key => $val) {
                                $subData = array(
                                        'texting_master_id' => $texting_master_id,
                                        'stakeholder_id' => $val->id,
                                        'mobile_no' => $val->mobile_no,
                                        'mode' => 2,
                                        'status' => 'AWAITED-DLR',
                                        'avatar_type' => $val->avatar_type,
                                        'is_read' => 0
                                );

                                if($val->mobile_no == '' || $val->mobile_no == NULL) {
                                        $subData['status'] = 'NO-NUMBER';
                                } else {
                                        $sms_send[] = $subData;
                                }
                                $sms_save[] = $subData;
                        }
                } else {
                        foreach ($sending_info as $key => $val) {
                                $subData = array(
                                        'texting_master_id' => $texting_master_id,
                                        'stakeholder_id' => $val->id,
                                        'mobile_no' => $val->mobile_no,
                                        'mode' => 1,
                                        'status' => 'Sent',
                                        'avatar_type' => $val->avatar_type,
                                        'is_read' => 0
                                );

                                if($val->tokenState == 0) {
                                        $subData['status'] = 'AWAITED-DLR';
                                        $subData['mode'] = 2;
                                        if($val->mobile_no == '' || $val->mobile_no == NULL) {
                                                $subData['status'] = 'NO-NUMBER';
                                        } else {
                                                $sms_send[] = $subData;
                                        }
                                        $sms_save[] = $subData;
                                } else {
                                        $subData['user_id'] = $val->user_id;
                                        $subData['token'] = $val->token;
                                        $notification_send[] = $subData;
                                        $notification_save[] = $subData;
                                }
                        }
                }

                if(!empty($notification_save)) {
                        $token_data = $CI->texting_model->save_notifications($notification_save);
                        if(!empty($notification_send)) {
                                $CI->load->helper('notification_helper');
                                $notification_status = commonNotifications($token_data, $message, $title, $url);
                                if($notification_status == -2) {
                                        $response['error'] .= '<li>Cannot send notifications from localhost.<br></li>';
                                } else if($notification_status == -3) {
                                        $response['error'] .= '<li>Notification is in TEST mode please contact Admin.</li>';
                                } else if($notification_status) {
                                        $response['success'] .= '<li>Successfully sent notifications.<br></li>';
                                } else {
                                        $response['error'] .= '<li>Failed to send notifications<br></li>';
                                }
                        }
                }

                if(!empty($sms_save)) {
                        $sms_data = $CI->texting_model->save_sms($sms_save);
                        if(!empty($sms_send)) {
                                $credits = count($sms_send) * $master_data->sms_credits;
                                $CI->load->helper('sms_v2_helper');
                                $sms_status = sendTextSms($sms_data, $message, $texting_master_id, $is_unicode);
                                if($sms_status == -2) {
                                        $response['error'] .= '<li>Cannot send notification from localhost.<br></li>';
                                } else if($sms_status == -3) {
                                        $response['error'] .= '<li>SMS is in TEST mode please contact Admin.</li>';
                                } else if($sms_status) {
                                        // $texting_credits = array(
                                        //         'sms_credits' => $credits,
                                        //         'action' => 'Unload',
                                        //         'action_by' => $master_data->sent_by,
                                        //         'texting_master_id' => $texting_master_id
                                        // );
                                        // $CI->texting_model->saveTextingCredtits($texting_credits);
                                        $response['success'] .= '<li>Successfully sent notifications SMS.<br></li>';
                                } else {
                                        $response['error'] .= '<li>Failed to send SMS<br></li>';
                                }
                        }
                }

                return $response;
        }
}

if(!function_exists('sendTextMessageById')) {
        //stakeholder_ids - staff,student ids, custom numbers
        //texting_master_id - master id 
        function sendTextMessageById($input = []) {
                $CI =& get_instance();
                $response = array(
                        'success' => '',
                        'error' => ''
                );
                if(empty($input)) {
                        $response['error'] .= '<li>No Data</li>';
                        return $response;
                }
                $CI->load->model('communication/texting_model');
                $master_id = $input['texting_master_id'];
                $stakeholder_ids = $input['stakeholder_ids'];
                $type = $input['type'];
                $send_to = $input['send_to'];

                $sent_data = array();
                if($type === 'student') {
                        $sent_data = $CI->texting_model->getStudents($stakeholder_ids, $send_to);
                } else if($type === 'staff') {
                        $sent_data = $CI->texting_model->getStaff($stakeholder_ids);
                } else if($type === 'custom_number') {
                        $sent_data =  $CI->texting_model->getCustomNumbers($stakeholder_ids);
                }

                $input['sending_info'] = $sent_data;
                return sendTextMessageByData($input);
        }
}


if(!function_exists('forgotUserOTP')) {
        //stakeholder_ids - staff,student ids, custom numbers
        //texting_master_id - master id 
        function forgotUserOTP($input = []) {
                $CI =& get_instance();
                $response = array(
                        'success' => '',
                        'error' => ''
                );
                if(empty($input)) {
                        $response['error'] = false;
                        return $response;
                }
                $CI->load->model('communication/texting_model');

                $is_unicode = 0;
                
                $credits = ($CI->texting_model->_calculateCredits($input['message'], $is_unicode));
                
                //forgot otp data
                $masterData = array(
                        'recovery_type' => 'Mobile Number',
                        'user_name' => $input['mobile_number'],
                        'otp' => $input['otp']
                );
                $textData = $CI->texting_model->save_forgt_user_otop($masterData);

                // Not checking for credits as it is reset password flow
                
                // if($CI->texting_model->checkCredits($credits)) {
                        $CI->load->helper('sms_v2_helper');
                        $sms_status = sendForgotOTP($input['mobile_number'], $input['message']);
                        // trigger_error('Forgot SMS Output');
                        // trigger_error(json_encode($sms_status));
                        if ($sms_status == '-2') {
                                return -2;
                        }
                        if (isset($sms_status->status) && $sms_status->status == 'OK') {
                                return true;
                        }else{
                                return false;
                        }
                // } else {
                //         return -1;
                // }
        }
}