<?php
defined('BASEPATH') OR exit('No direct script access allowed');
            
class Circular_model extends CI_Model {

    public function __construct()
  	{
       	 // Call the CI_Model constructor
	 	parent::__construct();
  	}

    public function getClassSectionNames($section_ids) {
      $sections = $this->db_readonly->select("CONCAT(class_name,section_name) as name")->where_in('id', $section_ids)->get('class_section')->result();
      $arr = array();
      foreach ($sections as $key => $val) {
        $arr[] = $val->name;
      }
      return $arr;
    }

    public function getCircularTitles($acad_year_id) {
      return $this->db_readonly->query("select id, title, category,sent_on from circularv2_master where acad_year_id=$acad_year_id order by id desc")->result();
    }

  	public function getPreviewData($input) {
  		$type = $input['type'];
  		$members = array();
  		if($type == 'Student') {
  			$sendTo = $input['sendTo'];
  			$members = $this->_getStudents($input['members'], $sendTo);
  		} else if($type == 'Class') {
  			$sendTo = $input['sendTo'];
  			$members = $this->getStudentsBySections($input['members'], $sendTo);
  		} else if ($type == 'Staff_all') {
  			$members = $this->_getAllStaff();
  		} else if ($type == 'Staff') {
  			$members = $this->_getStaff($input['members']);
  		} else if ($type == 'custom') {

  		}
  	}

    public function getCircularCategories() {
      $result = $this->db_readonly->select("*")
                                ->from('circularv2_categories')
                                ->get()->result();

      foreach ($result as &$category) {
          if (!empty($category->require_approval_by)) {
              $staff_ids = json_decode($category->require_approval_by, true);

              if (is_array($staff_ids)) {
                  $staff_names = $this->getStaffNames($staff_ids);
                  $category->staff_name = $staff_names;
              } else {
                  $category->staff_name = 'N/A';
              }
          } else {
              $category->staff_name = 'N/A';
          }
      }

      // echo "<pre>";print_r($result);die();
      return $result;
    }

    private function getStaffNames($staff_ids) {
        if (empty($staff_ids)) {
            return 'N/A';
        }

        $ids_str = implode(',', array_map('intval', $staff_ids));

        $query = $this->db_readonly->query("
            SELECT GROUP_CONCAT(CONCAT_WS(' ', first_name, last_name) SEPARATOR ', ') AS staff_names
            FROM staff_master
            WHERE id IN ($ids_str)
        ");

        $result = $query->row_array();

        return $result['staff_names'] ?? 'N/A';
    }

    public function add_category($input) {
      if (isset($input['require_approval_by']) && is_array($input['require_approval_by'])) {
          $filtered_require_approval_by = array_filter($input['require_approval_by'], function($value) {
              return $value !== '';
          });
          $indexed_require_approval_by = array_values($filtered_require_approval_by);
          $require_approval_by = !empty($indexed_require_approval_by) ? json_encode($indexed_require_approval_by) : null;
      } else {
          $require_approval_by = null;
      }

      $data = array(
          'category_name' => $input['category_name'],
          'require_approval' => isset($input['require_approval']) ? 1 : 0,
          'require_approval_by' => $require_approval_by,
      );

      if($input['category_id']) {
        return $this->db->where('id', $input['category_id'])->update('circularv2_categories', $data);
      }
      return $this->db->insert('circularv2_categories', $data);
    }

    public function get_staff_user_details($staff_id_str){
      $staff_ids = explode(',', $staff_id_str);
      $staff_details = $this->db->select("sm.id as staff_id, sm.contact_number as staff_mobile_no, u.id as user_id, u.token as user_token, IF(u.token IS NULL, 0, 1) as tokenState")
      ->from('staff_master sm')
      ->join('avatar a', 'a.stakeholder_id = sm.id', 'left')
      ->join('users u', 'u.id = a.user_id')
      ->where_in('sm.id', $staff_ids)
      ->group_by('sm.id')
      ->get()
      ->result();
      return $staff_details;
    }

    public function delete_category($category_id) {
      return $this->db->where('id', $category_id)->delete('circularv2_categories');
    }

    public function add_category_from_config($categories) {
      $data = [];
      foreach ($categories as $key => $category) {
        $data[] = array(
          'category_name' => $category
        );
      }

      return $this->db->insert_batch('circularv2_categories', $data);
    }

  	public function getStudents($members, $sendTo) {
  		$this->db->select("p.id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(sa.first_name,''),' ',ifnull(sa.last_name,''), ' (',sr.relation_type,')') as name, p.email, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as uname");
  		$this->db->from('student_admission sa');
      $this->db->join('student_year sy','sy.student_admission_id=sa.id');
      $this->db->where('admission_status','2'); // Approved 2
      $this->db->where('sy.promotion_status!=', '4'); 
      $this->db->where('sy.promotion_status!=', '5'); 
      $this->db->join('class_section cs','sy.class_section_id=cs.id');
  		$this->db->join('parent p', 'p.student_id=sa.id');
  		$this->db->join('student_relation sr', 'sr.relation_id=p.id');
  		if($sendTo != 'Both') {
  			$this->db->where('sr.relation_type', $sendTo);
  		}
  		$this->db->where_in('sa.id', $members);
  		return $this->db->get()->result();
  	}

  	public function getStudentsBySections($members, $sendTo, $batch='all') {
  		$this->db->select("p.id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(sa.first_name,''),' ',ifnull(sa.last_name,''), ' (',sr.relation_type,')') as name, p.email, CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as uname");
  		$this->db->from('student_admission sa');
  		$this->db->join('student_year sy','sy.student_admission_id=sa.id');
      $this->db->where('admission_status','2'); // Approved 2
      $this->db->where('sy.promotion_status!=', '4'); 
      $this->db->where('sy.promotion_status!=', '5'); 
  		$this->db->join('class_section cs','sy.class_section_id=cs.id');
  		$this->db->join('parent p', 'p.student_id=sa.id');
  		$this->db->join('student_relation sr', 'sr.relation_id=p.id');
      if($batch != 'all') {
        $this->db->where('sa.admission_acad_year_id',$batch);
      } 
  		if($sendTo != 'Both') {
  			$this->db->where('sr.relation_type', $sendTo);
  		}
  		$this->db->where_in('sy.class_section_id', $members);
  		return $this->db->get()->result();
  	}

  	public function getAllStaff() {
  		$this->db->select("sm.id, a.avatar_type, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, u.email, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as uname");
  		$this->db->from('staff_master sm');
  		$this->db->join('avatar a', 'a.stakeholder_id=sm.id');
  		$this->db->join('users u', 'u.id=a.user_id');
      $this->db->where('a.avatar_type', 4);
  		$this->db->where('sm.status', 2);
  		$this->db->order_by('sm.first_name');
  		return $this->db->get()->result();
  	}

  	public function getStaff($members) {
  		$this->db->select("sm.id, a.avatar_type, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as name, u.email, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as uname");
  		$this->db->from('staff_master sm');
  		$this->db->join('avatar a', 'a.stakeholder_id=sm.id');
      $this->db->join('users u', 'u.id=a.user_id');
  		$this->db->where('a.avatar_type', 4);
  		$this->db->where_in('sm.id', $members);
  		return $this->db->get()->result();
  	}

    public function insertCircular($master_data, $sendToArr) {
      $this->db->trans_start();

      $this->db->insert('circularv2_master', $master_data);
      $master_id = $this->db->insert_id();

      $data = array();
      foreach ($sendToArr as $key => $val) {
        $data[] = array(
          'circularv2_master_id' => $master_id,
          'stakeholder_id' => $val->id,
          'email' => $val->email,
          'avatar_type' => $val->avatar_type
        );
      }

      $this->db->insert_batch('circularv2_sent_to', $data);

      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        return 0;
      }
      $this->db->trans_commit();
      return $master_id;
    }

    private function _getRecievedStudents($circularId) {
      $result = $this->db->select("CONCAT(cs.class_name, cs.section_name, '-' ,ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, ' ')) as stdName")
      ->from('student_admission sa')
      ->join('parent p', 'p.student_id=sa.id')
      ->join('circularv2_sent_to ct', 'ct.stakeholder_id=p.id')
      ->join('student_year sy', 'sy.student_admission_id=sa.id')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->where('ct.circularv2_master_id', $circularId)
      ->where('admission_status','2') // Approved 2
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
      ->group_by('sa.id')
      ->get()->result();
      $students = array();
      foreach ($result as $key => $val) {
        array_push($students, $val->stdName);
      }
      return implode(", ", $students);
      // echo '<pre>'; print_r($students); die();
    }

    private function _getRecievedStaff($circularId) {
      $result = $this->db->select("CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ' ')) as staffName")
      ->from('staff_master sm')
      ->join('circularv2_sent_to ct', 'ct.stakeholder_id=sm.id')
      ->where('ct.circularv2_master_id', $circularId)
      ->get()->result();
      $staff = array();
      foreach ($result as $key => $val) {
        array_push($staff, $val->staffName);
      }
      return implode(", ", $staff);
      // echo '<pre>'; print_r($students); die();
    }

    public function getMobileCircularList($avatarId, $fromDate, $toDate, $displayType) {
      $acad_year = $this->acad_year->getAcadYearId();
      $sql = "select ifnull(cm.circular_number,'NA') as circular_number, cm.id, cm.sent_by, DATE_FORMAT(cm.sent_on, '%d-%b %I:%i %p') as sentOn, cm.title, cm.recievers, cm.category, cm.visible, cm.file_path, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as senderName, cm.mode, cm.is_approved 
        from circularv2_master cm 
        join avatar a on cm.sent_by=a.id 
        join staff_master sm on sm.id=a.stakeholder_id and a.avatar_type=4 
        where date_format(sent_on, '%Y-%m-%d')>='$fromDate' 
        and date_format(sent_on, '%Y-%m-%d')<='$toDate'
        and cm.acad_year_id=$acad_year ";
      if($displayType == 'mine') {
        $sql .= "and cm.sent_by=$avatarId and a.avatar_type=4 ";
      }
      $sql .= "order by cm.id desc";
      $result = $this->db_readonly->query($sql)->result();
      // foreach ($result as $key => $res) {
      //   $result[$key]->sentOn = date("d-M H:i a", strtotime($res->sentOn));
      // }
      foreach($result as $res){
        $require_approval_by = $this->get_require_approval_by($res->category);
        $res->require_approval_by = $require_approval_by;
      }
      return $result;
    }

    public function getCircularList($avatarId, $fromDate, $toDate, $displayType){
      // echo $displayType; die();
      $acad_year = $this->acad_year->getAcadYearId();
      $sql = "select ifnull(cm.circular_number,'NA') as circular_number, cm.id, cm.sent_by, DATE_FORMAT(cm.sent_on, '%d-%b %I:%i %p') as sentOn, cm.title, cm.recievers, cm.category as category, cm.visible, cm.file_path, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as senderName, cm.mode, cm.is_approved, cm.approved_reject_comments as comments, ifnull(cm.email_master_id, 0) as email_master_id
        from circularv2_master cm 
        join avatar a on cm.sent_by=a.id 
        join staff_master sm on sm.id=a.stakeholder_id and a.avatar_type=4 
        where date_format(sent_on, '%Y-%m-%d')>='$fromDate' 
        and date_format(sent_on, '%Y-%m-%d')<='$toDate'
        and cm.acad_year_id=$acad_year ";
      if($displayType == 'mine') {
        $sql .= "and cm.sent_by=$avatarId and a.avatar_type=4 ";
      }
      $sql .= "order by cm.id desc";
      $result = $this->db_readonly->query($sql)->result();

      $parent_circular_ids = [];
      $staff_circular_ids = [];
      foreach ($result as $key => $res) {
        if($res->recievers == 'Students') {
          $parent_circular_ids[] = $res->id;
        } else if($res->recievers == 'Staffs') {
          $staff_circular_ids[] = $res->id;
        }
      }

      $students = [];
      $staffs = [];
      if(!empty($parent_circular_ids)) {
        $p_cids = implode(",", $parent_circular_ids);
        $std_sql = "select CONCAT(cs.class_name, cs.section_name, '-' ,ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, ' ')) as stdName, ct.circularv2_master_id as cm_id 
            from student_admission sa 
            join circularv2_sent_to ct on ct.stakeholder_id=sa.id 
            join student_year sy on sy.student_admission_id=sa.id 
            join class_section cs on cs.id=sy.class_section_id 
            where ct.circularv2_master_id in ($p_cids) 
            and sy.acad_year_id=$acad_year 
            and ct.avatar_type=1 
            group by ct.circularv2_master_id 
            having count(ct.circularv2_master_id)<=1";
        $stds = $this->db_readonly->query($std_sql)->result();
        $students = array();
        foreach ($stds as $key => $std) {
          $students[$std->cm_id] = $std->stdName;
        }

        $std_sql = "select CONCAT(cs.class_name, cs.section_name, '-' ,ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, ' ')) as stdName, ct.circularv2_master_id as cm_id 
        from student_admission sa 
        join parent p on p.student_id=sa.id 
        join circularv2_sent_to ct on ct.stakeholder_id=p.id 
        join student_year sy on sy.student_admission_id=sa.id 
        join class_section cs on cs.id=sy.class_section_id 
        where ct.circularv2_master_id in ($p_cids) 
        and sy.acad_year_id=$acad_year 
        and ct.avatar_type=2 
        group by ct.circularv2_master_id 
        having count(ct.circularv2_master_id)<=2";
        $stds = $this->db_readonly->query($std_sql)->result();
        foreach ($stds as $key => $std) {
          $students[$std->cm_id] = $std->stdName;
        }
      }

      if(!empty($staff_circular_ids)) {
        $s_cids = implode(",", $staff_circular_ids);
        $staff_sql = "select CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ' ')) as staffName, ct.circularv2_master_id as cm_id 
          from staff_master sm 
          join circularv2_sent_to ct on ct.stakeholder_id=sm.id 
          where ct.circularv2_master_id in ($s_cids) 
          group by ct.circularv2_master_id 
          having count(ct.circularv2_master_id)=1";
          $stfs = $this->db_readonly->query($staff_sql)->result();
          $staffs = array();
          foreach ($stfs as $key => $stf) {
            $staffs[$stf->cm_id] = $stf->staffName;
          }
      }

      foreach ($result as $key => $res) {
        // $result[$key]->sentOn = date("d-M h:i a", strtotime($res->sentOn));
        if(array_key_exists($res->id, $students)) {
          $result[$key]->recievers = $students[$res->id];
        } else if(array_key_exists($res->id, $staffs)) {
          $result[$key]->recievers = $staffs[$res->id];
        }
      }
      foreach($result as $res){
        $require_approval_by = $this->get_require_approval_by($res->category);
        $res->require_approval_by = $require_approval_by;
      }
      // echo "<pre>";print_r($result);die();
      return $result;
    }

    public function get_require_approval_by($category){
      $require_approval_by = $this->db_readonly->select('require_approval_by')
                                              ->from('circularv2_categories')
                                              ->where('category_name', $category)
                                              ->get()->row();

      if (empty($require_approval_by) || $require_approval_by == null) {
          return [];
      }

      return json_decode($require_approval_by->require_approval_by, true) ?: [];
    } 

    public function getSingleCircularData($circular_id) {
      $acad_year = $this->acad_year->getAcadYearId();
      $sql = "select ifnull(cm.circular_number,'NA') as circular_number, cm.id, cm.sent_by, DATE_FORMAT(cm.sent_on, '%d-%b %h:%i %p') AS sentOn, cm.title, cm.recievers, cm.category, cm.visible, cm.file_path, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as senderName, cm.mode, cm.is_approved 
        from circularv2_master cm 
        join avatar a on cm.sent_by=a.id 
        join staff_master sm on sm.id=a.stakeholder_id and a.avatar_type=4 
        where cm.id=$circular_id";
      $result = $this->db_readonly->query($sql)->row();

      $parent_circular_id = 0;
      $staff_circular_id = 0;
      if($result->recievers == 'Students') {
        $parent_circular_id = $result->id;
      } else if($result->recievers == 'Staffs') {
        $staff_circular_id = $result->id;
      }

      $students = [];
      $staffs = [];
      if($parent_circular_id) {
        $std_sql = "select CONCAT(cs.class_name, cs.section_name, '-' ,ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, ' ')) as stdName, ct.circularv2_master_id as cm_id 
          from student_admission sa 
          join parent p on p.student_id=sa.id 
          join circularv2_sent_to ct on ct.stakeholder_id=p.id 
          join student_year sy on sy.student_admission_id=sa.id 
          join class_section cs on cs.id=sy.class_section_id 
          where ct.circularv2_master_id=$parent_circular_id 
          and sy.acad_year_id=$acad_year 
          group by ct.circularv2_master_id 
          having count(ct.circularv2_master_id)<=2";
          $stds = $this->db_readonly->query($std_sql)->result();
          $students = array();
          foreach ($stds as $key => $std) {
            $students[$std->cm_id] = $std->stdName;
          }
      }

      if($staff_circular_id) {
        $staff_sql = "select CONCAT(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ' ')) as staffName, ct.circularv2_master_id as cm_id 
          from staff_master sm 
          join circularv2_sent_to ct on ct.stakeholder_id=sm.id 
          where ct.circularv2_master_id=$staff_circular_id 
          group by ct.circularv2_master_id 
          having count(ct.circularv2_master_id)=1";
          $stfs = $this->db_readonly->query($staff_sql)->result();
          $staffs = array();
          foreach ($stfs as $key => $stf) {
            $staffs[$stf->cm_id] = $stf->staffName;
          }
      }

      if(array_key_exists($result->id, $students)) {
        $result->recievers = $students[$result->id];
      } else if(array_key_exists($result->id, $staffs)) {
        $result->recievers = $staffs[$result->id];
      }

      return $result;
    }

    public function getCircularData($circular_id) {
      $data = $this->db->select("cm.id, cm.sent_by, cm.approved_reject_comments, DATE_FORMAT(cm.sent_on, '%d-%b %I:%i %p') as sentOn, cm.title, cm.body, cm.recievers, cm.category, cm.visible, cm.file_path, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as senderName, cm.mode, cm.acad_year_id, cm.is_file_path_json")
        ->from('circularv2_master cm')
        ->join('avatar a', 'cm.sent_by=a.id', 'left')
        ->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left')
        ->where('cm.id', $circular_id)
        ->get()->row();
      // $data->sentOn = date("d-M h:i a", strtotime($data->sentOn));
      $data->paths = array();
      if($data->file_path != '') {
        if($data->is_file_path_json) {
          $paths = json_decode($data->file_path);
          foreach ($paths as $path) {
            array_push($data->paths, array('name' => $path->name, 'path' => $this->filemanager->getFilePath($path->path)));
          }
        } else {
          $paths = explode(",", $data->file_path);
          $i=1;
          foreach ($paths as $path) {
            array_push($data->paths, array('name' => $i++, 'path' => $this->filemanager->getFilePath($path)));
          }
        }
      }
      // $data->file_path = ($data->file_path == '')?'':$this->filemanager->getFilePath($data->file_path);
      return $data;
    }

    public function getCircularById($circular_id) {
      return $this->db_readonly->query("select cm.id, cm.title, cm.body, cm.category, cm.mode, require_approval from circularv2_master cm join circularv2_categories cc on cc.category_name=cm.category where cm.id=$circular_id")->row();
    }

    public function getCircularIds($circular_master_id) {
      return $this->db_readonly->select("id, email_master_id, texting_master_id, is_approved, mode")->where('id', $circular_master_id)->get('circularv2_master')->row();
    }

    public function updateCircular($circular_id,$circular_body,$title_edit_class) {
      return $this->db->where('id', $circular_id)->update('circularv2_master',array('body' => $circular_body,'is_approved'=> '4','title'=>$title_edit_class));
    }

    public function getCircularRecievers($circular_id, $acad_year_id) {
      $staff =  $this->db->select(" cm.email_master_id, cs.id, concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as name, cs.email, sm.id as rec_id, cs.is_read")
        ->from('circularv2_sent_to cs')
        ->join("circularv2_master cm", "cm.id=cs.circularv2_master_id")
        //->join("email_sent_to est","est.email_master_id=cm.email_master_id","left")
        ->join('staff_master sm', 'cs.stakeholder_id=sm.id')
        ->where('cs.circularv2_master_id', $circular_id)
        ->where('cs.avatar_type', '4')
        ->group_by("cs.id")
        ->get()->result();
        
      $parents =  $this->db->select("cm.email_master_id, cs.id, concat(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) as stdName, concat(ifnull(p.first_name,''), ' ', ifnull(p.last_name,''), '(', sr.relation_type,')') as parentName, sr.relation_type, cs.email, concat(c.class_name, c.section_name) as csName, cs.is_read, sa.id as rec_id, p.id as parent_id")
        ->from('circularv2_sent_to cs')
        ->join("circularv2_master cm","cm.id=cs.circularv2_master_id")
        //->join("email_sent_to est", "est.email_master_id=cm.email_master_id", "left")
        ->join('parent p', 'cs.stakeholder_id=p.id')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->join('student_admission sa', 'sa.id=p.student_id')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->join('class_section c', 'c.id=sy.class_section_id')
        ->where('cs.circularv2_master_id', $circular_id)
        ->where('sy.acad_year_id', $acad_year_id)
        ->where('cs.avatar_type', '2')
        ->group_by("cs.id")
      // ->where('admission_status','2') // Approved 2
        // ->where('sy.promotion_status!=', '4')
        // ->where('sy.promotion_status!=', '5')
        ->order_by('c.class_id, c.section_name, sa.first_name')
        ->get()->result();

      $students =  $this->db->select("cm.email_master_id, cs.id, concat(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) as stdName, 'Student' as relation_type, cs.email, concat(c.class_name, c.section_name) as csName, cs.is_read, sa.id as rec_id")
        ->from('circularv2_sent_to cs')
        ->join("circularv2_master cm", "cm.id=cs.circularv2_master_id")
        //->join("email_sent_to est", "est.email_master_id=cm.email_master_id", "left")
        ->join('student_admission sa', 'sa.id=cs.stakeholder_id')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->join('class_section c', 'c.id=sy.class_section_id')
        ->where('cs.circularv2_master_id', $circular_id)
        ->where('sy.acad_year_id', $acad_year_id)
        ->where('cs.avatar_type', '1')
        ->group_by("cs.id")
        // ->where('admission_status','2') // Approved 2
        // ->where('sy.promotion_status!=', '4')
        // ->where('sy.promotion_status!=', '5')
        ->order_by('c.class_id, c.section_name, sa.first_name')
        ->get()->result();

      // $students = array_merge($students, $parents);
      $emailMasterIds = [];
      if(!empty($staff)){
        foreach ($staff as $key => $val) {
          if($val->email_master_id){
            array_push($emailMasterIds, $val->email_master_id);
          }
        }
      }
      
      if(!empty($students)){
        foreach ($students as $key => $val) {
          if($val->email_master_id){
            array_push($emailMasterIds, $val->email_master_id);
          }
        }
      }

      if (!empty($parents)) {
        foreach ($parents as $key => $val) {
          if ($val->email_master_id) {
            array_push($emailMasterIds, $val->email_master_id);
          }
        }
      }

      $emailSentTO=[];
      if(!empty($emailMasterIds)){
        $emailSentTO = $this->db->select("est.stakeholder_id, est.avatar_type, est.status as email_read_status, est.email_master_id")
        ->from('email_sent_to est')
        ->where_in('est.email_master_id',$emailMasterIds)
        ->get()->result();
      }


      // $emailMasterArry = [];
      // foreach ($emailSentTO as $key => $val) {
        //   $emailMasterArry[$val->email_master_id] = $val->email_read_status;
        // }
        
        // if (!empty($staff)) {
        //   foreach ($staff as $key => $val) {
        //     if (array_key_exists($val->email_master_id, $emailMasterArry)) {
        //       $val->email_read_status = $emailMasterArry[$val->email_master_id];
        //     } else {
        //       $val->email_read_status = 'NA';
        //     }
        //   }
        // }

        // if (!empty($students)) {
        //   foreach ($students as $key => $val) {
        //     if (array_key_exists($val->email_master_id, $emailMasterArry)) {
        //       $val->email_read_status = $emailMasterArry[$val->email_master_id];
        //     } else {
        //       $val->email_read_status = 'NA';
        //     }
        //   }
        // }

        $emailMasterArryForStaff = [];
        $emailMasterArryForStudents = [];
        $emailMasterArryForParents = [];

        foreach ($emailSentTO as $key => $val) {
          if($val->avatar_type==4){
            $emailMasterArryForStaff[$val->stakeholder_id][$val->email_master_id] = $val->email_read_status;
          }else if ($val->avatar_type == 2){
            $emailMasterArryForParents[$val->stakeholder_id][$val->email_master_id] = $val->email_read_status;
          } else if ($val->avatar_type == 1) {
            $emailMasterArryForStudents[$val->stakeholder_id][$val->email_master_id] = $val->email_read_status;
          }
        }


      if (!empty($staff)) {
        foreach ($staff as $key => $val) {
          if (array_key_exists($val->rec_id, $emailMasterArryForStaff)) {
            $val->email_read_status = $emailMasterArryForStaff[$val->rec_id][$val->email_master_id];
          } else {
            $val->email_read_status = 'NA';
          }
        }
      }


      if (!empty($students)) {
        foreach ($students as $key => $val) {
          if (array_key_exists($val->rec_id, $emailMasterArryForStudents)) {
            $val->email_read_status = $emailMasterArryForStudents[$val->rec_id][$val->email_master_id];
          } else {
            $val->email_read_status = 'NA';
          }
        }
      }

      if (!empty($parents)) {
        foreach ($parents as $key => $val) {
          if (array_key_exists($val->parent_id, $emailMasterArryForParents)) {
            $val->email_read_status = $emailMasterArryForParents[$val->parent_id][$val->email_master_id];
          } else {
            $val->email_read_status = 'NA';
          }
        }
      }

      $students = array_merge($students, $parents);

      usort($students, function($a, $b) { return strcmp($a->stdName, $b->stdName); });
      $data = array('students' => $students, 'staff' => $staff);
      return $data;
    }

    public function changeStatus($circular_id, $is_visible) {
      $status = 1;
      if($is_visible == 1) {
        $status = 0;
      }
      return $this->db->where('id', $circular_id)->update('circularv2_master', array('visible' => $status));
    }

    public function approveCircular($circular_id, $status=1,$appoved_reject_comments,$rejected_approved_by) {
      return $this->db->where('id', $circular_id)->update('circularv2_master', array('visible' => (($status==1)?1:0), 'is_approved' => $status,'approved_reject_comments'=> $appoved_reject_comments,'approved_by'=>$rejected_approved_by));
    }

    public function getCircularData_report($from_date, $to_date){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db->select("cm.id as cmId, cm.sent_on as circular_date,cm.approved_reject_comments, recievers, title, if(cm.visible, 'published', 'Not Published') as status, category, a.friendly_name as creater_name");
      $this->db->from('circularv2_master cm');
      $this->db->join('avatar a', 'cm.sent_by=a.id', 'left');
      $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
      $this->db->where('date_format(cm.sent_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      $this->db->order_by('sent_on', 'desc');
      $result = $this->db->get()->result();
      foreach ($result as $key => $res) {
        $result[$key]->circular_date = $this->timezone_setter->setTimezone($result[$key]->circular_date, 'd-M h:i a');
      }
      return $result;
    }

    public function getCircularRcieverStdIds($circular_id) {
      $sql = "select distinct(student_id) as std_id from parent where id in (select stakeholder_id from circularv2_sent_to where avatar_type=2 and circularv2_master_id=$circular_id)";
      $student_ids = $this->db->query($sql)->result();
      $stdIds = [];
      if(!empty($student_ids)) {
        foreach ($student_ids as $key => $std) {
          array_push($stdIds, $std->std_id);
        }
      }
      return $stdIds;
    }

    public function getCircularRcieverStaffIds($circular_id) {
      $staffs = $this->db->select('stakeholder_id as staff_id')->where('circularv2_master_id', $circular_id)->where('avatar_type', 4)->get('circularv2_sent_to')->result();
      $staffIds = [];
      if(!empty($staffs)) {
        $stf_ids = [];
        foreach ($staffs as $key => $stf) {
          array_push($staffIds, $stf->staff_id);
        }
      }
      return $staffIds;
    }

    public function getCircularDetails($circular_id) {
      return $this->db->select("*")->where('id', $circular_id)->get('circularv2_master')->row();
    }

    public function saveCircular($master_data) {
      $this->db->insert('circularv2_master', $master_data);
      return $this->db->insert_id();
    }

    public function save_sending_data($sent_data, $master_id) {
      $data = array();
      foreach ($sent_data as $key => $val) {
        $data[] = array(
          'circularv2_master_id' => $master_id,
          'stakeholder_id' => $val->id,
          'email' => $val->email,
          'avatar_type' => $val->avatar_type
        );
      }

      return $this->db->insert_batch('circularv2_sent_to', $data);
    }

    public function addTextingId($master_id, $texting_master_id) {
      return $this->db->where('id', $master_id)->update('circularv2_master', ['texting_master_id' => $texting_master_id]);
    }

    public function addEmailId($master_id, $email_master_id) {
      return $this->db->where('id', $master_id)->update('circularv2_master', ['email_master_id' => $email_master_id]);
    }    

    public function update_circular_status($master_id) {
      return $this->db->where('id', $master_id)->update('circularv2_master', ['sending_status' => 'Completed', 'sender_list' => NULL]);
    }

    public function getCircularSentTo($circular_id) {
      return $this->db->select('stakeholder_id as id, email, avatar_type')->where('circularv2_master_id', $circular_id)->get('circularv2_sent_to')->result();
    }

    public function get_class_wise_student_data($classId){

      $result =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as student_name")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
      ->where('sy.class_id',$classId)
      ->where('admission_status','2')
      ->where('sy.promotion_status!=', '4')
      ->where('sy.promotion_status!=', '5')
      ->order_by('sd.first_name','ASC')
      ->get()->result();
     
      $cicurlars =  $this->db->select("cm.*")
      ->from('circularv2_sent_to ct')
      ->join('parent p', 'ct.stakeholder_id=p.id')
      ->join('circularv2_master cm','ct.circularv2_master_id=cm.id')
      ->join('student_year sy', 'sy.student_admission_id=p.student_id')
      ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
      ->where('sy.class_id',$classId)
      ->where('ct.avatar_type','2')
      ->where('cm.acad_year_id',$this->acad_year->getAcadYearId())
      ->group_by('cm.id')
      ->get()->result();

      return array('class' => $result, 'circulars'=>$cicurlars);

    }

    public function insert_resend_circular_data() {
      $circular_send_to = $this->input->post('circular_send_to_id');
      $circular_master_ids = $this->input->post('circular_master_ids');
      $resendCircular = array();
      foreach ($circular_send_to as $avatarType => $val) {
        foreach ($val as $stakeholder_id => $email) {
          $resendCircular[] = array(
            'stakeholder_id' => $stakeholder_id,
            'email' => $email,
            'avatar_type' => $avatarType
          );
        }
      }
      $insertData = [];
      foreach ($resendCircular as $key => $cirArry) {
        foreach ($circular_master_ids as $key => $master_id) {          
          $this->db->where('stakeholder_id',$cirArry['stakeholder_id']);
          $this->db->where('avatar_type',$cirArry['avatar_type']);
          $this->db->where('circularv2_master_id',$master_id);
          $query = $this->db->get('circularv2_sent_to');
          if ($query->num_rows() <= 0) {
            $insertData[] = array_merge($cirArry, ['circularv2_master_id' => $master_id]);
          }
        }
      }
      if (!empty($insertData)) {
        $result = $this->db->insert_batch('circularv2_sent_to', $insertData);
        if($result){
          $filtered = array_filter($insertData, function ($item) {
            return isset($item['avatar_type']) && $item['avatar_type'] == 1;
          });
          $stakeholderIds = array_column($filtered, 'stakeholder_id');
          $uniqueStakeholderIds = array_unique($stakeholderIds);
          $uniqueStakeholderIds = array_values($uniqueStakeholderIds);
          return array ('status' => 1, 'data' => $uniqueStakeholderIds);
        } else {
          return array ('status' => 0);
        }
      }else{
        return array ('status' => 0);
      }
    }

    public function getApprovedRejectedByName($id){
      $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) as staff_name");
      $this->db_readonly->from('staff_master');
      $this->db_readonly->where('id',$id);
      return $this->db_readonly->get()->row()->staff_name;
    }

    public function getSentBy($circular_id){
      $this->db_readonly->select("sent_by");
      $this->db_readonly->from('circularv2_master');
      $this->db_readonly->where('id',$circular_id);
      $avatar_id= $this->db_readonly->get()->row()->sent_by;

      $this->db_readonly->select("stakeholder_id");
      $this->db_readonly->from('avatar');
      $this->db_readonly->where('id',$avatar_id);
      return $this->db_readonly->get()->row()->stakeholder_id;
    }

    public function save_sending_user_data($sent_data, $master_id) {
      $data = array();
      foreach ($sent_data as $key => $val) {
        $textingData = array(
            'id'=>$val->stakeholder_id,
            'avatar_type'=>$val->avatar_type,
            'mobile_no'=>$val->mobile_no,
            'user_id'=> $val->user_id,
            'token'=>$val->token,
            'tokenState'=>$val->tokenState,
            'email'=>$val->email,
            'stakeholder_id'=>$val->stakeholder_id,
          );
        $data[] = array(
          'circularv2_master_id' => $master_id,
          'stakeholder_id' => $val->stakeholder_id,
          'email' => $val->email,
          'avatar_type' => $val->avatar_type,
          'temp_data'=>json_encode($textingData)
        );
      }
      return $this->db->insert_batch('circularv2_sent_to', $data);
    }

    public function update_circular_completed_status($master_id) {
      $result = $this->getCircularDetails($master_id);
      if($result->is_approved == 1){
        $this->db->where('circularv2_master_id',$master_id);
        $this->db->update('circularv2_sent_to',['temp_data'=>NULL]);
  
        return $this->db->where('id', $master_id)->update('circularv2_master', ['sending_status' => 'Completed', 'sender_list' => NULL]);
      }
    }

    public function get_circular_data_by_id($circular_id){
      return $this->db->select('temp_data')
      ->from('circularv2_sent_to')
      ->where('circularv2_master_id',$circular_id)
      ->get()->result();
    }
    public function get_circular_from_email_by_id($circular_id){
      return $this->db->select('id, from_email')
      ->from('circularv2_master')
      ->where('id',$circular_id)
      ->get()->row();
    }

    public function get_approvers_details_for_category($category){
      $result = $this->db->select('require_approval_by')
                        ->from('circularv2_categories')
                        ->where('category_name', $category)
                        ->where('require_approval', 1)
                        ->get()->row();
      if (!empty($result)) {
          $requireApprovalByArray = json_decode($result->require_approval_by, true);

          if (is_array($requireApprovalByArray)) {
            $requireApprovalByList = implode(',', $requireApprovalByArray);
            $staff_details = $this->get_staff_user_details($requireApprovalByList);
            return [
                'staff_details' => $staff_details,
                'require_approval_by_list' => $requireApprovalByList
            ];
          } else {
              return [
                'staff_details' => [],
            ];
          }
      } else {
          return [
              'staff_details' => [],
            ];
      }
    }

    public function getCircularsNotSentDetail($input){
      $acad_year = $this->acad_year->getAcadYearId();

      $email_master_id = $input['id'];

      if ($email_master_id == '' || $email_master_id == 0) {
        return false;
      }

      $attempts = $this->db_readonly->select('attempts')->from('email_master')->where('id', $email_master_id)->get()->row()->attempts;
      $email_details = $this->db_readonly->select('COUNT(id) AS total, 
              SUM(CASE WHEN status = "Awaited" THEN 1 ELSE 0 END) AS not_sent,
              SUM(CASE WHEN status = "Opened" THEN 1 ELSE 0 END) AS open,
              SUM(CASE WHEN status = "Delivered" THEN 1 ELSE 0 END) AS delivered,
              SUM(CASE WHEN status = "No Email" THEN 1 ELSE 0 END) AS no_email,
              SUM(CASE WHEN status = "Bounce-failed" THEN 1 ELSE 0 END) AS failed')
      ->from('email_sent_to')
      ->where('email_master_id', $email_master_id)
      ->get()->row();
      $data = [
        'email_master_id' => $email_master_id,
        'total_emails' => $email_details->total,
        'not_sent' => $email_details->not_sent,
        'delivered' => $email_details->delivered,
        'failed' => $email_details->failed,
        'no_email' => $email_details->no_email,
        'open' => $email_details->open,
        'attempts' => $attempts,
      ];
      
      return $data;
    }

    public function get_emails_to_resend($email_master_id){
      $acad_year = $this->acad_year->getAcadYearId();

      $this->db->select('cm.from_email, cm.file_path, cm.email_master_id, em.subject, em.body', false)
                        ->from('circularv2_master cm')
                        ->join('email_master em', 'cm.email_master_id = em.id', 'left')
                        ->where('cm.acad_year_id', $acad_year)
                        ->where('cm.email_master_id', $email_master_id)
                        ->where('cm.email_master_id IS NOT NULL');
      $email_master_data = $this->db->get()->row();

      if (empty($email_master_data)) {
          return false;
      }

      $email_sent_to_status = $this->db->select('email, stakeholder_id')
                                              ->from('email_sent_to')
                                              ->where('email_master_id', $email_master_id)
                                              ->where_in('status', ['Awaited', 'Bounce-failed'])
                                              ->get()
                                              ->result();
      $stake_holder_ids = array_map(function($item) { return $item->stakeholder_id; }, $email_sent_to_status);
      $email_ids = array_map(function($item) { return $item->email; }, $email_sent_to_status);
      $email_info = [
          'email_master_id'  => $email_master_data->email_master_id,
          'from_email'       => $email_master_data->from_email,
          'subject'          => $email_master_data->subject ?? '',
          'email_ids'        => $email_ids,
          'stake_holder_ids' => $stake_holder_ids,
          'file_path'        => $email_master_data->file_path ?? '',
          'body'             => $email_master_data->body ?? '',
      ];

      return $email_info;

    }

    public function update_attempts($email_master_id){
      return $this->db->set('attempts', 'attempts + 1', FALSE)
                ->where('id', $email_master_id)
                ->update('email_master');
    }
}