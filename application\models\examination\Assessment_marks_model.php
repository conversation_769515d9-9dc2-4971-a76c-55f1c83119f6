<?php

defined('BASEPATH') OR exit('No direct script access allowed');
            
class Assessment_marks_model extends CI_Model {
    private $yearId;
    private $current_branch;
    
    public function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }

	public function permittedAssessments($classId, $staffId) {
        if($staffId){
            $sql = "select id from assessments where id in (select distinct(assessment_id) from assessments_entities where id in (select assessments_entities_id from assessments_entities_access where staff_id=$staffId and access_level in ('write','read'))) and release_marks=1 and acad_year_id=$this->yearId";
            if($classId)
                $sql .= ' and class_id='.$classId;
        } else {
            $sql = "select id from assessments where acad_year_id=$this->yearId";
            if($classId)
                $sql .= ' and class_id='.$classId;
        }

        $result = $this->db_readonly->query($sql)->result();

        $assIds = array();
        if(!empty($result)){
            foreach ($result as $key => $value) {
                array_push($assIds, $value->id);
            }
        }
        return $assIds;
    }

    public function getReadPermission($assId, $sections){
        $this->db_readonly->select("ae.id as assEid");
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->where('ae.assessment_id', $assId);
        $return = $this->db_readonly->get()->result();
        $result = array();
        foreach ($return as $key => $value) {
            foreach ($sections as $k => $sec) {
                $result[$value->assEid.'_'.$sec->sectionId] = 'write';
            }
        }
        // echo "<pre>"; print_r($result);die();
        return $result;
    }

    public function getPermissionForStaff($assId, $staffId, $class_id){
        $this->db_readonly->select("aea.access_level, ae.id as assEid, aea.class_section_id as sectionId");
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->join('assessment_entity_master aem', 'aem.id=ae.entity_id');
        $this->db_readonly->join('assessments_entities_access aea', 'aea.assessments_entities_id=ae.id');
        $this->db_readonly->join('class_section cs', 'aea.class_section_id=cs.id');
        $this->db_readonly->join('staff_master sm', 'sm.id=aea.staff_id');
        $this->db_readonly->where('sm.id', $staffId);
        $this->db_readonly->order_by('sm.first_name', 'ASC');
        $return = $this->db_readonly->get()->result();
        $result = array();
        foreach ($return as $key => $value) {
            $result[$value->assEid.'_'.$value->sectionId] = $value->access_level;
        }
        return $result;
        // echo "<pre>"; print_r($return);die();
    }

    public function isFreshMarksEntry($assEid, $sectionId, $classId){
        $this->db_readonly->select('count(aem.id) as count');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('student_admission sa', 'sa.id=aem.student_id');
        $this->db_readonly->join('student_year s', 'sa.id=s.student_admission_id');
        $this->db_readonly->where('s.class_id', $classId);
        $this->db_readonly->where('s.class_section_id', $sectionId);
        $this->db_readonly->where('aem.assessments_entities_id', $assEid);
        $result = $this->db_readonly->get()->row();
        if($result->count != 0)
            return false;
        else
            return true;
    }

    public function isElective($entityId, $classId){
        $this->db_readonly->select('aeg.*');
        $this->db_readonly->from('assessment_entity_master aem');
        $this->db_readonly->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id');
        $this->db_readonly->where('aem.id', $entityId);
        $this->db_readonly->where('aeg.is_elective', '1');
        return $this->db_readonly->get()->row();
    }

    public function getEleStudents($entityGid, $electiveGid){
        return $this->_getElecStudents($entityGid, $electiveGid);
    }

    private function _getElecStudents($entityGid, $electiveGid){
        $this->db_readonly->select('se.student_id as stdId');
        $this->db_readonly->from('assessment_students_elective se');
        $this->db_readonly->where('se.ass_elective_gid', $electiveGid);
        $this->db_readonly->where('se.ass_entity_gid', $entityGid);
        $result = $this->db_readonly->get()->result();
        $stdArr = array();
        foreach ($result as $key => $value) {
            array_push($stdArr, $value->stdId);
        }
        return $stdArr;
    }

    public function getStudentList ($classId, $sectionId, $stdArr, $isElective) {
        $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as sName";
        $this->db_readonly->select("s.id as sId,sy.roll_no, $std_name");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->where('sy.class_id', $classId);
        $this->db_readonly->where('sy.class_section_id', $sectionId);
        $this->db_readonly->where('s.admission_status', 2);
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->where('sy.acad_year_id', $this->yearId);
        $this->db_readonly->order_by('sy.roll_no, s.first_name');
        if($isElective) {
            if (!empty($stdArr))
                $this->db_readonly->where_in('s.id', $stdArr);
            else
                return '';
        }
        $result = $this->db_readonly->get()->result();
        return $result;
    }

    public function getStudentListWithMarks($classId,$sectionId, $assEid, $stdArr, $isElective){
        $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as sName";
        $this->db_readonly->select("s.id as sId,sy.roll_no, $std_name, aem.id as assEms, aem.marks, aem.status");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('assessments_entities_marks_students aem', "aem.student_id=s.id and aem.assessments_entities_id=$assEid", 'left');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->where('sy.class_id', $classId);
        $this->db_readonly->where('sy.class_section_id', $sectionId);
        // $this->db_readonly->where('aem.assessments_entities_id', $assEid);
        $this->db_readonly->where('s.admission_status', 2);
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->order_by('sy.roll_no, s.first_name');
        if($isElective) {
            if (!empty($stdArr))
                $this->db_readonly->where_in('s.id', $stdArr);
            else
                return '';
        }
        $result = $this->db_readonly->get()->result();

        return $result;
        $this->db_readonly->select('s.id as sId,sy.roll_no, s.first_name as sName, aem.id as assEms, aem.marks, aem.status');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('student_admission s', 'aem.student_id=s.id');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->where('sy.class_id', $classId);
        $this->db_readonly->where('sy.class_section_id', $sectionId);
        $this->db_readonly->where('aem.assessments_entities_id', $assEid);
        $this->db_readonly->where('s.admission_status', 2);
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->order_by('sy.roll_no, s.first_name');
        if($isElective) {
            if (!empty($stdArr))
                $this->db_readonly->where_in('s.id', $stdArr);
            else
                return '';
        }
        $result = $this->db_readonly->get()->result();
        // echo "<pre>"; print_r($result);die();
        return $result;
    }

    private function _getVerifiedStaffAvatar($staffId) {
        $avatar = $this->db->select('id')->where('stakeholder_id',$staffId)->where('avatar_type', 4)->get('avatar')->row();
        if(empty($avatar))
            return 0;
        return $avatar->id;
    }

    private function insert_marks_entity_wise($input) {
        $stdArr = $input['studentId'];
        $marksArr = $input['marks'];
        $assessment_entities_id = $input['assEid'];
        $status = $input['status'];
        if($status == 2) {
            $avatarId = $this->_getVerifiedStaffAvatar($input['verifiedStaff']);
            $verifyAction = 'Nobody verified marks';
            if($avatarId) {
                $verifyAction = 'Marks verified';
            }
        }

        foreach ($stdArr as $k => $std_id) {
            $marks = trim($marksArr[$k]);
            if ($marks == '') {
                continue;
            }

        }
    }

    private function _generateHistory($stdMarksId, $status, $action_comment, $verifyStaff) {
        $marksHistory = array();
        if($action_comment != '') {
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => $action_comment,
                'action_by' => $this->authorization->getAvatarId()
            );
        }
        if($status == 2) {
            $verifyAvatar = $this->_getVerifiedStaffAvatar($verifyStaff);
            $verify_comment = 'Marks verified';
            if(!$verifyAvatar) {
                $verifyAvatar = $this->authorization->getAvatarId();
            }
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => $verify_comment,
                'action_by' => $verifyAvatar
            );
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => 'Marks Locked',
                'action_by' => $verifyAvatar
            );
        }
        return $marksHistory;
    }

    public function saveStudentMarks() {
        $input = $this->input->post();
        // echo "<pre>"; print_r($input); die();
        $assEid = $input['assEid'];
        $entity_id = $input['entity_id'];
        $stdArr = $input['studentId'];
        $assEmsArr = $input['assEms'];
        $marksArr = $input['marks'];
        $oldMarks = $input['oldMarks'];
        $assessment_entities_id = $input['assEid'];
        $verifiedStaff = $input['verifiedStaff'];
        $status = $input['status'];

        $total_history = array();
        $update_data = array();
        $std_list = array();
        $this->db->trans_begin();
        foreach ($assEmsArr as $k => $aemsId) {
            $marks = $marksArr[$k];
            if($aemsId == '' || $aemsId == null) {
                //insert row
                if($marks == '') continue;
                $insert_data = array(
                    'assessments_entities_id'=> $assEid,
                    'student_id'=> $stdArr[$k],
                    'marks'=> $marks,
                    'status' => $status,
                    'acad_year_id' => $this->yearId
                );
                $this->db->insert('assessments_entities_marks_students',$insert_data);
                $stdMarksId = $this->db->insert_id();
                $action_comment = 'Marks added '.$marks;
                $history = $this->_generateHistory($stdMarksId, $status, $action_comment, $verifiedStaff);
                foreach ($history as $key => $his) {
                    array_push($total_history, $his);
                }
            } else {
                //update row
                if ($marks == '') {
                    $marks = -2.00;
                }
                
                $update_data[] = array(
                    'id' => $aemsId,
                    'marks'=> $marks,
                    'status' => $status
                );
                $action_comment = '';
                if($oldMarks[$k] != $marks) {
                    $std_list[] = $stdArr[$k];
                    $action_comment = 'Marks changed to '.$marksArr[$k];
                }
                $history = $this->_generateHistory($aemsId, $status, $action_comment, $verifiedStaff);
                foreach ($history as $key => $his) {
                    array_push($total_history, $his);
                }
            }
        }
        // echo $status;
        // echo "<pre>"; print_r($total_history); die();

        if(!empty($total_history)) {
            $this->db->insert_batch('assessments_entities_marks_students_history',$total_history);
        }

        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students',$update_data, 'id');
        }

        //refresh derived assessments for changed marks
        if(!empty($std_list)) {
            $this->_refreshDerived($std_list, $entity_id);
        }

        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }

    private function _refreshDerivedSubjects($std_ids, $group_id, $ass_id, $entity_id) {
        $entity = $this->db_readonly->select("ass_type")->where('id', $entity_id)->get('assessment_entity_master')->row();
        if(empty($entity) || $entity->ass_type == 'Derived') {
            return 1;
        }

        $derived_subjects = $this->db->select("ae.id as ae_id, ae.entity_id, aem.ass_type, aem.derived_formula, rounding")
        ->from('assessments_entities ae')
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
        ->where('ae.assessment_id', $ass_id)
        ->where('aem.ass_entity_gid', $group_id)
        ->where('aem.ass_type', 'Derived')
        ->get()->result();

        if(empty($derived_subjects)) {
            return 1;
        }

        $derived = [];
        $entity_ids = [];
        foreach ($derived_subjects as $k => $subs) {
            if(!$subs->derived_formula) {
                unset($derived_subjects[$k]);
                continue;
            }
            $formula = json_decode($subs->derived_formula);
            $subs->formula = $formula->formula->name;
            $ent_ids = $formula->entityId;
            $subs->entity_ids = [];
            foreach ($ent_ids as $ent) {
                $entity_ids[] = $ent->id;
                $subs->entity_ids[] = $ent->id;
            }
        }

        $entity_marks = $this->db->select("ae.entity_id, aems.student_id, aems.marks")
        ->from('assessments_entities ae')
        ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id=ae.id')
        ->where('ae.assessment_id', $ass_id)
        ->where_in('ae.entity_id', $entity_ids)
        ->where_in('aems.student_id', $std_ids)
        ->get()->result();

        if(empty($entity_marks)) {
            return 1;
        }

        $std_wise_marks = [];
        foreach ($entity_marks as $key => $em) {
            if(!array_key_exists($em->student_id, $std_wise_marks)) {
                $std_wise_marks[$em->student_id] = [];
            }
            $std_wise_marks[$em->student_id][$em->entity_id] = $em->marks;
        }


        $insert_data = [];
        $update_data = [];
        foreach ($derived_subjects as $dev) {
            foreach ($std_wise_marks as $student_id => $ent) {
                $marks_arr = [];
                $totals_arr = [];
                foreach ($dev->entity_ids as $ent_id) {
                    if(array_key_exists($ent_id, $ent)) {
                        $marks_arr[] = $ent[$ent_id];
                    }
                }
                if(!empty($marks_arr)) {
                    $derived_marks = $this->_calculateDerivedSubjectMarks($marks_arr, $totals_arr, $dev->formula, $dev->rounding);
                    $exists = $this->db->select('id')->where('assessments_entities_id', $dev->ae_id)->where('student_id', $student_id)->get('assessments_entities_marks_students')->row();
                    if(!empty($exists)) {
                        $update_data[] = array(
                            'id' => $exists->id,
                            'marks' => $derived_marks,
                            'status' => 1
                        );
                    } else {
                        $insert_data[] = array(
                            'assessments_entities_id' => $dev->em_id,
                            'student_id' => $student_id,
                            'marks' => $derived_marks,
                            'status' => 1
                        );
                    }
                }
            }
        }

        if(!empty($insert_data)) {
            $this->db->insert_batch('assessments_entities_marks_students', $insert_data);
        }
        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }

        return 1;
    }

    private function _refreshDerived($std_list, $entity_id) {
        $consData = $this->db->query("select a.id, a.formula, ae.id as assessments_entities_id from assessments a join assessments_entities ae on ae.assessment_id=a.id where a.generation_type='Auto' and ae.entity_id=1 order by id")->result();
        // echo '<pre>'; print_r($consData); die();

        foreach ($consData as $key => $cons) {
            $jsonData = json_decode($cons->formula);
            $formula = $jsonData->merg_algorithm->name;
            $merge_set = $jsonData->merg_values;
            $entity_data_set = array();
            foreach ($merge_set as $key => $merge) {
                if($merge->entity_id == $entity_id) {
                    foreach ($merge->value_set as $k => $set) {
                        $entity_data_set[$set->assId] = $set->value;
                    }
                    break;
                }
            }
            $this->assessment_model->calculateDerivedMarksMultiple($std_list, $entity_id, $cons->assessments_entities_id, $formula, $entity_data_set, $rounding= 2);
        }
        return 1;
    }

    public function saveStudentMarks1() {
        $input = $this->input->post();
        $assEmsArr = $this->input->post('assEms');
        foreach ($assEmsArr as $aems) {
            echo 'aa: '.$emms.'<br>';
        }
        // echo "<pre>"; print_r($this->input->post());die();
        $assEid = $this->input->post('assEid');
        $stdArr = $this->input->post('studentId');
        $marksArr = $this->input->post('marks');
        $tMarks = $this->input->post('tMarks');
        $isFreshEntry = $this->input->post('isFreshEntry');
        $oldMarks = $this->input->post('oldMarks');
        $status = $this->input->post('status');
        $staffId = $this->input->post('verifiedStaff');
        $avatar = $this->db->select('id')->where('stakeholder_id',$staffId)->where('avatar_type', 4)->get('avatar')->row();
        $avatarId = $staffId;
        if(!empty($avatar))
            $avatarId = $avatar->id;
        $verifyAction = 'Marks verified';
        if($staffId == 0) {
            $verifyAction = 'Nobody verified marks';
        }


        $this->db->trans_begin();
        if ($isFreshEntry == 'true') {
        //Insert
        if (!empty($stdArr)) {
            foreach ($stdArr as $k => $std_id) {
                $marks = trim($marksArr[$k]);
                if ($marks == '') {
                    continue;
                }
              $studentMarksData = array(
                'assessments_entities_id'=>$assEid,
                'student_id'=>$std_id,
                'marks'=>$marks,
                'status' => $status,
                'acad_year_id' => $this->yearId
              );

                $this->db->insert('assessments_entities_marks_students',$studentMarksData);
                $stdMid = $this->db->insert_id();
                $action = 'Marks added '.$marksArr[$k];
                if($marksArr[$k] == '')
                    $action = 'Marks not added';
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $stdMid,
                    'action' => $action,
                    'action_by' => $this->authorization->getAvatarId()
                );
                if($status == 2) {
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMid,
                        'action' => $verifyAction,
                        'action_by' => $avatarId
                    );
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMid,
                        'action' => 'Marks Locked',
                        'action_by' => $avatarId
                    );
                }
            }
            if(!empty($marksHistory))
                $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        }
      } else {
        //Update
        // $this->db->trans_start();
        if (!empty($stdArr)) {
          foreach ($stdArr as $k => $std_id) {
            //Update marks
            $marks = trim($marksArr[$k]);
            if ($marks == '') {
                continue;
                // $marks = -2.00;
            }
            if($assEmsArr[$k] == null) {
                $studentMarksData = array(
                    'assessments_entities_id'=>$assEid,
                    'student_id'=>$std_id,
                    'marks'=>$marks,
                    'status' => $status,
                    'acad_year_id' => $this->yearId
                );
                $this->db->insert('assessments_entities_marks_students',$studentMarksData);
                $stdMid = $this->db->insert_id();
                $action = 'Marks added '.$marksArr[$k];
                if($marksArr[$k] == '')
                    $action = 'Marks not added';
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $stdMid,
                    'action' => $action,
                    'action_by' => $this->authorization->getAvatarId()
                );
                if($status == 2) {
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMid,
                        'action' => $verifyAction,
                        'action_by' => $avatarId
                    );
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMid,
                        'action' => 'Marks Locked',
                        'action_by' => $avatarId
                    );
                }
            } else {

            }
            $studentMarksData[] = array(
              'id' => $assEmsArr[$k],
              'marks'=>$marks,
              'status' => $status
            );
            if($status == 2) {
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $assEmsArr[$k],
                    'action' => $verifyAction,
                    'action_by' => $this->authorization->getAvatarId()
                );
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $assEmsArr[$k],
                    'action' => 'Marks Locked',
                    'action_by' => $this->authorization->getAvatarId()
                );
            }
            if($oldMarks[$k] != $marks) {
                $action = 'Marks changed to '.$marksArr[$k];
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $assEmsArr[$k],
                    'action' => $action,
                    'action_by' => $this->authorization->getAvatarId()
                );
            }
          }
          $this->db->update_batch('assessments_entities_marks_students',$studentMarksData, 'id');
          if(!empty($marksHistory))
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        }
      }
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }

    public function insertStudentMarks(){
    	// echo "<pre>"; print_r($this->input->post());die();
        $assEid = $this->input->post('assEid');
        $stdArr = $this->input->post('studentId');
        $marksArr = $this->input->post('marks');
        $tMarks = $this->input->post('tMarks');
        $assEmsArr = $this->input->post('assEms');
        $isFreshEntry = $this->input->post('isFreshEntry');
        $oldMarks = $this->input->post('oldMarks');
        $status = $this->input->post('status');
        $staffId = $this->input->post('verifiedStaff');
        $avatar = $this->db->select('id')->where('stakeholder_id',$staffId)->where('avatar_type', 4)->get('avatar')->row();
        $avatarId = $staffId;
        if(!empty($avatar))
            $avatarId = $avatar->id;
        $verifyAction = 'Marks verified';
        if($staffId == 0) {
            $verifyAction = 'Nobody verified marks';
        }


        $this->db->trans_begin();
        if ($isFreshEntry == 'true') {
        //Insert
        if (!empty($stdArr)) {
            foreach ($stdArr as $k => $v) {
                $marks = trim($marksArr[$k]);
                if ($marks == '') {
                    $marks = -2.00;
                }
              $studentMarksData = array(
                'assessments_entities_id'=>$assEid,
                'student_id'=>$stdArr[$k],
                'marks'=>$marks,
                'status' => $status,
                'acad_year_id' => $this->yearId
              );

                $this->db->insert('assessments_entities_marks_students',$studentMarksData);
                $stdMid = $this->db->insert_id();
                $action = 'Marks added '.$marksArr[$k];
                if($marksArr[$k] == '')
                    $action = 'Marks not added';
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $stdMid,
                    'action' => $action,
                    'action_by' => $this->authorization->getAvatarId()
                );
                if($status == 2) {
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMid,
                        'action' => $verifyAction,
                        'action_by' => $avatarId
                    );
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMid,
                        'action' => 'Marks Locked',
                        'action_by' => $avatarId
                    );
                }
            }
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        }
      } else {
        //Update
        // $this->db->trans_start();
        if (!empty($stdArr)) {
          foreach ($stdArr as $k => $v) {
            //Update marks
            $marks = trim($marksArr[$k]);
            if ($marks == '') {
                $marks = -2.00;
            }
            $studentMarksData[] = array(
              'id' => $assEmsArr[$k],
              'marks'=>$marks,
              'status' => $status
            );
            if($status == 2) {
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $assEmsArr[$k],
                    'action' => $verifyAction,
                    'action_by' => $this->authorization->getAvatarId()
                );
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $assEmsArr[$k],
                    'action' => 'Marks Locked',
                    'action_by' => $this->authorization->getAvatarId()
                );
            }
            if($oldMarks[$k] != $marks) {
                $action = 'Marks changed to '.$marksArr[$k];
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $assEmsArr[$k],
                    'action' => $action,
                    'action_by' => $this->authorization->getAvatarId()
                );
            }
          }
          $this->db->update_batch('assessments_entities_marks_students',$studentMarksData, 'id');
          if(!empty($marksHistory))
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        }
      }
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }

    public function __insertMarks($aeId, $std_id, $marks, $old_marks, $isFreshEntry, $status, $verifiedBy) {
        $action_by = $this->authorization->getAvatarId();
        if ($marks == '') {
            $marks = -2.00;
        }
        $studentMarksData = array(
            'assessments_entities_id'=>$aeId,
            'student_id'=>$std_id,
            'marks'=>$marks,
            'status' => $status,
            'acad_year_id' => $this->yearId
        );
        $verifyAction = 'Marks verified';
        if($verifiedBy == 0) {
            $verifyAction = 'Nobody verified marks';
        }
        $refresh_required = 0;
        $this->db->trans_begin();
        if($isFreshEntry) {
            $is_exists = $this->db->select('id')->where('assessments_entities_id', $aeId)->where('student_id', $std_id)->get('assessments_entities_marks_students')->row();
            if(!empty($is_exists)) return 0;
            $this->db->insert('assessments_entities_marks_students',$studentMarksData);
            $stdMarksId = $this->db->insert_id();
            $action = 'Marks added '.$marks;
            if($marks != -2.00) {
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $stdMarksId,
                    'action' => $action,
                    'action_by' => $action_by
                );
            }
        } else {
            $res = $this->db->select('id')->where('student_id', $std_id)->where('assessments_entities_id', $aeId)->get('assessments_entities_marks_students')->row();
            if(empty($res)) {
                $this->db->insert('assessments_entities_marks_students',$studentMarksData);
                $stdMarksId = $this->db->insert_id();
                $action = 'Marks added '.$marks;
                if($marks != -2.00) {
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMarksId,
                        'action' => $action,
                        'action_by' => $action_by
                    );
                }
            } else {
                $stdMarksId = $res->id;
                $this->db->where('id', $stdMarksId)->update('assessments_entities_marks_students', $studentMarksData);
                if($old_marks != $marks) {
                    $refresh_required = 1;
                    $action = 'Marks changed to '.$marks;
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMarksId,
                        'action' => $action,
                        'action_by' => $action_by
                    );
                }
            }
        }
        if($status == 2) {
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => $verifyAction,
                'action_by' => $verifiedBy
            );
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => 'Marks Locked',
                'action_by' => $action_by
            );
        }
        if(!empty($marksHistory)) {
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        }

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return $refresh_required;
        }
    }

    public function __insertGrades($aeId, $std_id, $grade, $old_grade, $isFreshEntry, $status, $verifiedBy) {
        $action_by = $this->authorization->getAvatarId();
        if ($grade == '') {
            $grade = 'TBD';
        }
        $studentGradeData = array(
            'assessments_entities_id'=>$aeId,
            'student_id'=>$std_id,
            'grade'=>$grade,
            'status' => $status,
            'acad_year_id' => $this->yearId
        );
        $verifyAction = 'Grades verified';
        if($verifiedBy == 0) {
            $verifyAction = 'Nobody verified grades';
        }
        $this->db->trans_begin();
        if($isFreshEntry) {
            $this->db->insert('assessments_entities_marks_students',$studentGradeData);
            $stdMarksId = $this->db->insert_id();
            $action = 'Grade added '.$grade;
            if($grade != '') {
                $marksHistory[] = array(
                    'assessments_entities_marks_students_id' => $stdMarksId,
                    'action' => $action,
                    'action_by' => $action_by
                );
            }
        } else {
            $res = $this->db->select('id')->where('student_id', $std_id)->where('assessments_entities_id', $aeId)->get('assessments_entities_marks_students')->row();
            if(empty($res)) {
                $this->db->insert('assessments_entities_marks_students',$studentGradeData);
                $stdMarksId = $this->db->insert_id();
                $action = 'Grade added '.$grade;
                if($grade != '') {
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMarksId,
                        'action' => $action,
                        'action_by' => $action_by
                    );
                }
            } else {
                $stdMarksId = $res->id;
                $this->db->where('id', $stdMarksId)->update('assessments_entities_marks_students', $studentGradeData);
                if($old_grade != $grade) {
                    $action = 'Grade changed to '.$grade;
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $stdMarksId,
                        'action' => $action,
                        'action_by' => $action_by
                    );
                }
            }
        }
        if($status == 2) {
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => $verifyAction,
                'action_by' => $verifiedBy
            );
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => 'Grades Locked',
                'action_by' => $action_by
            );
        }
        if(!empty($marksHistory)) {
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        }

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }

    private function _get_rounding_config () {
        //Get rounding configuration
        $rounding_digits = 2;
        if (null != $this->settings->getSetting('examination_marks_rounding_digits'))
            $rounding_digits = $this->settings->getSetting('examination_marks_rounding_digits');
        return $rounding_digits;
    }

    private function _addMarks($marksArr, $do_strict_round = '0'){
        $sum = 0;
        $mCount = count($marksArr);
        $absentCount = 0;
        $NACount = 0;
        $TBDCount = 0;
        foreach ($marksArr as $marks) {
            if($marks > 0) {
                if ($do_strict_round == '1') {
                    //always round!
                    $sum += round($marks,0);
                } else {
                    //round based on config
                    $sum += round($marks,$this->_get_rounding_config());
                }
            }
            if($marks == -1.00)
                $absentCount++;
            if($marks == -3.00)
                $NACount++;
            if($marks == -2.00)
                $TBDCount++;
        }
        if($mCount == $absentCount)
            return -1.00;
        if($mCount == $NACount)
            return -3.00;
        if($mCount == $TBDCount)
            return -2.00;
        if($sum == 0 && $absentCount) 
            return -1.00;
        return $sum;
    }

    private function _calculateDerivedSubjectMarks($marks_arr, $totals_arr, $formula, $rounding_parameter) {
        $marks = 0;
        switch ($formula) {
            case 'Average':
                $sum = $this->_addMarks($marks_arr);
                if($sum < 0) 
                    $marks = $sum;
                else 
                    $marks = $sum/count($marks_arr);
                break;
            case 'Round_Average':
                //Add with Rounding enforced to 0   
                $sum = $this->_addMarks($marks_arr, 1);
                if($sum < 0) 
                    $marks = $sum;
                else 
                    $marks = round($sum/count($marks_arr), 0);
                break;
            case 'Sum': 
                $sum = $this->_addMarks($marks_arr);
                $marks = $sum;
                break;
            default:
                $marks = 0;
                break;
        }

        return number_format($marks, $rounding_parameter);
        // return round($marks, $this->_get_rounding_config());
    }

    public function insertMarksV2($is_refresh_derived_subject_required=1) {
        $input = $this->input->post();
        if(!isset($input['aeIds']) || $input['aeIds'] <= 0 || $input['aeIds'] == '' ) {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('examination/assessments');
        }
        $aeIds = $input['aeIds'];
        $entity_ids = isset($input['entity_ids']) ? $input['entity_ids'] : [0];
        $group_ids = $input['group_id'];
        // $total = isset($input['total']) ? $input['total'] : 1;
        $isFreshEntry = $input['is_fresh_entry'];
        $status = $input['status'];
        $verifiedBy = $input['verifiedStaff'];
        if($verifiedBy) {
            $verifiedBy = $this->db->select('id')->where('stakeholder_id', $verifiedBy)->where('avatar_type', 4)->get('avatar')->row()->id;
        } 

        if(!isset($aeIds) || empty($aeIds)) {
            redirect('examination/Assessments/index'); 
        }
        foreach ($aeIds as $key => $aeId) {
            $marks = array();
            $old_marks = array();
            if(isset($input['marks_'.$aeId]) && isset($input['oldmarks_'.$aeId])) {
                $marks_list = $input['marks_'.$aeId];
                $old_marks_list = $input['oldmarks_'.$aeId];
                $std_list = array();
                $std_ids = array();
                $entity_id = $entity_ids[$key];
                $group_id = $group_ids[$key];
                foreach ($marks_list as $std_id => $marks) {
                    $is_refresh_required = $this->__insertMarks($aeId, $std_id, $marks, $old_marks_list[$std_id], $isFreshEntry, $status, $verifiedBy);
                    if($is_refresh_required) {
                        $std_list[] = $std_id;
                    }
                    if($isFreshEntry) {
                        $std_ids[] = $std_id;
                    } else {
                        if($is_refresh_required) {
                            $std_ids[] = $std_id;
                        }
                    }
                }
                if(!empty($std_list))
                    $this->_refreshDerived($std_list, $entity_id);

                if($is_refresh_derived_subject_required) {
                    if(!empty($std_ids)) {
                        $this->_refreshDerivedSubjects($std_ids, $group_id, $input['ass_id'], $entity_id);
                    }
                }
                continue;
            }

            if(isset($input['grades_'.$aeId]) && isset($input['oldgrades_'.$aeId])) {
                $grades_list = $input['grades_'.$aeId];
                $old_grades_list = $input['oldgrades_'.$aeId];
                foreach ($grades_list as $std_id => $grade) {
                    // if($grade == '') continue;
                    $this->__insertGrades($aeId, $std_id, $grade, $old_grades_list[$std_id], $isFreshEntry, $status, $verifiedBy);
                }
            }
        }

        return 1;
    }

    public function insertMarks() {
        $input = $this->input->post();
        // echo "<pre>"; print_r($input); die();
        $aeIds = $input['aeIds'];
        $entity_ids = $input['entity_ids'];
        $total = $input['total'];
        $isFreshEntry = $input['is_fresh_entry'];
        $status = $input['status'];
        $verifiedBy = $input['verifiedStaff'];
        if($verifiedBy) {
            $verifiedBy = $this->db->select('id')->where('stakeholder_id', $verifiedBy)->where('avatar_type', 4)->get('avatar')->row()->id;
        } 

        foreach ($aeIds as $key => $aeId) {
            $marks = array();
            $old_marks = array();
            if(isset($input['marks_'.$aeId]) && isset($input['oldmarks_'.$aeId])) {
                $marks_list = $input['marks_'.$aeId];
                $old_marks_list = $input['oldmarks_'.$aeId];
                $std_list = array();
                $entity_id = $entity_ids[$key];
                foreach ($marks_list as $std_id => $marks) {
                    $is_refresh_required = $this->__insertMarks($aeId, $std_id, $marks, $old_marks_list[$std_id], $isFreshEntry, $status, $verifiedBy);
                    if($is_refresh_required) {
                        $std_list[] = $std_id;
                    }
                }
                if(!empty($std_list))
                    $this->_refreshDerived($std_list, $entity_id);
                continue;
            }

            if(isset($input['grades_'.$aeId]) && isset($input['oldgrades_'.$aeId])) {
                $grades_list = $input['grades_'.$aeId];
                $old_grades_list = $input['oldgrades_'.$aeId];
                foreach ($grades_list as $std_id => $grade) {
                    // if($grade == '') continue;
                    $this->__insertGrades($aeId, $std_id, $grade, $old_grades_list[$std_id], $isFreshEntry, $status, $verifiedBy);
                }
            }
        }

        return 1;
    }

    public function saveMarks() {
        $input = $this->input->post();
        $aeIds = $input['aeIds'];
        $total = $input['total'];
        $isFreshEntry = $input['is_fresh_entry'];
        // $students = $input['students'];
        $status = $input['status'];
        $verifiedBy = $input['verifiedStaff'];
        if($verifiedBy) {
            $verifiedBy = $this->db->select('id')->where('stakeholder_id', $verifiedBy)->where('avatar_type', 4)->get('avatar')->row()->id;
        } 

        foreach ($aeIds as $key => $aeId) {
            $marks = array();
            $old_marks = array();
            if(isset($input['marks_'.$aeId]) && isset($input['oldmarks_'.$aeId])) {
                $marks_list = $input['marks_'.$aeId];
                $old_marks_list = $input['oldmarks_'.$aeId];
                foreach ($marks_list as $std_id => $marks) {
                    $this->__insertMarks($aeId, $std_id, $marks, $old_marks_list[$std_id], $isFreshEntry, $status, $verifiedBy);
                }
                continue;
            }

            if(isset($input['grades_'.$aeId]) && isset($input['oldgrades_'.$aeId])) {
                $grades_list = $input['grades_'.$aeId];
                $old_grades_list = $input['oldgrades_'.$aeId];
                foreach ($grades_list as $std_id => $grade) {
                    $this->__insertGrades($aeId, $std_id, $grade, $old_grades_list[$std_id], $isFreshEntry, $status, $verifiedBy);
                }
            }
        }
        /*if(isset($input['remarks'])) {
            $stdIds = implode(",", array_keys($input['remarks']));
            $assId = $input['ass_id'];
            $subject_id = $input['group_id'];
            $sql = "select id from assessment_subject_remarks where student_id in ($stdIds) and assessment_id=$assId and subject_id=$subject_id";
            $rows = $this->db->query($sql)->result();
            if(empty($rows)) {
                foreach ($input['remarks'] as $stdId => $remarks) {
                    $remarks_data[] = array(
                        'assessment_id' => $input['ass_id'],
                        'student_id' => $stdId,
                        'subject_id' => $input['group_id'],
                        'subject_remarks' => $remarks
                    );
                }
                $this->db->insert_batch('assessment_subject_remarks', $remarks_data);
            } else {
                foreach ($input['remarks'] as $stdId => $remarks) {
                    $remarks_data = array(
                        'assessment_id' => $input['ass_id'],
                        'student_id' => $stdId,
                        'subject_id' => $input['group_id'],
                        'subject_remarks' => $remarks
                    );
                    $this->db->where('student_id', $stdId)
                    ->where('subject_id', $input['group_id'])
                    ->where('assessment_id', $input['ass_id'])
                    ->update('assessment_subject_remarks', $remarks_data);
                }
            }
        }*/
        return 1;
    }

    public function unlockMarksEntry($assEid){
        $this->db->trans_start();
        $this->db->where('assessments_entities_id', $assEid);
        $this->db->update('assessments_entities_marks_students', array('status'=>1));
        
        $stdData = $this->db->select('id')->where('assessments_entities_id', $assEid)->get('assessments_entities_marks_students')->result();
        foreach ($stdData as $key => $val) {
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $val->id,
                'action' => 'Unlocked marks entry',
                'action_by' => $this->authorization->getAvatarId()
            );
        }
        if(!empty($marksHistory))
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        return $this->db->trans_complete();
    }

    public function unlockMarksStatus($assEids){
        $this->db->trans_start();
        $this->db->where_in('assessments_entities_id', $assEids);
        $this->db->update('assessments_entities_marks_students', array('status'=>1));
        
        $stdData = $this->db->select('id')->where_in('assessments_entities_id', $assEids)->get('assessments_entities_marks_students')->result();
        foreach ($stdData as $key => $val) {
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $val->id,
                'action' => 'Unlocked marks entry',
                'action_by' => $this->authorization->getAvatarId()
            );
        }
        if(!empty($marksHistory))
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        return $this->db->trans_complete();
    }

    public function lockUnlockMarksStatusForSection($ae_id, $section_id, $lock_status){
        //Get the Student ids to change
        $student_objs = $this->db->select('sa.id')
            ->from('student_admission sa')
            ->join('student_year sy', "sa.id=sy.student_admission_id and class_section_id=$section_id")
            ->get()->result();

        $std_arr = [];
        foreach ($student_objs as $std_obj) {
            $std_arr[] = $std_obj->id;
        }

        if (empty($std_arr)) {
            return 1;
        }

        //Update the marks and history
        $this->db->trans_start();
        $this->db->where('assessments_entities_id', $ae_id);
        $this->db->where_in('student_id', $std_arr);
        $this->db->update('assessments_entities_marks_students', array('status'=>$lock_status));

        $stdData = $this->db->select('id')->where('assessments_entities_id', $ae_id)->get('assessments_entities_marks_students')->result();
        foreach ($stdData as $key => $val) {
            $marksHistory[] = array(
                'assessments_entities_marks_students_id' => $val->id,
                'action' => 'Unlocked marks entry',
                'action_by' => $this->authorization->getAvatarId()
            );
        }
        if(!empty($marksHistory))
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
        return $this->db->trans_complete();
    }

    public function getMarksHistory($id){
        $this->db_readonly->select("aemh.action,aemh.action_by, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName");
        $this->db_readonly->from('assessments_entities_marks_students_history aemh');
        $this->db_readonly->where('aemh.assessments_entities_marks_students_id', $id);
        $this->db_readonly->join('avatar a','a.id=aemh.action_by');
        $this->db_readonly->join('staff_master sm','sm.id=a.stakeholder_id');
        return $this->db_readonly->get()->result();
    }

    public function getMarksCards($classId=0){
        $this->db_readonly->select('amct.id, amct.template_name, amct.generation_type, amct.template_background, amct.class_id, c.class_name as className, amct.assessments, amct.lock_remarks, amct.publish_status');
        $this->db_readonly->join('class c', 'amct.class_id=c.id');
        $this->db_readonly->where('c.acad_year_id', $this->yearId);
        if ($classId) {
            $this->db_readonly->where('class_id', $classId);
        }
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->order_by('c.id');
        return $this->db_readonly->get('assessment_marks_card_templates amct')->result();
    }

    public function getMarksCard($tempId) {
        return $this->db_readonly->select('at.*,c.id as classId, c.class_name')->from('assessment_marks_card_templates at')->join('class c', 'c.id=at.class_id')->where('at.id', $tempId)->where('c.acad_year_id', $this->yearId)->get()->row();
    }

    public function deleteMarksCard($template_id) {
        $this->db->trans_start();
        $this->db->where('id', $template_id)->delete('assessment_marks_card_templates');
        $card = $this->db->select('id')->where('marks_card_temp_id', $template_id)->get('assessments_marks_cards')->row();
        if(!empty($card)) {
            $this->db->where('marks_card_temp_id', $card->id)->delete('assessments_marks_cards');
            $this->db->where('ass_marks_card_id', $card->id)->delete('assessments_marks_card_history');
        }
        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    public function getMarksCardById($tempId){
        return $this->db_readonly->select('*')->where('id', $tempId)->get('assessment_marks_card_templates')->row();
    
    }

    public function addPictures($pictures, $marks_card_id, $stdId) {
        $res = $this->db->select('id, pictures')->where('marks_card_temp_id', $marks_card_id)->where('student_id', $stdId)->get('assessments_marks_cards')->row();
        // echo "<pre>"; print_r($res); die();
        if(empty($res)) {
            $data = array(
                'student_id' => $stdId,
                'remarks' => '',
                'created_by' => $this->authorization->getAvatarId(),
                'marks_card_temp_id' => $marks_card_id,
                'pictures' => json_encode($pictures)
            );
            return $this->db->insert('assessments_marks_cards', $data);
        } else {
            $old_pictures = json_decode($res->pictures);
            $new_pictures = array();
            $new_pictures = $pictures;
            foreach ($pictures as $key => $pic) {
                if($pic['path'] == '') {
                    if (isset($old_pictures[$key]) && isset($old_pictures[$key]->path)) {
                        $pic['path'] = $old_pictures[$key]->path;
                    } else {
                        $pic['path'] = '';
                    }
                }
                $new_pictures[$key] = $pic;
            }
            $picture_json = json_encode($new_pictures);
            // echo "<pre>"; print_r($new_pictures); die();
            return $this->db->where('id', $res->id)->update('assessments_marks_cards', array('pictures' => $picture_json));
        }
    }

    public function getSecStudents($secId, $tempId){
        $display_roll_no_with_student_name = $this->settings->getSetting('display_roll_no_with_student_name');
        if ($display_roll_no_with_student_name == 1) {
          $std_name = "CONCAT(sy.roll_no, ' - ', ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as stdName";
        }
         $showAlumni = $this->settings->getSetting('examination_show_alumni_students_in_marks_entry');

            if ($showAlumni) {
                $student_status_arr = [2, 4, 5]; // Include active + alumni
                $promotion_status_arr = ['JOINED']; // Filter out only current students
            } else {
                $student_status_arr = [2]; // Only active students
                $promotion_status_arr = ['JOINED', '4', '5']; // Exclude alumni
            }
        $this->db_readonly->select("s.id, sy.roll_no,s.admission_no, $std_name, am.status, ah.pdf_link, am.id as amId, cs.section_name, cs.id as csId, am.remark_status, am.ack_status, ah.status as genStatus, ah.date, ah.id as cardId, am.published, DATE_FORMAT(DATE_ADD(DATE_ADD(date, INTERVAL 5 HOUR), INTERVAL 30 MINUTE),'%d %b %H:%i') as date, ah.is_error,ifnull(am.remarks,'') as prev_remarks");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->join('assessments_marks_cards am', 's.id=am.student_id and marks_card_temp_id='.$tempId, 'left');
        $this->db_readonly->join('assessments_marks_card_history ah', 'ah.id=am.active_marks_card_id', 'left');
        $this->db_readonly->join('class_section cs', 'cs.id=sy.class_section_id');
        $this->db_readonly->where_in('s.admission_status', $student_status_arr);
        $this->db_readonly->where_not_in('sy.promotion_status', $promotion_status_arr);
        $this->db_readonly->where('sy.class_section_id', $secId);
        $this->db_readonly->where('sy.acad_year_id', $this->yearId);
        $this->db_readonly->order_by('sy.roll_no, s.first_name');
        return $this->db_readonly->get()->result();
    }

    public function getSectionIdIfClassTeacher() {
        $avatarId = $this->authorization->getAvatarId();
        $sql = "select cs.id as csId from class_section cs where cs.class_teacher_id = (select stakeholder_id from avatar a where avatar_type=4 and a.id=$avatarId)";

        $result = $this->db_readonly->query($sql)->result();

        $ret = array();
        foreach ($result as $key => $value) {
            $ret[] = $value->csId;
        }
        return $ret;
    }

    public function lockRemarksEntry(){
        $id = $_POST['mId'];
        $classId = $_POST['classId'];
        $status = $_POST['status'];
        $this->db->where('id', $id);
        $this->db->where('class_id', $classId);
        return $this->db->update('assessment_marks_card_templates', array('lock_remarks' => $status));
    }

    public function publishMarskCard(){
        $id = $_POST['mId'];
        $classId = $_POST['classId'];
        $status = $_POST['status'];
        $this->db->where('id', $id);
        $this->db->where('class_id', $classId);
        return $this->db->update('assessment_marks_card_templates', array('publish_status' => $status));
    }

    public function getAssmtDetails($classId){
        $this->db_readonly->select('id, short_name');
        $this->db_readonly->from('assessments as');
        $this->db_readonly->where('class_id', $classId);
        $this->db_readonly->where('as.acad_year_id', $this->yearId);
        return $this->db_readonly->get()->result();
    }

    public function getSectionsFromClass($classId) {
        $sections = $this->db_readonly->select('id as csId, section_name as csName')->where('class_id', $classId)->get('class_section')->result();
        return $sections;
    }

    public function getAckReport(){
        $sectionId = $_POST['sectionId'];
        $cardId = $_POST['cardId'];

        $this->db_readonly->select("amc.ack_status,amc.ack_by, sy.id as stduent_id, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) as stdName, amc.ack_on, amc.published, ifnull(amc.status,'Not Generated') as gen_status");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->join("assessments_marks_cards amc", "s.id=amc.student_id and amc.marks_card_temp_id='$cardId'", "left");
        $this->db_readonly->where('sy.class_section_id', $sectionId);
        $this->db_readonly->order_by('sy.roll_no, s.first_name');
        $students = $this->db_readonly->get()->result();

        $this->db_readonly->select("amc.student_id,CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as parentName");
        $this->db_readonly->from('assessments_marks_cards amc');
        $this->db_readonly->join('avatar av', 'av.id=amc.ack_by');
        $this->db_readonly->join('parent p', 'av.stakeholder_id=p.id');
        $this->db_readonly->where('av.avatar_type', 2);
        $this->db_readonly->where('amc.marks_card_temp_id', $cardId);
        $parents = $this->db_readonly->get()->result();

        $pCount = 0;
        foreach ($students as $s => $student) {
            $student->status = 'Not Seen';
            $ack = '-';
            $student->parentName = '-';
            foreach ($parents as $p => $parent) {
                if($student->ack_status == 1) {
                    $student->status = 'Seen';
                }
                $student->parentName = '';
                if($student->stduent_id == $parent->student_id) {
                    $student->parentName = $parent->parentName;
                    $ack = date('d-m-Y H:i a', strtotime($student->ack_on));
                    $pCount++;
                }
            }
            $student->ack_on = $ack;
        }
        return array('students' => $students, 'count'=>$pCount);
        // echo "<pre>"; print_r($students); die();
    }

    public function getRemarksReport(){
        $cardId = $_POST['cardId'];
        $sectionId = $_POST['sectionId'];
        $this->db_readonly->select("amc.status,CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) as stdName, amc.remarks, amc.additional_remarks");
        $this->db_readonly->from('student_admission s');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db_readonly->join("assessments_marks_cards amc", "s.id=amc.student_id and amc.marks_card_temp_id='$cardId'", "left");
        $this->db_readonly->where('sy.class_section_id', $sectionId);
        $this->db_readonly->where('s.admission_status', '2');
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->order_by('sy.roll_no, s.first_name');
        $students = $this->db_readonly->get()->result();
        return $students;
        // echo "<pre>"; print_r($students); die();
    }

    public function getStdObservations($stdId){
        return $this->db_readonly->select("observation")
        ->where('std_id', $stdId)
        ->get('student_observation')->result();
    }

    public function getRemarks($tempId, $stdId) {
        return $this->db_readonly->select('remarks, additional_remarks, remark_status, pictures')->where('marks_card_temp_id', $tempId)->where('student_id', $stdId)->get('assessments_marks_cards')->row();
    }

    public function getAdjacentStudents($stdId, $level){
        
        $currStd = $this->db_readonly->select("s.id as sid, s.first_name as name, sy.roll_no as rollNo, sy.class_section_id as csId")
            ->from('student_admission s')
            ->join('student_year sy', 'sy.student_admission_id=s.id')
            ->where('s.id', $stdId)
            ->where('sy.acad_year_id', $this->yearId)
            ->get()->row();

        $this->db_readonly->select("sa.id as sid");
        $this->db_readonly->from('student_year s');
        $this->db_readonly->join('student_admission sa', 'sa.id=s.student_admission_id');
        $this->db_readonly->limit(1);
        $this->db_readonly->where('s.class_section_id', $currStd->csId);
        $this->db_readonly->where('sa.admission_status', '2');
        $this->db_readonly->where('s.promotion_status!=', '4');
        $this->db_readonly->where('s.promotion_status!=', '5');
        if($level == 'prev'){
            $this->db_readonly->where('s.roll_no <', $currStd->rollNo);
            $this->db_readonly->order_by('s.roll_no desc');
        } else if($level == 'next') {
            $this->db_readonly->where('s.roll_no >', $currStd->rollNo);
            $this->db_readonly->order_by('s.roll_no asc');
        }
        $result = $this->db_readonly->get()->row();
        // echo '<pre>'; print_r($result); die();
        if (empty($result)) {
            $data = array('studentid' => $stdId,'roll_no'=>0);
            return $data;
        }

         $data= array('studentid' => $result->sid,'roll_no'=>1);
        return $data;
    }

    public function sortAssessments($assIds){
        $this->db_readonly->select('id, short_name as name');
        $this->db_readonly->where_in('id', $assIds);
        $this->db_readonly->order_by('sorting_order');
        return $this->db_readonly->get('assessments')->result();
    }

    public function getReportCardMarksData($assessment_ids, $student_id){
        $this->db_readonly->select('a.id as assessment_id, a.short_name as assessment_name, ae.id, e.id as entity_id, e.grading_system_id, e.name as entity_name, ae.total_marks, aems.marks, aems.grade, e.evaluation_type');
        $this->db_readonly->from('assessments a');
        $this->db_readonly->join('assessments_entities ae', 'ae.assessment_id=a.id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group eg', 'e.ass_entity_gid=eg.id');
        $this->db_readonly->join('assessment_elective_group aeg', 'eg.elective_group_id=aeg.id', 'left');
        $this->db_readonly->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id=ae.id', 'left');
        $this->db_readonly->where_in('a.id', $assessment_ids);
        $this->db_readonly->where('aems.student_id', $student_id);
        $this->db_readonly->order_by('eg.sorting_order, e.sorting_order, a.id');
        $result = $this->db_readonly->get()->result();

        $marks = array();

        foreach ($result as $key => $res) {
            if(!array_key_exists($res->entity_id, $marks)) {
                $marks[$res->entity_id] = array();
                $marks[$res->entity_id]['entity_name'] = $res->entity_name;
                $marks[$res->entity_id]['evaluation_type'] = $res->evaluation_type;
            }
            $grade = '-';
            $obtained_marks = '-';
            if($res->marks == -1) {
                $grade = 'AB';
                $obtained_marks = 'AB';
            } else {
                if($res->total_marks !=0){
                    $grade = ($res->evaluation_type == 'grade')?$res->grade:$this->__calculateGradeNew(($res->marks/$res->total_marks)*100, $res->entity_id, 'component');
                }
                $obtained_marks = ($res->marks == -2)?'-':$res->marks;
            
            }
            $marks[$res->entity_id][$res->assessment_id] = array(
                'marks' => $obtained_marks,
                'total_marks' => $res->total_marks,
                'grade' => $grade
            );
        }
        return $marks;
        // echo "<pre>"; print_r($marks);die();
    }

    public function getGradesByEntity($entityId) {
        return $this->db_readonly->select('grades')->where("id in (select grading_system_id from assessment_entity_master where id='$entityId')")->get('assessment_grading_system')->row();
    }

    public function getGradesByGroup($groupId){
        return $this->db_readonly->select('grades')->where("id in (select grading_system_id from assessment_entities_group where id='$groupId')")->get('assessment_grading_system')->row();
    }

    public function __calculateGradeNew($pm, $entityId, $type, $nameRequired='short_name') {
        if($type == 'component')
            $gData = $this->getGradesByEntity($entityId);
        else if($type == 'group')
            $gData = $this->getGradesByGroup($entityId);
        if(empty($gData)) {
            return -1;
        }
        $grades = json_decode($gData->grades);
        foreach ($grades as $key => $value) {
            if($pm >= $value->from && $pm <= $value->to) {
                if ($nameRequired == 'long_name') {
                    return $value->long_name;
                } else if($nameRequired == 'short_name'){
                    return $value->grade;
                } else if($nameRequired == 'grade_point'){
                    return (isset($value->grade_point))?$value->grade_point:'';
                }
            }
        }
    }

    // public function getReportBystdId($assId, $stdId){
    //     $this->db_readonly->select('ae.id, e.id as aemId,e.ass_type,e.grading_system_id as eGSId, eg.grading_system_id as gGSId, e.name,e.mapping_string as eString, ae.total_marks,eg.id as gid, eg.entity_name as gName, eg.mapping_string as gString, aeg.id as group_id, aeg.group_name,eg.entity_name as eleName, eg.is_elective, aeg.mapping_string as elMapString');
    //     $this->db_readonly->from('assessments_entities ae');
    //     $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
    //     $this->db_readonly->join('assessment_entities_group eg', 'e.ass_entity_gid=eg.id');
    //     $this->db_readonly->join('assessment_elective_group aeg', 'eg.elective_group_id=aeg.id', 'left');
    //     $this->db_readonly->where('ae.assessment_id', $assId);
    //     $this->db_readonly->order_by('eg.sorting_order, e.sorting_order');
    //     $result = $this->db_readonly->get()->result();
    //     $mArray = array();
    //     $tArray = array();

    //     foreach ($result as $key => $value) {
    //         $std = $this->db_readonly->select('marks')->where('assessments_entities_id', $value->id)->where('student_id', $stdId)->get('assessments_entities_marks_students')->result();
            
    //         if($value->ass_type != 'Derived') {
    //             if(!array_key_exists($value->gid, $mArray)) {
    //                 $mArray[$value->gid] = 0;
    //                 $isAdded[$value->gid] = 0;
    //                 $gCount[$value->gid] = 1;
    //                 $isAbsentInOne[$value->gid] = 0;
    //             } else {
    //                 $gCount[$value->gid]++;
    //             }
    //         }

    //         if(!array_key_exists($value->gid, $tArray))
    //             $tArray[$value->gid] = 0;

    //         // $value->mString = 'g_'.$value->mString;
    //         if($value->ass_type != 'Derived'){
    //             $tArray[$value->gid] += $value->total_marks;
    //         }
    //         $value->entityElective = '';
    //         if(!empty($std)) {
    //             $value->marks = $std->marks;
    //             //adding marks to group only if not absent and the subject is not derived
    //             if($value->ass_type != 'Derived' && $std->marks >= 0){
    //                 $isAdded[$value->gid] = 1; // setting value to indicate group has marks
    //                 $mArray[$value->gid] += $std->marks;// adding marks of all the entities in a group
    //             } 
    //             if($std->marks == '-1.00') {
    //                 $isAbsentInOne[$value->gid]++;
    //             }
    //             // $tArray[$value->gid] += $value->total_marks;//adding max marks of all entities within group
    //             $var1 = explode('_', $value->eString);
    //             array_shift($var1);
    //             $str = implode('_', $var1);
    //             // $var2 = explode('%%', $value->elMapString);
    //             $var2 = substr($value->elMapString, 0, -2);
    //             $value->entityElective = $var2.'_'.$str;//this is the mapping string for entityelective (eg. %%kannada_sanskrit_reading_pronounciation%%)
    //             // echo $str; echo "<pre>"; print_r($var2);die();
    //         }
    //         else {
    //             if($value->is_elective == 1) {
    //                 $value->elMapString = ''; // skip group marks if elective is not choosen
    //             }
    //             $value->marks = ' - ';
    //         }
    //     }
    //     foreach ($result as $key => $value) {
    //         if(!$isAdded[$value->gid]) {//group marks is not added
    //             if($gCount[$value->gid] == $isAbsentInOne[$value->gid]) //for single entity with group marks not added and is absent
    //                 $value->gMarks = '-1.00';
    //             else
    //                 $value->gMarks = ' - ';
    //         } else{ //group marks is present
    //             $value->gMarks = $mArray[$value->gid];
    //         }
    //         $value->gtotal = ($tArray[$value->gid])?$tArray[$value->gid]:' - ';
    //     }
    //      //echo "<pre>"; print_r($result);die();
    //     return $result;
    // }

    public function getReportBystdId($assId, $stdId) {
        $this->db_readonly->select('ae.id, e.id as aemId, e.ass_type, e.grading_system_id as eGSId, eg.grading_system_id as gGSId, 
            e.name, e.mapping_string as eString, ae.total_marks, eg.id as gid, eg.entity_name as gName, 
            eg.mapping_string as gString, aeg.id as group_id, aeg.group_name, eg.entity_name as eleName, 
            eg.is_elective, aeg.mapping_string as elMapString');
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group eg', 'e.ass_entity_gid=eg.id');
        $this->db_readonly->join('assessment_elective_group aeg', 'eg.elective_group_id=aeg.id', 'left');
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->order_by('eg.sorting_order, e.sorting_order');
        $result = $this->db_readonly->get()->result();
       
    
        $entityIds = array_map(function ($item) {
            return $item->id;
        }, $result);
       
        $marksData = $this->db_readonly->select('marks,assessments_entities_id')
            ->where_in('assessments_entities_id', $entityIds)
            ->where('student_id', $stdId)
            ->get('assessments_entities_marks_students')
            ->result();
            
        $marksMap = [];
        foreach ($marksData as $mark) {
            $marksMap[$mark->assessments_entities_id] = $mark->marks;
        }
         
        $mArray = [];
        $tArray = [];
        $isAdded = [];
        $gCount = [];
        $isAbsentInOne = [];
       
        // Process the main data
        foreach ($result as $value) {
            if (array_key_exists($value->id, $marksMap)) {
                $stdMarks = $marksMap[$value->id];
            } else {
                $stdMarks = '';
            }
    
            if ($value->ass_type != 'Derived') {
                if (!array_key_exists($value->gid, $mArray)) {
                    $mArray[$value->gid] = 0;
                    $isAdded[$value->gid] = 0;
                    $gCount[$value->gid] = 1;
                    $isAbsentInOne[$value->gid] = 0;
                } else {
                    $gCount[$value->gid]++;
                }
            }
          
           
    
            if (!array_key_exists($value->gid, $tArray)) {
                $tArray[$value->gid] = 0;
            }
    
            if ($value->ass_type != 'Derived') {
                $tArray[$value->gid] += $value->total_marks;
            }
    
            $value->entityElective = '';
    
            if (!empty($stdMarks)) {
                $value->marks = $stdMarks;
    
                if ($value->ass_type != 'Derived' && $stdMarks >= 0) {
                    $isAdded[$value->gid] = 1;
                    $mArray[$value->gid] += $stdMarks;
                }
    
                if ($stdMarks == '-1.00') {
                    $isAbsentInOne[$value->gid]++;
                }
    
                $var1 = explode('_', $value->eString);
                array_shift($var1);
                $str = implode('_', $var1);
                $var2 = substr($value->elMapString, 0, -2);
                $value->entityElective = $var2 . '_' . $str;
                
            } else {
                if ($value->is_elective == 1) {
                    $value->elMapString = '';
                }
                $value->marks = ' - ';
            }
        }
        foreach ($result as $value) {
            
            if (!$isAdded[$value->gid]) {
                if ($gCount[$value->gid] == $isAbsentInOne[$value->gid]) {
                    $value->gMarks = '-1.00';
                } else {
                    $value->gMarks = ' - ';
                }
            } else {
                $value->gMarks = $mArray[$value->gid];
            }
            $value->gtotal = $tArray[$value->gid];
        }
        return $result;
    }
    

    public function getSortedSubjects($classId) {
        $this->db_readonly->select('aem.id, aem.name');
        $this->db_readonly->from('assessment_entity_master aem');
        $this->db_readonly->join('assessment_entities_group aeg', 'aeg.id = aem.ass_entity_gid');
        $this->db_readonly->where('aem.class_id', $classId);
        $this->db_readonly->where('aeg.class_id', $classId);
        $this->db_readonly->order_by('aeg.sorting_order, aem.sorting_order');
        $x= $this->db_readonly->get()->result();

        // echo '<pre>'; print_r($x); die();
        return $x;
    }

    public function checkVerified($tempId, $stdId){
        return $this->db_readonly->select('remark_status as status')->where('marks_card_temp_id',$tempId)->where('student_id', $stdId)->get('assessments_marks_cards')->row();
    }

    public function getMarksDetails($assName){
        $ass = $this->db_readonly->select('id,short_name, generation_type, formula')->where('short_name', $assName)->order_by('sorting_order')->get('assessments')->row();
        $assIds = array();
        if($ass->generation_type == 'Auto') {
            $json = json_decode($ass->formula);
            foreach ($json->assessments as $key => $value) {
                $assIds[$value->id] = $value->name;
            }
        }
        $assIds[$ass->id] = $ass->short_name;
        return $assIds;
        // echo "<pre>";print_r($assIds);die();
    }

    public function unlockRemarks($tempId, $stdId){
        $data = array(
            'status' => '',
            'remark_status' => 'Not Verified'
        );
        $this->db->where('student_id', $stdId);
        $this->db->where('marks_card_temp_id', $tempId);
        return $this->db->update('assessments_marks_cards', $data);
    }

    public function getStdParentNames($stdId){
        $this->db_readonly->select("CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as name, sr.relation_type, ifnull(p.mobile_no, '') as mobile_no");
        $this->db_readonly->from('parent p');
        $this->db_readonly->join('student_relation sr', 'p.id=sr.relation_id');
        $this->db_readonly->where('sr.std_id', $stdId);
        // $this->db_readonly->where('sr.relation_type', 'Father');
        return $this->db_readonly->get()->result();
    }

    public function getStdAddress($stdId) {
         $result = $this->db_readonly->select("*")->where('stakeholder_id', $stdId)->where('avatar_type', 1)->get('address_info')->result();
        
        $data = array(
            'permanent_address' => '',
            'present_address' => ''
        );
        
        foreach ($result as $key => $val) {
            $address = formatAddress($val);
            $address = preg_replace('/[\s,\-]+$/', '', $address);
            $address = preg_replace('/\s*,\s*,\s*/', ', ', $address);
            $address = preg_replace('/\s+/', ' ', $address);
            
            if($val->address_type == 1) {
                $data['permanent_address'] = trim($address);
            } else {
                $data['present_address'] = trim($address); 
            }
        }
        return $data;
    }
    
    public function getPrincipalObject($classId) {
        $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as principal_name , ifnull(sm.staff_signature,'') as principal_signature");
        $this->db_readonly->from('class c');
        $this->db_readonly->join('staff_master sm', 'sm.id=c.principal_id');
        $this->db_readonly->where('c.id', $classId);
        $data = $this->db_readonly->get()->row();
        return $data;       
    }

    public function getStudentReportCardMarks($assIds, $stdId, $section_id, $generate_type = "") {
        $assessmentIds = implode(",", $assIds);
        $sql1 = "select a.short_name as assName,null as stdId, null as marks, null as grade, ae.assessment_id as assId, ae.id, e.id as aemId,e.ass_type,e.grading_system_id as eGSId,e.evaluation_type, eg.grading_system_id as gGSId, if(e.ass_type = 'Derived' AND e.short_name IS NOT NULL, e.short_name, e.name) as name,e.mapping_string as eString, ae.total_marks,eg.id as gid, eg.entity_name as gName, eg.mapping_string as gString,  ae.class_average, ae.class_highest, ae.section_average_highest, asr.subject_remarks, eg.is_elective, '' as eleName, eg.subject_code, '' as elMapString, '' as eleDisplayName 
            from assessments_entities ae 
            join assessment_entity_master e on ae.entity_id=e.id 
            join assessment_entities_group eg on e.ass_entity_gid=eg.id 
            join assessments a on a.id=ae.assessment_id 
            left join assessment_subject_remarks asr on (asr.subject_id=eg.id and asr.student_id=$stdId and asr.assessment_id=a.id) 
            where ae.assessment_id in ($assessmentIds) and eg.is_elective=0";
        $result1 = $this->db_readonly->query($sql1)->result();

        $sql2 = "select a.short_name as assName,null as stdId, null as marks, null as grade, ae.assessment_id as assId, ae.id, e.id as aemId,e.ass_type,e.grading_system_id as eGSId,e.evaluation_type, eg.grading_system_id as gGSId, if(e.ass_type = 'Derived' AND e.short_name IS NOT NULL, e.short_name, e.name) as name,e.mapping_string as eString, ae.total_marks,eg.id as gid, eg.entity_name as gName, eg.mapping_string as gString,  ae.class_average, ae.class_highest, ae.section_average_highest, asr.subject_remarks, eg.subject_code, aeg.id as group_id, aeg.group_name,eg.entity_name as eleName, eg.display_name as eleDisplayName, eg.is_elective, aeg.mapping_string as elMapString  
            from assessments_entities ae 
            join assessment_entity_master e on ae.entity_id=e.id 
            join assessment_entities_group eg on e.ass_entity_gid=eg.id 
            join assessment_elective_group aeg on eg.elective_group_id=aeg.id 
            join assessments a on a.id=ae.assessment_id 
            join assessment_students_elective ase on ase.ass_entity_gid=eg.id and ase.student_id=$stdId 
            left join assessment_subject_remarks asr on (asr.subject_id=eg.id and asr.student_id=$stdId and asr.assessment_id=a.id) 
            where ae.assessment_id in ($assessmentIds) and eg.is_elective=1 
            group by ae.id";
        $result2 = $this->db_readonly->query($sql2)->result();

        $result = $result1;
        if(!empty($result) && !empty($result2)) {
            $result = array_merge($result, $result2);
        }

        $sql2 = "select aems.student_id as stdId, marks, entity_grade_rank, entity_section_rank, ae.id, aems.grade
                from assessments_entities ae 
                join assessments_entities_marks_students aems on ae.id=aems.assessments_entities_id 
                where aems.student_id=$stdId and ae.assessment_id in ($assessmentIds)";
        $result2 = $this->db_readonly->query($sql2)->result();
        // echo '<pre>'; print_r($result2);
        $marksResult = array();
        foreach ($result2 as $key => $value) {
            $result2[$value->id] = $value;
            $marksResult[$value->id] = $value;
        }

        foreach ($result as $key => $value) {
            if(array_key_exists($value->id, $marksResult)) {
                $value->marks = $marksResult[$value->id]->marks;
                $value->stdId = $marksResult[$value->id]->stdId;
                $value->grade = $marksResult[$value->id]->grade;
                $value->entity_grade_rank = $marksResult[$value->id]->entity_grade_rank;
                $value->entity_section_rank = $marksResult[$value->id]->entity_section_rank;
            }
        }

        $mArray = array();
        $tArray = array();
        foreach ($result as $key => $value) {
            if(!array_key_exists($value->assName, $mArray)) {
                $mArray[$value->assName] = array();
                $isAdded[$value->assName] = array();
                $derivedMarks[$value->assName] = array();
                $derivedCount[$value->assName] = array();
                $derivedTotal[$value->assName] = array();
                $gCount[$value->assName] = array();
                $isAbsentInOne[$value->assName] = array();
                $isNAInOne[$value->assName] = array();
                $tArray[$value->assName] = array();
            }
            if($value->ass_type != 'Derived') {
                if(!array_key_exists($value->gid, $mArray[$value->assName])) {
                    $mArray[$value->assName][$value->gid] = 0;
                    $isAdded[$value->assName][$value->gid] = 0;
                    $gCount[$value->assName][$value->gid] = 1;
                    $isAbsentInOne[$value->assName][$value->gid] = 0;
                    $isNAInOne[$value->assName][$value->gid] = 0;
                    $derivedMarks[$value->assName][$value->gid] = 0;
                    $derivedCount[$value->assName][$value->gid] = 0;
                    $derivedTotal[$value->assName][$value->gid] = 0;
                } else {
                    $gCount[$value->assName][$value->gid]++;
                }
            } else {
                if(!array_key_exists($value->gid, $mArray[$value->assName])) {
                    $mArray[$value->assName][$value->gid] = 0;
                    $isAdded[$value->assName][$value->gid] = 0;
                    $gCount[$value->assName][$value->gid] = 0;
                    $isAbsentInOne[$value->assName][$value->gid] = 0;
                    $isNAInOne[$value->assName][$value->gid] = 0;
                    $derivedMarks[$value->assName][$value->gid] = 0;
                    $derivedCount[$value->assName][$value->gid] = 0;
                    $derivedTotal[$value->assName][$value->gid] = 0;
                }
            }

            if(!array_key_exists($value->gid, $tArray[$value->assName]))
                $tArray[$value->assName][$value->gid] = 0;

            // $value->mString = 'g_'.$value->mString;
            $value->entityElective = '';
            if($value->stdId != '') {
                //adding marks to group only if not absent and the subject is not derived
                if($value->ass_type != 'Derived' && $value->marks >= 0){
                    $isAdded[$value->assName][$value->gid] = 1; // setting value to indicate group has marks
                    $mArray[$value->assName][$value->gid] += $value->marks;// adding marks of all the entities in a group
                } else if($value->ass_type == 'Derived'){
                    $isAdded[$value->assName][$value->gid] = 1;
                    $mArray[$value->assName][$value->gid] += 0;
                    $derivedMarks[$value->assName][$value->gid] = $value->marks;
                    $derivedCount[$value->assName][$value->gid]++;
                }
                if($value->marks == '-1.00') {
                    $isAbsentInOne[$value->assName][$value->gid]++;
                }
                if($value->marks == '-3.00') {
                    $isNAInOne[$value->assName][$value->gid]++;
                }
                $var1 = explode('_', $value->eString);
                array_shift($var1);
                $str = implode('_', $var1);

                $var2 = substr($value->elMapString, 0, -2);
                $value->entityElective = $var2.'_'.$str;

            }
            else {
                if($value->is_elective == 1) {
                    // $value->elMapString = ''; // skip group marks if elective is not choosen
                    $value->marks = 'not_elected';
                    $value->gMarks = 'not_elected';
                } else {
                    $value->marks = 'not_added';
                    $value->gMarks = 'not_added';
                }
            }
            if($value->ass_type != 'Derived'){
                $tArray[$value->assName][$value->gid] += $value->total_marks;
            } else {
                $derivedTotal[$value->assName][$value->gid] = $value->total_marks;
            }
            if($derivedCount[$value->assName][$value->gid]) {
                $tArray[$value->assName][$value->gid] = 0;
            }
        }
        foreach ($result as $key => $value) {
            if(!$isAdded[$value->assName][$value->gid]) {//group marks is not added
                if($gCount[$value->assName][$value->gid] == $isAbsentInOne[$value->assName][$value->gid]) //for single entity with group marks not added and is absent
                    $value->gMarks = '-1.00';
                else if($gCount[$value->assName][$value->gid] == $isNAInOne[$value->assName][$value->gid])
                    $value->gMarks = '-3.00';
            } else{ //group marks is present
                $value->gMarks = $mArray[$value->assName][$value->gid];
                if($isAdded[$value->assName][$value->gid] && $derivedMarks[$value->assName][$value->gid]) {
                    $value->gMarks = ($derivedCount[$value->assName][$value->gid]==1)?$derivedMarks[$value->assName][$value->gid]:$value->gMarks;
                } 
            }
            $value->gtotal = ($tArray[$value->assName][$value->gid])?$tArray[$value->assName][$value->gid]:(($derivedTotal[$value->assName][$value->gid])?$derivedTotal[$value->assName][$value->gid]:' - ');
        }

        //Split the average and highest of the section
        foreach ($result as $key => $std_data_obj) {
            if (empty($std_data_obj->section_average_highest)) {
                $std_data_obj->section_highest = -1;
                $std_data_obj->section_average = -1;
                continue;
            }
            $section_values = json_decode($std_data_obj->section_average_highest);
            foreach ($section_values as $val) {
                if ($val->section_id == $section_id) {
                    $std_data_obj->section_highest = $val->highest;
                    $std_data_obj->section_average = $val->average;
                    continue;
                }
            }
        }
        // usort($result, function ($a, $b) {
        //     return $a->marks > $b->marks ? -1 : 1;
        // });

        usort($result, function($a, $b){
            return strcmp($a->marks, $b->marks);
        });

        if ($generate_type == 'dummy_marks') {
            foreach ($result as $res) {
                $min = 1;
                $entity_max = ($res->total_marks > 0) ? $res->total_marks : 100;
                $group_max = ($res->gtotal > 0) ? $res->gtotal : 100;

                $res->marks = mt_rand($min, $entity_max);
                $res->grade = 'A';
                $res->total_marks = $entity_max;
                $res->class_average = mt_rand($min, $entity_max);
                $res->class_highest = mt_rand($min, $entity_max);
                $res->section_average_highest = mt_rand($min, $entity_max);
                $res->subject_remarks = "Dummy Remarks";
                $res->entity_grade_rank = mt_rand($min, $entity_max);
                $res->entity_section_rank = mt_rand($min, $entity_max);
                $res->gMarks = mt_rand($min, $group_max);
                $res->gtotal = $group_max;
                $res->section_highest = mt_rand($min, $entity_max);
                $res->section_average = mt_rand($min, $entity_max);
            }
        }

        // echo "<pre>"; print_r($result); die();
        return $result;
    }
    
    public function get_students_data_with_rank($marks_template_id, $class_id) {
        $result = $this->db_readonly->select("CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name, amc.total_marks, amc.section_rank, amc.grade_rank, CONCAT(cs.class_name, cs.section_name) as class_section_name")
            ->from('student_admission sa')
            ->join('student_year sy', "sy.student_admission_id=sa.id and sy.class_id=$class_id")
            ->join('class_section cs', "sy.class_section_id=cs.id")
            ->join('assessments_marks_cards amc', "amc.student_id=sa.id and amc.marks_card_temp_id=$marks_template_id")
            ->where('sy.acad_year_id', $this->yearId)
            ->order_by('cs.display_order, sa.first_name')
            ->get()->result();

        // echo '<pre>';print_r($result);die();

        return $result;
    }

    public function get_assessments_for_ranking($class_id) {
        $assessments = $this->db_readonly->select('id as assessment_id, long_name as assessment_name, generation_type')
            ->from('assessments ass')
            ->where('class_id', $class_id)
            ->get()->result();

        $assessment_entities = $this->db_readonly->select('ae.id as ae_id, ae.assessment_id, ent.id as entity_id, ent.name as entity_name')
        ->from('assessments_entities ae')
        ->join('assessment_entity_master ent', 'ent.id=ae.entity_id')
        ->where('class_id', $class_id)
        ->get()->result();

        $ass_objects = array();
        foreach ($assessment_entities as $ent) {
            $ass_objects[$ent->assessment_id][] = $ent;
        }

        foreach ($assessments as $ass) {
            $ass->entities = $ass_objects[$ass->assessment_id];
        }

        return $assessments;
    }

    public function getStdArrayData($assIds, $stdId){
        $assessmentIds = implode(",", $assIds);
        
        $sql1 = "select a.short_name as assName,null as stdId, null as marks, null as grade, ae.assessment_id as assId, ae.id, e.id as aemId,e.ass_type,e.grading_system_id as eGSId,e.evaluation_type, eg.grading_system_id as gGSId, e.name,e.mapping_string as eString, ae.total_marks,eg.id as gid, eg.entity_name as gName, eg.mapping_string as gString, aeg.id as group_id, aeg.group_name,eg.entity_name as eleName, eg.is_elective, aeg.mapping_string as elMapString, ae.class_average, ae.class_highest, asr.subject_remarks, eg.subject_code 
            from assessments_entities ae 
            join assessment_entity_master e on ae.entity_id=e.id 
            join assessment_entities_group eg on e.ass_entity_gid=eg.id 
            left join assessment_elective_group aeg on eg.elective_group_id=aeg.id 
            join assessments a on a.id=ae.assessment_id 
            left join assessment_subject_remarks asr on (asr.subject_id=eg.id and asr.student_id=$stdId and asr.assessment_id=a.id) 
            where ae.assessment_id in ($assessmentIds)";
        $result = $this->db_readonly->query($sql1)->result();

        $sql2 = "select aems.student_id as stdId, marks, ae.id, aems.grade
                from assessments_entities ae 
                join assessments_entities_marks_students aems on ae.id=aems.assessments_entities_id
                where aems.student_id=$stdId and ae.assessment_id in ($assessmentIds)";
        $result2 = $this->db_readonly->query($sql2)->result();
        $marksResult = array();
        foreach ($result2 as $key => $value) {
            $result2[$value->id] = $value;
            $marksResult[$value->id] = $value;
        }

        foreach ($result as $key => $value) {
            if(array_key_exists($value->id, $marksResult)) {
                $value->marks = $marksResult[$value->id]->marks;
                $value->stdId = $marksResult[$value->id]->stdId;
                $value->grade = $marksResult[$value->id]->grade;
            }
        }
        // echo "<pre>"; print_r($result); die();

        $mArray = array();
        $tArray = array();
        foreach ($result as $key => $value) {
            if(!array_key_exists($value->assName, $mArray)) {
                $mArray[$value->assName] = array();
                $isAdded[$value->assName] = array();
                $derivedMarks[$value->assName] = array();
                $derivedCount[$value->assName] = array();
                $derivedTotal[$value->assName] = array();
                $gCount[$value->assName] = array();
                $isAbsentInOne[$value->assName] = array();
                $isNAInOne[$value->assName] = array();
                $tArray[$value->assName] = array();
            }
            if($value->ass_type != 'Derived') {
                if(!array_key_exists($value->gid, $mArray[$value->assName])) {
                    $mArray[$value->assName][$value->gid] = 0;
                    $isAdded[$value->assName][$value->gid] = 0;
                    $gCount[$value->assName][$value->gid] = 1;
                    $isAbsentInOne[$value->assName][$value->gid] = 0;
                    $isNAInOne[$value->assName][$value->gid] = 0;
                    $derivedMarks[$value->assName][$value->gid] = 0;
                    $derivedCount[$value->assName][$value->gid] = 0;
                    $derivedTotal[$value->assName][$value->gid] = 0;
                } else {
                    $gCount[$value->assName][$value->gid]++;
                }
            } else {
                if(!array_key_exists($value->gid, $mArray[$value->assName])) {
                    $mArray[$value->assName][$value->gid] = 0;
                    $isAdded[$value->assName][$value->gid] = 0;
                    $gCount[$value->assName][$value->gid] = 0;
                    $isAbsentInOne[$value->assName][$value->gid] = 0;
                    $isNAInOne[$value->assName][$value->gid] = 0;
                    $derivedMarks[$value->assName][$value->gid] = 0;
                    $derivedCount[$value->assName][$value->gid] = 0;
                    $derivedTotal[$value->assName][$value->gid] = 0;
                }
            }

            if(!array_key_exists($value->gid, $tArray[$value->assName]))
                $tArray[$value->assName][$value->gid] = 0;

            // $value->mString = 'g_'.$value->mString;
            if($value->ass_type != 'Derived'){
                $tArray[$value->assName][$value->gid] += $value->total_marks;
            } else {
                $derivedTotal[$value->assName][$value->gid] = $value->total_marks;
            }
            $value->entityElective = '';
            if($value->stdId != '') {
                //adding marks to group only if not absent and the subject is not derived
                if($value->ass_type != 'Derived' && $value->marks >= 0){
                    $isAdded[$value->assName][$value->gid] = 1; // setting value to indicate group has marks
                    $mArray[$value->assName][$value->gid] += $value->marks;// adding marks of all the entities in a group
                } else if($value->ass_type == 'Derived'){
                    $isAdded[$value->assName][$value->gid] = 1;
                    $mArray[$value->assName][$value->gid] += 0;
                    $derivedMarks[$value->assName][$value->gid] = $value->marks;
                    $derivedCount[$value->assName][$value->gid]++;
                }
                if($value->marks == '-1.00') {
                    $isAbsentInOne[$value->assName][$value->gid]++;
                }
                if($value->marks == '-3.00') {
                    $isNAInOne[$value->assName][$value->gid]++;
                }
                $var1 = explode('_', $value->eString);
                array_shift($var1);
                $str = implode('_', $var1);

                $var2 = substr($value->elMapString, 0, -2);
                $value->entityElective = $var2.'_'.$str;

            }
            else {
                if($value->is_elective == 1) {
                    $value->elMapString = ''; // skip group marks if elective is not choosen
                    $value->marks = 'not_elected';
                    $value->gMarks = 'not_elected';
                } else {
                    $value->marks = 'not_added';
                    $value->gMarks = 'not_added';
                }
            }

        }
        foreach ($result as $key => $value) {
            if(!$isAdded[$value->assName][$value->gid]) {//group marks is not added
                if($gCount[$value->assName][$value->gid] == $isAbsentInOne[$value->assName][$value->gid]) //for single entity with group marks not added and is absent
                    $value->gMarks = '-1.00';
                else if($gCount[$value->assName][$value->gid] == $isNAInOne[$value->assName][$value->gid])
                    $value->gMarks = '-3.00';
            } else{ //group marks is present
                $value->gMarks = $mArray[$value->assName][$value->gid];
                if($isAdded[$value->assName][$value->gid] && $derivedMarks[$value->assName][$value->gid]) {
                    $value->gMarks = ($derivedCount[$value->assName][$value->gid]==1)?$derivedMarks[$value->assName][$value->gid]:$value->gMarks;
                } 
            }
            $value->gtotal = ($tArray[$value->assName][$value->gid])?$tArray[$value->assName][$value->gid]:(($derivedTotal[$value->assName][$value->gid])?$derivedTotal[$value->assName][$value->gid]:' - ');
        }
        // echo "<pre>"; print_r($result); die();
        return $result;
    }

    public function getClassTeacher($stdId){
        $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName");
        $this->db_readonly->from('student_admission sa');
        $this->db_readonly->join('student_year s', 's.student_admission_id=sa.id');
        $this->db_readonly->join('class_section cs', 's.class_section_id=cs.id');
        $this->db_readonly->join('staff_master sm', 'sm.id=cs.class_teacher_id');
        $this->db_readonly->where('sa.id', $stdId);
        return $this->db_readonly->get()->row();
    }

    private function _staffName($staffId) {
        return $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName, (case when sm.gender='F' then 'Ms' else 'Mr' end) as initial")
        ->where('id', "$staffId")->get('staff_master sm')->row();
    }

    public function getClassTeachers($section_id) {
        $result = $this->db_readonly->select('cs.class_teacher_id, cs.assistant_class_teacher_id, sm.staff_signature')->from('class_section cs')->join('staff_master sm', 'cs.class_teacher_id=sm.id')->where('cs.id', $section_id)->get()->row();
        $teachers = array(
            'class_teacher' => '',
            'assistant_class_teacher' => '',
            'staff_signature' => ''

        );
        if(!empty($result->class_teacher_id)) {
            $name = $this->_staffName($result->class_teacher_id);
            $teachers['class_teacher'] = $name->initial.'. '.$name->staffName;
            $teachers['class_teacher_woi'] = $name->staffName;
        } else {
            $teachers['class_teacher'] = '';
            $teachers['class_teacher_woi'] = '';
        }
        if(!empty($result->assistant_class_teacher_id)) {
            $name = $this->_staffName($result->assistant_class_teacher_id);
            $teachers['assistant_class_teacher'] = $name->initial.'. '.$name->staffName;
            $teachers['assistant_class_teacher_woi'] = $name->staffName;
        } else {
            $teachers['assistant_class_teacher'] = '';
            $teachers['assistant_class_teacher_woi'] = '';
        }
        if(!empty($result->staff_signature)){
            $teachers['staff_signature'] = $result->staff_signature;
        }else{
            $teachers['staff_signature'] = '';
        }
        

        return $teachers;
    }

    public function check_pdf_generated($path) {
        $result = $this->db_readonly->select('id, status, DATE_FORMAT(DATE_ADD(DATE_ADD(date, INTERVAL 5 HOUR), INTERVAL 30 MINUTE),"%d %b %H:%i") as date')->where('pdf_link', $path)->get('assessments_marks_card_history')->row();
        return $result;
    }

    public function update_html_in_table ($stdId, $tempId, $html, $does_errors_exist) {
        $mId = $this->db->select('active_marks_card_id')->where('student_id', $stdId)->where('marks_card_temp_id', $tempId)->get('assessments_marks_cards')->row();
        if($mId->active_marks_card_id) {
            $active_marks_card_id = $mId->active_marks_card_id;
            $this->db->where('id', $active_marks_card_id)->update('assessments_marks_card_history', ['sample_html' => $html, 'is_error' => $does_errors_exist]);
        }
        return 1;
    }

    public function get_generated_html($student_card_id) {
        $result = $this->db_readonly->select('sample_html')->where('id', $student_card_id)->get('assessments_marks_card_history')->row();
        return $result->sample_html;
    }

    public function reset_regen_status($template_id) {
        $sql = "select id from assessments_marks_card_history where id in (select active_marks_card_id from assessments_marks_cards where marks_card_temp_id=$template_id) and status=2";

        $marks_card_id_objs = $this->db_readonly->query($sql)->result();

        $update_array = [];
        foreach ($marks_card_id_objs as $mc_obj) {
            $update_array[] = array (
                "id" => $mc_obj->id,
                "status" => '0'
            );
        }
        if (empty($update_array)) return 1;
        $status = $this->db->update_batch('assessments_marks_card_history', $update_array, 'id');
    }    

    public function updateManualMarksCard($stdId, $tempId, $path){
        $mId = $this->db->select('id')->where('student_id', $stdId)->where('marks_card_temp_id', $tempId)->get('assessments_marks_cards')->row();
        $marks_card_id = 0;
        $this->db->trans_start();
        if(empty($mId)) {
            $data = array(
                'marks_card_temp_id' => $tempId,
                'student_id' => $stdId,
                'remarks' => '',
                'additional_remarks' => '',
                'created_by' => $this->authorization->getAvatarId(),
                'active_marks_card_id' => 0,
                'remark_status' => 'Verified'
            );
            $this->db->insert('assessments_marks_cards', $data);
            $marks_card_id = $this->db->insert_id();
        } else {
            $marks_card_id = $mId->id;
        }
        $mData = array(
                'ass_marks_card_id' => $marks_card_id,
                'pdf_link' => $path,
                'action_comment' => 'Generated',
                'status' => 1,
                'action_by' => $this->authorization->getAvatarId()
            );
        $this->db->insert('assessments_marks_card_history', $mData);
        $thisId = $this->db->insert_id();
        $this->db->where('id', $marks_card_id);
        $this->db->update('assessments_marks_cards', array('active_marks_card_id' => $thisId, 'status' => 'Generated'));
        return $this->db->trans_complete();
    }

    public function replacePathIfExists($stdId, $tempId, $path, $type) {
        $mId = $this->db->select('id, active_marks_card_id')->where('student_id', $stdId)->where('marks_card_temp_id', $tempId)->get('assessments_marks_cards')->row();
        if($mId->active_marks_card_id) {
            $active_marks_card_id = $mId->active_marks_card_id;
            $this->db->trans_start();
            $this->db->where('id', $active_marks_card_id)->update('assessments_marks_card_history', ['status' => $type]);
            $this->db->where('id', $mId->id);
            $this->db->update('assessments_marks_cards', array('status' => 'Generated'));
            $this->db->trans_complete();
            $pdf_link = $this->db->query("select pdf_link from assessments_marks_card_history where id=$active_marks_card_id")->row()->pdf_link;
            return $pdf_link;
        } else {
            //insert new entry
            $mData = array(
                'ass_marks_card_id' => $mId->id,
                'pdf_link' => $path,
                'action_comment' => 'Generated',
                'status' => $type,
                'action_by' => $this->authorization->getAvatarId()
            );
            $this->db->trans_start();
            $this->db->insert('assessments_marks_card_history', $mData);
            $thisId = $this->db->insert_id();
            $this->db->where('id', $mId->id);
            $this->db->update('assessments_marks_cards', array('active_marks_card_id' => $thisId, 'status' => 'Generated'));
            $this->db->trans_complete();
            return $path;
        }
    }

    public function updateMarksCardNew($stdId, $tempId, $path, $type){
        $mId = $this->db->select('id')->where('student_id', $stdId)->where('marks_card_temp_id', $tempId)->get('assessments_marks_cards')->row();
        $mData = array(
                'ass_marks_card_id' => $mId->id,
                'pdf_link' => $path,
                'action_comment' => 'Generated',
                'status' => $type,
                'action_by' => $this->authorization->getAvatarId()
            );
        $this->db->trans_start();
        $this->db->insert('assessments_marks_card_history', $mData);
        $thisId = $this->db->insert_id();
        $this->db->where('id', $mId->id);
        $this->db->update('assessments_marks_cards', array('active_marks_card_id' => $thisId, 'status' => 'Generated'));
        return $this->db->trans_complete();
    }

    public function insertRemarks($tempId){
        $stdId = $this->input->post('stdId');
        $type = $this->input->post('type'); // add or verify
        $rem = $this->db->select('id')->where('marks_card_temp_id', $tempId)->where('student_id', $stdId)->get('assessments_marks_cards')->row();
         $action = $this->input->post('remarks');
        if($type == 'verify')
            $action .= ' - Remarks verified';

        $additional_remarks = NULL;
        $additional_remarks_present = $this->settings->getSetting('examination_allow_additional_remarks');
        if($additional_remarks_present) {
            $additional_remarks = $this->input->post('additional_remarks');
        }
        $this->db->trans_start();
        if(empty($rem)) {
            $data = array(
                'marks_card_temp_id' => $tempId,
                'student_id' => $stdId,
                'remarks' => $this->input->post('remarks'),
                'additional_remarks' => $additional_remarks,
                'created_by' => $this->authorization->getAvatarId(),
                'active_marks_card_id' => 0,
                'remark_status' => $this->input->post('status')
            );
            $this->db->insert('assessments_marks_cards', $data);
            $insId = $this->db->insert_id();
            $hdata = array(
                'ass_marks_card_id' => $insId,
                'remarks' => $action,
                'action_by' => $this->authorization->getAvatarId()
            );
            $this->db->insert('assessment_remarks_history', $hdata);
        } else {
            $data = array(
                'remarks' => $this->input->post('remarks'),
                'additional_remarks' => $additional_remarks,
                'remark_status' => $this->input->post('status')
            );
            $this->db->where('id', $rem->id);
            $this->db->update('assessments_marks_cards', $data);
            $insId = $this->db->insert_id();
            $hdata = array(
                'ass_marks_card_id' => $rem->id,
                'remarks' => $action,
                'action_by' => $this->authorization->getAvatarId()
            );
            $this->db->insert('assessment_remarks_history', $hdata);
        }
        return $this->db->trans_complete();
    }

    public function dummyRemarks($stdId, $tempId) {
        $data = array(
            'marks_card_temp_id' => $tempId,
            'student_id' => $stdId,
            'remarks' => '',
            'additional_remarks' => '',
            'created_by' => $this->authorization->getAvatarId(),
            'active_marks_card_id' => 0,
            'remark_status' => 'Verified'
        );
        $this->db->insert('assessments_marks_cards', $data);
        $insId = $this->db->insert_id();
        $hdata = array(
            'ass_marks_card_id' => $insId,
            'remarks' => 'Marks card generated',
            'action_by' => $this->authorization->getAvatarId()
        );
        return $this->db->insert('assessment_remarks_history', $hdata);
    }

    public function getGradingSystems(){
        return $this->db_readonly->select('*')->get('assessment_grading_system')->result();
    }

    public function saveMarksCard(){
        $input = $this->input->post();
        $generation_type = (isset($input['generation_type']))?1:0;
        $assIds = $input['assessment'];
        $cardName = $input['marks_card_name'];
        $classId = $input['classId'];
        $template_background = $input['template_background'];
        $grading_systems = implode(",", $input['grading_system']);
        $assessments = array();
        if(!empty($assIds))
            $assessments = $this->db->select('id,short_name')->where_in('id', $assIds)->get('assessments')->result();
        $json = array();
        foreach ($assessments as $key => $value) {
            $json[] = array('name'=> $value->short_name, 'id'=> $value->id);
        }
        $data = array(
            'template_name' => $cardName,
            'class_id' => $classId,
            'assessments' => json_encode($json),
            'grading_systems' => $grading_systems,
            'generation_type' => $generation_type,
            'template_background' => $template_background
        );
        // echo "<pre>"; print_r($data);die();
        return $this->db->insert('assessment_marks_card_templates', $data);
    }

    public function addMarksTemplate($cardId){
        $input = $this->input->post();
        $assmts = explode(",", rtrim($input['names'],","));
        $template = file_get_contents($_FILES['template_file']['tmp_name']);
        $json = array();
        foreach ($assmts as $key => $value) {
            $value = str_replace(" ", "_", rtrim($value));
            $row = $this->db->select('id, short_name')->where('id', $input[$value])->get('assessments')->row();
            $template = str_replace($value, $row->short_name, $template);
            $json[] = array('name'=> $row->short_name, 'id'=> $row->id);
        }
        
        $data = array(
            'assessments' => json_encode($json),
            'template_content' => $template
        );
        $this->db->where('id', $cardId);
        return $this->db->update('assessment_marks_card_templates', $data);
        // echo "<pre>"; print_r($data);die();
    }

    public function getSubjectsByAssessment($assIds, $subjectType){
        if(empty($assIds)) return array();
        $assIds = implode(",", $assIds);
        if ($subjectType == 'component') {
            $result = $this->db_readonly->select('aem.id, aem.name as subName, aem.sorting_order, aem.mapping_string')
                ->from('assessment_entity_master aem')
                ->where('id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))')
                ->order_by('aem.sorting_order')
                ->get()->result();
        } else if($subjectType == 'group'){
            $result = $this->db_readonly->select('aeg.id, aeg.entity_name as subName, aeg.sorting_order, aeg.mapping_string')
                ->from('assessment_entities_group aeg')
                ->where('id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.')))')
                ->order_by('aeg.sorting_order')
                ->get()->result();
        } else if($subjectType == 'elective'){
            $result = $this->db_readonly->select('aeg.id, aeg.friendly_name as subName')
                ->from('assessment_elective_group aeg')
                ->where('id in (select elective_group_id from assessment_entities_group where id in (select ass_entity_gid from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in ('.$assIds.'))))')
                ->get()->result();
        }
        return $result;
    }

    public function updateTemplate($template, $tempId) {
        $data = array(
            'template_content' => $template
        );
        $this->db->where('id', $tempId);
        return $this->db->update('assessment_marks_card_templates', $data);
    }

    public function get_random_student_id($class_id) {
        $student_obj = $this->db_readonly->select('sa.id')
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sa.admission_status', '2')
            ->where('sy.promotion_status!=', '4')
            ->where('sy.promotion_status!=', '5')    
            ->where('sy.class_id',$class_id)
            ->limit(1)->get()->row();

        return $student_obj->id;
    }

    public function updateXMLJSON() {
        $json_str = $this->input->post('json_str');
        $tempId = $this->input->post('tempId');
        $data = array(
            'assessments' => $json_str
        );
        $this->db->where('id', $tempId);
        return $this->db->update('assessment_marks_card_templates', $data);
    }
    
    public function getAttendanceSessionID() {
        $classId = $_POST['classId'];
        $sectionId = $_POST['sectionId'];
        $stdIds = $_POST['stdIds'];
        $from = date('Y-m-d', strtotime($_POST['from']));
        $to = date('Y-m-d', strtotime($_POST['to']));

        $result = $this->db->select("attstu.id,attstu.attendance_session_id,attstu.attendance_master_id,attstu.attendance_master_group_id,attstu.status,DATE_FORMAT(`attsess`.`day`, '%d-%m-%Y') as day,attstu.student_id,attstu.reference_type,attstu.reference_id, attstu.reference_status")
        ->from('attendance_student attstu') 
        ->join('attendance_session attsess','attsess.id = attstu.`attendance_session_id`')
        ->where('`attsess`.`day` >=', $from)
        ->where('`attsess`.`day` <=', $to)
        ->where('attsess.class_id`',$classId)
        ->where('attsess.class_section_id',$sectionId)
        ->where_in('attstu.student_id',$stdIds)
        ->get()->result_array();

        return $result;
        // echo "<pre>"; print_r($result); die();
    }

    private function _getStudentMarks($students, $sectionId, $aeIds) {
        if(empty($students) || empty($aeIds)) {
            return $students;
        }
        $return = $this->db_readonly->select("sa.id as student_id, aems.marks, aems.grade, aems.id as aemsId, aems.assessments_entities_id as aeId, aems.status, aem.ass_entity_gid as group_id")
        ->from('student_admission sa')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->join('assessments_entities_marks_students aems', 'aems.student_id=sa.id', 'left')
        ->join('assessments_entities ae', 'ae.id=aems.assessments_entities_id', 'left')
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id', 'left')
        ->where('sy.class_section_id', $sectionId)
        ->where('sa.admission_status', 2)
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where_in('aems.assessments_entities_id', $aeIds)
        ->order_by('sa.first_name')
        ->get()->result();

        $marksData = array();
        foreach ($return as $key => $ret) {
            $marksData[$ret->student_id.'_'.$ret->aeId] = $ret;
        }

        foreach ($students as $key => $student) {
            foreach ($aeIds as $aeId) {
                $id = $student->student_id.'_'.$aeId;
                $detail = new stdClass();
                $detail->marks = NULL;
                $detail->grade = NULL;
                $detail->status = 0;
                if(array_key_exists($id, $marksData)) {
                    $detail->marks = $marksData[$id]->marks;
                    $detail->grade = $marksData[$id]->grade;
                    $detail->status = $marksData[$id]->status;
                    $detail->group_id = $marksData[$id]->group_id;
                }
                $students[$key]->{$aeId} = $detail;
            }
        }
        return $students;
    }

    public function getStudentMarksList($section_id, $aeIds) {
        $school = $this->settings->getSetting('school_short_name');
        $this->db_readonly->select("CONCAT(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name,'')) as student_name, sa.id as student_id, sy.roll_no")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $section_id)
            ->where('sy.promotion_status!=', '4')
            ->where('sy.promotion_status!=', '5')
            ->where('sa.admission_status', 2);
            if($school == 'ourschool') {
                $this->db_readonly->order_by('sa.first_name');
            } else {
                $this->db_readonly->order_by('sy.roll_no');
            }
            $students = $this->db_readonly->get()->result();

        $addedMarksCount = $this->db_readonly->select("count(sa.id) as stdCount")
        ->from('student_admission sa')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->join('assessments_entities_marks_students aems', 'aems.student_id=sa.id')
        ->where('sy.class_section_id', $section_id)
        ->where('sa.admission_status', 2)
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where_in('aems.assessments_entities_id', $aeIds)
        ->get()->row()->stdCount;
        $isFreshEntry = 1;
        if($addedMarksCount) {
            $isFreshEntry = 0;
        }

        $data = $this->_getStudentMarks($students, $section_id, $aeIds);
        return array('list' => $data, 'isFreshEntry' => $isFreshEntry);
        // echo "<pre>"; print_r($data); die();
    }

    public function getSubjectRemarks($assId, $groupId) {
        $remarks = $this->db_readonly->select("student_id, subject_remarks, subject_id")
        ->where('assessment_id', $assId)
        ->where_in('subject_id', $groupId)
        ->get('assessment_subject_remarks')->result();
        $students = array();
        foreach ($remarks as $key => $value) {
            $students[$value->student_id.'_'.$value->subject_id] = $value->subject_remarks;
        }
        return $students;
    }

    private function _getStudentMarksData($students, $sectionId, $aeIds) {
        // echo '<pre>'; print_r($students); die();
        if(empty($students) || empty($aeIds)) {
            return $students;
        }
        $unique_students_ids= [];
        foreach($students as $key => $val) {
            $unique_students_ids[] = $val->student_id;
        }
        $showAlumni = $this->settings->getSetting('examination_show_alumni_students_in_marks_entry');

            if ($showAlumni) {
                $student_status_arr = [2, 4, 5]; // Include active + alumni
                $promotion_status_arr = ['JOINED']; // Filter out only current students
            } else {
                $student_status_arr = [2]; // Only active students
                $promotion_status_arr = ['JOINED', '4', '5']; // Exclude alumni
            }
        $aeids = implode(",", $aeIds);
        $return = $this->db_readonly->select("sa.id as student_id, aems.marks, aems.grade, aems.id as aemsId, aems.assessments_entities_id as aeId, aems.status, aems.entity_grade_rank, aems.entity_section_rank")
        ->from('student_admission sa')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->join('assessments_entities_marks_students aems', "aems.student_id=sa.id and aems.assessments_entities_id in ($aeids)", 'left')
        ->join('assessments_entities ae', 'ae.id=aems.assessments_entities_id', 'left')
        ->where('sy.class_section_id', $sectionId)
        ->where_in('sa.admission_status', $student_status_arr)
        ->where_not_in('sy.promotion_status', $promotion_status_arr)    
       
        ->order_by('sa.first_name')
        ->get()->result();

        $marksData = array();
        foreach ($return as $key => $ret) {
            $marksData[$ret->student_id.'_'.$ret->aeId] = $ret;
        }

        //Get the entity objects with elective information
        $ae_id_objs = $this->db_readonly->select('ae.id as ae_id, aeg.id as group_id, aeg.elective_group_id, if(isnull(elective_group_id), 0, 1) as is_elective')
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
            ->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id')
            ->where_in('ae.id', $aeIds)
            ->get()->result();


            // echo '<pre>'; print_r($ae_id_objs); die();


// check_if_student_chhose elective
            $uniqueEntitiesGroupIds= [];
            $uniqueElectiveGroupIds= [];
            if(!empty($ae_id_objs)) {
                foreach($ae_id_objs as $key => $val) {
                    if($val->is_elective == '1') {
                        if(empty($uniqueElectiveGroupIds) || !in_array($val->elective_group_id, $uniqueElectiveGroupIds)) {
                            $uniqueElectiveGroupIds[]= $val->elective_group_id;
                        }
                        if(empty($uniqueEntitiesGroupIds) || !in_array($val->group_id, $uniqueEntitiesGroupIds)) {
                            $uniqueEntitiesGroupIds[]= $val->group_id;
                        }
                    }
                }
            }

            $students_elective_arr_if_exists= [];
            //

            $students_elective_arr_if_exists= [];
            if(!empty($uniqueEntitiesGroupIds) && !empty($uniqueElectiveGroupIds)) {
                $ele_std_obj = $this->db_readonly->select('id, student_id, ass_entity_gid, ass_elective_gid')
                    ->from('assessment_students_elective')
                    ->where_in('student_id', $unique_students_ids)
                    ->where_in('ass_entity_gid', $uniqueEntitiesGroupIds)
                    ->where_in('ass_elective_gid', $uniqueElectiveGroupIds)
                    ->get()->result();
                
                if(!empty($ele_std_obj)) {
                    foreach($ele_std_obj as $key => $val) {
                        $students_elective_arr_if_exists[]= "$val->student_id-$val->ass_entity_gid-$val->ass_elective_gid";
                    }
                }
            }
// Ent check_if_student_chhose elective

        foreach ($students as $key => $student) {
            foreach ($ae_id_objs as $ae_id_obj) {
                $id = $student->student_id.'_'.$ae_id_obj->ae_id;

                //add the marks or mark as TBD for each assessment_entity
                $students[$key]->{$ae_id_obj->ae_id} = new stdClass();
                if(array_key_exists($id, $marksData)) {
                    // echo '<pre>';print_r($student->{$ae_id_obj->ae_id});die();
                    $students[$key]->{$ae_id_obj->ae_id}->marks = $marksData[$id]->marks;
                    $students[$key]->{$ae_id_obj->ae_id}->entity_grade_rank = $marksData[$id]->entity_grade_rank;
                    $students[$key]->{$ae_id_obj->ae_id}->entity_section_rank = $marksData[$id]->entity_section_rank;
                    $students[$key]->{$ae_id_obj->ae_id}->grade = $marksData[$id]->grade;
                    $students[$key]->{$ae_id_obj->ae_id}->status = $marksData[$id]->status;
                } else {
                    $students[$key]->{$ae_id_obj->ae_id}->marks = '';
                    $students[$key]->{$ae_id_obj->ae_id}->grade = '';
                    $students[$key]->{$ae_id_obj->ae_id}->entity_grade_rank = '';
                    $students[$key]->{$ae_id_obj->ae_id}->entity_section_rank = '';
                    $students[$key]->{$ae_id_obj->ae_id}->status = '0'; //Assuming'0' means not entered; '1' means saved; '2' means locked
                }

                //figure out elective
                $students[$key]->{$ae_id_obj->ae_id}->is_elective = $ae_id_obj->is_elective;
                if ($ae_id_obj->is_elective == '1' && !empty($students_elective_arr_if_exists)) {
                    // $students[$key]->{$ae_id_obj->ae_id}->has_elected = $this->_check_is_elected($student->student_id, $ae_id_obj->elective_group_id, $ae_id_obj->group_id); // I'm commenting the traditional code to check if the student has elected the group or not.
                    $elective_checker= "$student->student_id-$ae_id_obj->group_id-$ae_id_obj->elective_group_id";
                    $students[$key]->{$ae_id_obj->ae_id}->has_elected = in_array($elective_checker, $students_elective_arr_if_exists) ? 1 : 0;

                    // echo $students[$key]->{$ae_id_obj->ae_id}->has_elected;
                }

            }
        }
        // die();
        // echo '<pre>getStudentMarksData: ';print_r($students);die();
        return $students;
    }

    public function getStudentMarksData($groupId, $sectionId, $aeIds) {
        // $groupIds = implode(",", $groupId);
        $school = $this->settings->getSetting('school_short_name');
        // $aeids = implode(",", $aeIds);
        $groups= $this->db_readonly->select("ae.id as aeId, aeg.id as group_id, aeg.is_elective, aeg.elective_group_id")
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'ae.entity_id=aem.id')
            ->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id')
            ->where_in('ae.id', $aeIds)
            ->where_in('aeg.id', $groupId)
            ->get()->result();
        // $sql = "select ae.id as aeId, aeg.id as group_id, aeg.is_elective, aeg.elective_group_id 
        //         from assessments_entities ae 
        //         join assessment_entity_master aem on ae.entity_id=aem.id 
        //         join assessment_entities_group aeg on aem.ass_entity_gid=aeg.id 
        //         where ae.id in ($aeids) and aeg.id in ($groupIds)";
        // $groups = $this->db_readonly->query($sql)->result();
        // echo '<pre>';print_r($this->db_readonly->last_query());die();
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sa.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sa.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }
        
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else if ($prefix_student_name == "registration_no") {
            $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else {
          $std_name = "CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }
    
        if($this->settings->getSetting('examination_show_alumni_students_in_marks_entry')) {
            $student_status_arr = [2, 4, 5];
            $promotion_status_arr = ['JOINED'];
        } else {
            $student_status_arr = [2];
            $promotion_status_arr = ['JOINED', '4', '5'];

        }
        $this->db_readonly->select("$std_name,ifnull(sa.enrollment_number, 'NA') as enrollment_number,ifnull(sa.admission_no, 'NA') as admission_no, sa.id as student_id, sy.roll_no, sa.admission_status, sy.promotion_status")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $sectionId)
            ->where_not_in('sy.promotion_status', $promotion_status_arr)
            ->where_in('sa.admission_status', $student_status_arr)
            ->order_by($order_by);
            $students = $this->db_readonly->get()->result();

            // echo '<pre>';print_r($groups);
        // foreach ($students as $key => &$student) {
        //     foreach ($groups as $k => $group) {
        //         $detail = new stdClass();
        //         $detail->marks = NULL;
        //         $detail->grade = NULL;
        //         $detail->status = 0;
        //         if($group->is_elective) {
        //             if($this->_check_is_elected($student->student_id, $group->elective_group_id, $group->group_id)) {
        //                 $detail->group_id = $group->group_id;
        //                 $detail->elected = 'yes';
        //             } else {
        //                 $detail->elected = 'no';
        //             }
        //         } else {
        //             $detail->group_id = $group->group_id;
        //         }
        //         $student->{$group->aeId} = $detail;
        //     }
        // }
        // echo '<pre>'; print_r($students); die();
        $isFreshEntry = 0;
        if(empty($students)) {
            $isFreshEntry = 1;
        }

        $data = $this->_getStudentMarksData($students, $sectionId, $aeIds);
        return array('list' => $data, 'isFreshEntry' => $isFreshEntry);
    }

    private function _check_is_elected($student_id, $ele_id, $group_id) {
        $ele = $this->db_readonly->select('id')
                ->from('assessment_students_elective')
                ->where('student_id', $student_id)
                ->where('ass_entity_gid', $group_id)
                ->where('ass_elective_gid', $ele_id)
                ->get()->row();
        if(!empty($ele))
            return 1;
        return 0;
    }

    public function get_related_entities_of_derived_entity ($entity_id) {
      $entity_obj = $this->db_readonly->select('derived_formula')
        ->from('assessment_entity_master aem')
        ->where('aem.id', $entity_id)
        ->get()->row();
  
      $derived = json_decode($entity_obj->derived_formula);
      $entity_ids = [];        
      foreach ($derived->entityId as $eobj) {
          $entity_ids[] = $eobj->id;
      }
      $entity_ids[] = $entity_id;

    //   echo '<pre>get_related_entities_of_derived_entity: '; print_r($entity_ids); die();
      return $entity_ids;
    }

    public function generate_ranks($section_id, $derived_entity_id, $assessment_id, $ae_id) {
        //Get the marks of all the subjects for the grade
        $student_mark_objs = $this->db_readonly->select('id, marks')
            ->from('assessments_entities_marks_students')
            ->where('assessments_entities_id', $ae_id)
            ->order_by('marks desc')
            ->get()->result();

        $rank = 0;
        $next_highest_mark = 9999;
        $update_data = []; 
        foreach ($student_mark_objs as $mark_obj) {
            $input_obj = [];
            $input_obj['id'] = $mark_obj->id;
            if ($mark_obj->marks == -1 || $mark_obj->marks == -2) {
                $input_obj['entity_grade_rank'] = 0;
            } else {
                if ($mark_obj->marks < $next_highest_mark) {
                    $input_obj['entity_grade_rank'] = ++$rank;
                    $next_highest_mark = $mark_obj->marks;
                } else {
                    $input_obj['entity_grade_rank'] = $rank;
                }
            }
            $update_data[] = $input_obj;
        }

        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }

        //Get the marks of all the subjects for the section now
        $student_mark_objs = $this->db_readonly->select('aems.id, aems.marks')
            ->from('assessments_entities_marks_students aems')
            ->join('student_admission sa', 'aems.student_id=sa.id')
            ->join('student_year sy', "sy.student_admission_id=sa.id and class_section_id=$section_id")
            ->where('assessments_entities_id', $ae_id)
            ->order_by('marks desc')
            ->get()->result();

        $rank = 0;
        $next_highest_mark = 9999;
        $update_data = []; 
        foreach ($student_mark_objs as $mark_obj) {
            $input_obj = array();
            $input_obj["id"] = $mark_obj->id;
            if ($mark_obj->marks == -1 || $mark_obj->marks == -2) {
                $input_obj['entity_section_rank'] = 0;
            } else {
                if ($mark_obj->marks < $next_highest_mark) {
                    $input_obj['entity_section_rank'] = ++$rank;
                    $next_highest_mark = $mark_obj->marks;
                } else {
                    $input_obj['entity_section_rank'] = $rank;
                }
            }
            $update_data[] = $input_obj;
        }

        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }
        return 1;
    }

    public function generate_derived_entity_marks_without_derivedae($section_id, $derived_entity_id, $assessment_id) {
        $result = $this->db_readonly->select('id')
            ->from('assessments_entities ae')
            ->where('assessment_id', $assessment_id)
            ->where('entity_id', $derived_entity_id)
            ->get()->row();
        
        $derived_ae_id = $result->id;
        return $this->generate_derived_entity_marks ($section_id, $derived_entity_id, $assessment_id, $derived_ae_id);
    }

    public function generate_derived_entity_marks ($section_id, $derived_entity_id, $assessment_id, $derived_ae_id) {
        //Step1: Get the entity object of the given derived subject and get all the related entities
        $entity_obj = $this->db_readonly->select('derived_formula, rounding')
            ->from('assessment_entity_master aem')
            ->where('aem.id', $derived_entity_id)
            ->get()->row();
        $rounding_parameter= $entity_obj->rounding;
  
        $derived = json_decode($entity_obj->derived_formula);
        $entity_ids = []; 
        if(empty($derived->entityId)) return -1;   
        foreach ($derived->entityId as $eobj) {
            if (!empty($eobj->id)) {
                $entity_ids[] = $eobj->id;
            }
        }
        $entity_id_str = implode(',',$entity_ids);

        //Step1a: Get the Students
        $student_sql = "select sa.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id where class_section_id=$section_id and sy.promotion_status != '4' and sy.promotion_status != '5' and sa.admission_status=2";

        $student_objs = $this->db_readonly->query($student_sql)->result();

        //Step2: Get the assessment entities for all the related entities
        if(empty($entity_id_str)) return false;
        $marks_sql = "select aems.student_id, aems.marks, ae.total_marks from assessments_entities_marks_students aems
        join assessments_entities ae on aems.assessments_entities_id=ae.id and assessment_id=$assessment_id and entity_id in ($entity_id_str)
        where student_id in (select sa.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id where class_section_id=$section_id and sy.promotion_status != '4' and sy.promotion_status != '5' and sa.admission_status=2)";

        $marks_objs = $this->db_readonly->query($marks_sql)->result();

        //Step3: Calculate the derived marks and insert/update them
        $t_marks_obj = [];
        $update_data = [];
        $insert_data = [];
        foreach ($student_objs as $student_obj) {
            $marks_arr = [];
            $totals_arr = [];
            foreach ($marks_objs as $m_obj) {
                if ($student_obj->id == $m_obj->student_id) {
                    $marks_arr [] = $m_obj->marks;
                    $totals_arr [] = $m_obj->total_marks;
                }
            }
            $derived_marks = $this->_calculateDerivedSubjectMarks($marks_arr, $totals_arr, $derived->formula->name, $rounding_parameter);
            $exists = $this->db_readonly->select('id')->where('assessments_entities_id', $derived_ae_id)->where('student_id', $student_obj->id)->get('assessments_entities_marks_students')->row();

            if(!empty($exists)) {
                $update_data[] = array(
                    'id' => $exists->id,
                    'marks' => $derived_marks,
                    'status' => 1
                );
            } else {
                $insert_data[] = array(
                    'assessments_entities_id' => $derived_ae_id,
                    'student_id' => $student_obj->id,
                    'marks' => $derived_marks,
                    'status' => 1
                );
            }
        } 

        if(!empty($insert_data)) {
            $this->db->insert_batch('assessments_entities_marks_students', $insert_data);
        }
        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }

        return 1;
    }

    public function getElectiveStudentMarksList($groupId, $sectionId, $aeIds) {
        $school = $this->settings->getSetting('school_short_name');
        $this->db_readonly->select("CONCAT(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name,'')) as student_name, sa.id as student_id, sy.roll_no")
            ->from('student_admission sa')
            ->join('student_year sy', 'sa.id=sy.student_admission_id')
            ->join('assessment_students_elective ase', 'ase.student_id=sa.id')
            ->where('sy.class_section_id', $sectionId)
            ->where('sa.admission_status', 2)
            ->where('sy.promotion_status!=', '4')
            ->where('sy.promotion_status!=', '5')
            ->where_in('ase.ass_entity_gid', $groupId);
            if($school == 'ourschool') {
                $this->db_readonly->order_by('sa.first_name');
            } else {
                $this->db_readonly->order_by('sy.roll_no');
            }
            $students = $this->db_readonly->get()->result();

        $isFreshEntry = 0;
        if(empty($students)) {
            $isFreshEntry = 1;
        }

        $data = $this->_getStudentMarks($students, $sectionId, $aeIds);
        return array('list' => $data, 'isFreshEntry' => $isFreshEntry);
    }

    public function getGroupData($group_id) {
        return $this->db_readonly->select('id, is_elective, entity_name as group_name')->where_in('id', $group_id)->order_by('sorting_order, entity_name')->get('assessment_entities_group')->result();
    }

    public function get_subject_components($subjects) {
        $entities = $this->db_readonly->select("id")
        ->from('assessment_entity_master')
        ->where_in('ass_entity_gid', $subjects)
        ->get()->result();
        $entity_ids = [];
        foreach ($entities as $ent) {
            $entity_ids[] = $ent->id;
        }
        return $entity_ids;
    }

    public function getPermittedEntitiesList($assId, $sectionId, $groupId=[], $is_exam_admin) {
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $this->db_readonly->select("ae.entity_id, ae.total_marks,  ae.assessment_id, aeg.entity_name as group_name, aem.name as entity_name, ae.id as assessment_entities_id,  aem.id as entity_id, aem.evaluation_type, (case when isnull(aem.derived_formula) then 'Manual' else 'Derived' end) as subject_mode, aem.derived_formula")
        ->from('assessments_entities ae')
        ->join('assessments_entities_access aea', 'aea.assessments_entities_id=ae.id')
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
        ->join('assessment_entities_group aeg', 'aeg.id=aem.ass_entity_gid')
        ->join('assessment_grading_system ags', 'ags.id=aem.grading_system_id', 'left')
        ->where('ae.assessment_id', $assId)
        ->where('aea.class_section_id', $sectionId);
        if(!empty($groupId)) {
            $this->db_readonly->where_in('aem.ass_entity_gid', $groupId);
        }
        if(!$is_exam_admin) {
            $this->db_readonly->where('aea.staff_id', $staffId);
            $this->db_readonly->where("aea.access_level!='none'");
        } else {
            $this->db_readonly->group_by("aea.assessments_entities_id");
        }
        $this->db_readonly->order_by('aeg.sorting_order,aem.sorting_order,aem.name');
        $res = $this->db_readonly->get()->result();
        return $res;
        // echo "<pre>"; print_r($res); die();
    }

    public function getRemarksEntryStatus($assessment_id, $group_id, $elective_group_id, $section_id) {
      
        $groupIds = implode(",", $group_id);
        if($elective_group_id == 0) {
            $sql = "select count(sa.id) as total_students 
                    from student_admission sa 
                    join student_year sy on sy.student_admission_id=sa.id 
                    where sy.class_section_id=$section_id and sa.admission_status=2 and sy.promotion_status !=4 and sy.promotion_status !=5";
        } else {
            $sql = "select count(sa.id) as total_students 
                    from student_admission sa 
                    join student_year sy on sy.student_admission_id=sa.id 
                    join assessment_students_elective ase on ase.student_id=sa.id 
                    where sy.class_section_id=$section_id and sa.admission_status=2 and sy.promotion_status !=4 and sy.promotion_status !=5 
                    and ase.ass_elective_gid=$elective_group_id and ase.ass_entity_gid in ($groupIds)";
        }
        $stdCount = $this->db_readonly->query($sql)->row()->total_students;

        $sql = "select count(id) as added 
                from assessment_subject_remarks sr 
                where subject_id in ($groupIds) and assessment_id=$assessment_id 
                and student_id in (select sy.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sa.admission_status=2 and sy.promotion_status !=4 and sy.promotion_status !=5 and  sy.class_section_id=$section_id)";
        $remarks_count = $this->db_readonly->query($sql)->row()->added;

        return array('total' => $stdCount, 'added' => $remarks_count);
    }

    public function getMarksEntryStatus($assessment_id, $group_id, $section_id) {
        $groupIds = implode(",", $group_id);
        $statusCount = array();
        foreach ($group_id as $key => $gid) {
            $ele_gid = $this->db_readonly->query("select ifnull(elective_group_id, 0) as elective_group_id from assessment_entities_group where id=$gid")->row()->elective_group_id;
            
            $res = $this->db_readonly->select("ae.id")
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
            ->where('aem.ass_entity_gid', $gid)
            ->where('ae.assessment_id', $assessment_id)
            ->get()->result();
            $assessment_entities_ids = array();
            foreach ($res as $key => $ent) {
              array_push($assessment_entities_ids, $ent->id);
            }
  
            if($ele_gid == 0) {
                $sql = "select count(sa.id) as total_students 
                        from student_admission sa 
                        join student_year sy on sy.student_admission_id=sa.id 
                        where sy.class_section_id=$section_id and sa.admission_status=2 and sy.promotion_status !=4 and sy.promotion_status !=5";
            } else {
                $sql = "select count(sa.id) as total_students 
                        from student_admission sa 
                        join student_year sy on sy.student_admission_id=sa.id 
                        join assessment_students_elective ase on ase.student_id=sa.id 
                        where sy.class_section_id=$section_id and sa.admission_status=2 and sy.promotion_status !=4 and sy.promotion_status !=5
                        and ase.ass_elective_gid=$ele_gid and ase.ass_entity_gid=$gid";
            }
            $stdCount = $this->db_readonly->query($sql)->row()->total_students;
 
            $ids = implode(",", $assessment_entities_ids);
            /*$sql = "select aems.assessments_entities_id, sum(case when aems.status=1 then 1 else 0 end) as saved, sum(case when aems.status=2 then 1 else 0 end) as locked 
                    from assessments_entities_marks_students aems 
                    where aems.assessments_entities_id in ($ids) 
                    and student_id in (select id from student_admission where admission_status=2 and id in 
                    (select student_admission_id from student_year where class_section_id=$section_id)) 
                    group by aems.assessments_entities_id";*/
            $sql = "select aems.assessments_entities_id, sum(case when aems.marks!=-2 and aems.status=1 then 1 else 0 end) as saved, sum(case when aems.status=2 then 1 else 0 end) as locked 
                    from assessments_entities_marks_students aems 
                    where aems.assessments_entities_id in ($ids) 
                    and student_id in (select id from student_admission where admission_status=2 and id in 
                    (select student_admission_id from student_year where class_section_id=$section_id and promotion_status !=4 and promotion_status !=5 )) 
                    group by aems.assessments_entities_id";
            $marksCount = $this->db_readonly->query($sql)->result();
            $status = array();
            foreach ($marksCount as $key => $count) {
                $status[$count->assessments_entities_id] = $count;
            }

            foreach ($assessment_entities_ids as $id) {
                $statusCount[$id] = array();
                if(!array_key_exists($id, $status)) {
                    $statusCount[$id]['total'] = $stdCount;
                    $statusCount[$id]['saved'] = 0;
                    $statusCount[$id]['locked'] = 0;
                } else {
                    $statusCount[$id]['total'] = $stdCount;
                    $statusCount[$id]['saved'] = $status[$id]->saved;
                    $statusCount[$id]['locked'] = $status[$id]->locked;
                }
            }
        }
        return $statusCount;
    }

    public function getMarksEntities($assId, $entities, $sectionId, $is_exam_admin) {
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $this->db_readonly->select("distinct(ae.id) as aeId, aea.class_section_id, ae.entity_id, concat(aeg.entity_name, ' > ', aem.name) as name, aem.ass_entity_gid, aea.access_level, ae.total_marks, aem.evaluation_type, aem.grading_system_id, ags.grades, aem.ass_type, aem.derived_formula, ae.is_editable")
        ->from('assessments_entities ae')
        ->join('assessments_entities_access aea', "aea.assessments_entities_id=ae.id and aea.class_section_id='$sectionId'")
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
        ->join('assessment_entities_group aeg', 'aeg.id=aem.ass_entity_gid')
        ->join('assessment_grading_system ags', 'ags.id=aem.grading_system_id', 'left')
        ->where('ae.assessment_id', $assId)
        ->where_in('aem.id', $entities);
        if ($is_exam_admin == '0')
            $this->db_readonly->where('aea.staff_id', $staffId);
        $this->db_readonly->where('aea.class_section_id', $sectionId);
        $this->db_readonly->order_by('aem.ass_type desc, aeg.sorting_order,aeg.entity_name,aem.sorting_order,aem.name');
        if ($is_exam_admin != '0')
            $this->db_readonly->group_by('ae.id'); // Taking only one assessment_entities_id for one instance of subjects
        $res = $this->db_readonly->get()->result();

        // echo '<pre>getMarksEntities: '; print_r($res); die();

      return $res;
    }

    public function getPermittedEntities($assId, $sectionId, $groupId=0) {
        $staffId = $this->authorization->getAvatarStakeHolderId();
        $this->db_readonly->select("ae.id as aeId, ae.entity_id, aem.name, aem.ass_entity_gid, aea.access_level, ae.total_marks, aem.evaluation_type, aem.grading_system_id, ags.grades, aem.ass_type, aem.derived_formula")
        ->from('assessments_entities ae')
        ->join('assessments_entities_access aea', 'aea.assessments_entities_id=ae.id')
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
        ->join('assessment_grading_system ags', 'ags.id=aem.grading_system_id', 'left')
        ->where('ae.assessment_id', $assId)
        ->where('aea.staff_id', $staffId)
        ->where("aea.access_level!='none'")
        ->where('aea.class_section_id', $sectionId);
        if($groupId) {
            $this->db_readonly->where('aem.ass_entity_gid', $groupId);
        }
        $this->db_readonly->order_by('aem.sorting_order');
        $res = $this->db_readonly->get()->result();
        return $res;
        // echo "<pre>"; print_r($res); die();
    }

    public function downloadMarksCard($id){
        return $this->db_readonly->select('pdf_link')->where('id', $id)->get('assessments_marks_card_history')->row()->pdf_link;
    }

    public function getGrade($grading_scale_id, $percentage) {
        $grading =  $this->db_readonly->select('grades')->where("id", $grading_scale_id)->get('assessment_grading_system')->row();
        if(empty($grading)) return '';
        $grades = json_decode($grading->grades);
        $data = array();
        foreach ($grades as $key => $value) {
            if($percentage >= $value->from && $percentage <= $value->to) {
                $data = array(
                    'long_name' => $value->long_name,
                    'short_name' => $value->grade,
                    'grade_point' => $value->grade_point,
                );
                break;
            }
        }
        return $data;
    }

    public function getEntities($group_id, $assessment_id) {
        return $this->db_readonly->select('aem.id as entity_id, aem.name as entity_name')
        ->from('assessments_entities ae')
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
        ->where('ae.assessment_id', $assessment_id)
        ->where('aem.ass_entity_gid', $group_id)
        ->get()->result();
    }

    public function addSubjectRemarks() {
        $input = $this->input->post();

        $group = array(
            'group_name' => $input['group_name'],
            'remarks_for' => $input['remarks_for'],
        );

        $this->db->trans_start();
        $this->db->insert('assessment_subject_remarks_group', $group);
        $group_id = $this->db->insert_id();

        foreach ($input['subject_remarks'] as $remarks) {
            $subject_remarks[] = array(
                'remarks_group_id' => $group_id,
                'subject_remarks' => $remarks
            );
        }
        $this->db->insert_batch('assessment_subject_remarks_description', $subject_remarks);
        $this->db->trans_complete();
        if($this->db->trans_status === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    public function getAllSubjectRemarks() {
        $remarks = $this->db_readonly->select('sg.id as group_id, sg.group_name, sd.subject_remarks, sd.id as remarks_id')
        ->from('assessment_subject_remarks_group sg')
        ->join('assessment_subject_remarks_description sd', 'sd.remarks_group_id=sg.id')
        ->get()->result();

        $data = array();
        foreach ($remarks as $key => $val) {
            if(!array_key_exists($val->group_id, $data)) {
                $data[$val->group_id] = array();
                $data[$val->group_id]['group_name'] = $val->group_name;
                $data[$val->group_id]['remarks'] = array();
            }
            array_push($data[$val->group_id]['remarks'], array('id' => $val->remarks_id, 'description' => $val->subject_remarks));
        }
        return $data;
    }

    public function getRemarksList($group_id) {
        return $this->db_readonly->select('sd.subject_remarks')
        ->from('assessment_subject_remarks_group sg')
        ->join('assessment_subject_remarks_description sd', 'sd.remarks_group_id=sg.id')
        ->where('remarks_group_id', $group_id)
        ->get()->result();
    }

    public function getReportRemarksList() {
        return $this->db_readonly->select('subject_remarks')
        ->from('assessment_subject_remarks_description')
        ->where("remarks_group_id in (select id from assessment_subject_remarks_group where remarks_for=1)")
        ->get()->result();
    }

    public function updateSubjectRemarks() {
        $input = $this->input->post();
        return $this->db->where('id', $input['remarks_id'])->update('assessment_subject_remarks_description', array('subject_remarks' => $input['subject_remarks']));
    }

    public function deleteSubjectRemarks() {
        $group_id = $this->input->post('group_id');
        $description_id = $this->input->post('remarksId');
    
        $delete_description = $this->db->where('id', $description_id)
                                       ->delete('assessment_subject_remarks_description');
    
                                       if ($delete_description) {
                                        $check = $this->db_readonly->select("id, remarks_group_id")
                                                                   ->from('assessment_subject_remarks_description')
                                                                   ->where('remarks_group_id', $group_id)
                                                                   ->get()
                                                                   ->result();
                                
                                        if (empty($check)) {
                                            $delete_group = $this->db->where('id', $group_id)
                                                                     ->delete('assessment_subject_remarks_group');
                                
                                            if ($delete_group) {
                                                return 2; 
                                            } else {
                                                return -2; 
                                            }
                                        } else {
                                            return 0; 
                                        }
                                    } else {
                                        return -1; 
                                    }

    }

    public function addMoreSubjectRemarks() {
        $input = $this->input->post();
        foreach ($input['subject_remarks'] as $remarks) {
            $subject_remarks[] = array(
                'remarks_group_id' => $input['group_id'],
                'subject_remarks' => $remarks
            );
        }
        return $this->db->insert_batch('assessment_subject_remarks_description', $subject_remarks);
    }


    public function getClassess($is_exam_admin, $staffId) {
        if($is_exam_admin) {
            $result = $this->db_readonly->select('c.id, c.class_name')->from('class c')->where('c.acad_year_id', $this->yearId)->where('c.is_placeholder', 0);
            if($this->current_branch) {
                $this->db_readonly->where('c.branch_id',$this->current_branch);
            }
            return $this->db_readonly->get()->result();
        } else {
            $yearId = $this->yearId;
            $branch_str = "";
            if($this->current_branch) {
                $branch_str = " and branch_id=$this->current_branch";
            }
            $sql = "select id, class_name from class where id in 
                    (select distinct(class_id) from class_section where id in 
                    (select distinct(class_section_id) from assessments_entities_access where staff_id=$staffId and access_level!='none')) and acad_year_id=$yearId $branch_str";

            return $this->db_readonly->query($sql)->result();
        }
    }

    public function getSectionMarksCards($tempId, $sectionId) {
        $sql = "select pdf_link, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName,sa.id as stdId 
                from assessments_marks_cards am 
                join assessments_marks_card_history ah on am.active_marks_card_id=ah.id 
                join student_admission sa on am.student_id=sa.id
                where am.marks_card_temp_id=$tempId and am.student_id in 
                (select student_admission_id from student_year where class_section_id=$sectionId)";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
        // echo "<pre>"; print_r($result); die();
    }

    public function getMarksCardsByIds($report_card_ids) {
        $sql = "select pdf_link, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, sa.id as stdId, DATE_FORMAT(ah.date, '%d-%m-%Y') as created_date  
                from assessments_marks_cards am 
                join assessments_marks_card_history ah on am.active_marks_card_id=ah.id 
                join student_admission sa on am.student_id=sa.id 
                where am.id in ($report_card_ids)";
        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function getPermittedSections($assessment_id, $is_exam_admin) {
        if($is_exam_admin) {
            $sql = "select id, section_name, class_name from class_section where class_id in (
                    select class_id from assessments where id=$assessment_id) and is_placeholder=0";
        } else {
            $staff_id = $this->authorization->getAvatarStakeHolderId();
            $sectionIds= $this->db_readonly->select("class_section_id")
                ->from('assessments_entities_access aea')
                ->join('assessments_entities ae', 'ae.id=aea.assessments_entities_id')
                ->where('ae.assessment_id', $assessment_id)
                ->where('aea.staff_id', $staff_id)
                ->where('aea.access_level!=', 'none')
                ->get()->result();
            $section_ids_arr= [0];
           if(!empty($sectionIds)) {
                foreach($sectionIds as $key => $val) {
                    $section_ids_arr[]= $val->class_section_id;
                }
           }
           $section_ids_str= implode(',', $section_ids_arr);

            $sql = "select id, section_name, class_name 
                    from class_section where id in ($section_ids_str) and is_placeholder=0";
        }
        $sections = $this->db_readonly->query($sql)->result();
        return $sections;
    }

    public function get_permitted_subjects_for_remarks($assessment_id, $section_id) {
        //Get the group names
        $groups_sql = "select aeg.id as group_id, aeg.entity_name as group_name from assessment_entities_group aeg 
        where aeg.id in (select distinct(ass_entity_gid) from assessment_entity_master aem 
        where aem.id in (select distinct(entity_id) from assessments_entities where assessment_id=$assessment_id))";

        $groups = $this->db_readonly->query($groups_sql)->result();

        //Get Student count for the section
        $student_count = $this->db_readonly->select("count(*) as count")
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $section_id)
            ->where('sa.admission_status', '2')
            ->where('sy.promotion_status!=', '4')
            ->where('sy.promotion_status!=', '5')    
            ->get()->row()->count;

        //Get and add remarks count
        $remarks_count_sql = "select distinct(subject_id) as group_id, count(asr.id) as remarks_count from assessment_subject_remarks asr
        where asr.student_id in (select sa.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id and class_section_id=$section_id)
        and asr.assessment_id=$assessment_id group by asr.subject_id";

        $remarks_count = $this->db_readonly->query($remarks_count_sql)->result();

        foreach($groups as $group) {
            $found = 0;
            foreach ($remarks_count as $rcount) {
                if ($group->group_id == $rcount->group_id) {
                    $group->remarks_count = $rcount->remarks_count;
                    $found = 1;
                    break;
                }
            }
            if ($found == 0) {
                $group->remarks_count = 0;
            }
            $group->student_count = $student_count;
        }

        return $groups;
    }

    public function get_permitted_subjects($assessment_id, $is_exam_admin, $section_id) {
        if($is_exam_admin) {
            $sql = "select aeg.id as group_id, ae.total_marks, ae.id as ae_id, aeg.entity_name as group_name, aem.name as entity_name, 1 writes, aem.id as entity_id, (case when aem.evaluation_type = 'marks' then 'Marks' else 'Grade' end) as evaluation_type, (case when isnull(aem.derived_formula) then 'Manual' else 'Derived' end) as subject_mode, aem.derived_formula, aeg.is_elective, aeg.elective_group_id 
                    from assessments_entities ae 
                    join assessment_entity_master aem on aem.id=ae.entity_id 
                    join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid 
                    where ae.assessment_id=$assessment_id 
                    order by aeg.sorting_order,aeg.entity_name, aem.derived_formula, aem.sorting_order, aem.name";
        } else {
            $staff_id = $this->authorization->getAvatarStakeHolderId();
            $subjects_unique= $this->db_readonly->select("distinct(ae.id) as aeId")
                    ->from('assessments_entities ae')
                    ->join('assessments_entities_access aea', 'ae.id=aea.assessments_entities_id')
                    ->where("aea.class_section_id=$section_id")
                    ->where("aea.staff_id=$staff_id")
                    ->where("aea.access_level!='none'")
                    ->where("ae.assessment_id=$assessment_id")
                    ->get()->result();

            $subjects_unique_ids= [0];
            if(!empty($subjects_unique)) {
                foreach($subjects_unique as $key => $val) {
                    $subjects_unique_ids[]= $val->aeId;
                }
            }
            $subjects_unique_id_str= implode(',', $subjects_unique_ids);

            $sql = "select aeg.id as group_id, ae.total_marks, ae.id as ae_id, aeg.entity_name as group_name, aem.name as entity_name, aem.id as entity_id, (case when aem.evaluation_type = 'marks' then 'Marks' else 'Grade' end) as evaluation_type, (case when isnull(aem.derived_formula) then 'Manual' else 'Derived' end) as subject_mode, aeg.is_elective, aeg.elective_group_id 
                    from assessments_entities ae
                    join assessment_entity_master aem on aem.id=ae.entity_id 
                    join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid 
                    where ae.assessment_id=$assessment_id and ae.id in ($subjects_unique_id_str)  
                    order by aeg.sorting_order,aeg.entity_name, aem.derived_formula, aem.sorting_order, aem.name";


            // $sql = "select aeg.id as group_id, ae.total_marks, ae.id as ae_id, aeg.entity_name as group_name, aem.name as entity_name, (case when aea.access_level='write' then 1 else 0 end) as writes, aem.id as entity_id, (case when aem.evaluation_type = 'marks' then 'Marks' else 'Grade' end) as evaluation_type, (case when isnull(aem.derived_formula) then 'Manual' else 'Derived' end) as subject_mode, aeg.is_elective, aeg.elective_group_id 
            //         from assessments_entities_access aea 
            //         join assessments_entities ae on ae.id=aea.assessments_entities_id 
            //         join assessment_entity_master aem on aem.id=ae.entity_id 
            //         join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid 
            //         where ae.assessment_id=$assessment_id and aea.class_section_id=$section_id 
            //         and aea.staff_id=$staff_id and aea.access_level!='none' 
            //         order by aeg.sorting_order,aeg.entity_name, aem.derived_formula, aem.sorting_order, aem.name";
        }
        $groups = $this->db_readonly->query($sql)->result();

        //Get Student count for the section
        $student_count_1 = $this->db_readonly->select("count(*) as count")
            ->from('student_year sy')
            ->join('student_admission sa', 'sa.id=sy.student_admission_id')
            ->where('sy.class_section_id', $section_id)
            ->where('sa.admission_status', '2')
            ->where('sy.promotion_status!=', '4')
            ->where('sy.promotion_status!=', '5')    
            ->where('sy.promotion_status!=', 'JOINED')    
            ->get()->row()->count;

        $ae_ids = [];
        foreach ($groups as $group) {
            $ae_ids[] = $group->ae_id;
        }
        $ids = implode(",", $ae_ids);
        $sql = "select sy.class_section_id, aems.assessments_entities_id, if(  aem.evaluation_type = 'marks', sum(case when aems.status=1 && aems.marks !='-2' then 1 else 0 end), sum(case when aems.status=1 && aems.grade !='TBD' then 1 else 0 end)  ) as saved, sum(case when aems.status=2 then 1 else 0 end) as locked
                from assessments_entities_marks_students aems 

                join assessments_entities ae on ae.id=aems.assessments_entities_id 
                join assessment_entity_master aem on aem.id=ae.entity_id 

                join student_year sy on sy.student_admission_id=aems.student_id 
                where aems.assessments_entities_id in ($ids) 
                and sy.class_section_id='$section_id'  
                and sy.promotion_status not in('JOINED', '5', '4')
                group by sy.class_section_id, aems.assessments_entities_id";
        $marksCount = $this->db_readonly->query($sql)->result();

        // echo '<pre>'; print_r($marksCount); die();

        foreach($groups as $group) {
            if($group->is_elective != '1') {
                $student_count= $student_count_1;
            } else {
                //  Getting ae_gid students 
                $student_count_2= $this->db_readonly->select("count(*) as count")
                        ->from('student_year sy')
                        ->join('student_admission sa', 'sa.id=sy.student_admission_id')
                        ->join('assessment_students_elective ase', 'sa.id=ase.student_id')
                        ->where('sy.class_section_id', $section_id)
                        ->where('sa.admission_status', '2')
                        ->where('ase.ass_entity_gid', $group->group_id)
                        ->where('ase.ass_elective_gid', $group->elective_group_id)
                        ->where('sy.promotion_status!=', '4')
                        ->where('sy.promotion_status!=', '5')    
                        ->where('sy.promotion_status!=', 'JOINED')    
                        ->get()->row()->count;
                $student_count= $student_count_2;
            }
            $mc_obj = null;
            foreach ($marksCount as $mc) {
                if ($group->ae_id == $mc->assessments_entities_id) {
                    $mc_obj = $mc;
                    break;
                }
            }
            if ($mc_obj == null) {
                $group->marks_entry_status = 'Not Entered';
                continue;
            }
            $status = 'Not Entered';
            if ($mc_obj->locked != '0') {
                $status = 'Locked';
            } else {
                if ($mc_obj->saved != '0') {
                    if ($mc_obj->saved == $student_count) {
                        $status = 'Saved';
                    }
                    else {
                        $status = 'Partially Entered';
                    }
                }
            }
            $group->marks_entry_status = $status;
        }
        // echo '<pre>';print_r($groups);die();
        return $groups;
    }

    public function getPermittedSubjects($assessment_id, $is_exam_admin, $section_id) {
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $sql = "select aeg.id as group_id, aeg.entity_name as group_name
                from assessments_entities_access aea 
                join assessments_entities ae on ae.id=aea.assessments_entities_id 
                join assessment_entity_master aem on aem.id=ae.entity_id 
                join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid 
                where ae.assessment_id=$assessment_id and aea.class_section_id=$section_id";
        if(!$is_exam_admin)
            $sql .= " and aea.staff_id=$staff_id and aea.access_level!='none'";
        $sql .= " group by aeg.id order by aeg.sorting_order,aeg.entity_name";
        $groups = $this->db_readonly->query($sql)->result();
        return $groups;
    }

    public function getClassData($class_id) {
        return $this->db_readonly->query("select * from class where id=$class_id")->row();
    }

    public function getElective($group_id) {
        $groupIds = implode(",", $group_id);
        return $this->db_readonly->query("select ifnull(elective_group_id, 0) as elective_group_id from assessment_entities_group where id in ($groupIds)")->row()->elective_group_id;
    }

    public function unlockEntityMarksEntry($entities_id) {
        return $this->db->where('assessments_entities_id', $entities_id)->update('assessments_entities_marks_students', array('status' => 1));
    }

    public function getSectionStudents($section_id, $order_by = 'sy.roll_no') {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(sy.roll_no = 0, 'NA', sy.roll_no), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sa.enrollment_number, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sa.admission_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "registration_no") {
          $std_name = "CONCAT(ifnull(sa.registration_no, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(sy.alpha_rollnum, 'NA'), ' - ', ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name";
        }else {
          $std_name = "CONCAT(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) AS student_name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sa.first_name';
        if ($prefix_order_by == "roll_number") {
          $order_by = 'sy.roll_no';
        }else if($prefix_order_by == "enrollment_number"){
          $order_by = 'sa.enrollment_number';
        }else if($prefix_order_by == "admission_number"){
          $order_by = 'sa.admission_no';
        }else if($prefix_order_by == "alpha_rollnum"){
          $order_by = 'sy.alpha_rollnum';
        }  

        return $this->db_readonly->select("sa.id as student_id, $std_name, IF(sy.roll_no = 0, 'NA', sy.roll_no) as roll_no, sa.admission_no")
        ->from('student_admission sa')
        ->join('student_year sy', 'sa.id=sy.student_admission_id')
        ->where('sy.class_section_id', $section_id)
        ->where('sa.admission_status', 2)
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->order_by("$order_by")
        ->get()->result();
    }

    public function getSectionDetails($section_id) { // solution for: [SVC] | [2024-08-13 18:12:16] | unknown | Call to a member function row() on boolean (/home/<USER>/oxygenv2/application/models/examination/Assessment_marks_model.php:3370) | (0:0) | ************:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
        return $this->db_readonly->query("select id as section_id, section_name, class_id, class_name from class_section where id= '$section_id'")->row();
    }

    public function saveRemarks() {
        $input = $this->input->post();
        // echo '<pre>'; print_r($input);
        $existing = $this->db->select("id, student_id, subject_remarks, subject_id")->where('assessment_id', $input['assessment_id'])->where_in('subject_id', $input['subject_id'])->get('assessment_subject_remarks')->result();
        // echo '<pre>'; print_r($existing); die();
        $ex_data = array();
        foreach ($existing as $key => $ex) {
            $ex_data[$ex->student_id.'_'.$ex->subject_id] = $ex;
        }
        $subject_remarks = $input['remarks'];
        $update_data = array();
        $insert_data = array();
        if(empty($existing)) {
            foreach ($subject_remarks as $key => $remarks) {
                if($remarks == '') continue;
                list($std_id, $subject_id) = explode("_", $key);
                $insert_data[] = array(
                    'assessment_id' => $input['assessment_id'],
                    'subject_id' => $subject_id,
                    'subject_remarks' => $remarks,
                    'student_id' => $std_id
                );
            }
        } else {
            foreach ($subject_remarks as $key => $remarks) {
                list($std_id, $subject_id) = explode("_", $key);
                if(array_key_exists($std_id.'_'.$subject_id, $ex_data)) {
                    if($remarks == '') {
                        $this->db->where('id', $ex_data[$std_id.'_'.$subject_id]->id)->delete('assessment_subject_remarks');
                    } else {
                        $update_data[] = array(
                            'id' => $ex_data[$std_id.'_'.$subject_id]->id,
                            'subject_remarks' => $remarks
                        );
                    }
                } else {
                    if($remarks == '') continue;
                    $insert_data[] = array(
                        'student_id' => $std_id,
                        'assessment_id' => $input['assessment_id'],
                        'subject_id' => $subject_id,
                        'subject_remarks' => $remarks
                    );
                }
            }
        }

        $this->db->trans_start();
        if(!empty($insert_data)) {
            $this->db->insert_batch('assessment_subject_remarks', $insert_data);
        }

        if(!empty($update_data)) {
            $this->db->update_batch('assessment_subject_remarks', $update_data, 'id');
        }

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    public function getPermittedSubjectStatus($assessment_id, $class_id, $is_exam_admin) {
        $staff_id = $this->authorization->getAvatarStakeHolderId();
        $sql = "select aeg.id as group_id, ae.id as assEid, ifnull(elective_group_id,0) as elective_group_id, aem.id as entity_id, aem.name as entity_name, aeg.entity_name as group_name
                from assessments_entities ae 
                join assessment_entity_master aem on aem.id=ae.entity_id 
                join assessment_entities_group aeg on aeg.id=aem.ass_entity_gid 
                where aem.derived_formula is null and ae.assessment_id='$assessment_id' and aem.class_id='$class_id' order by aeg.sorting_order,aeg.id,aem.sorting_order,aem.id"; // Solution for: [VSIPS] | [2024-08-22 18:11:09] | unknown | Call to a member function result() on boolean (/home/<USER>/oxygenv2/application/models/examination/Assessment_marks_model.php:3453) | (0:0) | ************:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

        $data = $this->db_readonly->query($sql)->result();
        $result = array();
        foreach ($data as $key => $val) {
            if(!array_key_exists($val->group_id, $result)) {
                $result[$val->group_id] = array();
                $result[$val->group_id]['group_name'] = $val->group_name;
                $result[$val->group_id]['elective_id'] = $val->elective_group_id;
                $result[$val->group_id]['entities'] = array();
            }
            $result[$val->group_id]['entities'][$val->assEid] = $val->entity_name;
        }
        return $result;
    }

    public function getMarksEntryStatusClassWise($assessment_entities_ids, $elective_group_id, $group_id, $class_id) {
        if($elective_group_id == 0) {
            $sql = "select sy.class_section_id, count(sa.id) as total_students 
                    from student_admission sa 
                    join student_year sy on sy.student_admission_id=sa.id 
                    where sy.class_section_id in (select id from class_section where class_id='$class_id' and is_placeholder=0) and sa.admission_status=2 and sy.promotion_status not in ('4', '5', 'JOINED') group by sy.class_section_id";
        } else {
            $sql = "select sy.class_section_id, count(sa.id) as total_students 
                    from student_admission sa 
                    join student_year sy on sy.student_admission_id=sa.id 
                    join assessment_students_elective ase on ase.student_id=sa.id 
                    where sy.class_section_id in (select id from class_section where class_id='$class_id' and is_placeholder=0) and sa.admission_status=2 and sy.promotion_status not in ('4', '5', 'JOINED')
                    and ase.ass_elective_gid='$elective_group_id' and ase.ass_entity_gid='$group_id' group by sy.class_section_id";
        }
        $stdCount = $this->db_readonly->query($sql)->result();

        $totals = array();
        foreach ($stdCount as $key => $count) {
            $totals[$count->class_section_id] = $count->total_students;
        }

        $ids = implode(",", $assessment_entities_ids);
        $sql = "select sy.class_section_id, aems.assessments_entities_id, sum(case when aems.status=1 and aems.marks !=-2.00 then 1 else 0 end) as saved, sum(case when aems.status=2 and aems.marks !=-2.00 then 1 else 0 end) as locked 
                from assessments_entities_marks_students aems 
                join student_year sy on sy.student_admission_id=aems.student_id 
                where aems.assessments_entities_id in ($ids) 
                and sy.class_section_id in (select id from class_section where class_id=$class_id and is_placeholder=0) 
                and sy.promotion_status not in ('4', '5', 'JOINED')
                group by sy.class_section_id, aems.assessments_entities_id";
        $marksCount = $this->db_readonly->query($sql)->result();
        
        $sections = $this->db_readonly->query("select id from class_section where class_id=$class_id and is_placeholder=0")->result();
        $status = array();
        foreach ($marksCount as $key => $count) {
            $status[$count->assessments_entities_id][$count->class_section_id] = $count;
        }

        $statusCount = array();
        foreach ($assessment_entities_ids as $id) {
            $statusCount[$id] = array();
            foreach ($sections as $key => $sec) {
                if(!array_key_exists($id, $status)) {
                    $statusCount[$id][$sec->id]['total'] = (array_key_exists($sec->id, $totals))?$totals[$sec->id]:0;
                    $statusCount[$id][$sec->id]['saved'] = 0;
                    $statusCount[$id][$sec->id]['locked'] = 0;
                    $statusCount[$id][$sec->id]['not_added'] = $statusCount[$id][$sec->id]['total'];
                } else {
                    $statusCount[$id][$sec->id]['total'] = (array_key_exists($sec->id, $totals))?$totals[$sec->id]:0;
                    $statusCount[$id][$sec->id]['saved'] = (isset($totals[$sec->id]) && isset($status[$id]) && array_key_exists($sec->id, $status[$id]) && isset($status[$id][$sec->id])) ? ($status[$id][$sec->id]->saved > $totals[$sec->id] ? $totals[$sec->id] : $status[$id][$sec->id]->saved) : 0;
                    $statusCount[$id][$sec->id]['locked'] = (isset($totals[$sec->id]) && isset($status[$id]) && array_key_exists($sec->id, $status[$id]) && isset($status[$id][$sec->id]))?($status[$id][$sec->id]->locked > $totals[$sec->id]?$totals[$sec->id]:$status[$id][$sec->id]->locked):0;
                    $statusCount[$id][$sec->id]['not_added']= $statusCount[$id][$sec->id]['total'] - ($statusCount[$id][$sec->id]['saved'] + $statusCount[$id][$sec->id]['locked']);
                }
            }
        }
        return $statusCount;
    }

    public function getSections($class_id) {
        $secs= $this->db_readonly->query("select id, section_name from class_section where class_id=$class_id and is_placeholder=0")->result();
        if(!empty($secs)) {
            foreach($secs as $key => $val) {
                $students= $this->db_readonly->query("select count(sa.id) as count from student_year sy join student_admission sa on sa.id = sy.student_admission_id where sy.class_section_id=$val->id and sy.promotion_status not in ('4', '5', 'JOINED') and sa.admission_status = 2")->result();
                if(!empty($students)) {
                    $val->std_count= $students[0]->count;
                } else {
                    $val->std_count= 0;
                }
            }
        }
        return $secs;
    }

    public function changePublishStatus() {
        return $this->db->where('marks_card_temp_id', $_POST['tempId'])
        ->where('student_id', $_POST['stdId'])
        ->update('assessments_marks_cards', array('published' => $_POST['status']));
    }

    public function changeMultiPublishStatus() {
        return $this->db->where('marks_card_temp_id', $_POST['tempId'])
        ->where_in('student_id', $_POST['stdIds'])
        ->update('assessments_marks_cards', array('published' => $_POST['status']));
    }

    public function getAttendanceData($subject_id, $from_date, $to_date, $student_id) {
        $sql = "SELECT SUM(case when ats.status=1 or ats.status=3 then 1 else 0 end) as present, SUM(case when ats.status=2 then 1 else 0 end) as absent 
                from attendance_v2_master am 
                join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                where ats.student_admission_id=$student_id 
                and am.type=0 and am.type_id=$subject_id 
                and am.date>='$from_date' and am.date<='$to_date' ";
        return $this->db_readonly->query($sql)->row();
    }

    public function getAttendanceDataElective($elective_id, $from_date, $to_date, $student_id) {
        $subject_id_sql = "select subject_master_id from elective_master_group_subjects where id = (select elective_master_group_subject_id from elective_student_master where elective_master_group_subject_id in 
        (select id from elective_master_group_subjects where elective_master_group_id=$elective_id) and student_admission_id=$student_id)";

        $subject_id_result = $this->db_readonly->query($subject_id_sql)->row();

        if (empty($subject_id_result)) {
            $att = new stdClass();
            $att->present = 0;
            $att->absent = 0;
            return $att;
        }

        $subject_id = $subject_id_result->subject_master_id;
        $sql = "SELECT SUM(case when ats.status=1 or ats.status=3 then 1 else 0 end) as present, SUM(case when ats.status=2 then 1 else 0 end) as absent 
                from attendance_v2_master am 
                join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                where ats.student_admission_id=$student_id 
                and am.type=0 and am.type_id=$subject_id 
                and am.date>='$from_date' and am.date<='$to_date' ";
        $result = $this->db_readonly->query($sql)->row();

        return $result;
    }

    public function getAttendanceDataDayWise($from_date, $to_date, $student_id) {
        $sql = "SELECT am.date, am.id as att_id, ats.student_admission_id as student_id, sum(case when ats.status=1 or ats.status=3 then 1 else 0 end) as present, sum(case when ats.status=2 then 1 else 0 end) as absent 
                    from attendance_v2_master am 
                    join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                    where (am.date>='$from_date' and am.date<='$to_date') 
                    and ats.student_admission_id=$student_id 
                    and ats.status!=0 and am.type_id!=0 
                    group by am.date, ats.student_admission_id";
        $result = $this->db_readonly->query($sql)->result();
        $attendance = new stdClass();
        $attendance->present = 0;
        $attendance->absent = 0;
        foreach ($result as $res) {
            if($res->present) {
                $attendance->present++;
            } else if($res->absent) {
                $attendance->absent++;
            }
        }
        return $attendance;
    }

    public function getAttendanceDataDayWiseAll($from_date, $to_date, $student_id) {
        $sql = "SELECT am.date, am.id as att_id, ats.student_admission_id as student_id, sum(case when ats.status=1 or ats.status=3 then 1 else 0 end) as present, sum(case when ats.status=2 then 1 else 0 end) as absent 
                    from attendance_v2_master am 
                    join attendance_v2_student ats on ats.attendance_v2_master_id=am.id 
                    where (am.date>='$from_date' and am.date<='$to_date') 
                    and ats.student_admission_id=$student_id 
                    and ats.status!=0 and am.type_id!=0 
                    group by am.date, ats.student_admission_id";
        $result = $this->db_readonly->query($sql)->result();
        $attendance = new stdClass();
        $attendance->present = 0;
        $attendance->absent = 0;
        foreach ($result as $res) {
            if($res->present) {
                $attendance->present += $res->present;
            } 
            if($res->absent) {
                $attendance->absent += $res->absent;
            }
        }
        return $attendance;
    }

    public function getAttendanceDataDayWise_v1($from_date, $to_date, $student_id) {
        $sql = "SELECT DATE_FORMAT(`attsess`.`day`, '%d-%m-%Y') as day, count(`attstu`.attendance_master_id) as total_hours, sum(case when (`attstu`.`status` = 1) then 1 else 0 end) as total_present, attstu.history
        FROM `student_admission` `sd`
        JOIN `student_year` `ss` ON `sd`.`id` = `ss`.`student_admission_id`
        JOIN `attendance_student` as `attstu` ON `attstu`.`student_id` = `ss`.`id`
        JOIN `attendance_session` `attsess` ON `attsess`.`id` = `attstu`.`attendance_session_id`
        WHERE `attsess`.`day` >= '$from_date'
        AND `attsess`.`day` <= '$to_date'
        AND sd.id='$student_id'
        group by day";

        $result = $this->db_readonly->query($sql)->result();

        $attendance = new stdClass();
        $attendance->present = 0;
        $attendance->absent = 0;
        $attendance->count_late = 0;
        foreach ($result as $res) {
            $total_hours = $res->total_hours;
            $total_present = $res->total_present;
    
            $first_half_limit = ceil($total_hours / 2); 
            $second_half_limit = $total_hours - $first_half_limit; 
    
            if ($total_present == 0) {
                $attendance->absent++;
            } else if ($total_present >= $first_half_limit && $total_present < $total_hours) {
                $attendance->present += 0.5;
                $attendance->absent += 0.5;
            } else if ($total_present < $first_half_limit && $total_present > 0) {
                $attendance->present += 0.5;
                $attendance->absent += 0.5;
            } else {
                $attendance->present++;
            }
    
            $temp_late = json_decode($res->history);
            if (!empty($temp_late)) {
                if ($temp_late[count($temp_late) - 1]->type == '1') {
                    $attendance->count_late++;
                }
            }
        }
                return $attendance;
    }

    public function getAttendanceDataDayWise_v2($from_date, $to_date, $student_id, $class_section_id) {
        // 1. Get class_id for the section
        $section = $this->db_readonly->select('class_id')->from('class_section')->where('id', $class_section_id)->get()->row();
        if (!$section) {
            return ['error' => 'Invalid section'];
        }
        $class_id = $section->class_id;

      
        
        // 2. Get assigned academic calendar for the section
        $calendar = $this->db_readonly->select('cm.id, cm.start_date, cm.end_date')
            ->from('calendar_v2_master cm')
            ->join('calendar_events_v2_assigned ce', 'ce.calendar_v2_master_id = cm.id')
            ->where('ce.assigned_section_id', $class_section_id)
            ->where('cm.academic_year', $this->yearId)
            ->get()->row();
        if (!$calendar) {
            return ['error' => 'No academic calendar assigned to this section'];
        }
        // 3. Get session count map (day_name => session_count)
        $sessionCounts = $this->db_readonly->select('day_name, session_count')
            ->from('calendar_events_v2_override_sessions')
            ->where('calendar_v2_master_id', $calendar->id)
            ->get()->result();
        $sessionMap = [];
        foreach ($sessionCounts as $sc) {
            $sessionMap[strtolower($sc->day_name)] = (int)$sc->session_count;
        }
        // 4. Generate date range within calendar and requested dates
        $fromDate = max(date('Y-m-d', strtotime($from_date)), $calendar->start_date);
        $toDate = min(date('Y-m-d', strtotime($to_date)), $calendar->end_date);
        if ($fromDate > $toDate) {
            return ['error' => 'Date range outside academic calendar'];
        }
        $allDates = [];
        $start = new DateTime($fromDate);
        $end = new DateTime($toDate);
        while ($start <= $end) {
            $allDates[] = $start->format('Y-m-d');
            $start->modify('+1 day');
        }
        // 5. Get holidays
        $holidays = $this->db_readonly->select('ce.from_date, ce.to_date')
            ->from('calendar_events_v2 ce')
            ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = ce.calendar_v2_master_id')
            ->where('cea.assigned_section_id', $class_section_id)
            ->where_in('ce.event_type', ['holiday', 'holiday_range'])
            ->where('ce.from_date <=', $toDate)
            ->where('ce.to_date >=', $fromDate)
            ->get()->result();
        $holiday_dates = [];
        foreach ($holidays as $holiday) {
            $h_start = new DateTime($holiday->from_date);
            $h_end = new DateTime($holiday->to_date);
            while ($h_start <= $h_end) {
                $holiday_dates[] = $h_start->format('Y-m-d');
                $h_start->modify('+1 day');
            }
        }
        // 6. Exclude weekends (session_count=0)
        $workingDays = [];
        foreach ($allDates as $date) {
            $dayName = strtolower(date('l', strtotime($date)));
            if (in_array($date, $holiday_dates)) continue;
            if (isset($sessionMap[$dayName]) && $sessionMap[$dayName] == 0) continue;
            $workingDays[] = $date;
        }
        $total_working_days = count($workingDays);
        // 7. Get attendance for the student for all working days
        $present = 0;
        $absent = 0;
        $late = 0;
        $total_sessions = 0;
        foreach ($workingDays as $date) {
            $dayName = strtolower(date('l', strtotime($date)));
            $sessionCount = isset($sessionMap[$dayName]) ? $sessionMap[$dayName] : 2;
            $total_sessions += $sessionCount;
            $att = $this->db_readonly->select('morning_session_status, afternoon_session_status, is_late')
                ->from('attendance_std_day_v2_students s')
                ->join('attendance_std_day_v2_session sess', 'sess.id = s.attendance_day_v2_session_id')
                ->where('sess.class_id', $class_id)
                ->where('sess.class_section_id', $class_section_id)
                ->where('sess.att_taken_date', $date)
                ->where('s.student_admission_id', $student_id)
                ->get()->row();
            if ($sessionCount == 1) {
                if ($att && $att->morning_session_status == 1) {
                    $present++;
                } else {
                    $absent++;
                }
            } else {
                if ($att) {
                    $present += ($att->morning_session_status == 1 ? 0.5 : 0);
                    $absent += ($att->morning_session_status == 1 ? 0 : 0.5);
                    $present += ($att->afternoon_session_status == 1 ? 0.5 : 0);
                    $absent += ($att->afternoon_session_status == 1 ? 0 : 0.5);
                    if ($att->is_late == 1) {
                        $late++;
                    }
                } else {
                    $absent += 1;
                }
            }
        }
        $percentage = $total_sessions > 0 ? round(($present / (float)$total_sessions) * 100, 1) : 0;
       
        return (object) [
            'total_working_days' => $total_working_days,
            'total_present' => $present,
            'total_absent' => $absent,
            'total_late' => $late,
            'attendance_percentage' => $percentage,
            // Add properties expected by the controller
            'present' => $present,
            'absent' => $absent,
            'count_late' => $late
        ];
        
    }

    


    public function getClassMappings($class_id) {
        $sql = "SELECT aeg.id as group_id, aeg.entity_name as group_name, aeg.mapping_string as group_mapping_string, aeg.is_elective, aem.id as entity_id, aem.name as entity_name, aem.mapping_string as entity_mapping_string, aem.evaluation_type, e.group_name as elective_name, e.mapping_string as elective_mapping_string 
                from assessment_entities_group aeg 
                join assessment_entity_master aem on aem.ass_entity_gid=aeg.id 
                left join assessment_elective_group e on e.id=aeg.elective_group_id and aeg.is_elective=1 
                where aeg.class_id=$class_id 
                order by aeg.sorting_order, aeg.entity_name, aem.sorting_order, aem.name";
        $result = $this->db_readonly->query($sql)->result();

        $groups = [];
        $group_ids = [];
        $components = [];
        foreach ($result as $key => $res) {
            $group_id = $res->group_id;
            if(!array_key_exists($group_id, $groups)) {
                $groups[$group_id] = array(
                    'id' => $group_id,
                    'name' => $res->group_name,
                    'mapping_string' => $res->group_mapping_string,
                    'is_elective' => $res->is_elective,
                    'elective_name' => $res->elective_name,
                    'elective_mapping_string' => $res->elective_mapping_string,
                    'components' => []
                );
            }

            $entity_elective_mapping = '';
            if($res->is_elective) {
                $entity_elective_mapping = $this->_form_entity_elective_mapping($res->entity_mapping_string, $res->elective_mapping_string);
            }

            $groups[$group_id]['components'][] = array(
                'id' => $res->entity_id,
                'name' => $res->entity_name,
                'mapping_string' => $res->entity_mapping_string,
                'evaluation_type' => $res->evaluation_type,
                'is_elective' => $res->is_elective,
                'elective_mapping_string' => $entity_elective_mapping
            );
        }

        $subjects = [];
        foreach ($groups as $group) {
            $subjects[] = $group;
        }

        return $subjects;
    }

    private function _form_entity_elective_mapping($entity_mapping, $elective_mapping) {
        $var1 = explode('_', $entity_mapping);
        array_shift($var1);
        $str = implode('_', $var1);

        $var2 = substr($elective_mapping, 0, -2);
        return $var2.'_'.$str;
    }

    public function getClassMasterSubjects($class_id) {
        $sql = "SELECT sm.id, sm.subject_name, sm.short_name 
                from subject_master sm 
                where sm.id in (select subject_master_id from lp_subjects 
                where class_master_id in (select class_master_id from class where id=$class_id)) 
                and sm.status=1 
                order by sm.subject_name";
        return $this->db_readonly->query($sql)->result();
    }

    public function generate_average_high_rank_marks($section_id, $entity_id, $assessment_id, $class_id) {

        // Step 1: Generate Average and High for class
        $high_avg_class_obj= $this->db->select('max(aems.marks) as max, if(aems.marks > 0, round(avg(aems.marks), 2), 0) as avg, ae.total_marks, ae.id as ae_id')
                    ->from('assessments_entities_marks_students aems')
                    ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
                    ->where('ae.entity_id', $entity_id)
                    ->where('ae.assessment_id', $assessment_id)
                    // ->where('aems.acad_year_id', $this->yearId)
                    ->get()->row();

        $this->db->where('id', $high_avg_class_obj->ae_id)
            ->update('assessments_entities', array('class_average' => $high_avg_class_obj->avg, 'class_highest' => $high_avg_class_obj->max));

        // Step 2: Generate Average and High for section
        $x= $this->__generate_sec_avg_high($section_id, $assessment_id, $entity_id);
        
        $high_avg_class_obj->section_new_avg= $x->section_new_avg;
        $high_avg_class_obj->section_new_max= $x->section_new_max;

         //Step 3: Generating ranks for both class and section
         $this->generate_ranks($section_id, $entity_id, $assessment_id, $high_avg_class_obj->ae_id);

        return $high_avg_class_obj;
    }

    public function checkIfTotalMarksIsAdded() {
        return $this->db_readonly->select("ae.total_marks")->where('ae.id', $_POST['ae_id'])->get('assessments_entities ae')->row()->total_marks;
    }

    public function add_update_total_marks() {
        $this->db->where('id', $_POST['ae_id'])->update('assessments_entities', array('total_marks' => $_POST['add_total_marks_id']));
        return $this->db->affected_rows();
    }

    public function get_assessment_class_wise($input) {
        $res= $this->db_readonly->select("id, short_name, long_name, generation_type")
            ->where('class_id', $input['selected_class_id'])
            ->where('acad_year_id', $this->yearId)
            ->get('assessments')->result();
        $cf= $this->db_readonly->select("id, name, mapping_string, fields_operation, total_marks, rounding")
            ->where('class_id', $input['selected_class_id'])
            ->get('assessment_computed_field_master')->result();
        if(!empty($cf)) {
            foreach($cf as $key => $val) {
                $generated= $this->db_readonly->where('ass_computed_field_master_id', $val->id)->get('assessment_computed_field_details')->row();
                if(!empty($generated)) {
                    $val->is_generated= '1';
                    $val->grand_total_marks= $generated->grand_total_marks;
                } else {
                    $val->is_generated= '-1';
                    $val->grand_total_marks= 0;
                }
            }
        }

        if(!empty($res) && !empty($cf)) {
            return ['assessments' => $res, 'cf' => $cf];
        } else if(!empty($res)) {
            return ['assessments' => $res, 'cf' => []];
        } else if(!empty($cf)) {
            return ['assessments' => [], 'cf' => $cf];
        }
        return ['assessments' => [], 'cf' => []];
    }

    public function get_subjects_class_wise($input) {
        $selected_class_id= $input['selected_class_id'];
        $selected_assessment_id= $input['selected_assessment_id'];
        $res= $this->db_readonly->select("aem.id, concat(aeg.entity_name, '-', aem.name) as display_name, aem.name, aem.ass_entity_gid, ae.total_marks, aeg.is_elective, if(aeg.is_elective = '1', a_ele_g.group_name, '') as elective_name")
            ->from('assessment_entity_master aem')
            ->join('assessments_entities  ae', 'ae.entity_id= aem.id')
            ->join('assessment_entities_group  aeg', 'aem.ass_entity_gid= aeg.id')
            ->join('assessment_elective_group  a_ele_g', 'a_ele_g.id= aeg.elective_group_id', 'left')
            ->where('aem.class_id', $selected_class_id)
            ->where('ae.assessment_id', $selected_assessment_id)
            // ->where('aem.evaluation_type', 'marks') // Show only marks subjects which can be computed
            ->order_by('aem.ass_entity_gid')
            ->get()->result();

        if(!empty($res)) {
            return $res;
        }
        return array();
    }

    public function create_derived_fields($inputs) {
        // echo '<pre>'; print_r(json_decode("{'aas': '45'}")); die();
        // echo '<pre>'; print_r($inputs); die();
        $add_or_update= $inputs['add_or_update'];
        $hidden_acfm_id1= isset($inputs['hidden_acfm_id1']) ? $inputs['hidden_acfm_id1'] : 0;
// Formating Ass Data
        $array = array();
        $reduced_perc_sub_arr= [];
        if(isset($inputs['computed_fields_arr'])) {
            foreach($inputs['computed_fields_arr'] as $key => $val) {
                $information_arr= explode('___', $val);
                $array[$information_arr[2]][] = array(
                    'entity_id' => $information_arr[0],
                    'group_id' => $information_arr[1],
                    'assessment_id' => $information_arr[2],
                    'entity_name' => $information_arr[3],
                    'subject_total_mark' => $information_arr[4],
                    'is_elective' => $information_arr[5]
                );
                if($inputs['operation'] == 'Percentage') {
                    $index= $information_arr[2]. '___' .$information_arr[0];
                    $reduced_perc_sub_arr[$index]= $inputs['computed_fields_reduced_percentage_arr'][$key]; // index is assessment_id___entity_id
                }
            }
        }
// Formating Computed Field Data
        $reduced_perc_cf_arr= [];
        if(isset($inputs['cf_arr'])) {
            foreach($inputs['cf_arr'] as $key1 => $val1) {
                $information_arr= explode('___', $val1);
                $array['computed_fields'][] = array(
                    'cf_id' => $information_arr[0],
                    'cf_name' => $information_arr[1]
                );
                if($inputs['operation'] == 'Percentage') {
                    $reduced_perc_cf_arr[$information_arr[0]]= $inputs['cf_reduced_percentage_arr'][$key1]; // index is entity_id
                }
            }
        }

        $subject_json= json_encode($array);

        $insert_data= array(
            'name' => $inputs['field_name'],
            'mapping_string' => $inputs['field_mapping_string'],
            'subjects' => $subject_json,
            'acad_year_id' => $this->yearId,
            'class_id' => $inputs['selected_class_id'],
            'total_marks' => isset($inputs['total_marks']) ? $inputs['total_marks'] : NULL,
            'fields_operation' => $inputs['operation'],
            'rounding' => $inputs['rounding']
        );
        if($inputs['operation'] == 'Exam Result') {
            $perc= $inputs['pass_fail'];
            $stdObj= new stdClass();
            $stdObj->failure_percentage= $perc;
            $insert_data['field_parameters']= json_encode($stdObj);
        } else if($inputs['operation'] == 'Percentage') {
            $stdObj= new stdClass();
            if(!empty($reduced_perc_sub_arr)) {
                $stdObj->subjects_redued_percentage= $reduced_perc_sub_arr;
            } if(!empty($reduced_perc_cf_arr)) {
                $stdObj->cf_reduced_percentage= $reduced_perc_cf_arr;
            }
            $insert_data['field_parameters']= json_encode($stdObj);
        } else if($inputs['operation'] == 'GPA' || $inputs['operation'] == 'GPS') {
            $gradingId= $inputs['grading_system'];
            $stdObj= new stdClass();
            $stdObj->grading_system_id= $gradingId;
            $insert_data['field_parameters']= json_encode($stdObj);
        }

        $inf_arr= $array;

        $this->db->trans_start();
            if($add_or_update == 'add') {
                $this->db->insert('assessment_computed_field_master', $insert_data);
            } else {
                $this->db->where('id', $hidden_acfm_id1)->update('assessment_computed_field_master', $insert_data);
                $this->db->where('ass_computed_field_master_id', $hidden_acfm_id1)->delete('assessment_computed_field_details');
            }
        $this->db->trans_complete();
        if($this->db->trans_status()) {
            return $this->db->trans_status();
        } else {
            $this->db->trans_rollback();
            return false;
        }
    }

    private function __insert_best_of_all_ass_of_assessments_data($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id) {
        $details_arr= json_decode($detail_in_json);
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 0;
            $total= 0;
            $remarks= '';
            foreach($details_arr as $ass_key => $ass_val) {
                $sum_of_sub= 0;
                $total1= 0;
                foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                    if($ent_val->aems_marks[$std_key] > 0) {
                        if($ent_val->aems_marks[$std_key] != -1 && $ent_val->aems_marks[$std_key] != -2 && $ent_val->aems_marks[$std_key] != -3) {
                            $total1 += $ent_val->subject_total_mark;
                            $sum_of_sub += $ent_val->aems_marks[$std_key];
                        }
                    }
                }

                if($sum_of_sub > $result) {
                    $ass_name= $this->db->select('short_name')->where('id', $ent_val->assessment_id)->get('assessments')->row()->short_name;
                    $remarks= $ass_name;
                    $result= $sum_of_sub;
                    $total= $total1;
                }
            }

            $student_insert[]= array(
                'ass_computed_field_master_id' => $insert_id,
                'student_id' => $std_val,
                'class_section_id' => $class_section_ids_arr[$std_key],
                'result' => $result,
                'grand_total_marks' => $total
            );
        }

        $this->db->insert_batch('assessment_computed_field_details', $student_insert);
        return true;
    }

    private function __insert_exam_result($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding, $failure_perc) {
        $details_arr= json_decode($detail_in_json);
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 'Pass';
            $percent= 0;
            foreach($details_arr as $ass_key => $ass_val) {
                if($ass_key != 'computed_fields') {
                    foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                        if(isset($ent_val->aems_marks)) {
                            if($ent_val->aems_marks[$std_key] > 0) {
                                $perc= ( $ent_val->aems_marks[$std_key] / $ent_val->subject_total_mark ) * 100;
                                if(number_format($perc, 2) < number_format($failure_perc, 2)) {
                                    $result = 'Failed';
                                    $percent= $perc;
                                    break;
                                }
                            }
                        } 
                        // else {
                        //     $result= 'TBD';
                        //     break;
                        // }
                    }
                    if($result == 'Failed' || $result == 'TBD') {
                        break;
                    }
                } else {
                    foreach($details_arr->$ass_key as $computed_key => $computed_val) {
                        $computed_inf= $this->db->select("result, grand_total_marks")->where('ass_computed_field_master_id', $computed_val->cf_id)->where('student_id', $std_val)->get('assessment_computed_field_details')->result();
                        if(!empty($computed_inf)) {
                            $perc= ( $computed_inf[0]->result / $computed_inf[0]->grand_total_marks ) * 100;
                            if(number_format($perc, 2) < number_format($failure_perc, 2)) {
                                $result = 'Failed';
                                $percent= $perc;
                                break;
                            }
                        } 
                        // else {
                        //     $result= 'TBD';
                        //     break;
                        // }
                    }
                    if($result == 'Failed' || $result == 'TBD') {
                        break;
                    }
                }
            }

            $student_insert[]= array(
                'ass_computed_field_master_id' => $insert_id,
                'student_id' => $std_val,
                'class_section_id' => $class_section_ids_arr[$std_key],
                'result' => $result
                // ,'grand_total_marks' => number_format($total_marks, $rounding)
            );

            // echo $percent. ' - ';
        }
        // die();

        $this->db->insert_batch('assessment_computed_field_details', $student_insert);
        return true;
    }

    private function __insert_exam_result_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $failure_percentage) {
        $entities_ids= [];
        $assesments_ids= [];
        $computed_ids_if_there= [];
        $calculated_sub_totalMarks= 0;
        $total= 0;
        if(!empty($subjects)) {
            foreach($subjects as $key => $val) {
                foreach($val as $keyOne => $valOne) {
                    $total ++;
                    if($key == 'computed_fields') {
                        $computed_ids_if_there[]= $valOne->cf_id;
                    } else {
                        $calculated_sub_totalMarks = $valOne->subject_total_mark;
                        $entities_ids[]= $valOne->entity_id;
                        // $assesments_ids[]= $valOne->assessment_id;
                    }
                }
                if($key != 'computed_fields') {
                    $assesments_ids[$key]= $entities_ids;
                    $entities_ids= [];
                }
            }
        }

        // echo '<pre>'; print_r($assesments_ids); die();
  
          // Subject marks as student obj
          $entities_marks_student_wise= [];
         if(!empty($assesments_ids)) {
              $entities_marks_student_wise= $this->__get_entities_result_student_wise($student_ids_arr, $assesments_ids, $failure_percentage);
              $entities_marks_student_wise_v2= [];
              foreach($entities_marks_student_wise as $key => $val) {
                  $entities_marks_student_wise_v2[$val->student_id]= $val;
              }
              
         }
  
          // Computed marks as student obj
          $compted_marks_student_wise= [];
          if(!empty($computed_ids_if_there)) {
              $compted_marks_student_wise= $this->__get_computed_result_student_wise($student_ids_arr, $computed_ids_if_there, $failure_percentage);
              $compted_marks_student_wise_v2= [];
              foreach($compted_marks_student_wise as $key => $val) {
                  if($key == 0) {
                      $calculated_sub_totalMarks += $val->grand_total_marks;
                  }
                  $compted_marks_student_wise_v2[$val->student_id]= $val;
              }
          }
  
         
  
          $actual_total_marks= $calculated_sub_totalMarks;
          if($total_marks > 0) {
              $actual_total_marks= $total_marks;
          }

        $return= false;
        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                if(!empty($entities_marks_student_wise_v2) && !empty($compted_marks_student_wise_v2)) {
                    $result= $entities_marks_student_wise_v2[$val->student_id]->exam_result == 'Pass' && $compted_marks_student_wise_v2[$val->student_id]->exam_result == 'Pass' ? 'Pass' : 'Failed';
                } else if(!empty($entities_marks_student_wise_v2)) {
                    $result= $entities_marks_student_wise_v2[$val->student_id]->exam_result;
                } else if(!empty($compted_marks_student_wise_v2)) {
                    $result= $compted_marks_student_wise_v2[$val->student_id]->exam_result;
                }
                $val->result = $result;
                // echo '<pre>Pre: '; print_r(number_format($result, $rounding)); die();
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];
                }
                
                if(!empty($entities_marks_student_wise_v2) && !empty($compted_marks_student_wise_v2)) {
                    $result= $entities_marks_student_wise_v2[$val]->exam_result == 'Pass' && $compted_marks_student_wise_v2[$val]->exam_result == 'Pass' ? 'Pass' : 'Failed';
                } else if(!empty($entities_marks_student_wise_v2)) {
                    $result= $entities_marks_student_wise_v2[$val]->exam_result;
                } else if(!empty($compted_marks_student_wise_v2)) {
                    $result= $compted_marks_student_wise_v2[$val]->exam_result;
                }

                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => $result,
                    'grand_total_marks' => $actual_total_marks
                );
            }
            // echo '<pre>Fresh: '; print_r($fresh_generations_insert); die();
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }
        return true;
    }

    private function __insert_average_of_assessments_data($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding) {
        $details_arr= json_decode($detail_in_json);
        // echo '<pre>'; print_r($details_arr); die();
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 0;
            $total= 0;
            $total_marks= 0;
            foreach($details_arr as $ass_key => $ass_val) {
                if($ass_key != 'computed_fields') {
                    foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                        if(isset($ent_val->aems_marks)) {
                            if($ent_val->aems_marks[$std_key] > 0) {
                                $result += $ent_val->aems_marks[$std_key];
                            }
                        }
                        $total ++;
                        $total_marks= $ent_val->subject_total_mark;
                    }
                } else {
                    foreach($details_arr->$ass_key as $computed_key => $computed_val) {
                        $computed_inf= $this->db->select("result, grand_total_marks")->where('ass_computed_field_master_id', $computed_val->cf_id)->where('student_id', $std_val)->get('assessment_computed_field_details')->result();
                        if(!empty($computed_inf)) {
                            $total++;
                            $result += $computed_inf[0]->result;
                            $total_marks= $computed_inf[0]->grand_total_marks;
                        }
                        
                    }
                }
            }
            if($total != 0) {
                $result= number_format($result / $total, 2);
            } else {
                $result= 0;
            }

            $student_insert[]= array(
                'ass_computed_field_master_id' => $insert_id,
                'student_id' => $std_val,
                'class_section_id' => $class_section_ids_arr[$std_key],
                'result' => number_format($result, $rounding),
                'grand_total_marks' => number_format($total_marks, $rounding)
            );
        }

        $this->db->insert_batch('assessment_computed_field_details', $student_insert);
        return true;
    }

    private function __insert_average_of_assessments_data_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects) {
        // echo '<pre>subjects '; print_r($subjects); die();
        $entities_ids= [];
        $assesments_ids= [];
        $computed_ids_if_there= [];
        $calculated_sub_totalMarks= 0;
        $total= 0;
        $temp_elective_array= [];
        if(!empty($subjects)) {
            foreach($subjects as $key => $val) {
                foreach($val as $keyOne => $valOne) {
                   if(isset($valOne->is_elective) && $valOne->is_elective == '1') { //[DIYA] | [2024-11-28 21:39:11] | unknown | [error_statistics_string] | Undefined property: stdClass::$is_elective (/home/<USER>/oxygenv2/application/models/examination/Assessment_marks_model.php:4404) | (Notice:8) | 10.11.142.72:Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 
                        $elective_grp_id= $this->db->select('elective_group_id')->where('id', $valOne->group_id)->get('assessment_entities_group')->row();
                        if(!empty($elective_grp_id)) {
                            $elective_grp_id= $elective_grp_id->elective_group_id;
                            if(empty($temp_elective_array) || !in_array($elective_grp_id, $temp_elective_array)) {
                                $total ++;
                                $temp_elective_array[]= $elective_grp_id;
                            }
                        }
                   } else {
                       $total ++;
                   }
                    if($key == 'computed_fields') {
                        $computed_ids_if_there[]= $valOne->cf_id;
                    } else {
                        $calculated_sub_totalMarks = $valOne->subject_total_mark;
                        $entities_ids[]= $valOne->entity_id;
                        // $assesments_ids[]= $valOne->assessment_id;
                    }
                }
                if($key != 'computed_fields') {
                    $assesments_ids[$key]= $entities_ids;
                    $entities_ids= [];
                }
            }
        }
  
          // Subject marks as student obj
          $entities_marks_student_wise= [];
         if(!empty($assesments_ids)) {
              $entities_marks_student_wise= $this->__get_entities_marks_student_wise($student_ids_arr, $assesments_ids);
              $entities_marks_student_wise_v2= [];
              foreach($entities_marks_student_wise as $key => $val) {
                  $entities_marks_student_wise_v2[$val->student_id]= $val;
              }
         }
  
          // Computed marks as student obj
          $compted_marks_student_wise= [];
          if(!empty($computed_ids_if_there)) {
              $compted_marks_student_wise= $this->__get_computed_marks_student_wise($student_ids_arr, $computed_ids_if_there);
              $compted_marks_student_wise_v2= [];
              foreach($compted_marks_student_wise as $key => $val) {
                  if($key == 0) {
                      $calculated_sub_totalMarks = $val->grand_total_marks;
                  }
                  $compted_marks_student_wise_v2[$val->student_id]= $val;
              }
          }
  
          // echo '<pre>'; print_r($compted_marks_student_wise_v2); die();
  
          $actual_total_marks= $calculated_sub_totalMarks;
          if($total_marks > 0) {
              $actual_total_marks= $total_marks;
          }

        $return= false;
        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                $result= (!empty($entities_marks_student_wise_v2) ? $entities_marks_student_wise_v2[$val->student_id]->sumMarks : 0) + (!empty($compted_marks_student_wise_v2) ? $compted_marks_student_wise_v2[$val->student_id]->sumMarks : 0);
                $val->result = round($result / $total, $rounding); // average= result of total marks devided by total no. of components
                // echo '<pre>Pre: '; print_r(number_format($result, $rounding)); die();
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];
                }
                
                $result= (!empty($entities_marks_student_wise_v2) ? $entities_marks_student_wise_v2[$val]->sumMarks : 0) + (!empty($compted_marks_student_wise_v2) ? $compted_marks_student_wise_v2[$val]->sumMarks : 0);
                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result / $total, $rounding),
                    'grand_total_marks' => $actual_total_marks
                );
            }
            // echo '<pre>Fresh: '; print_r($fresh_generations_insert); die();
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }

        // $this->db->insert_batch('assessment_computed_field_details', $student_insert);
        return $return;
    }

    function __get_entities_marks_student_wise($student_ids_arr, $assesments_ids) {
        foreach($assesments_ids as $ass_id => $val) {
            $x[]= $this->db->select("ifnull(SUM( ifnull(if(aems.marks > 0, aems.marks, 0), 0) ), 0) as sumMarks, aems.student_id")
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                ->where_in('ae.entity_id', $val)
                ->where('ae.assessment_id', $ass_id)
                ->where_in('aems.student_id', $student_ids_arr)
                ->group_by('aems.student_id')
                ->order_by('aems.student_id')
                ->get()->result();
        // echo '<pre>'; print_r($this->db->last_query($x)); 
        }
        // die();
        foreach($x as $key => $val) {
            if(empty($val)) {
                unset($x[$key]);
            }
        }

        // Merging student wise
        $mergedData = [];
        foreach ($x as $scores) {
            foreach ($scores as $score) {
                $studentId = $score->student_id;
                if (isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->sumMarks += $score->sumMarks;
                } else {
                    $mergedData[$studentId] = (object) [
                        'student_id' => $studentId,
                        'sumMarks' => $score->sumMarks
                    ];
                }
            }
        }
        $finalResult = array_values($mergedData);

        return $finalResult;
    }

    function __get_entities_result_student_wise($student_ids_arr, $assesments_ids, $failure_percentage) {
        foreach($assesments_ids as $ass_id => $val) {
            $x[]= $this->db->select("GROUP_CONCAT( ifnull(if(aems.marks > 0, aems.marks, 0), 0) ) as concated_obtained_marks, GROUP_CONCAT( if(ae.total_marks > 0, ae.total_marks, 0) ) as concated_total_marks, aems.student_id")
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                ->where_in('ae.entity_id', $val)
                ->where('ae.assessment_id', $ass_id)
                ->where_in('aems.student_id', $student_ids_arr)
                ->group_by('aems.student_id')
                ->get()->result();
        }

        // Merging student wise
        $mergedData = [];
        foreach ($x as $scores) {
            foreach ($scores as $score) {
                $studentId = $score->student_id;
                if (isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->concated_obtained_marks += $score->concated_obtained_marks;
                    $mergedData[$studentId]->concated_total_marks += $score->concated_total_marks;
                } else {
                    $mergedData[$studentId] = (object) [
                        'student_id' => $studentId,
                        'concated_obtained_marks' => $score->concated_obtained_marks,
                        'concated_total_marks' => $score->concated_total_marks
                    ];
                }
            }
        }
        $finalResult = array_values($mergedData);


        if(!empty($finalResult)) {
            foreach($finalResult as $key => $val) {
                $concated_total_marks_arr= explode(',', $val->concated_total_marks);
                $concated_obtained_marks_arr= explode(',', $val->concated_obtained_marks);
                $result= 'Pass';
                foreach($concated_obtained_marks_arr as $k => $v) {
                    $percentage= ($v / $concated_total_marks_arr[$k]) * 100;
                    if($percentage < $failure_percentage) {
                        $result= 'Fail';
                        break;
                    }
                }
                $val->exam_result= $result;
            }
        }

        return $finalResult;
    }
    
    function __get_computed_result_student_wise($student_ids_arr, $computed_ids_if_there, $failure_percentage) {
        $x= $this->db->select("GROUP_CONCAT( if(result > 0, result, 0) ) as concated_obtained_marks, GROUP_CONCAT( if(grand_total_marks > 0, grand_total_marks, 0) ) as concated_total_marks, student_id")
            ->where_in('ass_computed_field_master_id', $computed_ids_if_there)
            ->where_in('student_id', $student_ids_arr)
            ->group_by('student_id')
            ->get('assessment_computed_field_details')->result();
        if(!empty($x)) {
            foreach($x as $key => $val) {
                $concated_total_marks_arr= explode(',', $val->concated_total_marks);
                $concated_obtained_marks_arr= explode(',', $val->concated_obtained_marks);
                $result= 'Pass';
                foreach($concated_obtained_marks_arr as $k => $v) {
                    $percentage= ($v / $concated_total_marks_arr[$k]) * 100;
                    if($percentage < $failure_percentage) {
                        $result= 'Failed';
                        break;
                    }
                }
                $val->exam_result= $result;
                $val->grand_total_marks= array_sum($concated_total_marks_arr);
            }
        }

        return $x;
    }

    function __get_computed_marks_student_wise($student_ids_arr, $computed_ids_if_there) {
        return $this->db->select("SUM( if(result > 0, result, 0) ) as sumMarks, student_id, SUM( if(grand_total_marks > 0, grand_total_marks, 0) ) as grand_total_marks")
            ->where_in('ass_computed_field_master_id', $computed_ids_if_there)
            ->where_in('student_id', $student_ids_arr)
            ->group_by('student_id')
            ->get('assessment_computed_field_details')->result();
    }

    private function __insert_sum_of_assessments_data_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects) {
        // echo 'he ' .$insert_id; die();
        $entities_ids= [];
        $assesments_ids= [];
        $computed_ids_if_there= [];
        $calculated_sub_totalMarks= 0;
        if(!empty($subjects)) {
            foreach($subjects as $key => $val) {
                foreach($val as $keyOne => $valOne) {
                    if($key == 'computed_fields') {
                        $computed_ids_if_there[]= $valOne->cf_id;
                    } else {
                        $calculated_sub_totalMarks += $valOne->subject_total_mark;
                        $entities_ids[]= $valOne->entity_id;
                        // $assesments_ids[]= $valOne->assessment_id;
                    }
                }
                if($key != 'computed_fields') {
                    $assesments_ids[$key]= $entities_ids;
                    $entities_ids=[];
                }
            }
        }

        // echo '<pre>subjects '; print_r($assesments_ids); die();

        // Subject marks as student obj
        $entities_marks_student_wise= [];
       if(!empty($assesments_ids)) {
            $entities_marks_student_wise= $this->__get_entities_marks_student_wise($student_ids_arr, $assesments_ids);
            $entities_marks_student_wise_v2= [];
            foreach($entities_marks_student_wise as $key => $val) {
                $entities_marks_student_wise_v2[$val->student_id]= $val;
            }
       }

        // Computed marks as student obj
        $compted_marks_student_wise= [];
        if(!empty($computed_ids_if_there)) {
            $compted_marks_student_wise= $this->__get_computed_marks_student_wise($student_ids_arr, $computed_ids_if_there);
            $compted_marks_student_wise_v2= [];
            foreach($compted_marks_student_wise as $key => $val) {
                if($key == 0) {
                    $calculated_sub_totalMarks += $val->grand_total_marks;
                }
                $compted_marks_student_wise_v2[$val->student_id]= $val;
            }
        }

        // echo '<pre>'; print_r($compted_marks_student_wise_v2); die();

        $actual_total_marks= $calculated_sub_totalMarks;
        if($total_marks > 0) {
            $actual_total_marks= $total_marks;
        }
        
        $return= false;
        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                $result= (!empty($entities_marks_student_wise_v2) ? $entities_marks_student_wise_v2[$val->student_id]->sumMarks : 0) + (!empty($compted_marks_student_wise_v2) ? $compted_marks_student_wise_v2[$val->student_id]->sumMarks : 0);
                $val->result = round($result, $rounding);
                // echo '<pre>Pre: '; print_r(number_format($result, $rounding)); die();
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];  // Output: Index: 2
                }
                
                $result= (!empty($entities_marks_student_wise_v2) ? $entities_marks_student_wise_v2[$val]->sumMarks : 0) + (!empty($compted_marks_student_wise_v2) ? $compted_marks_student_wise_v2[$val]->sumMarks : 0);
                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => $actual_total_marks
                );
            }
            // echo '<pre>Fresh: '; print_r($fresh_generations_insert); die();
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }
        
        return $return;
    }

    private function __insert_sum_of_assessments_data($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding) {
        // echo '<pre>'; print_r($detail_in_json); 
        // return true;
        $details_arr= json_decode($detail_in_json);
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 0;
            $total= 0;
            foreach($details_arr as $ass_key => $ass_val) {
                if($ass_key != 'computed_fields') {
                    foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                        $total += $ent_val->subject_total_mark;
                        if(isset($ent_val->aems_marks[$std_key])) {
                            if($ent_val->aems_marks[$std_key] > 0) {
                                $result += $ent_val->aems_marks[$std_key];
                            }
                        } else {
                            $result += 0;
                        }
                    }
                } else {
                    foreach($details_arr->$ass_key as $computed_key => $computed_val) {
                        $computed_inf= $this->db->select("result, grand_total_marks")->where('ass_computed_field_master_id', $computed_val->cf_id)->where('student_id', $std_val)->get('assessment_computed_field_details')->result();
                        if(!empty($computed_inf)) {
                            $total += $computed_inf[0]->grand_total_marks;
                            $result += $computed_inf[0]->result;
                        }
                        
                    }
                }
            }
            

            if($defined_total_marks > 0) {
                $total_final= $defined_total_marks;
            } else {
                $total_final = $total; 
            }

            $student_insert[]= array(
                'ass_computed_field_master_id' => $insert_id,
                'student_id' => $std_val,
                'class_section_id' => $class_section_ids_arr[$std_key],
                'result' => number_format($result, (int)$rounding),
                'grand_total_marks' => number_format($total_final, (int)$rounding)
            );
        }

        

        $this->db->insert_batch('assessment_computed_field_details', $student_insert);
        return true;
    }

    // private function __insert_heighest_of_assessments_data($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $highest_lowest_type) {
    //     $details_arr= json_decode($detail_in_json);
    //     foreach($student_ids_arr as $std_key => $std_val) {
    //         $result= 0;
    //         $total= 0;
    //         $remarks= '';
    //         foreach($details_arr as $ass_key => $ass_val) {
    //             foreach($details_arr->$ass_key as $ent_key => $ent_val) {
    //                 if($highest_lowest_type == 'highest') {
    //                     if($ent_val->aems_marks[$std_key] > $result && $ent_val->aems_marks[$std_key] != '-1.00' && $ent_val->aems_marks[$std_key] != '-2.00' && $ent_val->aems_marks[$std_key] != '-3.00') {
    //                         $total = $ent_val->subject_total_mark;
    //                         $result = $ent_val->aems_marks[$std_key];
    //                         $ass_name= $this->db->select('short_name')->where('id', $ent_val->assessment_id)->get('assessments')->row()->short_name;
    //                         $remarks= $ass_name. ' - ' .$ent_val->entity_name;
    //                     }
    //                 } else {
    //                     if($result == 0 || $ent_val->aems_marks[$std_key] < $result && $ent_val->aems_marks[$std_key] != '-1.00' && $ent_val->aems_marks[$std_key] != '-2.00' && $ent_val->aems_marks[$std_key] != '-3.00') {
    //                         $total = $ent_val->subject_total_mark;
    //                         $result = $ent_val->aems_marks[$std_key];
    //                         $ass_name= $this->db->select('short_name')->where('id', $ent_val->assessment_id)->get('assessments')->row()->short_name;
    //                         $remarks= $ass_name. ' - ' .$ent_val->entity_name;
    //                     }
    //                 }
                    
    //             }
    //         }

    //         $student_insert[]= array(
    //             'ass_computed_field_master_id' => $insert_id,
    //             'student_id' => $std_val,
    //             'class_section_id' => $class_section_ids_arr[$std_key],
    //             'result' => $result,
    //             'grand_total_marks' => $total
    //         );
    //     }

    //     $this->db->insert_batch('assessment_computed_field_details', $student_insert);
    //     return true;
    // }

    private function __check_if_marks_added_for_students($array, $selected_class_id, $generation_type) {
        $get_all_students= $this->db->select("sa.id as student_id, sy.class_section_id")
                ->from('student_admission sa')
                ->join('student_year sy', 'sy.student_admission_id= sa.id')
                ->where('sy.class_id', $selected_class_id)
                ->where('sa.admission_status', 2)
                ->where('sy.acad_year_id', $this->yearId)
                ->where_not_in('sy.promotion_status', ['4', '5', 'JOINED'])
                ->order_by('sa.id', 'asc')
                ->get()->result();
        $student_count= count($get_all_students);
        $student_ids_arr= [];
        $class_section_ids_arr= [];
        foreach($get_all_students as $k => $v) {
            array_push($student_ids_arr, $v->student_id);
            array_push($class_section_ids_arr, $v->class_section_id);
        }
        $is_marks_added= true;
        $failed_assessments_names= [];
        foreach($array as $key => $val) {
            $array_arr='';
            if($generation_type == 'regeneration') {
                $array_arr= $array->$key;
            } else {
                $array_arr= $array[$key];
            }
            
            // $computed_ids_arr= [];
            
            foreach($array_arr as $ekey => $eval) {
                    if($key != 'computed_fields') {
                        $ent_id='';
                        $ass_id='';
                        $is_elective= 0;
                        if($generation_type == 'regeneration') {
                            $ent_id= $eval->entity_id;
                            $ass_id= $eval->assessment_id;
                            $is_elective= $eval->is_elective;
                            $entity_name= $eval->entity_name;
                        } else {
                            $ent_id= $eval['entity_id'];
                            $ass_id= $eval['assessment_id'];
                            $is_elective= $eval['is_elective'];
                            $entity_name= $eval['entity_name'];
                        }

                        if($is_elective != '1') {
                            $res= $this->db->select("aems.id as aems_id, aems.marks, aems.grade")
                            ->from('assessments_entities_marks_students aems')
                            ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
                            ->where('ae.entity_id', $ent_id)
                            ->where('ae.assessment_id', $ass_id)
                            ->where_in('aems.student_id', $student_ids_arr)
                            ->order_by('aems.student_id', 'asc')
                            ->get()->result();
                        } else {
                            foreach($student_ids_arr as $kkk => $vvv) {
                                $temp_res[]= $this->db->select("aems.id as aems_id, aems.marks, aems.grade")
                                    ->from('assessments_entities_marks_students aems')
                                    ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
                                    ->where('ae.entity_id', $ent_id)
                                    ->where('ae.assessment_id', $ass_id)
                                    ->where('aems.student_id', $vvv)
                                    ->get()->result();
                            }

                        }

                        if(isset($res) && (count($res) >= $student_count) || isset($temp_res) && (count($temp_res))) {
                            $aems_ids= [];
                            if($is_elective != '1') {
                                foreach($res as $akey => $aval) {
                                    array_push($aems_ids, $aval->marks);
                                }
                            } 
                            else {
                                foreach($temp_res as $akey => $aval) {
                                    if(isset($aval[0]->marks)) {
                                        array_push($aems_ids, $aval[0]->marks);
                                    } else {
                                        array_push($aems_ids, 0);
                                    }
                                }
                                $temp_res= [];
                            }

                            if($generation_type == 'regeneration') {
                                $array->$key[$ekey]->aems_marks= $aems_ids;
                            } else {
                                $array[$key][$ekey]['aems_marks']= $aems_ids;
                            }


                        } else {
                            if($is_elective == '1' && $is_marks_added !== false) {
                                $is_marks_added= true;
                            } else {
                                $ass_name= $this->db->select("long_name")->where('id', $ass_id)->get('assessments')->row()->long_name;
                                array_push($failed_assessments_names, $ass_name. ' ~ ' .$entity_name);
                                $is_marks_added= false;
                            }
                        }
                    }

                }
        }

        if($is_marks_added) {
            return ['status' => 'success', 'student_ids_arr' => $student_ids_arr, 'class_section_ids_arr' => $class_section_ids_arr , 'array' => json_encode($array), 'failed_assessments_names' => []];
        } else {
            return ['status' => 'failed', 'student_ids_arr' => [], 'class_section_ids_arr' => [], 'array' => json_encode($array), 'failed_assessments_names' => $failed_assessments_names];
        }
    }

    public function get_derived_fields($class_id) {
        $this->db_readonly->select("acfm.id as acfm_id, acfm.rounding, acfm.name as acfm_name, acfm.mapping_string, acfm.fields_operation, c.class_name, ac.acad_year, c.id as class_id, ifnull(acfm.generation_status, 'Not Generated') as generation_status")
            ->from('assessment_computed_field_master acfm')
            ->join('class c', 'c.id= acfm.class_id and c.acad_year_id= acfm.acad_year_id')
            ->join('academic_year ac', 'ac.id= acfm.acad_year_id')
            ->where('acfm.acad_year_id', $this->yearId);
        if($class_id != 'all') {
            $this->db_readonly->where('acfm.class_id', $class_id);
        }
        $this->db_readonly->order_by('acfm.id', 'desc');
        $res= $this->db_readonly->get()->result();
        foreach($res as $key => $val) {
            $generation_count= $this->db_readonly->select("id")->where('ass_computed_field_master_id', $val->acfm_id)->get('assessment_computed_field_details')->result();
            if(!empty($generation_count)) {
                $val->is_generated= "<font color='green'>Generated<font>";
            } else {
                $val->is_generated= "<font color='red'>Not Generated<font>";
            }
        }
        if(!empty($res)) {
            return $res;
        }
        return array();
    }

    public function get_full_details_of_derived_field($input) {
        $acfm_id= $input['acfm_id'];
        $subs_json= $this->db_readonly->select('subjects, acad_year_id, class_id, fields_operation')->where('id', $acfm_id)->get('assessment_computed_field_master')->result();
        $res= array();
        if(!empty($subs_json)) {
            $res= $this->db_readonly->select("concat(sa.first_name, ' ', ifnull(sa.last_name, '')) as std_name, cs.section_name, acfd.result, acfd.grand_total_marks, acfm.fields_operation, sa.id as student_id")
                ->from('assessment_computed_field_master acfm')
                ->join('assessment_computed_field_details acfd', 'acfd.ass_computed_field_master_id= acfm.id')
                ->join('student_admission sa', 'sa.id= acfd.student_id')
                ->join('class_section cs', 'cs.id= acfd.class_section_id')
                ->where('acfm.id', $acfm_id)
                ->get()->result();

            $subs_decoded= json_decode($subs_json[0]->subjects);
            
            $sub_obj_arr= new stdClass();
            $computed_obj_arr= [];
            foreach($subs_decoded as $key => $val) {
                if($key != 'computed_fields') {
                    $ass= $this->db_readonly->select("concat(long_name, ' (', short_name, ')') as ass_name, ifnull(short_name, 'Assessment') short_name")->where('id', $key)->where('acad_year_id', $subs_json[0]->acad_year_id)->where('class_id', $subs_json[0]->class_id)->get('assessments')->row();
                    $pushed_arr= [];
                    foreach($subs_decoded->$key as $sub_key => $sub_val) {
                        $subs_details = $this->db_readonly->select("aeg.entity_name as entity_group_name, ifnull(ass_ele_g.group_name, '') as elective_name, aeg.is_elective")
                                ->from('assessment_entities_group aeg')
                                ->join('assessment_elective_group ass_ele_g', 'aeg.elective_group_id= ass_ele_g.id', 'left')
                                ->where('aeg.class_id', $subs_json[0]->class_id)
                                ->where('aeg.id', $sub_val->group_id)
                                ->get()->row();
                        $sub_val->entity_group_name= $subs_details->entity_group_name;
                        $sub_val->elective_name= $subs_details->elective_name;
                        $sub_val->is_elective= $subs_details->is_elective;
                        $sub_val->ass_short_name= $ass->short_name;
                        array_push($pushed_arr, $sub_val);
                    }
                    $rrr= $ass->ass_name;
                    $sub_obj_arr->$rrr= $pushed_arr;
                    $pushed_arr= [];
                } else {
                    foreach($val as $key2 => $val2) {
                        $mapping_string= $this->db_readonly->select("mapping_string")->where('id', $val2->cf_id)->get('assessment_computed_field_master')->row();
                        array_push($computed_obj_arr, ['name' => $val2->cf_name, 'cf_id' => $val2->cf_id, 'mapping_string' => $mapping_string->mapping_string]);
                    }
                }
            }

            $inputSubjectsOrder= [];
            $inputTotalOrder= [];
            if(!empty($res)) {
                foreach($res as $stdKey => $stdVal) {
                    $input_arr= [];
                    if(!empty($sub_obj_arr)) {
                        foreach($sub_obj_arr as $assKey => $assVal) {
                            foreach($assVal as $subKey => $subVal) {
                                $inputSubject= $this->db_readonly->select("ae.total_marks, aems.marks")
                                            ->from('assessments_entities ae')
                                            ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                                            ->where('ae.assessment_id', $subVal->assessment_id)
                                            ->where('ae.entity_id', $subVal->entity_id)
                                            ->where('aems.student_id', $stdVal->student_id)
                                            ->get()->row();
                                if(!empty($inputSubject)) {
                                    $input_arr[$subVal->ass_short_name. ' - ' .$subVal->entity_name]= $inputSubject->marks;
                                    if($stdKey == 0) {
                                        $inputSubjectsOrder[]= $subVal->ass_short_name. ' - ' .$subVal->entity_name;
                                        $inputTotalOrder[]= $subVal->subject_total_mark;
                                    }
                                }
                            }
                        }
                    }
                    if(!empty($computed_obj_arr)) {
                        foreach($computed_obj_arr as $cfKey => $cfVal) {
                            $inputCF= $this->db_readonly->select("result, grand_total_marks")->where('ass_computed_field_master_id', $cfVal['cf_id'])->where('student_id', $stdVal->student_id)->get('assessment_computed_field_details')->row();
                            if(!empty($inputCF)) {
                                $input_arr['CF - '.$cfVal['name']]= $inputCF->result;
                                if($stdKey == 0) {
                                    $inputSubjectsOrder[]= 'CF - '.$cfVal['name'];
                                    $inputTotalOrder[]= $inputCF->grand_total_marks;
                                }
                                if($stdKey < 5) {
                                    $computed_obj_arr[$cfKey]['grand_total_marks']= $inputCF->grand_total_marks;
                                }
                            }
                        }
                    }
                    $stdVal->input_marks_arr= $input_arr;
                }
            }

        }

        // echo '<pre>'; print_r($res); die();
        

        if(!empty($subs_json)) {
            return ['status' => 'success', 'subject_details' => $sub_obj_arr, 'computed_detrails' => $computed_obj_arr, 'student_details' => $res, 'fields_operation' => $subs_json[0]->fields_operation, 'inputSubjectsOrder' => $inputSubjectsOrder, 'inputTotalOrder' => $inputTotalOrder];
        }
        return ['status' => 'failed', 'subject_details' => [], 'subject_details' => [], 'student_details' => [], 'fields_operation' => '', 'inputSubjectsOrder' => [], 'inputTotalOrder' => []];
    }

    function __check_if_marks_added_for_students_v2($array, $selected_class_id, $generation_type, $acfm_id) {

        // echo '<pre>'; print_r($array); die();
        
        $get_all_students= $this->db->select("sa.id as student_id, sy.class_section_id")
            ->from('student_admission sa')
            ->join('student_year sy', 'sy.student_admission_id= sa.id')
            ->where('sy.class_id', $selected_class_id)
            ->where_in('sa.admission_status', [1, 2, 4])
            ->where('sy.acad_year_id', $this->yearId) // alumni and pending students
            ->where_not_in('sy.promotion_status', ['5']) // 4 taking because need to take alumini, 'JOINED is taking because pending need to take also
            ->order_by('sa.id', 'asc')
            ->get()->result();
        $student_count= count($get_all_students);
        $student_ids_arr= [];
        $class_section_ids_arr= [];
        foreach($get_all_students as $k => $v) {
            array_push($student_ids_arr, $v->student_id);
            array_push($class_section_ids_arr, $v->class_section_id);
        }

        

        $details_if_pregenerated= $this->db->where('ass_computed_field_master_id', $acfm_id)->get('assessment_computed_field_details')->result();
        if(!empty($details_if_pregenerated)) {
            $insert_miss_students_data= [];
            if(count($student_ids_arr) != count($details_if_pregenerated) && count($student_ids_arr) > count($details_if_pregenerated)) {
                $stds=[];
                foreach($details_if_pregenerated as $kee => $vaa) {
                    $stds[]= $vaa->student_id;
                }
                foreach($student_ids_arr as $kee1 => $vaa1) {
                    if(!in_array($vaa1, $stds))
                        $insert_miss_students_data[]= array(
                            'ass_computed_field_master_id' => $details_if_pregenerated[0]->ass_computed_field_master_id,
                            'student_id' => $vaa1,
                            'class_section_id' => $class_section_ids_arr[$kee1], // corresponding class section id is already exist
                            'result' => 0,
                            'grand_total_marks' => $details_if_pregenerated[0]->grand_total_marks,
                        );
                }

            }
            if(!empty($insert_miss_students_data)) {
                // echo '<pre>'; print_r($insert_miss_students_data); die();
                $this->db->insert_batch('assessment_computed_field_details', $insert_miss_students_data);
            }
            return ['status' => 'success', 'student_ids_arr' => $student_ids_arr, 'class_section_ids_arr' => $class_section_ids_arr, 'failed_assessments_names' => [], 'details_if_pregenerated' => $details_if_pregenerated, 'generation_status' => 'Generated'];
        }

        $is_marks_added = true;
        // $exitLoop = false; // Flag to break both loops
        $failed_assessments_names= [];
        foreach($array as $key => $val) {
            if($key != 'computed_fields') {
                foreach($val as $key_level => $vallevel) {
                    $addedMarksCount = $this->db->select("count(aems.id) as count")
                        ->from('assessments_entities ae')
                        ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                        ->where('ae.assessment_id', $vallevel->assessment_id)
                        ->where('ae.entity_id', $vallevel->entity_id)
                        ->get()->row();

                        // echo '<pre>'; print_r($addedMarksCount); die();

                    if($vallevel->is_elective != '1') {
                        if(!empty($addedMarksCount)) {
                            // if($student_count > $addedMarksCount->count) { // comment and replace with bottom line
                            if($addedMarksCount->count <= 0) { // if marks added for some students
                                $is_marks_added = false;
                                $ass_name= $this->db->select("long_name")->where('id', $vallevel->assessment_id)->get('assessments')->row()->long_name;
                                array_push($failed_assessments_names, $ass_name. ' ~ ' .$vallevel->entity_name);
                                // $exitLoop = true; // Set flag to break outer loop
                                // break;
                            }
                        } else {
                            $is_marks_added = false;
                            $ass_name= $this->db->select("long_name")->where('id', $vallevel->assessment_id)->get('assessments')->row()->long_name;
                            array_push($failed_assessments_names, $ass_name. ' ~ ' .$vallevel->entity_name);
                            // $exitLoop = true; // Set flag to break outer loop
                            // break;
                        }
                    }
                }
            }
            // if($exitLoop) {
            //     $is_marks_added= false;
            //     break; // Break the outer loop if flag is set
            // }
        }

        

        if($is_marks_added) {
            return ['status' => 'success', 'student_ids_arr' => $student_ids_arr, 'class_section_ids_arr' => $class_section_ids_arr, 'failed_assessments_names' => [], 'details_if_pregenerated' => [], 'generation_status' => 'Generated'];
        } else {
            return ['status' => 'failed', 'student_ids_arr' => $student_ids_arr, 'class_section_ids_arr' => $class_section_ids_arr, 'failed_assessments_names' => $failed_assessments_names, 'details_if_pregenerated' => [], 'generation_status' => 'Partially Generated'];
        }
    }

    public function rgenerate_computed_fields_v2($acfm_id) {
        $information_arr= $this->db->select("subjects, class_id, acad_year_id, fields_operation, field_parameters, ifnull(total_marks, 0) as total_marks, rounding")->where('id', $acfm_id)->get('assessment_computed_field_master')->row();
        $inf_arr= json_decode($information_arr->subjects);
        $marks_added_status_and_data= $this->__check_if_marks_added_for_students_v2($inf_arr, $information_arr->class_id, 'regeneration', $acfm_id);
        $marks_added_status_and_data['subjects']= $inf_arr;
        $marks_added_status_and_data['class_id']= $information_arr->class_id;
        $marks_added_status_and_data['acad_year_id']= $information_arr->acad_year_id;
        $marks_added_status_and_data['fields_operation']= $information_arr->fields_operation;
        $marks_added_status_and_data['field_parameters']= $information_arr->field_parameters;
        $marks_added_status_and_data['total_marks']= $information_arr->total_marks;
        $marks_added_status_and_data['rounding']= $information_arr->rounding;
        // $marks_added_status_and_data['generation_status']= $information_arr->generation_status;
        // echo '<pre>'; print_r($marks_added_status_and_data); die();
        return $marks_added_status_and_data;


        // if($marks_added_status_and_data['status'] == 'failed') {
        //     return ['status' => '-1', 'failed_ass_generationn' => $marks_added_status_and_data['failed_assessments_names']];
        // }
        
        // $this->db->trans_start();

        //     $this->db->where('ass_computed_field_master_id', $acfm_id)->delete('assessment_computed_field_details');
            
        //     $insert_id= $acfm_id;
        //     if($information_arr->fields_operation == 'Sum') {
        //         $this->__insert_sum_of_assessments_data_v2($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $insert_id, $information_arr->total_marks, $information_arr->rounding);
        //     }
            
        //     if($information_arr->fields_operation == 'Average') {
        //         $this->__insert_average_of_assessments_data($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding);
        //     }
        //     if($information_arr->fields_operation == 'Exam Result') {
        //         $f_prec= json_decode($information_arr->field_parameters);
        //         $this->__insert_exam_result($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding, $f_prec->failure_percentage);
        //     }
            
        // $this->db->trans_complete();
        
        // if($this->db->trans_status()) {
        //     return ['status' => '1', 'failed_ass_generationn' => []];
        // } else {
        //     $this->db->trans_rollback();
        //     return ['status' => '-2', 'failed_ass_generationn' => []];
        // }
    }

    public function calback_function_to_regenerate_details($data, $acfm_id) {
        $data= json_decode($data);
        $class_section_ids_arr= $data->class_section_ids_arr;
        $student_ids_arr= $data->student_ids_arr;
        $details_if_pregenerated= $data->details_if_pregenerated;

        $subjects= $data->subjects;
        $class_id= $data->class_id;
        $acad_year_id= $data->acad_year_id;
        $fields_operation= $data->fields_operation;
        $field_parameters= $data->field_parameters;
        $total_marks= $data->total_marks;
        $rounding= $data->rounding;
        $generation_status= $data->generation_status;

        // echo '<pre>'; print_r($gradesSystem); die();

        $this->db->trans_start();

            // if(empty($details_if_pregenerated))  {
            //     $this->db->where('ass_computed_field_master_id', $acfm_id)->delete('assessment_computed_field_details');
            // }
            
            $insert_id= $acfm_id;
            if($fields_operation == 'Sum') {
                $status= $this->__insert_sum_of_assessments_data_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects);
            }
            if($fields_operation == 'Average') {
                $status= $this->__insert_average_of_assessments_data_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects);
            }
            if($fields_operation == 'Exam Result') {
                $f_prec= json_decode($field_parameters);
                $status= $this->__insert_exam_result_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $f_prec->failure_percentage);
            }
            if($fields_operation == 'Percentage') {
                // $f_prec= json_decode($field_parameters);
                $status= $this->__insert_percentage_reduced_to($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $field_parameters);
            }
            if($fields_operation == 'GPA' || $fields_operation == 'GPS') {
                $gradesSystem= json_decode($field_parameters);
                $status= $this->__insert_gpa_result_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $gradesSystem->grading_system_id, $fields_operation);
            }
            if($fields_operation == 'Best of All') {
                // $gradesSystem= json_decode($field_parameters);
                $status= $this->__insert_best_of_all($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects);
            }
            if($fields_operation == 'Second Best of All') {
                // $gradesSystem= json_decode($field_parameters);
                $status= $this->__insert_second_best_of_all($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects);
            }
            if($fields_operation == 'Best 2 Sum' || $fields_operation == 'Best 2 Average') {
                // $gradesSystem= json_decode($field_parameters);
                $status= $this->__insert_best2sum_and_best2average($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $fields_operation);
            }
            if($fields_operation == 'Sum and then Percentage Ignore NA') {
                // $gradesSystem= json_decode($field_parameters);
                $status= $this->__insert_sum_percent_ignore_NA($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects);
            }
            
        $this->db->trans_complete();

        if($status == true) {
            $this->db->where('id', $insert_id)->update('assessment_computed_field_master', array('generation_status' => $generation_status));
        }

        return  $status;

        // echo '<pre>'; print_r($class_section_ids_arr); 
        // echo '<pre>'; print_r($student_ids_arr); 
        // echo '<pre>'; print_r($details_if_pregenerated); die();
    }

    // public function rgenerate_computed_fields($acfm_id) {
    //     $information_arr= $this->db->select("subjects, class_id, acad_year_id, fields_operation, field_parameters, ifnull(total_marks, 0) as total_marks, rounding, field_parameters")->where('id', $acfm_id)->get('assessment_computed_field_master')->row();
    //     $inf_arr= json_decode($information_arr->subjects);
    //     $marks_added_status_and_data= $this->__check_if_marks_added_for_students($inf_arr, $information_arr->class_id, 'regeneration');
    //     if($marks_added_status_and_data['status'] == 'failed') {
    //         return ['status' => '-1', 'failed_ass_generationn' => $marks_added_status_and_data['failed_assessments_names']];
    //     }
        
    //     $this->db->trans_start();

    //         $this->db->where('ass_computed_field_master_id', $acfm_id)->delete('assessment_computed_field_details');
            
    //         $insert_id= $acfm_id;
    //         if($information_arr->fields_operation == 'Sum') {
    //             $this->__insert_sum_of_assessments_data($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding);
    //         }
            
    //         if($information_arr->fields_operation == 'Average') {
    //             $this->__insert_average_of_assessments_data($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding);
    //         }
    //         if($information_arr->fields_operation == 'Exam Result') {
    //             $f_prec= json_decode($information_arr->field_parameters);
    //             $this->__insert_exam_result($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding, $f_prec->failure_percentage);
    //         }
            
    //     $this->db->trans_complete();
        
    //     if($this->db->trans_status()) {
    //         return ['status' => '1', 'failed_ass_generationn' => []];
    //     } else {
    //         $this->db->trans_rollback();
    //         return ['status' => '-2', 'failed_ass_generationn' => []];
    //     }
    // }

    public function mass_generation_function($filtered_class) { // crud
        $acfm_ids= $this->db->select("id, subjects, class_id, acad_year_id, fields_operation")->where('class_id', $filtered_class)->where('acad_year_id', $this->yearId)->get('assessment_computed_field_master')->result();
        if(!empty($acfm_ids)) {
            $delete_acfm_ids= [];
            $this->db->trans_start();
            foreach($acfm_ids as $key => $val) {
                $generations= $this->db->select('id')->where('ass_computed_field_master_id', $val->id)->get('assessment_computed_field_details')->result();
                if(!empty($generations)) { // If created: delete and re-create
                    $inf_arr= json_decode($val->subjects);
                    $marks_added_status_and_data= $this->__check_if_marks_added_for_students($inf_arr, $val->class_id, 'regeneration');

                    if($marks_added_status_and_data['status'] == 'failed') {
                        return '-1';
                    }

                    $this->db->where('ass_computed_field_master_id', $val->id)->delete('assessment_computed_field_details');
                    
                    $insert_id= $val->id;
                    if($val->fields_operation == 'Sum') {
                        $this->__insert_sum_of_assessments_data($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id);
                    }
                } else { // If not created: fresh create
                    $inf_arr= json_decode($val->subjects);
                    $marks_added_status_and_data= $this->__check_if_marks_added_for_students($inf_arr, $val->class_id, 'regeneration');

                    if($marks_added_status_and_data['status'] == 'failed') {
                        return '-1';
                    }
                        
                    $insert_id= $val->id;
                    if($val->fields_operation == 'Sum') {
                        $this->__insert_sum_of_assessments_data($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id);
                    }
                }
            }
            $this->db->trans_complete();
            if(!$this->db->trans_status()) {
                $this->db->trans_rollback();
            }
            return $this->db->trans_status();
        }
    }

    public function get_prev_remarks($std_id, $template_id) {
        $res= $this->db_readonly->select("remarks")->where('student_id', $std_id)->where('marks_card_temp_id', $template_id)->get('assessments_marks_cards')->result();
        if(!empty($res)) {
            return $res[0]->remarks;
        }
        return 'Remarks not added';
    }

    public function get_computed_field_result($std_id, $mapping_string, $show_mgp, $grading_system_id) {
        $res= $this->db_readonly->select("acfm.name, acfd.result, acfd.grand_total_marks, round(((acfd.result/acfd.grand_total_marks)*100), 2) as percentage")
            ->from('assessment_computed_field_details acfd')
            ->join('assessment_computed_field_master acfm', 'acfm.id= acfd.ass_computed_field_master_id')
            ->where('acfd.student_id', $std_id)
            ->where('acfm.mapping_string', $mapping_string)
            ->get()->result();
        if(!empty($res)) {
            $grading_value= '';
            if (strlen($grading_system_id) > 0) {
                $grading_value= 'Range mismatch';
                $grades= $this->db_readonly->select("grades")->where('id', $grading_system_id)->get('assessment_grading_system')->result();
                if(!empty($grades)) {
                    $grading_decoded= json_decode($grades[0]->grades);
                    foreach($grading_decoded as $gkey => $gval) {

                        if(floatval($res[0]->percentage) >= floatval($gval->from) && floatval($res[0]->percentage) <= floatval($gval->to)) {
                            $grading_value= $gval->grade;
                            break;
                        }
                    }
                }
            }
            return ['status' => 'success', 'name' => $res[0]->name, 'result' => $res[0]->result, 'total' => $res[0]->grand_total_marks, 'grade' => $grading_value, 'percentage' => $res[0]->percentage];
        }
        return ['status' => 'success', 'name' => 'NA', 'result' => 'NA', 'total' => 'NA', 'grade' => 'NA', 'percentage' => 'NA']; // making status success to ignore if computed marks are created only and not generated
    }

    public function check_unique_mapping_string($mapping_string) {
        $res= $this->db_readonly->where('mapping_string', $mapping_string)->get('assessment_computed_field_master')->result();
        if(!empty($res)) {
            return ['status' => 'success'];
        }
        return ['status' => 'failed'];
    }

    public function get_class_wise_computed_fields($selected_class_id) {
        $res= $this->db_readonly->select("id, name, mapping_string, subjects, class_id, fields_operation, total_marks, rounding")
            ->where('class_id', $selected_class_id)
            ->get('assessment_computed_field_master')->result();
        if(!empty($res)) {
            foreach($res as $key => $val) {
                $generated= $this->db_readonly->where('ass_computed_field_master_id', $val->id)->get('assessment_computed_field_details')->result();
                if(!empty($generated)) {
                    $val->is_generated= '1';
                } else {
                    $val->is_generated= '-1';
                }
            }
        }
        if(!empty($res)) {
            return $res;
        }
        return array();
    }

    public function get_computed_data_by_id($acfm_id) {
        return $this->db_readonly->where('id', $acfm_id)->get('assessment_computed_field_master')->row();
    }

    public function delete_whole_data($acfm_id) {
        $this->db->trans_start();
            $this->db->where('id', $acfm_id)->delete('assessment_computed_field_master');
            $this->db->where('ass_computed_field_master_id', $acfm_id)->delete('assessment_computed_field_details');
        $this->db->trans_complete();

        if($this->db->trans_status()) {
            return $this->db->trans_status();
        } else {
            $this->db->trans_rollback();
            return $this->db->trans_status();
        }
    }

    public function fetch_and_regenerate_derived_subject($inputs) {
        // Steps:
        // 1: Search, how many times entity is used in derived_subjects creation
        // 2: Regenerate that derived subjects only
        $class_id= $inputs['class_id'];
        $aem_id= $inputs['aem_id'];
        $class_section_id= $inputs['class_section_id'];
        $student_id= $inputs['student_id'];
        $assessment_id= $inputs['assessment_id'];
        $all_derived_subjects= $this->db->select("id, derived_formula")
            ->where('class_id', $class_id)
            ->where('ass_type', 'Derived')
            ->where('derived_formula is not null')
            ->get('assessment_entity_master')->result();
            
        $used_in_derived_subject_arr= [];
        $status= true;
        $comparioson_marks_arr= []; // to get all the derived subject old and new marks
        if(!empty($all_derived_subjects)) {
            foreach($all_derived_subjects as $key => $val) {
                $derived_subjects= json_decode($val->derived_formula);
                foreach($derived_subjects->entityId as $id_key => $entity_id) {
                    if($entity_id->id == $aem_id) {
                        array_push($used_in_derived_subject_arr, $val->id);
                        break;
                    }
                }
            }
            

            if(!empty($used_in_derived_subject_arr)) {
                foreach($used_in_derived_subject_arr as $keyy => $derived_subject_id) {
                    $result = $this->db->select('id')
                        ->from('assessments_entities ae')
                        ->where('assessment_id', $assessment_id)
                        ->where('entity_id', $derived_subject_id)
                        ->get()->row();
                    
                    $derived_ae_id = $result->id;
                    $sst_temp= $this->generate_derived_entity_marks_of_a_student($class_section_id, $derived_subject_id, $assessment_id, $derived_ae_id, $student_id);
                    if($sst_temp['status'] != '1' && $sst_temp['status'] != 1) {
                        $status= false;
                    } else {
                        array_push($comparioson_marks_arr, $sst_temp['comparioson_marks_arr']);
                    }
                }
            }
        }
        if(!empty($used_in_derived_subject_arr)) {
            return ['status' => $status, 'found' => 'yes', 'comparioson_marks_arr' => $comparioson_marks_arr];
        }
        return ['status' => $status, 'found' => 'no', 'comparioson_marks_arr' => []];
        
    }

    public function generate_derived_entity_marks_of_a_student ($section_id, $derived_entity_id, $assessment_id, $derived_ae_id, $student_id) {
        //Step1: Get the entity object of the given derived subject and get all the related entities
        
        $entity_obj = $this->db->select('derived_formula, rounding')
            ->from('assessment_entity_master aem')
            ->where('aem.id', $derived_entity_id)
            ->get()->row();
        $rounding_parameter= $entity_obj->rounding;
  
        $derived = json_decode($entity_obj->derived_formula);
        $entity_ids = []; 
        if(empty($derived->entityId)) return false;   
        foreach ($derived->entityId as $eobj) {
            if (!empty($eobj->id)) {
                $entity_ids[] = $eobj->id;
            }
        }
        $entity_id_str = implode(',',$entity_ids);

        //Step2: Get the assessment entities for all the related entities
        if(empty($entity_id_str)) return false;
        $marks_sql = "select aems.student_id, aems.marks, ae.total_marks from assessments_entities_marks_students aems
        join assessments_entities ae on aems.assessments_entities_id=ae.id and assessment_id=$assessment_id and entity_id in ($entity_id_str)
        where student_id in ($student_id)";
        $marks_objs = $this->db->query($marks_sql)->result();

        //Step3: Calculate the derived marks and insert/update them
        $update_data = [];
        $insert_data = [];
        $marks_arr = [];
        $totals_arr = [];
        foreach ($marks_objs as $m_obj) {
            if ($student_id == $m_obj->student_id) {
                $marks_arr [] = $m_obj->marks;
                $totals_arr [] = $m_obj->total_marks;
            }
        }
        $derived_marks = $this->_calculateDerivedSubjectMarks($marks_arr, $totals_arr, $derived->formula->name, $rounding_parameter);
        $exists = $this->db->select('aems.id, aems.marks, aem.name')->where('aems.assessments_entities_id', $derived_ae_id)->where('aems.student_id', $student_id)
        ->from('assessments_entities_marks_students aems')
        ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
        ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')
        ->get()->row();
        $comparioson_marks_arr= [];
        if(!empty($exists)) {
            $update_data[] = array(
                'id' => $exists->id,
                'marks' => $derived_marks,
                'status' => 1
            );
            $stdObj= new stdClass();
            $stdObj->name= $exists->name;
            $stdObj->marks= $exists->marks;
            $stdObj->derived_marks= $derived_marks;
            $comparioson_marks_arr= $stdObj;
        } else {
            $insert_data[] = array(
                'assessments_entities_id' => $derived_ae_id,
                'student_id' => $student_id,
                'marks' => $derived_marks,
                'status' => 1
            );
        }

        $this->db->trans_start();
        if(!empty($insert_data)) {
            $this->db->insert_batch('assessments_entities_marks_students', $insert_data);
        }
        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }
        $this->db->trans_complete();

        if($this->db->trans_status()) {
            return ['comparioson_marks_arr' => $comparioson_marks_arr, 'status' => 1];
        }
        return ['comparioson_marks_arr' => [], 'status' => 0];
    }

    public function fetch_and_regenerate_derived_subjects_in_derived_assessments($inputs) {
        // Steps:
        // 1: Search, how many times entity is used in derived_assessment creation
        // 2: Regenerate that derived assessments only
        $class_id= $inputs['class_id'];
        $aem_id= $inputs['aem_id'];
        $class_section_id= $inputs['class_section_id'];
        $student_id= $inputs['student_id'];
        $assessment_id= $inputs['assessment_id'];
// Getting subject used in derived subjects
        $all_derived_subjects= $this->db->select("id, derived_formula")
            ->where('class_id', $class_id)
            ->where('ass_type', 'Derived')
            ->where('derived_formula is not null')
            ->get('assessment_entity_master')->result();
            
        $used_in_derived_subject_arr= [$aem_id];
        if(!empty($all_derived_subjects)) {
            foreach($all_derived_subjects as $key => $val) {
                $derived_subjects= json_decode($val->derived_formula);
                foreach($derived_subjects->entityId as $id_key => $entity_id) {
                    if($entity_id->id == $aem_id) {
                        array_push($used_in_derived_subject_arr, $val->id);
                        break;
                    }
                }
            }
        }
        
// Getting assessments used in derived assessments : Required if derived assessments are used to create another derived assessments
        $all_derived_assessments= $this->db->select("id, formula")
            ->where('class_id', $class_id)
            ->where('generation_type', 'Auto')
            ->where('formula is not null')
            ->order_by('id', 'asc')
            ->get('assessments')->result();
        $entity_used_in_derived_assessments_arr= [];
        $assessment_used_in_derived_assessments_arr= [];
        $status= true;
        $comparioson_marks_arr= [];
        if(!empty($all_derived_assessments)) {
            foreach($all_derived_assessments as $keyy => $der_ass_id) {
                $decoded_formulae= json_decode($der_ass_id->formula);
                foreach($decoded_formulae->merg_values as $ekeys => $evals) {
                    if(in_array($evals->entity_id, $used_in_derived_subject_arr)) {
                        // if(!in_array($der_ass_id->id, $entity_used_in_derived_assessments_arr)) {
                            array_push($entity_used_in_derived_assessments_arr, $der_ass_id->id);
                        // }
                        break;
                    }
                }
                foreach($decoded_formulae->assessments as $akeys => $avals) {
                    if($avals->id == $assessment_id) {
                        // if(!in_array($der_ass_id->id, $assessment_used_in_derived_assessments_arr)) {
                            array_push($assessment_used_in_derived_assessments_arr, $der_ass_id->id);
                        // }
                        break;
                    }
                }
            }
            $regeneatable_ass_arr= array_unique(array_merge($assessment_used_in_derived_assessments_arr, $entity_used_in_derived_assessments_arr));

            if(!empty($regeneatable_ass_arr)) {
                foreach($regeneatable_ass_arr as $ass_key => $ass_id) {
                    $sts= $this->generatederivedMarks_of_a_student($class_id, $aem_id, $class_section_id, $student_id, $ass_id, $type= 1);
                    // echo '<pre>'; print_r($sts);
                    if($sts['status'] != '1' && $sts['status'] != 1) {
                        $status= false;
                    } else {
                        array_push($comparioson_marks_arr, $sts['comparioson_marks_arr']);
                    }
                }
                // die();
            }
        }
        
        if(!empty($regeneatable_ass_arr)) {
            return ['status' => $status, 'found' => 'yes', 'comparioson_marks_arr' => $comparioson_marks_arr];
        }
        return ['status' => $status, 'found' => 'no', 'comparioson_marks_arr' => []];
        
    }

    private function generatederivedMarks_of_a_student($class_id, $entity_id, $section_id, $student_id, $derived_assessment_id, $type) {

        $entity_data = $this->db->query("select id,name,grading_system_id from assessment_entity_master where id=$entity_id")->row();
        
       
        $consData = $this->db->query("select * from assessments where id=$derived_assessment_id")->row();
        $derived_entities_id = $this->db->query("select id from assessments_entities where assessment_id=$derived_assessment_id and entity_id=$entity_id");
        if($derived_entities_id->num_rows() == 1) {
            $derived_entities_id= $derived_entities_id->row()->id;
        } else {
            $derived_entities_id= 0;
        }
        $jsonData = json_decode($consData->formula);
        $formula = $jsonData->merg_algorithm->name;
        $assessments = $jsonData->assessments;
        $merge_set = $jsonData->merg_values;
        $entity_data_set = array();
        foreach ($merge_set as $key => $merge) {
            if($merge->entity_id == $entity_id) {
                $entity_data_set = $merge->value_set;
            }
        }
        

        $set_values = array();
        foreach ($entity_data_set as $key => $set) {
            $set_values[$set->assId] = $set->value;
        }

        $students[] = $student_id;
        $total_std= 1;
        
        $not_added = $this->calculateDerivedMarksMultiple($students, $entity_id, $derived_entities_id, $formula, $set_values, $consData->rounding);
        
        return $not_added;
    }

    private function calculateDerivedMarksMultiple($students, $entity_id, $derived_entities_id, $formula, $set_values, $rounding) {
        // 
        if(empty($set_values)) {
            return -10; //marks not added for all assessmets
        }
        $assessment_ids = implode(',', array_keys($set_values));
        $std_ids = implode(",", $students);
        $sql = "select student_id, (case when marks<0 then 0 else marks end) as marks, marks as actual_marks, assessment_id, total_marks from assessments_entities_marks_students aems 
                join assessments_entities ae on ae.id=aems.assessments_entities_id 
                where assessment_id in ($assessment_ids) and entity_id=$entity_id and aems.student_id in ($std_ids)";
        $stdMarks = $this->db->query($sql)->result();

        

        $std_marks = array();
        foreach ($stdMarks as $key => $std) {
            if(!array_key_exists($std->student_id, $std_marks)) {
                $std_marks[$std->student_id] = array();
            }
            $marks_rounded = number_format($std->marks, $rounding);
            $std_marks[$std->student_id][$std->assessment_id] = array('marks' => $marks_rounded, 'total_marks' => $std->total_marks, 'actual_marks' => $std->actual_marks);
        }

        $total_assessments = count($set_values);

        $data = array();
        $not_added = 0;
        switch ($formula) {
            case 'percentage':
                foreach ($std_marks as $std_id => $std) {
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $derived_marks = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    foreach ($set_values as $ass_id => $val) {
                        if (!isset($std[$ass_id]['actual_marks'])) continue;

                        $derived_marks += ($std[$ass_id]['marks']/$std[$ass_id]['total_marks'])*$val;
                        if($std[$ass_id]['actual_marks'] == '-3') {
                            $not_applicables++;
                        }
                        if($std[$ass_id]['actual_marks'] == '-1') {
                            $absents++;
                        }
                    }
                    $temp['marks'] = $derived_marks;
                    if(count($std) == $not_applicables) {
                        $temp['marks'] = '-3';
                    } else if(count($std) == $absents) {
                        $temp['marks'] = '-1';
                    }
                    array_push($data, $temp);
                }
                break;
            case 'aggregate_to_100':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $total = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    foreach ($set_values as $ass_id => $val) {
                        if($std[$ass_id]['actual_marks'] == '-1') {
                            $absents++;
                        }
                        if($std[$ass_id]['actual_marks'] == '-3') {
                            $not_applicables++;
                            continue;
                        }
                        $marks += $std[$ass_id]['marks'];
                        $total += $std[$ass_id]['total_marks'];
                    }
                    if($total != 0) {
                        $temp['marks'] = ($marks/$total)*100;
                    } else {
                        $temp['marks'] = 0;
                    }

                    if(count($std) == $not_applicables) {
                        $temp['marks'] = '-3';
                    } else if(count($std) == $absents) {
                        $temp['marks'] = '-1';
                    }
                    array_push($data, $temp);
                }
                break;
            case 'average':
                foreach ($std_marks as $std_id => $std) {
                    $ass_count = $total_assessments;
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $minus_three = 0;
                    foreach ($set_values as $ass_id => $val) {
                        if($std[$ass_id]['actual_marks'] == -3) {
                            $minus_three ++;
                            continue;
                        }
                        $marks += $std[$ass_id]['marks'];
                    }

                    if ($minus_three === $ass_count) {
                        $temp['marks'] = -3;
                    } else {
                        $temp['marks'] = $marks/$ass_count;
                    }
                    array_push($data, $temp);
                }
                break;
            case 'best_2_sum':
                foreach ($std_marks as $std_id => $std) {
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    uasort($std, function($a, $b) { 
                        $result = 0;
                        if ($a['marks'] > $b['marks']) {
                            $result = -1;
                        } else if ($a['marks'] < $b['marks']) {
                            $result = 1;
                        }
                        return $result;
                    });
                    $i=1;
                    foreach ($std as $ass_id => $stdm) {
                        if($i++ == 3) break;
                        $marks += $stdm['marks'];
                    }
                    $temp['marks'] = $marks;
                    array_push($data, $temp);
                }
                break;
            case 'best_2_average':
                foreach ($std_marks as $std_id => $std) {
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    uasort($std, function($a, $b) { 
                        $result = 0;
                        if ($a['marks'] > $b['marks']) {
                            $result = -1;
                        } else if ($a['marks'] < $b['marks']) {
                            $result = 1;
                        }
                        return $result;
                    });
                    $i=1;
                    foreach ($std as $ass_id => $stdm) {
                        if($i++ == 3) break;
                        $marks += $stdm['marks'];
                    }
                    $temp['marks'] = $marks/2;
                    array_push($data, $temp);
                }
                break;
            case 'best_of_all':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    uasort($std, function($a, $b) { 
                        $result = 0;
                        if ($a['marks'] > $b['marks']) {
                            $result = -1;
                        } else if ($a['marks'] < $b['marks']) {
                            $result = 1;
                        }
                        return $result;
                    });
                    $i=1;
                    foreach ($std as $ass_id => $stdm) {
                        if($i++ == 2) break;
                        $marks += $stdm['marks'];
                    }
                    $temp['marks'] = $marks;
                    array_push($data, $temp);
                }
                break;
            case 'second_best_of_all':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    uasort($std, function($a, $b) { 
                        $result = 0;
                        if ($a['marks'] > $b['marks']) {
                            $result = -1;
                        } else if ($a['marks'] < $b['marks']) {
                            $result = 1;
                        }
                        return $result;
                    });
                    $i=1;
                    foreach ($std as $ass_id => $stdm) {
                        if($i++ == 3) break;
                        $marks = $stdm['marks'];
                    }
                    $temp['marks'] = $marks;
                    array_push($data, $temp);
                }
                break;
            case 'sum':
                foreach ($std_marks as $std_id => $std) {
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    foreach ($set_values as $ass_id => $val) {
                        if (!isset($std[$ass_id]['actual_marks'])) continue;

                        $marks += $std[$ass_id]['marks'];
                        if($std[$ass_id]['actual_marks'] == '-3') {
                            $not_applicables++;
                        }
                        if($std[$ass_id]['actual_marks'] == '-1') {
                            $absents++;
                        }
                    }
                    $temp['marks'] = $marks;
                    if(count($std) == $not_applicables) {
                        $temp['marks'] = '-3';
                    } else if(count($std) == $absents) {
                        $temp['marks'] = '-1';
                    }
                    array_push($data, $temp);
                }
                break;
                case 'sum_perc_ignore_na':
                    foreach ($std_marks as $std_id => $std) {
                        $temp = array();
                        $temp['student_id'] = $std_id;
                        $temp['assessments_entities_id'] = $derived_entities_id;
                        $marks = 0;
                        $total_marks = 0;
                        $not_applicables = 0;
                        $absents = 0;
                        foreach ($set_values as $ass_id => $val) {
                            if (!isset($std[$ass_id]['actual_marks'])) continue;
                            if($std[$ass_id]['actual_marks'] != '-3') {
                                $marks += $std[$ass_id]['marks'];
                                $total_marks += $std[$ass_id]['total_marks'];
                            }
                            if ($std[$ass_id]['actual_marks'] == '-3') {
                                $not_applicables ++;
                            }
                            if ($std[$ass_id]['actual_marks'] == '-2') {
                                $absents ++;
                            }
                        }
                        if ($total_marks > 0) {
                            $temp['marks'] = $marks / $total_marks * 100;
                        } else {
                            $temp['marks'] = '-3';
                        }
                        if(count($std) == $not_applicables) {
                            $temp['marks'] = '-3';
                        } else if(count($std) == $absents) {
                            $temp['marks'] = '-1';
                        }
                        array_push($data, $temp);
                    }
                    break;
            case 'multiply':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    foreach ($set_values as $ass_id => $val) {
                        $marks += ($std[$ass_id]['marks'] * $val);
                    }
                    $temp['marks'] = $marks;
                    array_push($data, $temp);
                }
                break;
            default:
                break;
        }

        
        $existing = $this->db->query("select id,student_id,assessments_entities_id from assessments_entities_marks_students where assessments_entities_id=$derived_entities_id and student_id in ($std_ids) ")->result_array();
        // echo '<pre>Uu: '; print_r($existing); die();
        // echo '<pre>U: '; print_r($data); die();
        $update_data = array();
        foreach ($existing as $key => $value) {
            $update_data[$value['student_id']] = $value;
        }

        $comparioson_marks_arr= [];
        foreach ($data as $key => $val) {
            $std_id = $val['student_id'];
            if(array_key_exists($std_id, $update_data)) {
                $update_data[$std_id]['marks'] = round($val['marks'], $rounding);
                unset($data[$key]);
                $name_marks= $this->db->select("a.long_name as ass_name, aems.marks")
                ->from('assessments_entities_marks_students aems')
                ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
                ->join('assessments a', 'a.id= ae.assessment_id')
                ->where('aems.assessments_entities_id', $derived_entities_id)
                ->where_in('aems.student_id', explode(',', $std_ids))
                ->get()->result();
                if(!empty($name_marks)) {
                    $stdObj= new stdClass();
                    $stdObj->name= $name_marks[0]->ass_name;
                    $stdObj->marks= $name_marks[0]->marks;
                    $stdObj->derived_marks= round($val['marks'], $rounding);
                    $comparioson_marks_arr= $stdObj;
                }
            }
        }

        foreach($data as $kii => $vaal) {
            $data[$kii]['marks']= round($vaal['marks'], $rounding);
        }

        // echo '<pre>I: '; print_r($data);
        // echo '<pre>U: '; print_r($rounding); die();

        // $status= 0;
        $this->db->trans_start();
        if(!empty($data)) {
            $this->db->insert_batch('assessments_entities_marks_students', $data);
        }
        if(!empty($update_data)) {
            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }
        $this->db->trans_complete();
        if($this->db->trans_status()) {
            return ['comparioson_marks_arr' => $comparioson_marks_arr, 'status' => 1];
        }
        return ['comparioson_marks_arr' => [], 'status' => 0];
    }

    public function fetch_and_regenerate_ccomputed_fields($inputs) {
        // Steps:
        // 1: Search, how many times entity is used in computed fields creation
        // 2: Regenerate that computed fields only
        $class_id= $inputs['class_id'];
        $aem_id= $inputs['aem_id'];
        $class_section_id= $inputs['class_section_id'];
        $student_id= $inputs['student_id'];
        $assessment_id= $inputs['assessment_id'];
// Getting computed fields where subject used in computed fields
        $all_computed_fields= $this->db->select("id, subjects")
            ->where('class_id', $class_id)
            ->order_by('sorting_order')
            ->get('assessment_computed_field_master')->result();
            
        $used_in_computed_fields_arr= [];
        // $computed_used_in_computed_fields_arr= [];
        if(!empty($all_computed_fields)) {
            foreach($all_computed_fields as $key => $val) {
                $subs_and_computed= json_decode($val->subjects);
                foreach($subs_and_computed as $id_key => $computed_id) {
                    foreach($computed_id  as $key1 => $val1) {
                        if($id_key != 'computed_fields') {
                            if($val1->entity_id == $aem_id) {
                                array_push($used_in_computed_fields_arr, $val->id);
                            }
                        }
                    }
                }
            }
        }
        $used_in_computed_fields_arr= array_unique($used_in_computed_fields_arr);
// Re-generating computed fields

        $comparioson_marks_arr= [];
        $status= true;
        foreach($used_in_computed_fields_arr as $c_key => $c_val) {
            $sts= $this->rgenerate_computed_fields_of_a_student($c_val, $student_id, $class_section_id);
            if($sts['status'] != 1 && $sts['status'] != '1') {
                $status = false;
            } else {
                array_push($comparioson_marks_arr, $sts['comparioson_marks_arr']);
            }
        }
        
        
        if(!empty($used_in_computed_fields_arr)) {
            return ['status' => $status, 'found' => 'yes', 'comparioson_marks_arr' => $comparioson_marks_arr];
        }
        return ['status' => 0, 'found' => 'no', 'comparioson_marks_arr' => []];
    }

    private function rgenerate_computed_fields_of_a_student($acfm_id, $student_id, $class_section_id) {
        $information_arr= $this->db->select("subjects, class_id, acad_year_id, fields_operation, ifnull(total_marks, 0) as total_marks, rounding, field_parameters")->where('id', $acfm_id)->get('assessment_computed_field_master')->row();
        $inf_arr= json_decode($information_arr->subjects);
        $marks_added_status_and_data= $this->__check_if_marks_added_for_a_stuydent($inf_arr, $information_arr->class_id, 'regeneration', $student_id, $class_section_id);
        if($marks_added_status_and_data['status'] == 'failed') {
            return ['status' => '-1', 'failed_ass_generationn' => $marks_added_status_and_data['failed_assessments_names'], 'comparioson_marks_arr' => []];
        }
        
        $this->db->trans_start();
        $comparioson_marks_arr= [];
            $insert_id= $acfm_id;
            if($information_arr->fields_operation == 'Sum') {
                $comparioson_marks_arr= $this->__update_sum_of_assessments_data_of_a_student($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding);
            }
            if($information_arr->fields_operation == 'Average') {
                $comparioson_marks_arr= $this->__update_average_of_assessments_data_of_a_student($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding);
            }
            if($information_arr->fields_operation == 'Exam Result') {
                $f_prec= json_decode($information_arr->field_parameters);
                $comparioson_marks_arr= $this->__update_exam_result_of_a_student($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding, $f_prec->failure_percentage);
            }
            if($information_arr->fields_operation == 'Percentage') {
                $reduced_percentage= json_decode($information_arr->field_parameters);
                $comparioson_marks_arr= $this->__update_reduced_percentage_of_a_student($marks_added_status_and_data['student_ids_arr'], $marks_added_status_and_data['class_section_ids_arr'], $marks_added_status_and_data['array'], $insert_id, $information_arr->total_marks, $information_arr->rounding, $reduced_percentage);
            }
        $this->db->trans_complete();

        if($this->db->trans_status()) {
            return ['comparioson_marks_arr' => $comparioson_marks_arr, 'status' => 1, 'failed_ass_generationn' => []];
        }
        $this->db->trans_rollback();
        return ['comparioson_marks_arr' => [], 'status' => 0, 'failed_ass_generationn' => []];
    }

    private function __check_if_marks_added_for_a_stuydent($array, $selected_class_id, $generation_type, $student_id, $class_section_id) {
        
        $student_count= 1;
        $student_ids_arr= [$student_id];
        $class_section_ids_arr= [$class_section_id];
        
        $is_marks_added= true;
        $failed_assessments_names= [];
        foreach($array as $key => $val) {
            $array_arr='';
                $array_arr= $array->$key;
            
            foreach($array_arr as $ekey => $eval) {
                    if($key != 'computed_fields') {
                        $ent_id='';
                        $ass_id='';
                        $is_elective= 0;
                            $ent_id= $eval->entity_id;
                            $ass_id= $eval->assessment_id;
                            $is_elective= $eval->is_elective;
                            $entity_name= $eval->entity_name;

                        if($is_elective != '1') {
                            $res= $this->db->select("aems.id as aems_id, aems.marks, aems.grade")
                            ->from('assessments_entities_marks_students aems')
                            ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
                            ->where('ae.entity_id', $ent_id)
                            ->where('ae.assessment_id', $ass_id)
                            ->where_in('aems.student_id', $student_ids_arr)
                            ->order_by('aems.student_id', 'asc')
                            ->get()->result();
                        } else {
                            foreach($student_ids_arr as $kkk => $vvv) {
                                $temp_res[]= $this->db->select("aems.id as aems_id, aems.marks, aems.grade")
                                    ->from('assessments_entities_marks_students aems')
                                    ->join('assessments_entities ae', 'ae.id= aems.assessments_entities_id')
                                    ->where('ae.entity_id', $ent_id)
                                    ->where('ae.assessment_id', $ass_id)
                                    ->where('aems.student_id', $vvv)
                                    ->get()->result();
                            }

                        }

                        if(isset($res) && (count($res) >= $student_count) || isset($temp_res) && (count($temp_res))) {
                            $aems_ids= [];
                            if($is_elective != '1') {
                                foreach($res as $akey => $aval) {
                                    array_push($aems_ids, $aval->marks);
                                }
                            } else {
                                foreach($temp_res as $akey => $aval) {
                                    if(isset($aval[0]->marks)) {
                                        array_push($aems_ids, $aval[0]->marks);
                                    } else {
                                        array_push($aems_ids, 0);
                                    }
                                }
                                $temp_res= [];
                            }

                                $array->$key[$ekey]->aems_marks= $aems_ids;

                        } else {
                            if($is_elective == '1' && $is_marks_added !== false) {
                                $is_marks_added= true;
                            } else {
                                $ass_name= $this->db->select("long_name")->where('id', $ass_id)->get('assessments')->row()->long_name;
                                array_push($failed_assessments_names, $ass_name. ' ~ ' .$entity_name);
                                $is_marks_added= false;
                            }
                        }
                    }

                }
        }

        if($is_marks_added) {
            return ['status' => 'success', 'student_ids_arr' => $student_ids_arr, 'class_section_ids_arr' => $class_section_ids_arr , 'array' => json_encode($array), 'failed_assessments_names' => []];
        } else {
            return ['status' => 'failed', 'student_ids_arr' => [], 'class_section_ids_arr' => [], 'array' => json_encode($array), 'failed_assessments_names' => $failed_assessments_names];
        }
    }

    private function __update_sum_of_assessments_data_of_a_student($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding) {
        $details_arr= json_decode($detail_in_json);
        $returnable= [];
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 0;
            $total= 0;
            foreach($details_arr as $ass_key => $ass_val) {
                if($ass_key != 'computed_fields') {
                    foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                        $total += $ent_val->subject_total_mark;
                        if(isset($ent_val->aems_marks[$std_key])) {
                            if($ent_val->aems_marks[$std_key] > 0) {
                                $result += $ent_val->aems_marks[$std_key];
                            }
                        } else {
                            $result += 0;
                        }
                    }
                } else {
                    foreach($details_arr->$ass_key as $computed_key => $computed_val) {
                        $computed_inf= $this->db->select("result, grand_total_marks")->where('ass_computed_field_master_id', $computed_val->cf_id)->where('student_id', $std_val)->get('assessment_computed_field_details')->result();
                        if(!empty($computed_inf)) {
                            $total += $computed_inf[0]->grand_total_marks;
                            $result += $computed_inf[0]->result;
                        }
                        
                    }
                }
            }
            

            if($defined_total_marks > 0) {
                $total_final= $defined_total_marks;
            } else {
                $total_final = $total; 
            }

            $student_update= array(
                'result' => round($result, (int)$rounding)
            );
            $returnable= $this->db->select("acfd.result as marks, acfm.name")
                    ->from('assessment_computed_field_details acfd')
                    ->join('assessment_computed_field_master acfm', 'acfm.id= acfd.ass_computed_field_master_id')
                    ->where('acfd.ass_computed_field_master_id', $insert_id)
                    ->where('acfd.student_id', $std_val)
                    ->get()->row();
            $returnable->derived_marks= round($result, (int)$rounding);
            $this->db->where('ass_computed_field_master_id', $insert_id)->where('student_id', $std_val)->update('assessment_computed_field_details', $student_update);
        }

        return $returnable;
    }

    private function __update_average_of_assessments_data_of_a_student($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding) {
        $details_arr= json_decode($detail_in_json);
        $returnable= [];
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 0;
            $total= 0;
            $total_marks= 0;
            foreach($details_arr as $ass_key => $ass_val) {
                if($ass_key != 'computed_fields') {
                    foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                        if(isset($ent_val->aems_marks)) {
                            if($ent_val->aems_marks[$std_key] > 0) {
                                $result += $ent_val->aems_marks[$std_key];
                            }
                        }
                        $total ++;
                        $total_marks= $ent_val->subject_total_mark;
                    }
                } else {
                    foreach($details_arr->$ass_key as $computed_key => $computed_val) {
                        $computed_inf= $this->db->select("result, grand_total_marks")->where('ass_computed_field_master_id', $computed_val->cf_id)->where('student_id', $std_val)->get('assessment_computed_field_details')->result();
                        if(!empty($computed_inf)) {
                            $total++;
                            $result += $computed_inf[0]->result;
                            $total_marks= $computed_inf[0]->grand_total_marks;
                        }
                        
                    }
                }
            }
            if($total != 0) {
                $result= round($result / $total, $rounding);
            } else {
                $result= 0;
            }

            $student_update= array(
                'result' => $result
            );

            $returnable= $this->db->select("acfd.result as marks, acfm.name")
            ->from('assessment_computed_field_details acfd')
            ->join('assessment_computed_field_master acfm', 'acfm.id= acfd.ass_computed_field_master_id')
            ->where('acfd.ass_computed_field_master_id', $insert_id)
            ->where('acfd.student_id', $std_val)
            ->get()->row();
            $returnable->derived_marks= $result;
            $this->db->where('ass_computed_field_master_id', $insert_id)->where('student_id', $std_val)->update('assessment_computed_field_details', $student_update);
        }

        return $returnable;
    }

    

    private function __update_exam_result_of_a_student($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding, $failure_perc) {
        $details_arr= json_decode($detail_in_json);
        $returnable= [];
        foreach($student_ids_arr as $std_key => $std_val) {
            $result= 'Pass';
            $percent= 0;
            foreach($details_arr as $ass_key => $ass_val) {
                if($ass_key != 'computed_fields') {
                    foreach($details_arr->$ass_key as $ent_key => $ent_val) {
                        if(isset($ent_val->aems_marks)) {
                            if($ent_val->aems_marks[$std_key] > 0) {
                                $perc= ( $ent_val->aems_marks[$std_key] / $ent_val->subject_total_mark ) * 100;
                                if(number_format($perc, 2) < number_format($failure_perc, 2)) {
                                    $result = 'Failed';
                                    $percent= $perc;
                                    break;
                                }
                            }
                        }
                    }
                    if($result == 'Failed' || $result == 'TBD') {
                        break;
                    }
                } else {
                    foreach($details_arr->$ass_key as $computed_key => $computed_val) {
                        $computed_inf= $this->db->select("result, grand_total_marks")->where('ass_computed_field_master_id', $computed_val->cf_id)->where('student_id', $std_val)->get('assessment_computed_field_details')->result();
                        if(!empty($computed_inf)) {
                            $perc= ( $computed_inf[0]->result / $computed_inf[0]->grand_total_marks ) * 100;
                            if(number_format($perc, 2) < number_format($failure_perc, 2)) {
                                $result = 'Failed';
                                $percent= $perc;
                                break;
                            }
                        }
                    }
                    if($result == 'Failed' || $result == 'TBD') {
                        break;
                    }
                }
            }

            $student_update= array(
                'result' => $result
            );

            $returnable= $this->db->select("acfd.result as marks, acfm.name")
            ->from('assessment_computed_field_details acfd')
            ->join('assessment_computed_field_master acfm', 'acfm.id= acfd.ass_computed_field_master_id')
            ->where('acfd.ass_computed_field_master_id', $insert_id)
            ->where('acfd.student_id', $std_val)
            ->get()->row();
            $returnable->derived_marks= $result;
            $this->db->where('ass_computed_field_master_id', $insert_id)->where('student_id', $std_val)->update('assessment_computed_field_details', $student_update);
        }

        return $returnable;
    }

    public function regenerate_average_high_and_rank($input) {
        $student_id= $input['student_id'];
        $class_section_id= $input['class_section_id'];
        $class_id= $input['class_id'];
        $aem_id= $input['aem_id'];
        $assessment_id= $input['assessment_id'];
        $aems_id= $input['aems_id'];
        $updated_marks= $input['updated_marks'];
        $entities_group_id= $input['entities_group_id'];
        $elective_group_id= $input['elective_group_id'];

        $this->db->trans_start();
            $sec_avg_high= $this->__generate_sec_avg_high($class_section_id, $assessment_id, $aem_id);
            $class_avg_high= $this->__generate_class_avg_high($class_id, $updated_marks, $assessment_id, $aem_id, $sec_avg_high);
            $class_section_rank= $this->__generate_class_section_rank($class_section_id, $class_id, $updated_marks, $assessment_id, $aem_id, $class_avg_high, $student_id);
        $this->db->trans_complete();

        unset($class_section_rank->all_sec_students);
        unset($class_section_rank->all_class_students);

        return $class_section_rank;

        // echo '<pre>Final: '; print_r($class_section_rank); die();

    }

    private function __generate_sec_avg_high($class_section_id, $assessment_id, $aem_id) { // it is connected to average high rank generation : 'Generate average/high/rank' tab
        $old_sec_high_avg= $this->db->select('section_average_highest, id')->where('assessment_id', $assessment_id)->where('entity_id', $aem_id)->get('assessments_entities')->result();
        
        $ae_id= 0;
        $old_section_average_highest= [];
        if(!empty($old_sec_high_avg)) { // getting ae_id and storing old average highest
            $ae_id= $old_sec_high_avg[0]->id;
            if(isset($old_sec_high_avg[0]->section_average_highest) && $old_sec_high_avg[0]->section_average_highest != 'null' && $old_sec_high_avg[0]->section_average_highest != '' && trim($old_sec_high_avg[0]->section_average_highest)) {
                $old_section_average_highest= $old_sec_high_avg[0]->section_average_highest;
                $old_section_average_highest= json_decode($old_section_average_highest);
            }
        }

       

        $all_sec_students= $this->db->select('student_admission_id')
                            ->where('class_section_id', $class_section_id)
                            ->where('acad_year_id', $this->yearId)
                            ->where_not_in('promotion_status', ['4', '5', 'JOINED'])
                            ->get('student_year')->result();
        $student_ids_arr= [];
        foreach($all_sec_students as $key => $val) {
            array_push($student_ids_arr, $val->student_admission_id);
        }
        // 

        $section_max_and_avg= $this->db->select("if(marks > 0, round(avg(marks), 2), 0) as avg, max(marks) as max")
                ->where_in('student_id', $student_ids_arr)
                ->where('assessments_entities_id', $ae_id)
                ->get('assessments_entities_marks_students')->row();

        $stdObj= new stdClass();
        $stdObj->section_old_avg= 0;
        $stdObj->section_old_max= 0;
        $stdObj->section_new_avg= 0;
        $stdObj->section_new_max= 0;
        if(!empty($old_section_average_highest)) {
            $is_section_found= false;
            foreach($old_section_average_highest as $key1 => $val1) {
                if($val1->section_id == $class_section_id) {
                    $is_section_found= true;
                    // storing old and new values to track
                    $stdObj->section_old_avg= $val1->average;
                    $stdObj->section_old_max= $val1->highest;
                    $stdObj->section_new_avg= isset($section_max_and_avg->avg) ? $section_max_and_avg->avg : 0;
                    $stdObj->section_new_max= isset($section_max_and_avg->max) ? $section_max_and_avg->max : 0;
                    // updating section avg and highest
                    $val1->average= isset($section_max_and_avg->avg) ? $section_max_and_avg->avg : 0;
                    $val1->highest= isset($section_max_and_avg->max) ? $section_max_and_avg->max : 0;
                    break;
                }
            }
            if(!$is_section_found) {
                $stdObj->section_new_avg= isset($section_max_and_avg->avg) ? $section_max_and_avg->avg : 0;
                $stdObj->section_new_max= isset($section_max_and_avg->max) ? $section_max_and_avg->max : 0;
    
                $sec_avg_high_obj= new stdClass();
                $sec_avg_high_obj->id= $ae_id;
                $sec_avg_high_obj->section_id= $class_section_id;
                $sec_avg_high_obj->average= isset($section_max_and_avg->avg) ? $section_max_and_avg->avg : 0;
                $sec_avg_high_obj->highest= isset($section_max_and_avg->max) ? $section_max_and_avg->max : 0;
                array_push($old_section_average_highest, $sec_avg_high_obj);
            }
        } else { //If setion average highest not generated pastly
            $stdObj->section_new_avg= isset($section_max_and_avg->avg) ? $section_max_and_avg->avg : 0;
            $stdObj->section_new_max= isset($section_max_and_avg->max) ? $section_max_and_avg->max : 0;

            $sec_avg_high_obj= new stdClass();
            $sec_avg_high_obj->id= $ae_id;
            $sec_avg_high_obj->section_id= $class_section_id;
            $sec_avg_high_obj->average= isset($section_max_and_avg->avg) ? $section_max_and_avg->avg : 0;
            $sec_avg_high_obj->highest= isset($section_max_and_avg->max) ? $section_max_and_avg->max : 0;
            array_push($old_section_average_highest, $sec_avg_high_obj);
        }

        

        // echo '<pre>'; print_r($old_section_average_highest); die();
        $old_section_average_highest= json_encode($old_section_average_highest);
        $status= $this->db->where('id', $ae_id)->update('assessments_entities', ['section_average_highest' => $old_section_average_highest]);
        $stdObj->all_sec_students= $student_ids_arr;
        $stdObj->ae_id= $ae_id;
        if($status) {
            $stdObj->section_status= 1;
            return $stdObj;
        }
        $stdObj->section_status= 0;
        return $stdObj;

    }

    public function __generate_class_avg_high($class_id, $updated_marks, $assessment_id, $aem_id, $stdObj) {
        $old_class_high_avg= $this->db->select('class_average, class_highest, id')->where('assessment_id', $assessment_id)->where('entity_id', $aem_id)->get('assessments_entities')->result();
        $ae_id= 0;
        $old_class_average= 0;
        $old_class_highest= 0;
        if(!empty($old_class_high_avg)) { // getting ae_id and storing old average highest
            $ae_id= $old_class_high_avg[0]->id;
            $old_class_average= $old_class_high_avg[0]->class_average;
            $old_class_highest= $old_class_high_avg[0]->class_highest;
        }

        // Class avg high
        $all_class_students= $this->db->select('student_admission_id')
                            ->where('class_id', $class_id)
                            ->where('acad_year_id', $this->yearId)
                            ->where_not_in('promotion_status', ['4', '5', 'JOINED'])
                            ->get('student_year')->result();
        $student_ids_arr= [];
        foreach($all_class_students as $key => $val) {
            array_push($student_ids_arr, $val->student_admission_id);
        }
        $class_max_and_avg= $this->db->select("if(marks > 0, round(avg(marks), 2), 0) as avg, max(marks) as max")
            ->where_in('student_id', $student_ids_arr)
            ->where('assessments_entities_id', $ae_id)
            ->get('assessments_entities_marks_students')->row();
        
        $stdObj->old_class_avg= $old_class_average;
        $stdObj->old_class_max= $old_class_highest;
        $stdObj->new_class_avg= isset($class_max_and_avg->avg) ? $class_max_and_avg->avg : 0;
        $stdObj->new_class_max= isset($class_max_and_avg->max) ? $class_max_and_avg->max : 0;

        $status= $this->db->where('id', $ae_id)->update('assessments_entities', ['class_average' => isset($class_max_and_avg->avg) ? $class_max_and_avg->avg : 0, 'class_highest' => isset($class_max_and_avg->max) ? $class_max_and_avg->max : 0]);
        $stdObj->all_class_students= $student_ids_arr;
        if($status) {
            $stdObj->class_status= 1;
            return $stdObj;
        }
        $stdObj->class_status= 0;
        return $stdObj;
    }

    public function __generate_class_section_rank($class_section_id, $class_id, $updated_marks, $assessment_id, $aem_id, $stdObj, $student_id) {
        $ae_id= $stdObj->ae_id;
        // sec stds
        $section_student_ids_arr= $stdObj->all_sec_students;
        // class stds
        $class_student_ids_arr= $stdObj->all_class_students;

// START: section rank generation
        $sec_std_ids = implode(",", $section_student_ids_arr);  

        $marks_sql = "SELECT aems.id as id, aems.student_id, (((sum(case when aems.marks>=0 then aems.marks else 0 end)) / (sum(ae.total_marks))) * 100) as percentage  
            from assessments_entities ae 
            join assessments_entities_marks_students aems on aems.assessments_entities_id=ae.id 
            where ae.assessment_id=$assessment_id 
            and aems.student_id in ($sec_std_ids) 
            and aems.marks!=-3 and aems.marks!=-2 
            and ae.entity_id in ($aem_id) 
            group by aems.student_id";
        $section_rank_list = $this->db_readonly->query($marks_sql)->result();

        usort($section_rank_list, function ($item1, $item2) { // sorting rank wise
            return $item2->percentage <=> $item1->percentage;
        });

        $ranks = array();
        $rank = 1;
        foreach ($section_rank_list as $i => $std) {
            if(in_array($section_rank_list[$i]->percentage, $ranks)) {
                array_push($ranks, $section_rank_list[$i]->percentage);
                $section_rank_list[$i]->entity_section_rank = $rank;
            } else {
                $prev_rank_count = count($ranks);
                $ranks = array();
                array_push($ranks, $section_rank_list[$i]->percentage);
                $section_rank_list[$i]->entity_section_rank = $rank + $prev_rank_count;
                $rank = $rank + $prev_rank_count;
            }
            unset($section_rank_list[$i]->percentage);
        }
// END: section rank generation

// START: class rank generation
        $class_std_ids = implode(",", $class_student_ids_arr);  
       
        $marks_sql = "SELECT aems.id as id, aems.student_id, (((sum(case when aems.marks>=0 then aems.marks else 0 end)) / (sum(ae.total_marks))) * 100) as percentage  
            from assessments_entities ae 
            join assessments_entities_marks_students aems on aems.assessments_entities_id=ae.id 
            where ae.assessment_id=$assessment_id 
            and aems.student_id in ($class_std_ids) 
            and aems.marks!=-3 and aems.marks!=-2 
            and ae.entity_id in ($aem_id) 
            group by aems.student_id";
        $class_rank_list = $this->db_readonly->query($marks_sql)->result(); // The students who are not added to the marks table, will not reflects here

        // 
        usort($class_rank_list, function ($item1, $item2) { // sorting rank wise
            return $item2->percentage <=> $item1->percentage;
        });

        $ranks = array();
        $rank = 1;
        foreach ($class_rank_list as $i => $std) {
            if(in_array($class_rank_list[$i]->percentage, $ranks)) {
                array_push($ranks, $class_rank_list[$i]->percentage);
                $class_rank_list[$i]->entity_grade_rank = $rank;
            } else {
                $prev_rank_count = count($ranks);
                $ranks = array();
                array_push($ranks, $class_rank_list[$i]->percentage);
                $class_rank_list[$i]->entity_grade_rank = $rank + $prev_rank_count;
                $rank = $rank + $prev_rank_count;
            }
            unset($class_rank_list[$i]->percentage);
        }

        $old_class_section_rank= $this->db->select("entity_section_rank, entity_grade_rank")->where('student_id', $student_id)->where('assessments_entities_id', $ae_id)->get('assessments_entities_marks_students')->result();
        $stdObj->old_class_rank= 0;
        $stdObj->old_section_rank= 0;
        if(!empty($old_class_section_rank)) {
            $stdObj->old_class_rank= $old_class_section_rank[0]->entity_grade_rank;
            $stdObj->old_section_rank= $old_class_section_rank[0]->entity_section_rank;
        }
// END: class rank generation

        $status1= $this->db->update_batch('assessments_entities_marks_students', $section_rank_list, 'id');
        $status2= $this->db->update_batch('assessments_entities_marks_students', $class_rank_list, 'id');
        
        $new_class_section_rank= $this->db->select("entity_section_rank, entity_grade_rank")->where('student_id', $student_id)->where('assessments_entities_id', $ae_id)->get('assessments_entities_marks_students')->result();
        $stdObj->new_class_rank= 0;
        $stdObj->new_section_rank= 0;
        if(!empty($new_class_section_rank)) {
            $stdObj->new_class_rank= $new_class_section_rank[0]->entity_grade_rank;
            $stdObj->new_section_rank= $new_class_section_rank[0]->entity_section_rank;
        }

        if($status1) {
            $stdObj->sec_rank_status= 1;
        } else {
            $stdObj->sec_rank_status= 0;
        }
        if($status2) {
            $stdObj->class_rank_status= 1;
        } else {
            $stdObj->class_rank_status= 0;
        }

        return $stdObj;

    }
    public function update_template(){
        $input=$this->input->post();
        $id = $input['id'];
        $gen_type = (isset($input['generation_type']))?1:0;
        $template_background = array_filter($input, function ($key) {
            return strpos($key, 'template_background_') == 0;
        }, ARRAY_FILTER_USE_KEY);
        $template_background_json = json_encode($template_background);
        $data = array(
            'generation_type' => $gen_type,
            'template_background' => $template_background_json,
        );
        $this->db->where('id', $id)
                 ->update('assessment_marks_card_templates', $data);

       return 1;
                
    }

    public function gettemplate_BG($tempId) {
        $this->db_readonly->select("id,template_background");
        $this->db_readonly->from('assessment_marks_card_templates');
        $this->db_readonly->where('id', $tempId);
        $data = $this->db_readonly->get()->row();
        return $data;       
    }

    public function get_created_date_of_marksCArd($id) {
        return $this->db_readonly->select('date_format(date, "%d-%m-%Y") as created_date')->where('id', $id)->get('assessments_marks_card_history')->row()->created_date;
    }

    public function onblur_auto_save() {
        $input= $this->input->post();
        // echo "<pre>"; print_r($input); die();

        // [SVC] | [2024-10-09 12:39:51] | unknown | [error_statistics_string] | Undefined index: student_id (/home/<USER>/oxygenv2/application/models/examination/Assessment_marks_model.php:5841) | (Notice:8) | 10.11.141.188:Mozilla/5.0 (Linux; Android 14; CPH2467 Build/UKQ1.230924.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/129.0.6668.70 Mobile Safari/537.36 
        if(!isset($input['student_id']) || !isset($input['aeeseement_entities_id']) || !isset( $input['new_marks']) || !isset($input['evaluation_type'])) {
            return false;
        }
        
        $check= $this->db->where('student_id', $input['student_id'])->where('assessments_entities_id', $input['aeeseement_entities_id'])->get('assessments_entities_marks_students')->row();
        $this->db->trans_start();
        if(!empty($check)) {
            $data= ['marks' => $input['new_marks'], 'status' => 1];
            if($input['evaluation_type'] != 'marks') {
                $data= ['grade' => $input['new_marks'], 'status' => 1];
            }

            $this->db->where('student_id', $input['student_id'])->where('assessments_entities_id', $input['aeeseement_entities_id'])->update('assessments_entities_marks_students', $data);
            $stdMarksId= $check->id;
            $markas= $input['new_marks'];
            $marksHistory = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => "Marks changed to $markas",
                'action_by' => $this->authorization->getAvatarId()
            );
        } else {
            $data= [
                'marks' => $input['new_marks'],
                'student_id' => $input['student_id'],
                'assessments_entities_id' => $input['aeeseement_entities_id'],
                'status' => 1
            ];
            if($input['evaluation_type'] != 'marks') {
                $data= [
                    'grade' => $input['new_marks'],
                    'student_id' => $input['student_id'],
                    'assessments_entities_id' => $input['aeeseement_entities_id'],
                    'status' => 1
                ];
            }

            $x= $this->db->insert('assessments_entities_marks_students', $data);
            $stdMarksId= $this->db->insert_id();
            $markas= $input['new_marks'];
            $marksHistory = array(
                'assessments_entities_marks_students_id' => $stdMarksId,
                'action' => "Marks added $markas",
                'action_by' => $this->authorization->getAvatarId()
            );
        }
        $this->db->insert('assessments_entities_marks_students_history',$marksHistory);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    // public function insert_rows_initially_ifNotAdded($entities, $students) {
    //     // echo '<pre>'; print_r($entities); print_r($students); die();
    //     $insertArr= [];
    //     foreach($entities as $key => $ent) {
    //         foreach($students as $key2 => $std) {
    //             $check= $this->db_readonly->where('student_id', $std->student_id)->where('assessments_entities_id', $ent->aeId)->get('assessments_entities_marks_students')->row();
    //             if(empty($check)) {
    //                 $insertArr[]= array(
    //                     'student_id'=> $std->student_id,
    //                     'assessments_entities_id'=> $ent->aeId,
    //                     'marks' => -3
    //                 );
    //             }
    //         }
    //     }
    //     if(!empty($insertArr)) {
    //         $this->db->trans_start();
    //         $this->db->insert_batch('assessments_entities_marks_students', $insertArr);
    //         $this->db->trans_complete();
    //         return $this->db->trans_status();
    //     }
    //     return false;

    // }

     public function get_section_class_for_remarks($class_id){
        return $this->db_readonly->select('c.class_name,cs.id, cs.id as sectionId, cs.section_name as sectionName')
        ->from('class c')
        ->where('c.id', $class_id)
        ->join('class_section cs', 'cs.class_id=c.id')
        ->where('cs.is_placeholder', 0)
        ->order_by('cs.id')
        ->get()->result();
     }

     public function save_remark_studentwise(){
        $temp_id=$this->input->post('template_id');
        $stdId=$this->input->post('student_id');
        $class_id=$this->input->post('class_id');
        $section_id=$this->input->post('section_id');
        $remarks=$this->input->post('remarks');
        $verify = $this->db->select('id')
                            ->where('marks_card_temp_id', $temp_id)
                            ->where('student_id', $stdId)
                            ->get('assessments_marks_cards')
                            ->row();
        $data = array(
            'marks_card_temp_id' => $temp_id,
            'student_id' => $stdId,
            'remarks' => $remarks,
            'created_by' => $this->authorization->getAvatarId(),
            'active_marks_card_id' => 0,
            'remark_status' => null
        );
        if(empty($verify)){
            $this->db->insert('assessments_marks_cards', $data);
            return 'inserted';
        }else{
            $this->db->where('id', $verify->id)
            ->update('assessments_marks_cards', $data);
            return 'updated';

     }
    }

     public function get_default_remarks(){
        return $this->db_readonly->select('asrg.id,asrg.group_name,asrg.remarks_for,asrd.subject_remarks,asrd.id as description_id')
        ->from('assessment_subject_remarks_group asrg')
        ->join('assessment_subject_remarks_description asrd', 'asrd.remarks_group_id=asrg.id')
        ->where('asrg.remarks_for', 1)
        ->get()->result();

     }
public function selected_remarks(){
    $id= $this->input->post('selected_val');
    return $this->db_readonly->select("ifnull(asrd.subject_remarks, ' ')as remarks")
        ->from('assessment_subject_remarks_description asrd')
        ->where('asrd.id', $id)
        ->get()->row()->remarks;

}

    private function __insert_percentage_reduced_to($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $fields_parameter_json_encoded) {
       

        $fields_parameter= json_decode($fields_parameter_json_encoded);
        $entities_ids= [];
        $assesments_ids= [];
        $computed_ids_if_there= [];
        $calculated_sub_totalMarks= 0;
        $total= 0;
        $unique_entity_id= []; // single entity id means in percentage operation, only one subject from multiple assessments can be there, more than one subject_id from a same assessments is not allowed for calculate percentage
        if(!empty($subjects)) {
            foreach($subjects as $key => $val) {
                foreach($val as $keyOne => $valOne) {
                    $total ++;
                    if($key == 'computed_fields') {
                        $computed_ids_if_there[]= $valOne->cf_id;
                    } else {
                        $calculated_sub_totalMarks = $valOne->subject_total_mark;
                        $entities_ids[]= $valOne->entity_id;
                        $unique_entity_id[]= $valOne->entity_id;
                        // $assesments_ids[]= $valOne->assessment_id;
                    }
                }
                if($key != 'computed_fields') {
                    $assesments_ids[$key]= $entities_ids;
                    $entities_ids= [];
                }
            }
        }

        $unique_entity_id = array_unique($unique_entity_id);

          // Subject marks as student obj
          $entities_marks_student_wise_v2= [];
          $entities_marks_student_wise= [];
         if(!empty($assesments_ids)) {
              $entities_marks_student_wise= $this->__get_entities_marks_student_wise_assessment_wise($student_ids_arr, $assesments_ids);
              foreach($entities_marks_student_wise as $key => $val) {
                  $entities_marks_student_wise_v2[$val->student_id]= $val;
              }
              
         }
  
        

          // Computed marks as student obj
          $compted_marks_student_wise_v2= [];
          $compted_marks_student_wise= [];
          if(!empty($computed_ids_if_there)) {
              $compted_marks_student_wise= $this->__get_computed_marks_student_wise_comp_field_wise($student_ids_arr, $computed_ids_if_there);
              foreach($compted_marks_student_wise as $key => $val) {
                  $compted_marks_student_wise_v2[$val->student_id]= $val;
                }
            }

          
            
        $return= false;
        $subjects_redued_percentage= [];
        if(isset($fields_parameter) && isset($fields_parameter->subjects_redued_percentage)) {
            $subjects_redued_percentage= $fields_parameter->subjects_redued_percentage;
        }
        $cf_reduced_percentage= [];
        if(isset($fields_parameter) && isset($fields_parameter->cf_reduced_percentage)) {
            $cf_reduced_percentage= $fields_parameter->cf_reduced_percentage;
        }
            
        

        if(!empty($details_if_pregenerated)) {
             
            foreach($details_if_pregenerated as $key => $val) {
                $addedReducedMarks= 0;
                $addedReducedTotalMarks= 0;
                if(!empty($entities_marks_student_wise_v2) && !empty($subjects_redued_percentage) && !empty($compted_marks_student_wise_v2) && !empty($cf_reduced_percentage)) {
                    foreach($assesments_ids as $assKey => $assVal){
                        if(!empty($unique_entity_id)) {
                            foreach($unique_entity_id as $entKey => $single_entity_id) {
                                $indexKeySub= $assKey. '___' .$single_entity_id;
                                if(isset($entities_marks_student_wise_v2[$val->student_id]->$indexKeySub)) {
                                    $scored_and_total= explode('___', $entities_marks_student_wise_v2[$val->student_id]->$indexKeySub);
                                    $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $subjects_redued_percentage->$indexKeySub;
                                    $addedReducedTotalMarks += $subjects_redued_percentage->$indexKeySub;
                                }
                            }
                        }
                    }
                    foreach($computed_ids_if_there as $cfKey => $cfVal) {
                        if (isset($compted_marks_student_wise_v2[$val->student_id])) {  
                            foreach($compted_marks_student_wise_v2[$val->student_id] as $k7 => $v7) {
                                if($k7 == $cfVal) {
                                    $scored_and_total = explode('___', $v7);
                                    $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                                    $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                                    
                                    $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                                    $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                                }
                            }
                        }

                        // if (isset($compted_marks_student_wise_v2[$val->student_id]->{$cfVal})) {  
                        //     $scored_and_total = explode('___', $compted_marks_student_wise_v2[$val->student_id]->{$cfVal});
                        //     $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                        //     $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                            
                        //     $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                        //     $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                        // }
                        // $scored_and_total= explode('___', $compted_marks_student_wise_v2[$val->student_id]->$cfVal);
                        // $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $cf_reduced_percentage->$cfVal;
                        // $addedReducedTotalMarks += $cf_reduced_percentage->$cfVal;
                    }
                } else if(!empty($entities_marks_student_wise_v2) && !empty($subjects_redued_percentage)) {
                    foreach($assesments_ids as $assKey => $assVal){
                        if(!empty($unique_entity_id)) {
                            foreach($unique_entity_id as $entKey => $single_entity_id) {
                                $indexKeySub= $assKey. '___' .$single_entity_id;
                                if(isset($entities_marks_student_wise_v2[$val->student_id]->$indexKeySub)) {
                                    $scored_and_total= explode('___', $entities_marks_student_wise_v2[$val->student_id]->$indexKeySub);
                                    $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $subjects_redued_percentage->$indexKeySub;
                                    $addedReducedTotalMarks += $subjects_redued_percentage->$indexKeySub;
                                }
                            }
                        }
                    }
                } else if(!empty($compted_marks_student_wise_v2) && !empty($cf_reduced_percentage)) {
                    foreach($computed_ids_if_there as $cfKey => $cfVal) {
                        if (isset($compted_marks_student_wise_v2[$val->student_id])) {  
                            foreach($compted_marks_student_wise_v2[$val->student_id] as $k7 => $v7) {
                                if($k7 == $cfVal) {
                                    $scored_and_total = explode('___', $v7);
                                    $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                                    $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                                    
                                    $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                                    $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                                }
                            }
                        }

                        // if (isset($compted_marks_student_wise_v2[$val->student_id]->{$cfVal})) {
                        //     $scored_and_total = explode('___', $compted_marks_student_wise_v2[$val->student_id]->{$cfVal});
                        //     $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                        //     $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                            
                        //     $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                        //     $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                        // }

                        // $scored_and_total= explode('___', $compted_marks_student_wise_v2[$val->student_id]->$cfVal);
                        // $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $cf_reduced_percentage->$cfVal;
                        // $addedReducedTotalMarks += $cf_reduced_percentage->$cfVal;
                    }
                }
                $result= $addedReducedMarks;
                $val->result = round($result, $rounding);
            }

            $arrayofData = array_map(function($item) {
                return (array) $item;
            }, $details_if_pregenerated);

            

            $this->db->update_batch('assessment_computed_field_details', $arrayofData, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            
            foreach($student_ids_arr as $key => $val) {
                $addedReducedMarks= 0;
                $addedReducedTotalMarks= 0;
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];
                }
                
                if(!empty($entities_marks_student_wise_v2) && !empty($subjects_redued_percentage) && !empty($compted_marks_student_wise_v2) && !empty($cf_reduced_percentage)) {
                    foreach($assesments_ids as $assKey => $assVal){
                        if(!empty($unique_entity_id)) {
                            foreach($unique_entity_id as $entKey => $single_entity_id) {
                                $indexKeySub= $assKey. '___' .$single_entity_id;
                                if(isset($entities_marks_student_wise_v2[$val]->$indexKeySub)) {
                                    $scored_and_total= explode('___', $entities_marks_student_wise_v2[$val]->$indexKeySub);
                                    $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $subjects_redued_percentage->$indexKeySub;
                                    $addedReducedTotalMarks += $subjects_redued_percentage->$indexKeySub;
                                }
                            }
                        }
                    }
                    foreach($computed_ids_if_there as $cfKey => $cfVal) {
                        if (isset($compted_marks_student_wise_v2[$val])) {  
                            foreach($compted_marks_student_wise_v2[$val] as $k7 => $v7) {
                                if($k7 == $cfVal) {
                                    $scored_and_total = explode('___', $v7);
                                    $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                                    $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                                    
                                    $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                                    $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                                }
                            }
                        }

                        // if (isset($compted_marks_student_wise_v2[$val]->{$cfVal})) {  
                        //     $scored_and_total = explode('___', $compted_marks_student_wise_v2[$val]->{$cfVal});
                        //     $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                        //     $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                            
                        //     $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                        //     $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                        // }



                        // $scored_and_total= explode('___', $compted_marks_student_wise_v2[$val]->$cfVal);
                        // $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $cf_reduced_percentage->$cfVal;
                        // $addedReducedTotalMarks += $cf_reduced_percentage->$cfVal;
                    }
                } else if(!empty($entities_marks_student_wise_v2) && !empty($subjects_redued_percentage)) {
                    foreach($assesments_ids as $assKey => $assVal){
                        if(!empty($unique_entity_id)) {
                            foreach($unique_entity_id as $entKey => $single_entity_id) {
                                $indexKeySub= $assKey. '___' .$single_entity_id;
                                if(isset($entities_marks_student_wise_v2[$val]->$indexKeySub)) {
                                    $scored_and_total= explode('___', $entities_marks_student_wise_v2[$val]->$indexKeySub);
                                    $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $subjects_redued_percentage->$indexKeySub;
                                    $addedReducedTotalMarks += $subjects_redued_percentage->$indexKeySub;
                                }
                            }
                        }
                    }
                } else if(!empty($compted_marks_student_wise_v2) && !empty($cf_reduced_percentage)) {
                    foreach($computed_ids_if_there as $cfKey => $cfVal) {
                        if (isset($compted_marks_student_wise_v2[$val])) {  
                            foreach($compted_marks_student_wise_v2[$val] as $k7 => $v7) {
                                if($k7 == $cfVal) {
                                    $scored_and_total = explode('___', $v7);
                                    $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                                    $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                                    
                                    $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                                    $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                                }
                            }
                        }



                        // if (isset($compted_marks_student_wise_v2[$val]->{$cfVal})) {  
                        //     $scored_and_total = explode('___', $compted_marks_student_wise_v2[$val]->{$cfVal});
                        //     $scored = isset($scored_and_total[0]) ? $scored_and_total[0] : 0;
                        //     $total = isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1;
                            
                        //     $addedReducedMarks += ($scored / $total) * $cf_reduced_percentage->{$cfVal};
                        //     $addedReducedTotalMarks += $cf_reduced_percentage->{$cfVal};
                        // }


                        // $scored_and_total= explode('___', $compted_marks_student_wise_v2[$val]->$cfVal);
                        // $addedReducedMarks += ($scored_and_total[0] / (isset($scored_and_total[1]) && $scored_and_total[1] > 0 ? $scored_and_total[1] : 1)) * $cf_reduced_percentage->$cfVal;
                        // $addedReducedTotalMarks += $cf_reduced_percentage->$cfVal;
                    }
                }
                $result= $addedReducedMarks;

                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => $addedReducedTotalMarks
                );
            }

            // echo '<pre>'; print_r($fresh_generations_insert); die();

            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }
        return $return;
    }

    function __get_entities_marks_student_wise_assessment_wise($student_ids_arr, $assesments_ids) {
        foreach($assesments_ids as $ass_id => $val) {
            $x[]= $this->db->select("ifnull(SUM( if(aems.marks > 0, aems.marks, 0) ), 0) as sumMarks, aems.student_id, ae.assessment_id, ae.entity_id, ae.total_marks") // It is valid only for single subject in each assessments
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                ->where_in('ae.entity_id', $val)
                ->where('ae.assessment_id', $ass_id)
                ->where_in('aems.student_id', $student_ids_arr)
                ->group_by('aems.student_id')
                ->order_by('aems.student_id')
                ->get()->result();
        }
        // echo '<pre>'; print_r($x); die();
        foreach($x as $key => $val) {
            if(empty($val)) {
                unset($x[$key]);
            }
        }

        // Merging student wise
        $mergedData = [];
        foreach ($x as $scores) {
            foreach ($scores as $score) {
                $studentId = $score->student_id;
                if (isset($mergedData[$studentId])) {
                    $obj_key= $score->assessment_id. '___' .$score->entity_id;
                    $mergedData[$studentId]->$obj_key = $score->sumMarks. '___' .$score->total_marks; // scored___total
                } else {
                    $mergedData[$studentId] = (object) [
                        'student_id' => $studentId,
                        $score->assessment_id. '___' .$score->entity_id => $score->sumMarks. '___' .$score->total_marks
                    ];
                }
            }
        }
        $finalResult = array_values($mergedData);

        return $finalResult;
    }
    
    function __get_computed_marks_student_wise_comp_field_wise($student_ids_arr, $computed_ids_if_there) {
        foreach($computed_ids_if_there as $key => $val) {
            $x[]= $this->db->select("'$val' as cf_id, SUM( if(result > 0, result, 0) ) as sumMarks, student_id, SUM( if(grand_total_marks > 0, grand_total_marks, 0) ) as grand_total_marks")
            ->where('ass_computed_field_master_id', $val)
            ->where_in('student_id', $student_ids_arr)
            ->group_by('student_id')
            ->get('assessment_computed_field_details')->result();
        }

        // Merging student wise
        $mergedData = [];
        foreach ($x as $scores) {
            foreach ($scores as $score) {
                $studentId = $score->student_id;
                $obj_key= $score->cf_id;
                if (isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->$obj_key = $score->sumMarks. '___' .$score->grand_total_marks; // scored___total
                } else {
                    $mergedData[$studentId] = (object) [
                        'student_id' => $studentId,
                        $obj_key => $score->sumMarks. '___' .$score->grand_total_marks
                    ];
                }
            }
        }
        $finalResult = array_values($mergedData);

        return $finalResult;
    }

    function getGradingSystem() {
        return $this->db_readonly->get('assessment_grading_system')->result();
    }

    private function __insert_gpa_result_v2($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $grading_system_id, $fields_operation) {
        $gradingSystem= $this->db->where('id', $grading_system_id)->get('assessment_grading_system')->row();
        $gradePointsObj= [];
        if(empty($gradingSystem)) {
            return false;
        } else {
            $gradePointsObj= json_decode($gradingSystem->grades);
        }
        if(empty($gradePointsObj)) {
            return false;
        }

        // echo '<pre>'; print_r($gradePointsObj); die();

        $entities_ids= [];
        $assesments_ids= [];
        $computed_ids_if_there= [];
        $calculated_sub_totalMarks= 0;
        $total= 0;
        if(!empty($subjects)) {
            foreach($subjects as $key => $val) {
                foreach($val as $keyOne => $valOne) {
                    $total ++;
                    if($key == 'computed_fields') {
                        $computed_ids_if_there[]= $valOne->cf_id;
                    } else {
                        $calculated_sub_totalMarks = $valOne->subject_total_mark;
                        $entities_ids[]= $valOne->entity_id;
                        // $assesments_ids[]= $valOne->assessment_id;
                    }
                }
                if($key != 'computed_fields') {
                    $assesments_ids[$key]= $entities_ids;
                    $entities_ids= [];
                }
            }
        }

        // echo '<pre>'; print_r($assesments_ids); die();
  
          // Subject marks as student obj
          $entities_marks_student_wise= [];
         if(!empty($assesments_ids)) {
              $entities_marks_student_wise= $this->__get_entities_gpa_student_wise($student_ids_arr, $assesments_ids, $gradePointsObj, $fields_operation);
              $entities_marks_student_wise_v2= [];
              foreach($entities_marks_student_wise as $key => $val) {
                  $entities_marks_student_wise_v2[$val->student_id]= $val;
              }
              
         }

        //  echo '<pre>'; print_r($entities_marks_student_wise_v2); die();
  
          // Computed marks as student obj
          $compted_marks_student_wise= [];
          if(!empty($computed_ids_if_there)) {
              $compted_marks_student_wise= $this->__get_computed_gpa_student_wise($student_ids_arr, $computed_ids_if_there, $gradePointsObj, $fields_operation);
              $compted_marks_student_wise_v2= [];
              foreach($compted_marks_student_wise as $key => $val) {
                  if($key == 0) {
                      $calculated_sub_totalMarks += $val->grand_total_marks;
                  }
                  $compted_marks_student_wise_v2[$val->student_id]= $val;
              }
          }
  
         
  
          $actual_total_marks= $calculated_sub_totalMarks;
          if($total_marks > 0) {
              $actual_total_marks= $total_marks;
          }

        $return= false;
        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                if(!empty($entities_marks_student_wise_v2) && !empty($compted_marks_student_wise_v2)) {
                    $entMarks= $entities_marks_student_wise_v2[$val->student_id]->grade_point < 0 ? 0 : $entities_marks_student_wise_v2[$val->student_id]->grade_point;
                    $cmpMarks= $compted_marks_student_wise_v2[$val->student_id]->grade_point < 0 ? 0 : $compted_marks_student_wise_v2[$val->student_id]->grade_point;
                    // $result= $entities_marks_student_wise_v2[$val->student_id]->exam_result == 'Pass' && $compted_marks_student_wise_v2[$val->student_id]->exam_result == 'Pass' ? 'Pass' : 'Failed';
                    $sum = $entMarks + $cmpMarks;
                    $average = $sum / 2;
                    if($fields_operation == 'GPA') {
                        $result= $average;
                    } else {
                        $result= $sum;
                    }

                } else if(!empty($entities_marks_student_wise_v2)) {
                    $result=$entities_marks_student_wise_v2[$val->student_id]->grade_point;
                } else if(!empty($compted_marks_student_wise_v2)) {
                    $result= $compted_marks_student_wise_v2[$val->student_id]->grade_point;
                }
                $val->result = round($result, $rounding);
                // echo '<pre>Pre: '; print_r(number_format($result, $rounding)); die();
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];
                }
                
                if(!empty($entities_marks_student_wise_v2) && !empty($compted_marks_student_wise_v2)) {
                    $entMarks= $entities_marks_student_wise_v2[$val]->grade_point < 0 ? 0 : $entities_marks_student_wise_v2[$val]->grade_point;
                    $cmpMarks= $compted_marks_student_wise_v2[$val]->grade_point < 0 ? 0 : $compted_marks_student_wise_v2[$val]->grade_point;
                    // $result= $entities_marks_student_wise_v2[$val]->exam_result == 'Pass' && $compted_marks_student_wise_v2[$val]->exam_result == 'Pass' ? 'Pass' : 'Failed';
                    $sum = $entities_marks_student_wise_v2[$val]->grade_point + $compted_marks_student_wise_v2[$val]->grade_point;
                    $average = $sum / 2;
                    if($fields_operation == 'GPA') {
                        $result= $average;
                    } else {
                        $result= $sum;
                    }

                } else if(!empty($entities_marks_student_wise_v2)) {
                    $result= $entities_marks_student_wise_v2[$val]->grade_point;
                } else if(!empty($compted_marks_student_wise_v2)) {
                    $result= $compted_marks_student_wise_v2[$val]->grade_point;
                }

                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => $actual_total_marks
                );
            }
            // echo '<pre>Fresh: '; print_r($fresh_generations_insert); die();
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }
        return true;
    }

    function __get_entities_gpa_student_wise($student_ids_arr, $assesments_ids, $gradingPoints, $fields_operation) {
        foreach($assesments_ids as $ass_id => $val) {
            $x[]= $this->db_readonly->select("GROUP_CONCAT( ifnull(if(aems.grade is not null and aems.grade != '', aems.grade, aems.marks), 0) ) as concated_obtained_marks, GROUP_CONCAT( if(ae.total_marks > 0, ae.total_marks, 0) ) as concated_total_marks, aems.student_id")
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                ->where_in('ae.entity_id', $val)
                ->where('ae.assessment_id', $ass_id)
                ->where_in('aems.student_id', $student_ids_arr)
                ->group_by('aems.student_id')
                ->get()->result();
        }

        // echo '<pre>'; print_r($x); die();

        // Merging student wise
        $mergedData = [];
        foreach ($x as $scores) {
            foreach ($scores as $score) {
                $studentId = $score->student_id;
                if (isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->concated_obtained_marks .= ',' . $score->concated_obtained_marks;
                    $mergedData[$studentId]->concated_total_marks .= ',' .$score->concated_total_marks;
                } else {
                    $mergedData[$studentId] = (object) [
                        'student_id' => $studentId,
                        'concated_obtained_marks' => $score->concated_obtained_marks,
                        'concated_total_marks' => $score->concated_total_marks
                    ];
                }
            }
        }
        $finalResult = array_values($mergedData);

        // echo '<pre>'; print_r($gradingPoints); die();

        if(!empty($finalResult)) {
            foreach($finalResult as $key => $val) {
                $concated_total_marks_arr= explode(',', $val->concated_total_marks);
                $concated_obtained_marks_arr= explode(',', $val->concated_obtained_marks);
                // $result= 'Pass';
                $grade_point_arr= [];
                // echo '<pre>'; print_r(count($concated_obtained_marks_arr)); die();
                foreach($concated_obtained_marks_arr as $k => $v) {
                    $num_value= $v * 1;
                    if($num_value < 0 && count($concated_obtained_marks_arr) > 1) {
                        $grade_point_arr[]= 0;
                        break; 
                    } else if($num_value < 0 && count($concated_obtained_marks_arr) == 1 ){
                        $grade_point_arr[]= $v;
                        break; 
                    }
                   if(is_numeric($v)) {
                    $percentage= ($v / $concated_total_marks_arr[$k]) * 100;
                        foreach($gradingPoints as $gp => $gv) {
                            if($percentage >= $gv->from && $percentage <= $gv->to) {
                                $grade_point_arr[]= $gv->grade_point ? $gv->grade_point : 0;
                                // echo '<pre>'; print_r($grade_point_arr);
                                break; 
                            }
                        }
                   } else {
                        foreach($gradingPoints as $gp => $gv) {
                            if($v == $gv->grade) {
                                $grade_point_arr[]= $gv->grade_point ? $gv->grade_point :  0;
                                break; 
                            }
                        }
                   }
                }


                // echo '<pre>'; print_r($grade_point_arr); 

                $sum = array_sum($grade_point_arr);
                $average = $sum / count($grade_point_arr);
                if($fields_operation == 'GPA') {
                    $val->grade_point= $average;
                } else {
                    $val->grade_point= $sum;
                }
            }
            // die();
        }

        return $finalResult;
    }

    function __get_computed_gpa_student_wise($student_ids_arr, $computed_ids_if_there,  $gradingPoints, $fields_operation) {
        $x= $this->db_readonly->select("GROUP_CONCAT( result ) as concated_obtained_marks, GROUP_CONCAT( if(grand_total_marks > 0, grand_total_marks, 0) ) as concated_total_marks, student_id")
            ->where_in('ass_computed_field_master_id', $computed_ids_if_there)
            ->where_in('student_id', $student_ids_arr)
            ->group_by('student_id')
            ->get('assessment_computed_field_details')->result();
        if(!empty($x)) {
            foreach($x as $key => $val) {
                $concated_total_marks_arr= explode(',', $val->concated_total_marks);
                $concated_obtained_marks_arr= explode(',', $val->concated_obtained_marks);
                // echo '<pre>'; print_r($concated_obtained_marks_arr); die();
                // $result= 'Pass';
                $grade_point_arr= [];
                foreach($concated_obtained_marks_arr as $k => $v) {
                    $num_value= $v * 1;
                    if($num_value < 0 && count($concated_obtained_marks_arr) > 1) {
                        $grade_point_arr[]= 0;
                        break; 
                    } else if($num_value < 0 && count($concated_obtained_marks_arr) == 1 ){
                        $grade_point_arr[]= $v;
                        break; 
                    }


                    $percentage= ($v / $concated_total_marks_arr[$k]) * 100;
                    foreach($gradingPoints as $gp => $gv) {
                        if($percentage >= $gv->from && $percentage <= $gv->to) {
                            $grade_point_arr[]= $gv->grade_point ? $gv->grade_point : 0;
                            
                            break; 
                        }
                    }
                }
                $sum = array_sum($grade_point_arr);
                $average = $sum / count($grade_point_arr);
                if($fields_operation == 'GPA') {
                    $val->grade_point= $average;
                } else {
                    $val->grade_point= $sum;
                }
            }
            // die();
        }

        return $x;
    }

    private function __update_reduced_percentage_of_a_student($student_ids_arr, $class_section_ids_arr, $detail_in_json, $insert_id, $defined_total_marks, $rounding, $reduced_percentage) {
        $student_id= $student_ids_arr[0];
        $class_section_id= $class_section_ids_arr[0];
        $detail_in_json_decoded=json_decode($detail_in_json, true);
        $subjects_redued_percentage= isset($reduced_percentage->subjects_redued_percentage) ? $reduced_percentage->subjects_redued_percentage : [];
        $cf_reduced_percentage= isset($reduced_percentage->cf_reduced_percentage) ? $reduced_percentage->cf_reduced_percentage : [];

        if(!empty($detail_in_json_decoded)) {
            $total_marks= 0;
            $resultant_marks= 0;
            foreach ($detail_in_json_decoded as $ass_id_OR_type => $subjects_OR_cmpFields) {
                if($ass_id_OR_type != 'computed_fields') { // if ass's subject
                    foreach($subjects_OR_cmpFields as $key => $val) { // max one iteration, don't worry
                        // Formula: ($scored / $total) * $cf_reduced_percentage;
                        $subject_total_mark= $val['subject_total_mark'];
                        $aems_marks= $val['aems_marks'][0];
                        $reducedKey= $ass_id_OR_type. "___" .$val['entity_id'];
                        $reducedPerc= $subjects_redued_percentage->$reducedKey;
                        $total_marks += $reducedPerc;
                        $resultant_marks += ($aems_marks / $subject_total_mark) * $reducedPerc;
                    }
                } else { // if computed field
                    foreach($subjects_OR_cmpFields as $key => $val) { // max one iteration, don't worry
                        $cf_id= $val['cf_id'];
                        $reducedPerc= $cf_reduced_percentage->$cf_id;
                        $cf_marks= $this->db->select('result, grand_total_marks')->where('ass_computed_field_master_id', $cf_id)->where('student_id', $student_id)->get('assessment_computed_field_details')->row();
                        if(!empty($cf_marks)) {
                            $grand_total_marks= $cf_marks->grand_total_marks > 0 ? $cf_marks->grand_total_marks : 1;
                            $result= $cf_marks->result;
                            $total_marks += $reducedPerc;
                            $resultant_marks += ($result / $grand_total_marks) * $reducedPerc;
                        }
                        
                    }
                }
            }
        }


// 
        $returnable= $this->db->select("acfd.result as marks, acfm.name")
            ->from('assessment_computed_field_details acfd')
            ->join('assessment_computed_field_master acfm', 'acfm.id= acfd.ass_computed_field_master_id')
            ->where('acfd.ass_computed_field_master_id', $insert_id)
            ->where('acfd.student_id', $student_id)
            ->get()->row();
        $returnable->derived_marks= round($resultant_marks, $rounding);
// 
        $if_exist= $this->db->select('id')->where('ass_computed_field_master_id', $insert_id)->where('student_id', $student_id)->get('assessment_computed_field_details')->row();
        if(!empty($if_exist)) {
            $this->db->where('id', $if_exist->id)->update('assessment_computed_field_details', array('result' => round($resultant_marks, $rounding)));
        } else {
            $array= array(
                'ass_computed_field_master_id' => $insert_id,
                'student_id' => $student_id,
                'class_section_id' => $class_section_id,
                'result' => round($resultant_marks, $rounding),
                'grand_total_marks' => $total_marks
            );
            $this->db->insert('assessment_computed_field_details', $array);
        }

        return $returnable;
    }

    function generate_average_high_rank_marks_version_2() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();
        $assessment_id = isset($input['assessment_id']) ? $input['assessment_id'] : 0;
        $section_id = isset($input['section_id']) ? $input['section_id'] : 0;
        $entity_id = isset($input['entity_id']) ? $input['entity_id'] : 0;
        $class_id = isset($input['class_id']) ? $input['class_id'] : 0;

        $students= $this->db->select("student_admission_id, if(class_section_id = $section_id, student_admission_id, 0) as sectionStudentId")->where('class_id', $class_id)->where_not_in('promotion_status', ['JOINED'])->get('student_year')->result();
        $classStudentIdsArr=[];
        $sectionStudentIdsArr=[];
        if(!empty($students)) {
            foreach($students as $key => $val) {
                $classStudentIdsArr[]= $val->student_admission_id;
                if($val->sectionStudentId != '0') {
                    $sectionStudentIdsArr[]= $val->sectionStudentId;
                }
            }
        }
        
        $this->db->trans_start();
            // Class averages and highest
            $returnable= $this->__updateClassAverageAndHighest($classStudentIdsArr, $class_id, $assessment_id, $entity_id);
            // Section averages and highest
            $returnable= $this->__updateSectionAverageAndHighest($sectionStudentIdsArr, $section_id, $assessment_id, $entity_id, $returnable);
            // Section averages and highest
            $this->__updateSectionAndClassRank($sectionStudentIdsArr, $section_id, $classStudentIdsArr, $class_id, $assessment_id, $entity_id);



        $this->db->trans_complete();
        if(! $this->db->trans_status()) {
            $this->db->trans_rollback();
            return [];
        }

        return $returnable;
    }

    private function __updateClassAverageAndHighest($classStudentIdsArr, $class_id, $assessment_id, $entity_id) {
        $avgAndHigh= $this->db->select("round(max(ifnull(aems.marks, 0)), 2) as classHigh, round(avg(ifnull(aems.marks, 0)), 2) as classAvg")
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                ->where('ae.entity_id', $entity_id)
                ->where('ae.assessment_id', $assessment_id)
                ->where_in('aems.student_id', $classStudentIdsArr)
                ->get()->row();
        if(!empty($avgAndHigh)) {
            $this->db->where('entity_id', $entity_id)->where('assessment_id', $assessment_id)->update('assessments_entities', ['class_average' => $avgAndHigh->classAvg, 'class_highest' => $avgAndHigh->classHigh]);
        }
        $retObj= new stdClass();
        $retObj->cAvg= $avgAndHigh->classAvg;
        $retObj->cHigh= $avgAndHigh->classHigh;

        return $retObj;
    }

    private function __updateSectionAverageAndHighest($sectionStudentIdsArr, $section_id, $assessment_id, $entity_id, $returnable) {
        $avgAndHigh= $this->db->select("round(max(ifnull(aems.marks, 0)), 2) as sectionHigh, round(avg(ifnull(aems.marks, 0)), 2) as sectionAvg, ae.section_average_highest, ae.id")
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
                ->where('ae.entity_id', $entity_id)
                ->where('ae.assessment_id', $assessment_id)
                ->where_in('aems.student_id', $sectionStudentIdsArr)
                ->get()->row();
        if($avgAndHigh) {
            $section_average_highest= $avgAndHigh->section_average_highest;
            $ae_id= $avgAndHigh->id;
            $section_average_highest= json_decode($section_average_highest);
            
            // echo '<pre>'; print_r($section_average_highest); die();

            $is_updated= false;
            if(!empty($section_average_highest)) { // update
                $agar_hai_to_kya= '';
                foreach($section_average_highest as $key => $val) {
                    if($val->id != $ae_id) {
                        unset($section_average_highest[$key]);
                        continue;
                    }
                    if($val->id == $ae_id && $val->section_id == $section_id) {
                        $is_updated= true;
                        $val->average=  $avgAndHigh->sectionAvg;
                        $val->highest=  $avgAndHigh->sectionHigh;
                    }
                }
            }
            if(!$is_updated || empty($section_average_highest)) { // insert
                $obj= new stdClass();

                $obj->id= $ae_id;
                $obj->section_id= $section_id;
                $obj->average= $avgAndHigh->sectionAvg;
                $obj->highest= $avgAndHigh->sectionHigh;

                $section_average_highest[]= $obj;
            }

            if(!empty($section_average_highest)) {
                $this->db->where('id', $ae_id)->update('assessments_entities', array('section_average_highest' => json_encode($section_average_highest)));
            }
        }

        $returnable->sAvg= $avgAndHigh->sectionAvg;
        $returnable->sHigh= $avgAndHigh->sectionHigh;

        return $returnable;
    }

    private function __updateSectionAndClassRank($sectionStudentIdsArr, $section_id, $classStudentIdsArr, $class_id, $assessment_id, $entity_id) {
        $studentRank = $this->db->select("
                aems.id as aems_id,
                aems.marks,
                sy.class_id,
                sy.class_section_id,
                sy.student_admission_id,
                rank() over(partition by sy.class_section_id order by aems.marks desc rows between unbounded preceding and unbounded following) as sectionRank,
                rank() over(order by aems.marks desc) as classRank
                ")
            ->from('assessments_entities ae')
            ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
            ->join('student_year sy', 'sy.student_admission_id = aems.student_id')
            ->where('sy.class_id', $class_id)
            ->where('ae.assessment_id', $assessment_id)
            ->where('ae.entity_id', $entity_id)
            ->where_in('aems.student_id', $classStudentIdsArr)
            ->order_by('aems.id')
            ->get()->result();

        if(!empty($studentRank))
        foreach($studentRank as $key => $val) {
            $ranks_students[]= array(
                'id' => $val->aems_id,
                'entity_section_rank' => $val->sectionRank,
                'entity_grade_rank' => $val->classRank,
            );
        }

        if(!empty($ranks_students)) {
            $this->db->update_batch('assessments_entities_marks_students', $ranks_students, 'id');
        }

        return true;
    }

/* --------------------------------------------------------------------------------------------------------------------------------- */
/* --------------------------------------------------------------------------------------------------------------------------------- */

    function __insert_best_of_all($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects) {
        
        $assessmentsIds= [];
        $entitiesIds= [];
        $computedFieldsIds= [];
        foreach($subjects as $key => $val) {
            if($key != 'computed_fields') {
                $assessmentsIds[]= $key;
                foreach($val as $keyOne => $valOne) {
                    $entitiesIds[]= $valOne->entity_id;
                }
            } else {
                foreach($val as $keyOne => $valOne) {
                    $computedFieldsIds[]= $valOne->cf_id;
                }
            }
        }

        if(!empty($assessmentsIds)) {
            $totalMarks= $this->db->select('total_marks')->where('assessment_id', $assessmentsIds[0])->where('entity_id', $entitiesIds[0])->get('assessments_entities')->row();
        } else {
            $totalMarks= $this->db->select('grand_total_marks total_marks')->where('ass_computed_field_master_id', $computedFieldsIds[0])->where('student_id', $student_ids_arr[0])->get('assessment_computed_field_master')->row();
        }


        if(!empty($totalMarks)) {
            $total_marks= $totalMarks->total_marks;
        } else {
            $total_marks= 0;
        }

        $entities_marks_student_wise= $this->__get_entities_best_of_all_marks_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds);
        $computed_field_student_wise= $this->__get_computed_field_best_of_all_marks_student_wise($student_ids_arr, $computedFieldsIds);

        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                // $result= (!empty($entities_marks_student_wise_v2) ? $entities_marks_student_wise_v2[$val->student_id]->sumMarks : 0) + (!empty($compted_marks_student_wise_v2) ? $compted_marks_student_wise_v2[$val->student_id]->sumMarks : 0);
                
                if(!empty($entities_marks_student_wise) && empty($computed_field_student_wise)) {
                    $result= $entities_marks_student_wise[$val->student_id]->maxMarks;
                } else if(empty($entities_marks_student_wise) && !empty($computed_field_student_wise)) {
                    $result= $computed_field_student_wise[$val->student_id]->maxMarks;
                } else if(!empty($entities_marks_student_wise) && !empty($computed_field_student_wise)) {
                    $result= $entities_marks_student_wise[$val->student_id]->maxMarks > $computed_field_student_wise[$val->student_id]->maxMarks ?  $entities_marks_student_wise[$val->student_id]->maxMarks :  $computed_field_student_wise[$val->student_id]->maxMarks;
                }

                $val->result = round($result, $rounding);
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];  // Output: Index: 2
                }
                
                // $result= (!empty($entities_marks_student_wise_v2) ? $entities_marks_student_wise_v2[$val]->sumMarks : 0) + (!empty($compted_marks_student_wise_v2) ? $compted_marks_student_wise_v2[$val]->sumMarks : 0);
                
                
                
                if(!empty($entities_marks_student_wise) && empty($computed_field_student_wise)) {
                    $result= $entities_marks_student_wise[$val]->maxMarks;
                } else if(empty($entities_marks_student_wise) && !empty($computed_field_student_wise)) {
                    $result= $computed_field_student_wise[$val]->maxMarks;
                } else if(!empty($entities_marks_student_wise) && !empty($computed_field_student_wise)) {
                    $result= $entities_marks_student_wise[$val]->maxMarks > $computed_field_student_wise[$val]->maxMarks ?  $entities_marks_student_wise[$val]->maxMarks :  $computed_field_student_wise[$val]->maxMarks;
                }
                
                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => $total_marks
                );
            }
            // echo '<pre>Fresh: '; print_r($fresh_generations_insert); die();
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }
        return true;
    }

    function __get_computed_field_best_of_all_marks_student_wise($student_ids_arr, $computedFieldsIds) {
        if(!empty($computedFieldsIds))
        $best_of_all = $this->db->select("
                MAX(acfd.result) OVER (PARTITION BY acfd.student_id) AS maxMarks,
                acfd.student_id
            ")
        ->where_in('acfd.student_id', $student_ids_arr) // Use where_in for array
        ->where_in('acfd.ass_computed_field_master_id', $computedFieldsIds)
        ->get('assessment_computed_field_details acfd')
        ->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->maxMarks= $val->maxMarks;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'maxMarks' => $val->maxMarks
                    ];
                }
            }
        }

        return $mergedData;

    }

    function __get_entities_best_of_all_marks_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds) {
        if(!empty($entitiesIds) && !empty($assessmentsIds))
        $best_of_all = $this->db->select("
                    MAX(if(aems.marks > 0, aems.marks, 0)) OVER (PARTITION BY aems.student_id) AS maxMarks,
                    aems.student_id
                ")
        ->from('assessments_entities ae')
        ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
        ->where_in('ae.assessment_id', $assessmentsIds)
        ->where_in('ae.entity_id', $entitiesIds)
        ->where_in('aems.student_id', $student_ids_arr)
        ->get()
        ->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->maxMarks= $val->maxMarks;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'maxMarks' => $val->maxMarks
                    ];
                }
            }
        }

        return $mergedData;
    }

/* --------------------------------------------------------------------------------------------------------------------------------- */
/* --------------------------------------------------------------------------------------------------------------------------------- */

    function __insert_second_best_of_all($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects) {
        
        $assessmentsIds= [];
        $entitiesIds= [];
        $computedFieldsIds= [];
        foreach($subjects as $key => $val) {
            if($key != 'computed_fields') {
                $assessmentsIds[]= $key;
                foreach($val as $keyOne => $valOne) {
                    $entitiesIds[]= $valOne->entity_id;
                }
            } else {
                foreach($val as $keyOne => $valOne) {
                    $computedFieldsIds[]= $valOne->cf_id;
                }
            }
        }

        if(!empty($assessmentsIds)) {
            $totalMarks= $this->db->select('total_marks')->where('assessment_id', $assessmentsIds[0])->where('entity_id', $entitiesIds[0])->get('assessments_entities')->row();
        } else {
            $totalMarks= $this->db->select('grand_total_marks total_marks')->where('ass_computed_field_master_id', $computedFieldsIds[0])->where('student_id', $student_ids_arr[0])->get('assessment_computed_field_master')->row();
        }


        if(!empty($totalMarks)) {
            $total_marks= $totalMarks->total_marks;
        } else {
            $total_marks= 0;
        }

        $entities_marks_student_wise= $this->__get_entities_second_best_of_all_marks_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds);
        $computed_field_student_wise= $this->__get_computed_field_second_best_of_all_marks_student_wise($student_ids_arr, $computedFieldsIds);

        $merge_marks_str= $this->__merge_marks_for_entity_and_cField($entities_marks_student_wise, $computed_field_student_wise, $student_ids_arr);

       

        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                    $result= $merge_marks_str[$val->student_id];
                $val->result = round($result, $rounding);
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];  // Output: Index: 2
                }
                    $result= $merge_marks_str[$val];
                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => $total_marks
                );
            }
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }

        return true;
    }

    function __merge_marks_for_entity_and_cField($entities_marks_student_wise, $computed_field_student_wise, $student_ids_arr) {



        $studentsSecondHighestMArks= [];
        foreach($student_ids_arr as $key => $studen_id) {
            $ent_arr= [];
            $cf_arr= [];
            if(!empty($entities_marks_student_wise) && isset($entities_marks_student_wise[$studen_id])) {
                $ent_arr= explode(',', $entities_marks_student_wise[$studen_id]->all_marks_str);
            }
            if(!empty($computed_field_student_wise) && isset($computed_field_student_wise[$studen_id])) {
                $cf_arr= explode(',', $computed_field_student_wise[$studen_id]->all_marks_str);
            }

            $marks_arr= array_merge($ent_arr, $cf_arr);
            $second_hghest= $this->__findSecondHighest($marks_arr);
            $studentsSecondHighestMArks[$studen_id]= $second_hghest;

            // echo '<pre>'; print_r($marks_arr);
        }

        // echo '<pre>'; print_r($studentsSecondHighestMArks); 
        // die();

        return $studentsSecondHighestMArks;
    }

    function __findSecondHighest($arr) {
        // $uniqueArr = array_unique($arr);
        rsort($arr);
        if (isset($arr[1])) {
            return $arr[1];
        } else if(isset($arr[0])) {
            return $arr[0]; // Handle cases where there is no second highest element so it will return the highest element
        } else {
            return 0;
        }
    }

    function __get_computed_field_second_best_of_all_marks_student_wise($student_ids_arr, $computedFieldsIds) {
        if(!empty($computedFieldsIds))
        $best_of_all = $this->db->select("
               group_concat(acfd.result) as all_marks_str,
                acfd.student_id
            ")
        ->where_in('acfd.student_id', $student_ids_arr) // Use where_in for array
        ->where_in('acfd.ass_computed_field_master_id', $computedFieldsIds)
        ->group_by('acfd.student_id')
        ->get('assessment_computed_field_details acfd')->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->all_marks_str= $val->all_marks_str;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'all_marks_str' => $val->all_marks_str
                    ];
                }
            }
        }

        return $mergedData;

    }

    function __get_entities_second_best_of_all_marks_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds) {
        if(!empty($entitiesIds) && !empty($assessmentsIds))
        $best_of_all = $this->db->select("
                    group_concat(if(aems.marks > 0, aems.marks, 0)) as all_marks_str,
                    aems.student_id
                ")
        ->from('assessments_entities ae')
        ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
        ->where_in('ae.assessment_id', $assessmentsIds)
        ->where_in('ae.entity_id', $entitiesIds)
        ->where_in('aems.student_id', $student_ids_arr)
        ->group_by('aems.student_id')
        ->get()->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->all_marks_str= $val->all_marks_str;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'all_marks_str' => $val->all_marks_str
                    ];
                }
            }
        }

        return $mergedData;
    }

/* --------------------------------------------------------------------------------------------------------------------------------- */
/* --------------------------------------------------------------------------------------------------------------------------------- */

    function __insert_best2sum_and_best2average($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects, $fields_operation) {
        $assessmentsIds= [];
        $entitiesIds= [];
        $computedFieldsIds= [];
        foreach($subjects as $key => $val) {
            if($key != 'computed_fields') {
                $assessmentsIds[]= $key;
                foreach($val as $keyOne => $valOne) {
                    $entitiesIds[]= $valOne->entity_id;
                }
            } else {
                foreach($val as $keyOne => $valOne) {
                    $computedFieldsIds[]= $valOne->cf_id;
                }
            }
        }

        if(!empty($assessmentsIds)) {
            $totalMarks= $this->db->select('total_marks')->where('assessment_id', $assessmentsIds[0])->where('entity_id', $entitiesIds[0])->get('assessments_entities')->row();
        } else {
            $totalMarks= $this->db->select('grand_total_marks total_marks')->where('ass_computed_field_master_id', $computedFieldsIds[0])->where('student_id', $student_ids_arr[0])->get('assessment_computed_field_master')->row();
        }


        if(!empty($totalMarks)) {
            if($fields_operation == 'Best 2 Sum')
                $total_marks= $totalMarks->total_marks * 2;
            else
                $total_marks= $totalMarks->total_marks;
        } else {
            $total_marks= 0;
        }

        $entities_marks_student_wise= $this->__get_entities_best2sum_and_best2average_marks_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds);
        $computed_field_student_wise= $this->__get_computed_field_best2sum_and_best2average_student_wise($student_ids_arr, $computedFieldsIds);

        $merge_marks_str= $this->__merge_best2sum_and_best2average_for_entity_and_cField($entities_marks_student_wise, $computed_field_student_wise, $student_ids_arr, $fields_operation);

       

        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                    $result= $merge_marks_str[$val->student_id];
                $val->result = round($result, $rounding);
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];  // Output: Index: 2
                }
                    $result= $merge_marks_str[$val];
                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Clas section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => $total_marks
                );
            }
            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }

        return true;
    }

    function __merge_best2sum_and_best2average_for_entity_and_cField($entities_marks_student_wise, $computed_field_student_wise, $student_ids_arr, $fields_operation) {
        $studentsSecondHighestMArks= [];
        foreach($student_ids_arr as $key => $studen_id) {
            $ent_arr= [];
            $cf_arr= [];
            if(!empty($entities_marks_student_wise) && isset($entities_marks_student_wise[$studen_id])) {
                $ent_arr= explode(',', $entities_marks_student_wise[$studen_id]->all_marks_str);
            }
            if(!empty($computed_field_student_wise) && isset($computed_field_student_wise[$studen_id])) {
                $cf_arr= explode(',', $computed_field_student_wise[$studen_id]->all_marks_str);
            }

            $marks_arr= array_merge($ent_arr, $cf_arr);
            $second_hghest= $this->__findBest2Sum_OR_Best2Average($marks_arr, $fields_operation);
            $studentsSecondHighestMArks[$studen_id]= $second_hghest;

            // echo '<pre>'; print_r($marks_arr);
        }

        // echo '<pre>'; print_r($studentsSecondHighestMArks); 
        // die();

        return $studentsSecondHighestMArks;
    }

    function __findBest2Sum_OR_Best2Average($marks_arr, $fields_operation) {
        // Remove any empty or invalid elements (if present)
        $marks_arr = array_filter($marks_arr, function($value) {
            return !is_null($value) && $value !== '';
        });

        // Sort the array in descending order
        rsort($marks_arr);

        if($fields_operation == 'Best 2 Sum') {
            if (isset($marks_arr[0]) && isset($marks_arr[1])) {
                return ($marks_arr[0] + $marks_arr[1]);
            } else if(isset($marks_arr[0])) {
                return $marks_arr[0]; // Handle cases where there is no second element so it will return the first element
            } else {
                return 0;
            }
        } else {
            if (isset($marks_arr[0]) && isset($marks_arr[1])) {
                return ($marks_arr[0] + $marks_arr[1]) / 2;
            } else if(isset($marks_arr[0])) {
                return $marks_arr[0]; // Handle cases where there is no second element so it will return the first element
            } else {
                return 0;
            }
        }
    }

    function __get_computed_field_best2sum_and_best2average_student_wise($student_ids_arr, $computedFieldsIds) {
        if(!empty($computedFieldsIds))
        $best_of_all = $this->db->select("
               group_concat(acfd.result) as all_marks_str,
                acfd.student_id
            ")
        ->where_in('acfd.student_id', $student_ids_arr) // Use where_in for array
        ->where_in('acfd.ass_computed_field_master_id', $computedFieldsIds)
        ->group_by('acfd.student_id')
        ->get('assessment_computed_field_details acfd')->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->all_marks_str= $val->all_marks_str;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'all_marks_str' => $val->all_marks_str
                    ];
                }
            }
        }

        return $mergedData;

    }

    function __get_entities_best2sum_and_best2average_marks_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds) {
        if(!empty($entitiesIds) && !empty($assessmentsIds))
        $best_of_all = $this->db->select("
                    group_concat(if(aems.marks > 0, aems.marks, 0)) as all_marks_str,
                    aems.student_id
                ")
        ->from('assessments_entities ae')
        ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
        ->where_in('ae.assessment_id', $assessmentsIds)
        ->where_in('ae.entity_id', $entitiesIds)
        ->where_in('aems.student_id', $student_ids_arr)
        ->group_by('aems.student_id')
        ->get()->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->all_marks_str= $val->all_marks_str;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'all_marks_str' => $val->all_marks_str
                    ];
                }
            }
        }

        return $mergedData;
    }

/* --------------------------------------------------------------------------------------------------------------------------------- */
/* --------------------------------------------------------------------------------------------------------------------------------- */

    function __insert_sum_percent_ignore_NA($student_ids_arr, $class_section_ids_arr, $insert_id, $total_marks, $rounding, $details_if_pregenerated, $subjects) {
        
        $assessmentsIds= [];
        $entitiesIds= [];
        $computedFieldsIds= [];
        foreach($subjects as $key => $val) {
            if($key != 'computed_fields') {
                $assessmentsIds[]= $key;
                foreach($val as $keyOne => $valOne) {
                    $entitiesIds[]= $valOne->entity_id;
                }
            } else {
                foreach($val as $keyOne => $valOne) {
                    $computedFieldsIds[]= $valOne->cf_id;
                }
            }
        }

        $entities_marks_student_wise= $this->__get_entities_sum_percent_ignore_NA_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds);
        $computed_field_student_wise= $this->__get_computed_field_sum_percent_ignore_NA_student_wise($student_ids_arr, $computedFieldsIds);

        $merge_marks_str= $this->__merge_sum_percent_ignore_NA_for_entity_and_cField($entities_marks_student_wise, $computed_field_student_wise, $student_ids_arr);

    //    echo '<pre>merge_marks_str: '; print_r($merge_marks_str); die();

        if(!empty($details_if_pregenerated)) {
            foreach($details_if_pregenerated as $key => $val) {
                    $result= $merge_marks_str[$val->student_id];
                $val->result = round($result, $rounding);
            }
            $this->db->update_batch('assessment_computed_field_details', $details_if_pregenerated, 'id');
            $return= true;
        } else { // Agar fresh generation ke liye aaye to
            $fresh_generations_insert= [];
            
            foreach($student_ids_arr as $key => $val) {
                // Finding clas section id of a particular student
                $index = array_search($val, $student_ids_arr);
                $class_section_id= 0;
                if ($index !== false) {
                    $class_section_id= $class_section_ids_arr[$index];  // Output: Index: 2
                }
                    $result= $merge_marks_str[$val];
                $fresh_generations_insert[]= array(
                    'ass_computed_field_master_id' => $insert_id,
                    'student_id' => $val,
                    'class_section_id' => $class_section_id, // Class section id find karenge, index value se jo student id array me hai, usi ke corresponding me section ids arr me hoga
                    'result' => round($result, $rounding),
                    'grand_total_marks' => 100
                );
            }

           

            $this->db->insert_batch('assessment_computed_field_details', $fresh_generations_insert);
            $return= true;
        }

        return true;
    }

    function __merge_sum_percent_ignore_NA_for_entity_and_cField($entities_marks_student_wise, $computed_field_student_wise, $student_ids_arr) {
        $studentsSecondHighestMArks= [];
        foreach($student_ids_arr as $key => $studen_id) {
            $ent_arr= [];
            $ent_actual_arr= [];
            $ent_tot_arr= [];
            $cf_arr= [];
            $cf_actual_arr= [];
            $cf_tot_arr= [];
            if(!empty($entities_marks_student_wise) && isset($entities_marks_student_wise[$studen_id])) {
                $ent_arr= explode(',', $entities_marks_student_wise[$studen_id]->all_marks_str);
                $ent_actual_arr= explode(',', $entities_marks_student_wise[$studen_id]->actual_marks);
                $ent_tot_arr= explode(',', $entities_marks_student_wise[$studen_id]->all_total_marks_str);
            }
            if(!empty($computed_field_student_wise) && isset($computed_field_student_wise[$studen_id])) {
                $cf_arr= explode(',', $computed_field_student_wise[$studen_id]->all_marks_str);
                $cf_actual_arr= explode(',', $computed_field_student_wise[$studen_id]->actual_marks);
                $cf_tot_arr= explode(',', $computed_field_student_wise[$studen_id]->all_total_marks_str);
            }

            $marks_arr= array_merge($ent_arr, $cf_arr);
            $marks_actual_arr= array_merge($ent_actual_arr, $cf_actual_arr);
            $total_marks_arr= array_merge($ent_tot_arr, $cf_tot_arr);
            $second_hghest= $this->__findSum_percent_ignore_NA($marks_arr, $total_marks_arr, $marks_actual_arr);
            $studentsSecondHighestMArks[$studen_id]= $second_hghest;

            // echo '<pre>A: '; print_r($marks_arr);
            // echo '<pre>'; print_r($marks_actual_arr);
            // echo '<pre>'; print_r($total_marks_arr);
        }

        // echo '<pre>'; print_r($studentsSecondHighestMArks); 
        // die();

        return $studentsSecondHighestMArks;
    }

    function __findSum_percent_ignore_NA($marks_arr, $total_marks_arr, $marks_actual_arr) {
        if(empty($total_marks_arr)) {
            return 0;
        }

        // echo '<pre>'; print_r($total_marks_arr); die();
        // // Remove any empty or invalid elements (if present)
        // $marks_arr = array_filter($marks_arr, function($value) {
        //     return !is_null($value) && $value !== '' && $value !== '-'; // Ignoring NA values
        // });
        // $total_marks_arr = array_filter($total_marks_arr, function($value) {
        //     return !is_null($value) && $value !== '' && $value !== '-'; // Ignoring NA values
        // });

        $marks_arr_ignored= [0];
        $total_marks_arr_ignored= [0];
        foreach($marks_actual_arr as $key => $actual_marks) {
            if($actual_marks != '-3' && $actual_marks != -3) {
                $marks_arr_ignored[]= $marks_arr[$key];
                $total_marks_arr_ignored[]= $total_marks_arr[$key];
            }
        }

        $sumMarks= array_sum($marks_arr_ignored);
        $sumTotalMarks= array_sum($total_marks_arr_ignored);

        // echo '<pre>'; print_r($sumMarks); 
        // echo '<pre>'; print_r($sumTotalMarks);
       
        if($sumTotalMarks > 0) {
            return ($sumMarks / $sumTotalMarks) * 100;
        } else {
            return 0;
        }
    }

    function __get_computed_field_sum_percent_ignore_NA_student_wise($student_ids_arr, $computedFieldsIds) {
        if(!empty($computedFieldsIds))
        $best_of_all = $this->db->select("
               group_concat(ifnull(acfd.result, 0)) as all_marks_str,
               group_concat(ifnull(acfd.grand_total_marks, 0)) as all_total_marks_str,
               group_concat(acfd.result) as actual_marks,
                acfd.student_id
            ")
        ->where_in('acfd.student_id', $student_ids_arr) // Use where_in for array
        ->where_in('acfd.ass_computed_field_master_id', $computedFieldsIds)
        ->group_by('acfd.student_id')
        ->get('assessment_computed_field_details acfd')->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->all_marks_str= $val->all_marks_str;
                    $mergedData[$studentId]->all_total_marks_str= $val->all_total_marks_str;
                    $mergedData[$studentId]->actual_marks= $val->actual_marks;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'all_marks_str' => $val->all_marks_str,
                        'all_total_marks_str' => $val->all_total_marks_str,
                        'actual_marks' => $val->actual_marks
                    ];
                }
            }
        }

        return $mergedData;

    }

    function __get_entities_sum_percent_ignore_NA_student_wise($student_ids_arr, $assessmentsIds, $entitiesIds) {
        if(!empty($entitiesIds) && !empty($assessmentsIds))
        $best_of_all = $this->db->select("
                    group_concat(if(aems.marks > 0, aems.marks, 0)) as all_marks_str,
                    group_concat(ifnull(ae.total_marks, 0)) as all_total_marks_str,
                    group_concat(aems.marks) as actual_marks,
                    aems.student_id
                ")
        ->from('assessments_entities ae')
        ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
        ->where_in('ae.assessment_id', $assessmentsIds)
        ->where_in('ae.entity_id', $entitiesIds)
        ->where_in('aems.student_id', $student_ids_arr)
        ->group_by('aems.student_id')
        ->get()->result();

        $mergedData= [];
        if(!empty($best_of_all)) {
            foreach($best_of_all as $key => $val) {
                $studentId= $val->student_id;
                if(isset($mergedData[$studentId])) {
                    $mergedData[$studentId]->all_marks_str= $val->all_marks_str;
                    $mergedData[$studentId]->all_total_marks_str= $val->all_total_marks_str;
                    $mergedData[$studentId]->actual_marks= $val->actual_marks;
                } else {
                    $mergedData[$studentId]= (object) [
                        'student_id' => $studentId,
                        'all_marks_str' => $val->all_marks_str,
                        'all_total_marks_str' => $val->all_total_marks_str,
                        'actual_marks' => $val->actual_marks
                    ];
                }
            }
        }

        return $mergedData;
    }

    function getAllComputefFieldsClassWise() {
        $class= $this->input->post('filtered_class');
        $this->db_readonly->select("c.class_name, acfm.id, acfm.name, acfm.mapping_string, if(acfd.id is not null and acfd.id != '', 'Generated', 'Not generated') as generatio_status")
            ->from('assessment_computed_field_master acfm')
            ->join('assessment_computed_field_details acfd', 'acfd.ass_computed_field_master_id = acfm.id', 'left')
            ->join('class c', 'c.id = acfm.class_id');
        if($class == 'all') {
            $this->db_readonly->where('acfm.acad_year_id', $this->yearId);
        } else {
            $this->db_readonly->where('c.id', $class);
            $this->db_readonly->where('acfm.class_id', $class);
        }
        return $this->db_readonly->group_by('acfm.id')->get()->result();
    }

    function get_computed_fields_by_ids($assIdsArr) {
        return $this->db_readonly->select("acfm.id as cf_id, acfm.name as cf_name, acfd.grand_total_marks")
                ->from('assessment_computed_field_master acfm')
                ->join('assessment_computed_field_details acfd', 'acfd.ass_computed_field_master_id = acfm.id')
                ->where_in('acfm.id', $assIdsArr)
                ->group_by('acfm.id')
                ->get()->result();
    }

}
?>