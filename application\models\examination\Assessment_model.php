<?php

defined('BASEPATH') OR exit('No direct script access allowed');
            
class Assessment_model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct() {
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }

    /*
	Group: Class Details
    */

    public function getClassess(){
    	$this->db_readonly->select('c.id, c.class_name')->from('class c')->where('c.is_placeholder',0)->where('c.acad_year_id', $this->yearId);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->result();
    }

    public function getClassName($class_id){
        return $this->db_readonly->select('class_name as className')->from('class')->where('id', $class_id)->where('acad_year_id', $this->yearId)->get()->row();
    }

    public function getClassTeacherBySectionId($sectionId){
        return $this->db_readonly->query("select CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as staffName from class_section cs left join staff_master s on cs.class_teacher_id=s.id where cs.id=$sectionId")->row()->staffName;
    }

    //get sections of a class
    public function getClassSections($class_id=0){
    	$this->db_readonly->select('cs.id as sectionId, cs.section_name as sectionName, c.id as classId, c.class_name as className');
    	$this->db_readonly->from('class_section cs');
    	$this->db_readonly->join('class c', 'c.id=cs.class_id');
    	if($class_id) {
    		$this->db_readonly->where('cs.class_id', $class_id);
    	}
        $this->db_readonly->where('c.acad_year_id', $this->yearId);
    	$this->db_readonly->where('cs.is_placeholder', 0);
        $this->db_readonly->order_by('cs.class_id,cs.id');
    	return $this->db_readonly->get()->result();
    }

    public function getRemarksGroup() {
        return $this->db_readonly->select("*")->get('assessment_subject_remarks_group')->result();
    }

    public function checkMarksStatus($assEid, $secId){
        $this->db_readonly->select('sum(case when aems.status=2 then 1 else 0 end) as lockedCount, sum(case when aems.status=1 then 1 else 0 end) as savedCount');
        $this->db_readonly->from('assessments_entities_marks_students aems');
        $this->db_readonly->join('student_admission s', 's.id=aems.student_id');
        $this->db_readonly->join('student_year sy', 's.id=sy.student_admission_id');
        $this->db_readonly->where('aems.assessments_entities_id', $assEid);
        $this->db_readonly->where('sy.class_section_id', $secId);
        return $this->db_readonly->get()->row();
    }

    public function getSectionsByClass($classId) {
        return $this->db_readonly->select('cs.id, cs.id as sectionId, cs.section_name, cs.section_name as sectionName')
        ->where('cs.class_id', $classId)
        ->where('cs.is_placeholder', 0)
        ->order_by('cs.id')
        ->get('class_section cs')->result();
    }

    public function getSectionName($sectionId) {
        $result = $this->db_readonly->select("concat(class_name, section_name) as csName")
            ->from('class_section')
            ->where('id', $sectionId)
            ->get()->row();

        return $result->csName;
    }

    public function getClassAndClassTeacher($sectionId) {
    	return $this->db_readonly->select("c.id, c.class_name, cs.section_name, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName")
			    	->from('class_section cs')
			    	->join('class c', 'c.id=cs.class_id')
			    	->join('staff_master sm', 'cs.class_teacher_id=sm.id', 'left')
			    	->where_in('cs.id', $sectionId)
			    	->where('c.acad_year_id', $this->yearId)
			    	->get()->row();
    }

    //sections included for assessment
    public function getSectionsOfAssessment($assId){
        // return $this->db->select('as.class_section_id as sectionId, cs.section_name as sectionName')->from('assessment_sections as')->join('class_section cs', 'cs.id=as.class_section_id')->where('as.assessment_id', $assId)->order_by('as.class_section_id')->get()->result();
        return $this->db_readonly->select('cs.id as sectionId, cs.section_name as sectionName, cs.class_id as classId, cs.class_name as className')->from('class_section cs')->where("cs.class_id in (select class_id from assessments where id=$assId)")->order_by('cs.id')->get()->result();
    }

    public function getClassDetails($assId){
        return $this->db_readonly->query("select id,class_name from class where id in (select class_id from assessments where id=$assId)")->row();
    }

    public function getSections($assessments, $flag){
        $staff = $this->staffcache->getStaffCache();
        $staffId = 0;
        if(!empty($staff))
            $staffId = $staff->staffId;
        $sql = 'select id,section_name from class_section where is_placeholder=0 and class_id in (select class_id from assessments where id in ('.$assessments.'))';
        if($flag) {
            $sql .= ' and id in (select cs.id as csId from class_section cs where cs.class_teacher_id='.$staffId.')';
        }
        return $this->db_readonly->query($sql)->result();
    }

    public function get_entities_for_assessment($assessment_id) {
        return $this->db_readonly->select('aem.name as entity_name, aem.id as entity_id, ae.section_average_highest as avg_high, ae.class_average as cls_avg, ae.class_highest as cls_high, ae.total_marks')
            ->from('assessment_entity_master aem')
            ->join('assessments_entities ae', 'ae.entity_id=aem.id')
            ->where('ae.assessment_id', $assessment_id)
            ->get()->result();
    }

   public function getSectionData($classId) {
        $sql = "select id, section_name from class_section cs where cs.class_id=$classId ";
        $result = $this->db_readonly->query($sql)->result();

        return $result;
   }

   public function getSubjectDatas($assessment_id) {
        
        $this->db_readonly->select('entity.name as subject_name, entity.id as entity_id');
        $this->db_readonly->from('assessment_entity_master entity');
        $this->db_readonly->join('assessments_entities ae', 'ae.entity_id=entity.id');
        $this->db_readonly->where('ae.assessment_id', $assessment_id);
        $result = $this->db_readonly->get()->result();
       return $result;
   }

    public function getSubject() {
         $sql = "select distinct(subject_name) from subject_master";
        $result = $this->db_readonly->query($sql)->result();

        return $result;
    }




    /*
	Group: Assessment Details
    */

    private function _isAssessmentNameDuplicate($class_id, $assId, $short_name, $long_name) {
    	//query to check if assessment short name|long name is duplicate inside same class and same year
        $sql = "SELECT count(id) AS count FROM assessments WHERE class_id=$class_id AND id!=$assId AND acad_year_id=$this->yearId AND (short_name='".$short_name."' OR long_name='".$long_name."')";

        $isDuplicate = $this->db->query($sql)->row()->count;
        //check for duplicate assessment names under same class and academic year
        if($isDuplicate > 0) {
        	return true;
        }
        return false;
    }

    public function addNewAssessment($class_id) {
        $input = $this->input->post();

        $short_name = trim($input['short_name']);

        if($this->_isAssessmentNameDuplicate($class_id, 0, $short_name, $input['long_name'])) {
            return -1;
        }


         $assData = array(
            'ass_type' => $input['ass_type'],
            'short_name' => $short_name,
            'long_name' => $input['long_name'],
            'description' => $input['description'],
            'acad_year_id' => $this->yearId,
            'marks_release_type' => isset($input['marks_release_type']) ? $input['marks_release_type'] : 'manual',
            'display_name' => isset($input['display_name']) ? $input['display_name'] : $short_name,
            'class_id' => $input['class_id']
        );
        if(isset($input['marks_release_type']) && $input['marks_release_type'] == 'auto') {
            $open= $input['open_date']. ' '. $input['time_open']. ':00';
            $close= $input['close_date']. ' '. $input['time_close']. ':00';
            $assData['marks_entry_open_date'] = date('Y-m-d H:i:s', strtotime($open));
            $assData['marks_entry_close_date'] = date('Y-m-d H:i:s', strtotime($close));
            // echo '<pre>A'; print_r(date('Y-m-d H:i:s', strtotime($open))); die();
        }


        if(isset($input['enable_subject_remarks'])) {
            $assData['enable_subject_remarks'] = 1;
            if(isset($input['remarks_group']))
                $assData['remarks_group_id'] = $input['remarks_group'];
        }

        if(isset($input['show_marks_to_parents'])) {
            $assData['show_marks_to_parents'] = 1;
        }

        $this->db->trans_start();
        $this->db->insert('assessments', $assData);
        $assId = $this->db->insert_id();

        $assHistory = array(
            'assessment_id' => $assId,
            'action' => 'Assessment Created',
            'action_by' => $this->authorization->getAvatarId(),
            'date' => date('Y-m-d')
        );
        $this->db->insert('assessments_history', $assHistory);
        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    public function addAssessment($class_id){
    	$input = $this->input->post();

    	if($this->_isAssessmentNameDuplicate($class_id, 0, $input['short_name'], $input['long_name'])) {
    		return -1;
    	}

    	$assData = array(
    		'ass_type' => $input['ass_type'],
    		'short_name' => $input['short_name'],
    		'long_name' => $input['long_name'],
    		'description' => $input['description'],
    		'acad_year_id' => $this->yearId
    	);

    	$this->db->trans_start();
	    	$this->db->insert('assessments', $assData);
	    	$assId = $this->db->insert_id();

	        $assHistory = array(
	            'assessment_id' => $assId,
	            'action' => 'Assessment Created',
	            'action_by' => $this->authorization->getAvatarId(),
	            'date' => date('Y-m-d')
	        );
	        $this->db->insert('assessments_history', $assHistory);

	    	/*$classSections = $input['class_section'];
	    	$sectionData = array();
	    	foreach ($classSections as $key => $value) {
	    		$cs = explode("_", $value);
	    		$sectionData[] = array(
	    			'assessment_id' => $assId,
	    			'class_section_id' => $cs[1],
	    			'class_id' => $cs[0],
	    		);
	    	}
	    	$this->db->insert_batch('assessment_sections', $sectionData);*/
    	return $this->db->trans_complete();
    }

    public function getAssessmentName($assId){
        return $this->db_readonly->select("*, date_format(marks_entry_open_date, '%d-%m-%Y %h:%i %p') as marks_entry_open_date_f1, date_format(marks_entry_close_date, '%d-%m-%Y %h:%i %p') as marks_entry_close_date_f1")->where('id', $assId)->where('acad_year_id', $this->yearId)->get('assessments')->row();
    }

    public function getAssessmentById($assId){
        return $this->db_readonly->select('*')->from('assessments')->where('id', $assId)->where('acad_year_id', $this->yearId)->get()->row();
    }
    
    public function getDerivedAssessments($classId) {
        if($classId == '') return array();
        $sql = "select * from assessments a where a.generation_type='Auto' and a.class_id=$classId and a.acad_year_id=$this->yearId";
        $result = $this->db_readonly->query($sql)->result();

        return $result;
    }

    public function getAssessments_v2($classId, $show_derived = 0) {
        if($classId == '') return array();
        $str = "a.generation_type='Manual' ";
        if ($show_derived == '1') {
            $str = "";
        }

        $this->db_readonly->select("a.id, a.ass_type, a.short_name, a.long_name, a.description, a.publish_status, a.release_marks, a.generation_type, a.formula, a.sorting_order, a.acad_year_id, a.class_id, a.enable_subject_remarks, a.remarks_group_id, a.show_marks_to_parents, a.marks_entry_open_date, a.marks_entry_close_date, a.marks_release_type, a.rounding, a.display_name, ifnull(ap.publish_status, -1) as portion_publish_status, ifnull( GROUP_CONCAT( CONCAT(aeg.entity_name) ) , '-') AS subAdded")
                ->from('assessments a')
                ->join('assessment_portions ap', 'ap.assessment_id = a.id', 'left')
                ->join('assessment_subject_group asg', 'asg.assessment_id = a.id', 'left')
                ->join('assessment_entities_group aeg', 'aeg.id=asg.ass_entity_gid', 'left')
                ->where('a.class_id', $classId)
                ->where('a.acad_year_id', $this->yearId);
        if($str && !empty($str) && strlen($str)) {
            $this->db_readonly->where($str);
        }
        $result= $this->db_readonly->group_by('a.id')->get()->result();


        foreach($result as $key => $val) {
            if($val->subAdded == '-') {
                $val->is_assessment_deletable= '1';
            } else {
                $val->is_assessment_deletable= '-1';
            }


            if($val->portion_publish_status == '-1') {
                $val->isPortionsPublished= 'Not Created';
            } else if($val->portion_publish_status == '0') {
                $val->isPortionsPublished= 'Not Published';
            } else {
                $val->isPortionsPublished= 'Published';
            }



            if($val->marks_release_type == 'auto') {
                    $open= date('Y-m-d', strtotime($val->marks_entry_open_date));
                    $close= date('Y-m-d', strtotime($val->marks_entry_close_date));
                    if($open != '1970-01-01' && $close != '1970-01-01' && date('Y-m-d H:i:s') >= date('Y-m-d H:i:s', strtotime($val->marks_entry_open_date)) && date('Y-m-d H:i:s') <= date('Y-m-d H:i:s', strtotime($val->marks_entry_close_date))) {
                        $val->add_marks_enability= '1';
                    } else {
                        $val->add_marks_enability= '-1';
                    }
            } else {
                if($val->release_marks == 1) {
                    $val->add_marks_enability= '1';
                } else {
                    $val->add_marks_enability= '-1';
                }
            }
        }

        return $result;
    }

    public function getAssessments($classId, $show_derived = 0) {
        // if($classId == '') return array();
        // $str = "a.generation_type='Manual' and ";
        // if ($show_derived == '1') {
        //     $str = "";
        // }
        // $sql = "select * from assessments a where $str a.class_id=$classId and a.acad_year_id=$this->yearId";
        // $result = $this->db->query($sql)->result();

        // foreach($result as $key => $val) {
        //     $is_sub_added= $this->db_readonly->where('assessment_id', $val->id)->get('assessments_entities')->row();
        //     if(!empty($is_sub_added)) {
        //         $val->is_assessment_deletable= '-1';
        //     } else {
        //         $val->is_assessment_deletable= '1';
        //     }
        //     if($val->marks_release_type == 'auto') {
        //             $open= date('Y-m-d', strtotime($val->marks_entry_open_date));
        //             $close= date('Y-m-d', strtotime($val->marks_entry_close_date));
        //             if($open != '1970-01-01' && $close != '1970-01-01' && date('Y-m-d H:i:s') >= date('Y-m-d H:i:s', strtotime($val->marks_entry_open_date)) && date('Y-m-d H:i:s') <= date('Y-m-d H:i:s', strtotime($val->marks_entry_close_date))) {
        //                 $val->add_marks_enability= '1';
        //             } else {
        //                 $val->add_marks_enability= '-1';
        //             }
        //     } else {
        //         if($val->release_marks == 1) {
        //             $val->add_marks_enability= '1';
        //         } else {
        //             $val->add_marks_enability= '-1';
        //         }
        //     }
        // }

        // // echo '<pre>'; print_r($result); die();

        // return $result;




        if ($classId == '') return array();

        $this->db_readonly->select("ae.assessment_id AS is_sub_added,a.id, a.ass_type, a.short_name, a.long_name, a.description, a.publish_status, a.release_marks, a.generation_type, a.formula, a.sorting_order, a.acad_year_id, a.class_id, a.enable_subject_remarks, a.remarks_group_id, a.show_marks_to_parents, a.marks_entry_open_date, a.marks_entry_close_date, a.marks_release_type, a.rounding, a.display_name
,ifnull(ae.assessment_id, 0) as assessment_id")
            ->from('assessments a')
            ->join('assessments_entities ae', 'a.id = ae.assessment_id', 'left')
            ->where('a.class_id', $classId)
            ->where('a.acad_year_id', $this->yearId);
        if ($show_derived != '1') {
            $this->db_readonly->where('a.generation_type', 'Manual');
        }
        $result= $this->db_readonly->group_by('a.id, ae.assessment_id')->get()->result();

        foreach ($result as $key => $val) {
            $val->is_assessment_deletable = (!empty($val->is_sub_added)) ? '-1' : '1';
            if ($val->marks_release_type == 'auto') {
                $open = date('Y-m-d', strtotime($val->marks_entry_open_date));
                $close = date('Y-m-d', strtotime($val->marks_entry_close_date));
                $currentDate = date('Y-m-d H:i:s');
                if ($open != '1970-01-01' && $close != '1970-01-01' && 
                    $currentDate >= date('Y-m-d H:i:s', strtotime($val->marks_entry_open_date)) && 
                    $currentDate <= date('Y-m-d H:i:s', strtotime($val->marks_entry_close_date))) {
                    $val->add_marks_enability = '1';
                } else {
                    $val->add_marks_enability = '-1';
                }
            } else {
                $val->add_marks_enability = ($val->release_marks == 1) ? '1' : '-1';
            }
        }

        return $result;

    }

     public function get_derived_entities_for_assessment($assessment_id) {
        return $this->db_readonly->select('aem.name as entity_name, aem.id as entity_id')
            ->from('assessment_entity_master aem')
            ->join('assessments_entities ae', 'ae.entity_id=aem.id')
            ->where('ae.assessment_id', $assessment_id)
            ->where('aem.ass_type', 'derived')
            ->get()->result();
    }


// derived and non-derived assessment
    public function getAss($classId) {
         if($classId == '') return array();       
        $sql = "select * from assessments a where a.class_id=$classId and a.acad_year_id=$this->yearId";
        $result = $this->db_readonly->query($sql)->result();

        return $result;
    }

    private function _checkDifference($string1, $string2, $field) {
    	if($string1 != $string2) {
    		return $field.' : '.$string1.' to '.$string2.', ';
    	}
    	return '';
    }

    public function editAssessment($assId, $classId){
    	$input = $this->input->post();

		$assessment = $this->db->select('*')->from('assessments')->where('id', $assId)->get()->row();

        if($this->_isAssessmentNameDuplicate($classId, $assId, $input['short_name'], $input['long_name'])) {
    		return -1;
    	}

        $assData = array(
            'ass_type' => $input['ass_type'],
            'short_name' => $input['short_name'],
            'long_name' => $input['long_name'],
            'display_name' => isset($input['display_name']) ? $input['display_name'] : '',
            'description' => $input['description']
        );

        if(isset($input['enable_subject_remarks'])) {
            $assData['enable_subject_remarks'] = 1;
            if(isset($input['remarks_group']))
                $assData['remarks_group_id'] = $input['remarks_group'];
        } else {
            $assData['enable_subject_remarks'] = 0;
            $assData['remarks_group_id'] = NULL;
        }

        $assData['show_marks_to_parents'] = 0;
        if(isset($input['show_marks_to_parents'])) {
            $assData['show_marks_to_parents'] = 1;
        }

        //Start DB Transaction
        $this->db->trans_start();

        //update assessment table
        $this->db->where('id', $assId);
        $this->db->update('assessments', $assData);

        //add changes into history table
        $action = 'Fields modified: ';
        $action .= $this->_checkDifference($assessment->ass_type, $input['ass_type'], 'ass_type');
        $action .= $this->_checkDifference($assessment->short_name, $input['short_name'], 'short_name');
        $action .= $this->_checkDifference($assessment->long_name, $input['long_name'], 'long_name');
        $action .= $this->_checkDifference($assessment->description, $input['description'], 'description');
        
        $assHistory = array(
            'assessment_id' => $assId,
            'action' => $action,
            'action_by' => $this->authorization->getAvatarId(),
            'date' => date('Y-m-d')
        );

        $this->db->insert('assessments_history', $assHistory);

        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    public function cloneComplete($currentId, $copyId){
        $subjects = $this->getSubjectSections($currentId);
        $addedd = $this->getPermissionsAdded($currentId);
        $staffs = array();
        if(!empty($addedd)) {
            foreach ($addedd as $key => $staff) {
                foreach ($staff as $k => $val) {
                    if(!in_array($val->staff_id, $staffs))
                        array_push($staffs, $val->staff_id);
                }
            }
        }
        $this->db->select('ae1.id as newId, ae2.id as oldId, aea.staff_id, aea.access_level, aea.class_section_id');
        $this->db->from('assessments_entities ae1');
        $this->db->join('assessments_entities ae2', 'ae2.entity_id=ae1.entity_id', 'right');
        $this->db->join('assessments_entities_access aea', 'aea.assessments_entities_id=ae2.id');
        $this->db->where('ae1.assessment_id', $currentId);
        $this->db->where('ae2.assessment_id', $copyId);
        if(!empty($staffs))
            $this->db->where_not_in('aea.staff_id',$staffs);
        $result = $this->db->get()->result();
        
        $subIds = array();
        $staff = array();
        $sections = array();
        foreach ($result as $key => $value) {
            if(!in_array($value->newId, $subIds))
                array_push($subIds, $value->newId);
            if(!in_array($value->staff_id, $staff))
                array_push($staff, $value->staff_id);
            if(!in_array($value->class_section_id, $sections))
                array_push($sections, $value->class_section_id);
        }
        foreach ($subjects as $key => $value) {
            if(in_array($value->assEid, $subIds)) {
                continue;
            }
            foreach ($staff as $s => $st) {
                foreach ($sections as $sc => $sec) {
                    $dt = new stdClass();
                    $dt->newId = $value->assEid;
                    $dt->staff_id = $st;
                    $dt->access_level = 'none';
                    $dt->class_section_id = $sec;
                    array_push($result, $dt);
                }
            }
        }
        // echo "<pre>"; print_r($result);die();
        $is_insertable= false;
        foreach ($result as $key => $value) {
            $is_insertable= true;
            $data[] = array(
                'assessments_entities_id' => $value->newId,
                'staff_id' => $value->staff_id,
                'access_level' => $value->access_level,
                'class_section_id' => $value->class_section_id,
            );
        }
        if($is_insertable) {
            return $this->db->insert_batch('assessments_entities_access', $data);
        }
        return false;
    }

    public function addPortions(){
        $input = $this->input->post();
        $publish = 0;
        if(isset($input['publish'])) {
            $publish = $input['publish'];
        }
        $assId = $input['assessment'];
        $ass = $this->db->query('select id assId from assessment_portions where assessment_id='.$assId)->row();
        $data = array(
            'assessment_id' => $assId,
            'portions' => $input['portions'],
            'publish_status' => $publish
        );
        // echo "<pre>"; print_r($data);die();
        if(empty($ass)) {
            return $this->db->insert('assessment_portions', $data);
        } else {
            $this->db->where('assessment_id', $assId);
            return $this->db->update('assessment_portions', $data);
        }
        
    }

    public function getAssessmentTT($assId){
        $this->db_readonly->select('asg.id as asgId, asg.assessment_id as assId, asg.ass_entity_gid assEGid, asg.portions as gPortions, asg.date as gDate, asg.start_time as gStart, asg.end_time as gEnd, asg.portions_at, ae.entity_id, ae.portions, ae.date, ae.start_time, ae.end_time,ae.total_marks');
        $this->db_readonly->from('assessment_subject_group asg');
        $this->db_readonly->join('assessments_entities ae', 'asg.id=ae.ass_subject_gid');
        $this->db_readonly->join('assessment_entity_master aem', 'aem.id=ae.entity_id');
        $this->db_readonly->where('asg.assessment_id', $assId);
        $this->db_readonly->where("aem.ass_type!='Attendance' and aem.ass_type!='derived'");
        $this->db_readonly->order_by('asg.date, ae.date');
        $result = $this->db_readonly->get()->result();
        return $result;
    }

    public function getAssessmentSections($assId, $class_id){
    	$this->db_readonly->select('as.id as assId, cs.section_id as sectionId, cs.class_id as classId, c.class_name as className, cs.section_name as sectionName');
    	$this->db_readonly->from('assessments as');
    	$this->db_readonly->join('class c', 'c.id=as.class_id');
    	$this->db_readonly->join('class_section cs', 'cs.class_id=c.class_id');
    	$this->db_readonly->where('as.class_id', $class_id);
        $this->db_readonly->where('as.id', $assId);
    	$assData = $this->db_readonly->get()->result();
    	$sectionIds = array();
		foreach ($assData as $key => $value) {
			$sectionIds[] = array('id' => $value->sectionId, 'name' => $value->sectionName);
    	}
	    return $sectionIds;
    }

    public function getAssEntitiesOfClass($classId, $subjectType){
        if ($subjectType == 'component') {
            $result = $this->db_readonly->select('aem.id, aem.name as subName, aem.sorting_order')
                ->from('assessment_entity_master aem')
                ->where('class_id', $classId)
                ->order_by('aem.sorting_order')
                ->get()->result();
        } else if($subjectType == 'group'){
            $result = $this->db_readonly->select('aeg.id, aeg.entity_name as subName, aeg.sorting_order')
                ->from('assessment_entities_group aeg')
                ->where('class_id', $classId)
                ->order_by('aeg.sorting_order')
                ->get()->result();
        } else if($subjectType == 'elective'){
            $result = $this->db_readonly->select('aeg.id, aeg.friendly_name as subName')
                ->from('assessment_elective_group aeg')
                ->where('class_id', $classId)
                ->get()->result();
        }

        return $result;
    }



    /*
	Group: Status Checks|Cahnges
    */

    //check overall marks entry status :: This function is not used anymore
//     public function checkOverallStatus($assId){
// //         $res = $this->db_readonly->select('id')->from('assessments_entities')->where('assessment_id', $assId)->get()->result();
// //         if(empty($res)) return -1;
// //         $i = 0;
// //         $j = 0;
// //         foreach ($res as $key => $value) {
// //             $result = $this->db_readonly->select('id, status')->from('assessments_entities_marks_students')->where('assessments_entities_id', $value->id)->where('status', 1)->group_by('assessments_entities_id')->get()->result();
// //             if(count($result) > 0) $i++;
// //             $res1 = $this->db_readonly->select('id, status')->from('assessments_entities_marks_students')->where('assessments_entities_id', $value->id)->where('status', 2)->group_by('assessments_entities_id')->get()->result();
// //             if(count($res1) > 0) $j++;
// //         }
// // echo '<pre>res: '; print_r(count($res));
// // echo '<pre>i: '; print_r($i);
// // echo '<pre>j: '; print_r($j);
// //  die();
// //         if($j == count($res))
// //             return 1;
// //         if($i)
// //             return 0;
// //         else return -1;




// // My code
//         $res = $this->db_readonly->select("ae.id, aems.assessments_entities_id, if(aems.status = 1, count(distinct(aems.assessments_entities_id)), 0) as status_1, if(aems.status = 2, count(distinct(aems.assessments_entities_id)), 0) as status_2")
//         ->from('assessments_entities ae')
//         ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id = ae.id')
//         ->where('ae.assessment_id', $assId)
//         ->group_by('aems.assessments_entities_id, ae.id')
//         ->get()->result();

//         if(empty($res)) return -1;
//         $i = 0;
//         $j = 0;
        
//         foreach($res as $key => $val) {
//             $i += $val->status_1;
//             $j += $val->status_2;
//         }

// // echo '<pre>res: '; print_r(count($res));
// // echo '<pre>i: '; print_r($i);
// // echo '<pre>j: '; print_r($j);
// //  die();

//         if($j == count($res))
//             return 1;
//         if($i && $i != 0)
//             return 0;
//         else return -1;

//     }

    public function changeStatus($assId, $status){
        $this->db->where('id', $assId);
        return $this->db->update('assessments', array('publish_status'=>$status));
    }

    public function changeParentMarksPublishStatus($assId, $status){
        $this->db->where('id', $assId);
        return $this->db->update('assessments', array('show_marks_to_parents'=>$status));
    }

    public function getMarksEntryStatus($staffId=0){
        $res = $this->db_readonly->select('assessments_entities_id as aeId')->from('assessments_entities_access')->where('staff_id', $staffId)->where('access_level', 'write')->group_by('assessments_entities_id')->get()->result();
        // echo "<pre>"; print_r($res);
        if(empty($res)) return 0;

        $ae_ids= [];
        foreach ($res as $key => $value) {
            $ae_ids[]= $value->aeId;
        }
        // status = 1 means saved, status = 2 means locked
        $result = $this->db_readonly->select('assessments_entities_id')->from('assessments_entities_marks_students')->where_in('assessments_entities_id', $ae_ids)->where('status', 1)->group_by('assessments_entities_id')->get()->result();
        if(!empty($result)) return 1;
        return 0;

        // foreach ($res as $key => $value) {
        //     $result = $this->db_readonly->select('assessments_entities_id')->from('assessments_entities_marks_students')->where('assessments_entities_id', $value->aeId)->where('status', 2)->group_by('assessments_entities_id')->get()->result();
        //     if(count($result) == 0) return 1;
        // }
        // return 0;
    }

    public function deleteAccess($assId, $staffId){
        $this->db->select('ae.id');
        $this->db->from('assessments_entities ae');
        $this->db->where('ae.assessment_id', $assId);
        $assEids = $this->db->get()->result();
        $ids = array();
        foreach ($assEids as $key => $val) {
            if(!in_array($val->id, $ids))
                array_push($ids, $val->id);
        }
        // echo "<pre>"; print_r($ids);die();
        $this->db->where('staff_id', $staffId);
        $this->db->where_in('assessments_entities_id', $ids);
        return $this->db->delete('assessments_entities_access');
    }

    /*
	Group: Assessment Subjects
    */

    //get subjects addedd to assessment
    public function getsubAdded($assId){
        $this->db_readonly->select('ag.entity_name');
        $this->db_readonly->from('assessment_subject_group asg');
        $this->db_readonly->where('asg.assessment_id', $assId);
        $this->db_readonly->join('assessment_entities_group ag', 'ag.id=asg.ass_entity_gid');
        $return = $this->db_readonly->get()->result();
        $subStr = array();
        foreach ($return as $key => $value) {
            array_push($subStr, $value->entity_name);
        }
        return implode(", ", $subStr);
    }

    //get the subject groups addedd
    public function getAddedGroups($assId, $classId){
        $this->db_readonly->select('asg.*, aeg.entity_name, aeg.id as assGid');
        $this->db_readonly->from('assessment_subject_group asg');
        $this->db_readonly->join('assessment_entities_group aeg', 'asg.ass_entity_gid=aeg.id');
        $this->db_readonly->where('asg.assessment_id', $assId);
        return $this->db_readonly->get()->result();
    }

    public function getComponents($subId){
        return $this->db_readonly->select('id, name, ass_type')->where('ass_entity_gid', $subId)->order_by('sorting_order')->get('assessment_entity_master')->result();
    }

    //get components
    public function getCompo($subGid){
        $this->db_readonly->select('aem.id, aem.name, ae.id as ass_ent_id, ae.total_marks');
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->join('assessment_entity_master aem', 'aem.id=ae.entity_id');
        $this->db_readonly->where('ae.ass_subject_gid', $subGid);
        $result = $this->db_readonly->get()->result();
        $arrNames = array();
        $arrIds = array();
        $tMarks = 0;
        $assEntIds = array();
        foreach ($result as $key => $value) {
            if(!in_array($value->id, $arrIds)){
                array_push($arrNames, $value->name);
                array_push($arrIds, $value->id);
                array_push($assEntIds, $value->ass_ent_id);
            }
            $tMarks += $value->total_marks;
        }
        return array('arrIds'=>$arrIds, 'arrNames'=>$arrNames, 'tMarks'=>$tMarks, 'assEntIds' => $assEntIds);
    }

    //entity group names
    public function getGroupNames($classId){
        $result = $this->db_readonly->select('id,entity_name')->where('class_id', $classId)->get('assessment_entities_group')->result();
        $arr = array();
        foreach ($result as $key => $value) {
            $arr[$value->id] = $value->entity_name;
        }
        return $arr;
    }

    //entity names
    public function getEntityNames($classId){
        $result = $this->db_readonly->select('id,name')->where('class_id', $classId)->get('assessment_entity_master')->result();
        $arr = array();
        foreach ($result as $key => $value) {
            $arr[$value->id] = $value->name;
        }
        return $arr;
    }

    public function getEntityName($entityId){
        $result = $this->db_readonly->select('name')->from('assessment_entity_master')->where('id',$entityId)->get()->row();
        return $result->name;
    }

    public function getClassSubjects($assId, $class_id){
        $this->db_readonly->select('*');
        $this->db_readonly->from('assessment_entity_master ae');
        $this->db_readonly->where('ae.class_id', $class_id);
        $this->db_readonly->where("ae.id NOT IN (select entity_id from assessments_entities where assessment_id=".$assId.")");
        $this->db_readonly->order_by('ae.sorting_order');
        return $this->db_readonly->get()->result();
    }

    public function getSubGroups($assId, $classId){
        $sql = 'select * from assessment_entities_group where id in (select distinct(ass_entity_gid) from assessment_entity_master where ass_entity_gid NOT IN (select ass_entity_gid from assessment_subject_group where assessment_id='.$assId.') and class_id='.$classId.') order by sorting_order';
        return $this->db_readonly->query($sql)->result();
    }

    public function getSectionSubject($assId){
        $this->db->select('aeg.id as group_id, aeg.entity_name as group_name, ae.id as assEid, ae.entity_id, ae.assessment_id as assId, aem.name, aem.class_id, ae.total_marks, aem.ass_type, aem.short_name');
        $this->db->from('assessments_entities ae');
        $this->db->join('assessment_entity_master aem', 'ae.entity_id=aem.id');
        $this->db->join('assessment_entities_group aeg', 'aeg.id=aem.ass_entity_gid');
        $this->db->where('ae.assessment_id', $assId);
        $this->db->order_by('aeg.sorting_order,aem.sorting_order,aem.id');
        $result = $this->db->get()->result();

        $subjects = array();
        foreach ($result as $key => $value) {
            if(!array_key_exists($value->group_id, $subjects)) {
                $subjects[$value->group_id] = array();
            }
            $subjects[$value->group_id][] = $value;
        }
        return $subjects;
    }

    public function getSubjectSections($assId){
        $this->db_readonly->select('ae.id as assEid, ae.entity_id, ae.assessment_id as assId, aem.name, aem.class_id, ae.total_marks, aem.ass_type, aem.short_name, aem.evaluation_type');
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->join('assessment_entity_master aem', 'ae.entity_id=aem.id');
        $this->db_readonly->join('assessment_entities_group aeg', 'aem.ass_entity_gid=aeg.id');
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->order_by('aeg.sorting_order,aem.sorting_order,aeg.id,aem.id');
        $result = $this->db_readonly->get()->result();
        return $result;
    }

    public function getGroupsByAssId($assId){
        $sql = "select id, entity_name, grading_system_id, is_elective from assessment_entities_group where id in 
                (select ass_entity_gid from assessment_entity_master where id in 
                (select entity_id from assessments_entities where assessment_id=$assId)) order by sorting_order,id";
        $result = $this->db_readonly->query($sql)->result();
        $data = array();
        foreach ($result as $key => $val) {
            $data[$val->id] = $val;
        }
        return $data;
    }

    public function getPortion($assId){
        return $this->db_readonly->query('select * from assessment_portions where assessment_id='.$assId)->row();
    }

    public function addAssessmentSubject($assId){
        $input = $this->input->post();
        // echo "<pre>"; print_r($input); die();
        $level = (isset($input['level']))?$input['level']:'off';
        $subGid = $input['subject'];
        $subPortions = ($input['subPortions'])?$input['subPortions']:NULL;
        $date = ($input['date'])?date('Y-m-d', strtotime($input['date'])):NULL;
        $start_time = ($input['start_time'])?date('H:i:s', strtotime($input['start_time'])):NULL;
        $end_time = ($input['end_time'])?date('H:i:s', strtotime($input['end_time'])):NULL;
        $state = 0;
        if(!empty($level) && $level == 'on')
            $state = 1;
        $subData = array(
            'assessment_id' => $assId,
            'ass_entity_gid' => $subGid,
            'portions' => $subPortions,
            'date' => $date,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'portions_at' => $state
        );
        // echo "<pre>"; print_r($subData);
        $this->db->trans_start();
        $this->db->insert('assessment_subject_group', $subData);
        $assSubGid = $this->db->insert_id();

        $portions = isset($input['portions']) ? $input['portions'] : null;
        $total_marks = $input['total_marks'];
        $dates = isset($input['dates']) ? $input['dates'] : null;
        $start_times = isset($input['start_times']) ? $input['start_times'] : null;
        $end_times = isset($input['end_times']) ? $input['end_times'] : null;
        $eId = array();
        foreach ($input['enity'] as $k => $entity) {
            list($entityId, $ass_type) = explode("_", $entity);
            $is_editable = 1;
            if($ass_type == 'Derived') {
                $is_editable = 0;
                if(!empty($input['is_editable']) && array_key_exists($entityId, $input['is_editable'])) {
                    $is_editable = 1;
                }
            }
            $data = array(
                'assessment_id' => $assId,
                'entity_id' => $entityId,
                'portions' => $portions[$k],
                'date' => ($dates[$k])?date('Y-m-d', strtotime($dates[$k])):NULL,
                'start_time' => ($start_times[$k])?$start_times[$k]:NULL,
                'end_time' => ($end_times[$k])?$end_times[$k]:NULL,
                'total_marks' => $total_marks[$k],
                'ass_subject_gid' => $assSubGid,
                'is_editable' => $is_editable
            );
            $this->db->insert('assessments_entities', $data);
            $eId[] = $this->db->insert_id();
        }
        

        //get Staffs
        $this->db->select('aes.staff_id as staffId');
        $this->db->from('assessments_entities_access aes');
        $this->db->join('assessments_entities ae', 'ae.id=aes.assessments_entities_id');
        $this->db->where('ae.assessment_id', $assId);
        $this->db->group_by('aes.staff_id');
        $staffs = $this->db->get()->result();
        //get sections
        $this->db->select('aes.class_section_id as csId');
        $this->db->from('assessments_entities_access aes');
        $this->db->join('assessments_entities ae', 'ae.id=aes.assessments_entities_id');
        $this->db->where('ae.assessment_id', $assId);
        $this->db->group_by('aes.class_section_id');
        $sections = $this->db->get()->result();

        foreach ($eId as $e => $id) {
            foreach ($staffs as $s => $staff) {
                foreach ($sections as $sec => $section) {
                    $accessData[] = array(
                        'assessments_entities_id' => $id,
                        'staff_id' => $staff->staffId,
                        'access_level' => 'none',
                        'class_section_id' => $section->csId
                    );
                }
            }
        }
        // echo "<pre>"; print_r($accessData); die();
        if(!empty($accessData)){
            $this->db->insert_batch('assessments_entities_access', $accessData);
        }
        return $this->db->trans_complete();
    }

    public function getAllEntities($assEGid, $classId){
        $this->db_readonly->select('aem.id,aem.name, aem.ass_type');
        $this->db_readonly->from('assessment_entity_master aem');
        $this->db_readonly->where('aem.class_id', $classId);
        $this->db_readonly->where('aem.ass_entity_gid', $assEGid);
        return $this->db_readonly->get()->result();
    }

    public function getAddedEntities($subGid){
        $this->db_readonly->select('aem.id,aem.name, aem.ass_type, ae.is_editable');
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->join('assessment_entity_master aem', 'aem.id=ae.entity_id');
        $this->db_readonly->where('ae.ass_subject_gid', $subGid);
        return $this->db_readonly->get()->result();
    }

    public function getSubjectData($id){
        return $this->db_readonly->select('asg.*, aeg.entity_name')->from('assessment_subject_group asg')->join('assessment_entities_group aeg', 'aeg.id=asg.ass_entity_gid')->where('asg.id', $id)->get()->row();
    }

    public function getEntitiesData($subGid){
        return $this->db_readonly->select('ae.*,aem.id as eId,aem.name, aem.ass_type, ae.is_editable')->from('assessments_entities ae')->join('assessment_entity_master aem', 'aem.id=ae.entity_id')->where('ae.ass_subject_gid', $subGid)->get()->result();
    }

    public function checkMarks($id){
        $result = $this->db_readonly->select('id')->from('assessments_entities_marks_students')->where('assessments_entities_id', $id)->group_by('assessments_entities_id')->get()->result();
        if(count($result) > 0) return 1;
        return 0;
    }

    public function updateAssessmentSubject($assId){
        $input = $this->input->post();
        // echo "<pre>"; print_r($input); die();
        $state = $input['level'];
        $subPortions = '';
        $date = NULL;
        $start_time = NULL;
        $end_time = NULL;
        if($state == 0){
            $subPortions = $input['subPortions'];
            $date = ($input['date'] != '')?date('Y-m-d', strtotime($input['date'])):NULL;
            $start_time = ($input['start_time'] != '')?date('H:i:s', strtotime($input['start_time'])):NULL;
            $end_time = ($input['end_time'] != '')?date('H:i:s', strtotime($input['end_time'])):NULL;
        }
        
        $subData = array(
            'portions' => $subPortions,
            'date' => $date,
            'start_time' => $start_time,
            'end_time' => $end_time
        );
        // echo "<pre>"; print_r($subData);die();
        $assSubGid = $input['subGid'];
        $entIds = $input['entIds'];
        $this->db->trans_start();
        $this->db->where('id', $assSubGid);
        $this->db->update('assessment_subject_group', $subData);

        foreach ($entIds as $i => $ent) {
            $total_marks = $input['utotal_marks'];
            $u_is_editable = $input['u_is_editable'];
            $is_editable_val = 1;
            if(!array_key_exists($ent, $u_is_editable)) {
                $is_editable_val = 0;
            }
            $data = array(
                'total_marks' => $total_marks[$i],
                'is_editable' => $is_editable_val
            );
            if($state){
                $portions = $input['uportions'];
                $dates = $input['udates'];
                $start_times = $input['ustart_times'];
                $end_times = $input['uend_times'];
                $date = ($dates[$i])?date('Y-m-d', strtotime($dates[$i])):NULL;
                $stime = ($start_times[$i])?$start_times[$i]:NULL;
                $etime = ($end_times[$i])?$end_times[$i]:NULL;
                $data = array_merge($data,['portions' => $portions[$i]]);
                $data = array_merge($data,['date' => $date]);
                $data = array_merge($data,['start_time' => $stime]);
                $data = array_merge($data,['end_time' => $etime]);
            }
            $this->db->where('id', $ent);
            $this->db->update('assessments_entities', $data);
        }

        $remIds = $input['remEids'];
        if(!empty($remIds)){
            //remove access control addedd
            $this->db->where_in('id', $remIds);
            $this->db->delete('assessments_entities_access');
            //remove the entities entry
            $this->db->where_in('id', $remIds);
            $this->db->delete('assessments_entities');
        }

        //add new entities
        if($input['newRow'] != 0){
            if($state){
                $portions = $input['portions'];
                $dates = $input['dates'];
                $start_times = $input['start_times'];
                $end_times = $input['end_times'];
            }
            $eId = array();
            foreach ($input['entity'] as $k => $entity) {
                list($entityId, $ass_type) = explode("_", $entity);
                $total_marks = $input['total_marks'];
                $portion = '';
                $date = NULL;
                $start_time = NULL;
                $end_time = NULL;
                if($state){
                    $portion = $portions[$k];
                    $date = ($dates[$k] != '')?date('Y-m-d', strtotime($dates[$k])):NULL;
                    $start_time = ($start_times[$k] != '')?$start_times[$k]:NULL;
                    $end_time = ($end_times[$k] != '')?$end_times[$k]:NULL;
                }
                $is_editable = 1;
                if($ass_type == 'Derived') {
                    $is_editable = 0;
                    if(!empty($input['is_editable']) && array_key_exists($entityId, $input['is_editable'])) {
                        $is_editable = 1;
                    }
                }
                $idata = array(
                    'assessment_id' => $assId,
                    'entity_id' => $entityId,
                    'portions' => $portion,
                    'date' => $date,
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                    'total_marks' => $total_marks[$k],
                    'ass_subject_gid' => $assSubGid,
                    'is_editable' => $is_editable
                );
                $this->db->insert('assessments_entities', $idata);
                $eId[] = $this->db->insert_id();
            }

            //adding default permissions to the entity
            //get Staffs
            $this->db->select('aes.staff_id as staffId');
            $this->db->from('assessments_entities_access aes');
            $this->db->join('assessments_entities ae', 'ae.id=aes.assessments_entities_id');
            $this->db->where('ae.assessment_id', $assId);
            $this->db->group_by('aes.staff_id');
            $staffs = $this->db->get()->result();
            //get sections
            $this->db->select('aes.class_section_id as csId');
            $this->db->from('assessments_entities_access aes');
            $this->db->join('assessments_entities ae', 'ae.id=aes.assessments_entities_id');
            $this->db->where('ae.assessment_id', $assId);
            $this->db->group_by('aes.class_section_id');
            $sections = $this->db->get()->result();

            foreach ($eId as $e => $id) {
                foreach ($staffs as $s => $staff) {
                    foreach ($sections as $sec => $section) {
                        $accessData[] = array(
                            'assessments_entities_id' => $id,
                            'staff_id' => $staff->staffId,
                            'access_level' => 'none',
                            'class_section_id' => $section->csId
                        );
                    }
                }
            }

            if(!empty($accessData)){
                $this->db->insert_batch('assessments_entities_access', $accessData);
            }
        }
        
        return $this->db->trans_complete();
    }

    public function deleteSubjects($eId, $compId){
        $this->db->trans_begin();

        $entities = explode(',', $compId);
        $this->db->where_in('entity_id', $entities);
        $this->db->where('ass_subject_gid', $eId);
        $this->db->delete('assessments_entities');

        $this->db->where('id', $eId);
        $this->db->delete('assessment_subject_group');
        
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }


    /* Access controls */
    public function getStaffWithNoPermission($assId,$staffType=-1){
        $this->db_readonly->select("aea.staff_id");
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->join('assessments_entities_access aea', 'aea.assessments_entities_id=ae.id');
        $return = $this->db_readonly->get()->result();
        $arr = array();
        foreach ($return as $key => $value) {
            if(!in_array($value->staff_id, $arr))
                array_push($arr, $value->staff_id);
        }
        $stfStr = implode(",", $arr);
        $this->db_readonly->select("sm.id, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName");
        $this->db_readonly->from('staff_master sm');
        if(!empty($arr))
            $this->db_readonly->where('id NOT IN ('.$stfStr.')');
        $this->db_readonly->where('sm.status', '2');

        if($staffType>=0){
            $this->db_readonly->where('sm.staff_type', $staffType);
        }
        
        $this->db_readonly->order_by('sm.first_name', 'ASC');
        return $this->db_readonly->get()->result();
    }

    public function getPermissionsAdded($assId){
        $this->db_readonly->select("aea.id as accessId, aea.staff_id, aea.access_level, ae.id as assEid, aea.class_section_id as sectionId, aem.name, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName, cs.section_name as sectionName");
        $this->db_readonly->from('assessments_entities ae');
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->join('assessment_entity_master aem', 'aem.id=ae.entity_id');
        $this->db_readonly->join('assessments_entities_access aea', 'aea.assessments_entities_id=ae.id');
        $this->db_readonly->join('class_section cs', 'aea.class_section_id=cs.id');
        $this->db_readonly->join('staff_master sm', 'sm.id=aea.staff_id');
        $this->db_readonly->order_by('sm.first_name', 'ASC');
        $return = $this->db_readonly->get()->result();
        $result = array();
        foreach ($return as $key => $value) {
            $result[$value->staffName][] = $value;
        }
        return $result;
        // echo "<pre>"; print_r($result);die();
    }

    public function submitAccessControl(){
        $input = $this->input->post();
        $staffId = $input['staffId'];
        $staff = $input['staff'];
        $permissions = $input['permissions'];
        $data = array();
        $this->db->trans_start();
        foreach ($staff as $sId) {
            foreach ($permissions as $key => $value) {
                list($perm, $ent, $sec) = explode("_", $value);
                $data[] = array(
                    'assessments_entities_id' => $ent,
                    'staff_id' => $sId,
                    'access_level' => $perm,
                    'class_section_id' => $sec
                );
            }
        }

        $this->db->insert_batch('assessments_entities_access',$data);
        return $this->db->trans_complete();
    }

    public function updateAccessControl(){
        $input = $this->input->post();
        $staffId = $input['staffId'];
        $permissions = $input['permissions'];
        $insert_batches = array();
        
        foreach ($permissions as $key => $value) {
            list($perm, $ent, $sec) = explode("_", $value);
            $query = $this->db->get_where('assessments_entities_access', array('assessments_entities_id' => $ent, 'class_section_id'=>$sec, 'staff_id'=>$staffId));
            $count = $query->num_rows();
            if($count == 0) {
                $insert_batches[] = array(
                    'assessments_entities_id' => $ent,
                    'staff_id' => $staffId,
                    'class_section_id' => $sec,
                    'access_level' => $perm
                );
                
            } else {
                if(!empty($query->row())) {
                    $update_batches[]= array(
                        'id' => $query->row()->id,
                        'access_level'=> $perm
                    );
                }
                
            }
        }
        $this->db->trans_start();
            if(!empty($insert_batches)) {
                $this->db->insert_batch('assessments_entities_access', $insert_batches);
            }
            if(!empty($update_batches)) {
                $this->db->update_batch('assessments_entities_access', $update_batches, 'id');
            }
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    //Staff
    public function getStaff(){
        return $this->db_readonly->select("s.id, CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) as staffName")->from('staff_master s')->where('s.status', '2')->where('s.staff_type', '0')->get()->result();
    }


    /* Derived assessment */
    public function getConsolAss($classId){
        if($classId == 0) {
            $sql = "select * from assessments where generation_type='Auto' and acad_year_id=$this->yearId";
        } else {
            $sql = "select * from assessments where generation_type='Auto' and class_id=$classId and acad_year_id=$this->yearId";
        }
        return $this->db_readonly->query($sql)->result();
    }

    public function isMarksGenerated($assId){
        $result = $this->db_readonly->query('select id from assessments_entities_marks_students where assessments_entities_id in (select id from assessments_entities where assessment_id='.$assId.') limit 2');
        return $result->num_rows();
    }

    public function deleteDerivedAssessment($assessment_id) {
        return $this->db->where('id', $assessment_id)->delete('assessments');
    }

    public function getClsAssessments($classId){
        $sections = $this->db_readonly->select('id as sectionId, section_name as sectionName')->where('class_id', $classId)->get('class_section')->result();
        $ass = $this->db_readonly->select('id, short_name')->from('assessments')->where('class_id', $classId)->get()->result();
        return array('assIds'=> $ass, 'sections'=>$sections);
    }

    public function getSecAssessments($sectionId){
        $this->db->select('a.id,a.short_name,');
        $this->db->from('assessments a');
        $this->db->where("a.class_id in (select class_id from class_section where id=$sectionId)");
        return $this->db->get()->result();
    }

    public function addConsolidatedAssessment(){
        $input = $this->input->post();
        // echo "<pre>";print_r($input);
        $data = array(
            'ass_type' => 'External',
            'short_name' => $input['con_name'],
            'long_name' => $input['con_name'],
            'generation_type' => 'Auto',
            'acad_year_id' => $this->yearId,
            'class_id' => $input['con_class']
        );

        $assIds = explode(",", $input['assIds'][0]);
        $assNames = explode(",", $input['assNames'][0]);
        $entities = explode(",", $input['entities'][0]);
        $ass = array();
        foreach ($assIds as $i => $val) {
            $ass[] = ['name' => $assNames[$i], 'id' => $assIds[$i]];
        }

        $mergAlgo = ['name' => $input['formula']];
        $entityArr = array();
        foreach ($entities as $key => $entity) {
            $tempArr = array();
            $tempArr['entity_id'] = $entity;
            foreach ($assIds as $i => $val) {
                $tempArr['value_set'][] = ['assId'=>$val, 'value'=>$input[$entity.'_'.$val]];
            }
            array_push($entityArr, $tempArr);
        }
        
        $data['formula'] = json_encode(array('assessments' => $ass, 'merg_algorithm' => $mergAlgo, 'merg_values' => $entityArr));
        $this->db->trans_start();
        $this->db->insert('assessments', $data);
        $assInsertId = $this->db->insert_id();
        $assHistory = array(
            'assessment_id' => $assInsertId,
            'action' => 'Consolidated assessment created',
            'action_by' => $this->authorization->getAvatarId(),
            'date' => date('Y-m-d')
        );
        $this->db->insert('assessments_history', $assHistory);
        $classId = $input['con_class'];
        return $this->db->trans_complete();
        // echo "<pre>";print_r($assSections); die();
    }

    private function _calculateTotal($formula, $totals, $split) {
        $final_total = 0;
        switch ($formula) {
            case 'percentage':
                foreach ($split as $ass_id => $percent) {
                    $final_total += $percent;
                }
                break;
            case 'aggregate_to_100':
                $final_total = 100;
                break;
            case 'average':
                $final_total = array_sum($totals)/count($totals);
                break;
            case 'best_2_sum':
                if(count($totals) >= 2)
                    $value = array_map('array_sum', array_chunk($totals, 2));
                    $final_total = $value[0];
                break;
            case 'best_of_all':
                    $t = array_values($totals);
                    $final_total = $t[0];
                break;
            case 'second_best_of_all':
                    $t = array_values($totals);
                    $final_total = $t[0];
                break;
            case 'best_2_average':
                    $t = array_values($totals);
                    $final_total = $t[0];
                break;
            case 'sum':
                $final_total = array_sum($totals);
                break;
            case 'sum_perc_ignore_na':
                $final_total = 100;
                break;
            case 'multiply':
                foreach ($totals as $ass_id => $total) {
                    $final_total += ($total*$split[$ass_id]);
                }
                break;
            default:
                break;
        }
        return $final_total;
    }

    public function updateConsolidatedAssessmentNew(){
        $input = $this->input->post();
        $json = $this->db->query("select formula from assessments where id=".$input['assId'])->row()->formula;
        $json = json_decode($json);
        $formula = ($json->merg_algorithm)->name;
        // unset($json->merg_values);

        $entities = $input['entities'];
        $assIds = $input['assIds'];
        $entity_ids = implode(',',$entities);
        $assessment_ids = implode(',',$assIds);
        $der_ass_id = $input['assId'];
        $sql = "select id,entity_id from assessments_entities where assessment_id=$der_ass_id";
        $der_entities = $this->db->query($sql)->result();
        $derived_entities = array();
        foreach ($der_entities as $key => $val) {
            $derived_entities[$val->entity_id] = array(
                'id' => $val->id,
                'total_marks' => 0
            );
        }
        $sql = "select entity_id,assessment_id, total_marks from assessments_entities where assessment_id in ($assessment_ids) and entity_id in ($entity_ids)";
        $entity_total = $this->db->query($sql)->result();
        $totals = array();
        foreach ($entity_total as $key => $ent) {
            $totals[$ent->entity_id][$ent->assessment_id] = $ent->total_marks;
        }
        $entityArr = array();
        $ass_entities = array();
        foreach ($entities as $e => $entId) {
            $tempArr = array();
            $tempArr['entity_id'] = $entId;
            $split = array();
            foreach ($assIds as $i => $val) {
                $value = $input[$entId.'_'.$val];
                $tempArr['value_set'][] = ['assId'=>$val, 'value'=>$value];
                $temp['assessment_id'] = $val;
                $split[$val] = $value;
            }
            $derived_entities[$entId]['total_marks'] = $this->_calculateTotal($formula, $totals[$entId], $split);
            // $temp['total_marks'] = $this->_calculateTotal($formula, $totals[$entId], $split);
            array_push($entityArr, $tempArr);
            array_push($ass_entities, $derived_entities[$entId]);
        }
        $json->merg_values = $entityArr;
        $data = array('formula' => json_encode($json));
        // echo "<pre>"; print_r($json); die();
        
        $this->db->trans_start();
        
        $this->db->where('id', $der_ass_id);
        $this->db->update('assessments', $data);
        
        $this->db->update_batch('assessments_entities', $ass_entities, 'id');
        
        $this->db->trans_complete();

        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 0;
    }

    public function saveConsolidatedAssessment(){
        $input = $this->input->post();
        // echo "<pre>";print_r($input); die();
        $data = array(
            'ass_type' => 'External',
            'short_name' => $input['con_name'],
            'long_name' => $input['con_name'],
            'generation_type' => 'Auto',
            'acad_year_id' => $this->yearId,
            'class_id' => $input['con_class'],
            'rounding' => $input['rounding_parameter'],
            'display_name' => $input['display_name']
        );

        $entity_ids = implode(",", $input['entities']);
        $assIds = implode(",", $input['assIds']);
        $sql = "select entity_id,assessment_id, total_marks from assessments_entities where assessment_id in ($assIds) and entity_id in ($entity_ids)";
        $entity_total = $this->db->query($sql)->result();
        $totals = array();
        foreach ($entity_total as $key => $ent) {
            $totals[$ent->entity_id][$ent->assessment_id] = $ent->total_marks;
        }

        $assIds = $input['assIds'];
        $assNames = $input['assNames'];
        $entities = $input['entities'];
        $groupids = $input['groupids'];

        $ass = array();
        foreach ($assIds as $i => $val) {
            $ass[] = ['name' => $assNames[$i], 'id' => $assIds[$i]];
        }

        $formula = $input['formula'];
        $mergAlgo = ['name' => $formula];
        $entityArr = array();
        $ass_entities = array();
        foreach ($entities as $key => $entity) {
            $tempArr = array();
            $tempArr['entity_id'] = $entity;
            $temp = array('entity_id' => $entity, 'ass_subject_gid' => $groupids[$key]);
            $split = array();
            foreach ($assIds as $i => $val) {
                $value = $input[$entity.'_'.$val];
                $tempArr['value_set'][] = ['assId'=>$val, 'value'=>$value];
                $split[$val] = $value;
                $temp['total_marks'] = $this->_calculateTotal($formula, $totals[$entity], $split);
            }
            array_push($entityArr, $tempArr);
            array_push($ass_entities, $temp);
        }
        
        $data['formula'] = json_encode(array('assessments' => $ass, 'merg_algorithm' => $mergAlgo, 'merg_values' => $entityArr));

        $this->db->trans_start();
        //Insert into assessments
        $this->db->insert('assessments', $data);
        $assInsertId = $this->db->insert_id();

        //Insert into assessment_subject_group
        //We could have used the input group_ids but I have already written this code!
        $ass_groups = $this->_get_ass_groups($entities);
        $ass_group_data = array();
        foreach ($ass_groups as $key => $ass_gid) {
            $ass_group_data['assessment_id'] = $assInsertId;
            $ass_group_data['ass_entity_gid'] = $ass_gid;

            $this->db->insert('assessment_subject_group', $ass_group_data);
            $ass_group_ids[$ass_gid] = $this->db->insert_id(); 
        }

        //Insert into assessments entities
        foreach ($ass_entities as $key => $ent) {
            $ass_entities[$key]['assessment_id'] = $assInsertId;
            $entity_group_id = $ent['ass_subject_gid'];
            $ass_entities[$key]['ass_subject_gid'] = $ass_group_ids[$entity_group_id];
        }
        $this->db->insert_batch('assessments_entities', $ass_entities);

        $assHistory = array(
            'assessment_id' => $assInsertId,
            'action' => 'Consolidated assessment created',
            'action_by' => $this->authorization->getAvatarId(),
            'date' => date('Y-m-d')
        );
        $this->db->insert('assessments_history', $assHistory);
        $classId = $input['con_class'];
        return $this->db->trans_complete();
    }

    private function _get_ass_groups($entities) {
        $group_id_objs = $this->db_readonly->select('distinct(ass_entity_gid) as group_id')
            ->from('assessment_entity_master')
            ->where_in("id", $entities)
            ->get()->result();

        $group_ids = [];
        foreach($group_id_objs as $gid_obj) {
            $group_ids[] = $gid_obj->group_id;
        }
        return $group_ids;
    }

    private function _addMarks($marksArr){
        $sum = 0;
        $mCount = count($marksArr);
        $absentCount = 0;
        $NACount = 0;
        foreach ($marksArr as $marks) {
            if($marks > 0) {
                $sum += $marks;
            }
            if($marks == -1.00)
                $absentCount++;
            if($marks == -3.00)
                $NACount++;
        }
        if($mCount == $absentCount)
            return -1.00;
        if($mCount == $NACount)
            return -3.00;
        return $sum;
    }

    private function _formDerivedMarks($formula, $marksArr){
        $digit = $this->settings->getSetting('examination_marks_rounding_digits');
        $sum = $this->_addMarks($marksArr);
        $marks = 0;
        switch ($formula) {
            case 'Average':
                if($sum == -1.00)
                    $marks = $sum;
                else 
                    $marks = $sum/count($marksArr);
                break;
            case 'Sum': 
                $marks = $sum;
                break;
            default:
                $marks = 0;
                break;
        }
        return round($marks, $digit);
    }

    public function getDerivedMarks($assId, $sectionId, $entityId, $assEid){
        $digit = $this->settings->getSetting('examination_marks_rounding_digits');
        $derEntData = $this->db_readonly->query('select * from assessment_entity_master where id='.$entityId)->row();
        $jsonData = json_decode($derEntData->derived_formula);
        $entityIds = array();
        // echo "<pre>"; print_r($jsonData); die();
        foreach ($jsonData->entityId as $key => $value) {
            $entityIds[] = $value->id;
        }
        if(empty($entityIds)) return array();
        $entIds = implode(",", $entityIds);

        $sql = "select s.id as stdId, aems.marks as marks, assent.total_marks from assessments_entities_marks_students aems left join student_admission s on aems.student_id=s.id left join assessments_entities assent on aems.assessments_entities_id=assent.id where assent.assessment_id=$assId and assent.entity_id in ($entIds) and student_id in (select sa.id from student_admission sa join student_year sy on sy.student_admission_id=sa.id where sy.class_section_id=$sectionId) and s.admission_status=2";
        $marksList = $this->db_readonly->query($sql)->result();
        // echo "<pre>"; print_r($marksList); die();
        if(empty($marksList)) {
            return array();
        }

        $stdData = array();
        $totalMarks = array();
        $studentId = 0;
        foreach ($marksList as $k => $marks) {
            if($studentId == 0) {
                $studentId = $marks->stdId;
            }
            if(!array_key_exists($marks->stdId, $stdData)) {
                $stdData[$marks->stdId] = array();
                $stdData[$marks->stdId]['total_marks'] = array();
                $stdData[$marks->stdId]['marks'] = array();
            }
            array_push($stdData[$marks->stdId]['total_marks'], $marks->total_marks);
            array_push($stdData[$marks->stdId]['marks'], round($marks->marks, $digit));
        }

        $totalMarks = $stdData[$studentId]['total_marks'];

        $formula = ($jsonData->formula)->name;
        // $derTotal = $this->_formDerivedMarks($formula, $totalMarks);
        $stdMarks = array();
        foreach ($stdData as $stdId => $value) {
            $stdMarks[] = array('id'=>$stdId, 'marks'=>$this->_formDerivedMarks($formula, $value['marks']));  
        }
        // if($derTotal) {
        //     $this->db->where('id', $assEid);
        //     $status = $this->db->update('assessments_entities', array('total_marks'=>$derTotal));
        //     if($status) {
        //         foreach ($stdData as $stdId => $value) {
        //             $stdMarks[] = array('id'=>$stdId, 'marks'=>$this->_formDerivedMarks($formula, $value['marks']));  
        //         }
        //     }
        // }
        return $stdMarks;
        // echo "<pre>"; print_r($stdMarks); die();
    }

    public function getAssDetails($assessment){
        // $assIds = implode(",", $assessment);
        return $this->db_readonly->where_in('id', $assessment)->order_by('sorting_order')->get('assessments')->result();
        // return $this->db_readonly->query('select * from assessments where id in ('.$assIds.') order by sorting_order')->result();
    }

    public function getSubjectsUnion($assessment){
        $assIds = implode(",", $assessment);
        $sql = "select distinct(id),ass_entity_gid as group_id, name from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in (".$assIds."))";
        return $this->db_readonly->query($sql)->result();
        // echo "<pre>"; print_r($return); die();
    }

    public function getDerivedAssessmentSubjects($assessment_id){
        $sql = "select id , name from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id=$assessment_id)";
        return $this->db_readonly->query($sql)->result();
    }
    
    public function get_student_derived_assessments_marks($section_id, $assessment_id, $entity_id) {
        $ass_ids = implode(",", $assessment_id);
        $assessments = $this->db_readonly->query("SELECT id, short_name from assessments where id in ($ass_ids)")->result();
        
        $ass_total= "SELECT a.id, a.short_name, ae.total_marks  
            from assessments_entities ae 
            left join assessments a on a.id= ae.assessment_id
            where ae.assessment_id in ($ass_ids) and a.id in ($ass_ids) and ae.entity_id=$entity_id
            ";
        $ass_total_marks= $this->db_readonly->query($ass_total)->result();

        $sql = "SELECT sa.id, concat(ifnull(sa.first_name,''), ifnull(sa.last_name,'')) as student_name, aems.marks  as marks, ae.assessment_id 
            from assessments_entities ae 
            join assessments_entities_marks_students aems on ae.id=aems.assessments_entities_id 
            right join student_admission sa on sa.id=aems.student_id 
            right join student_year sy on sy.student_admission_id=sa.id and class_section_id=$section_id
            where ae.assessment_id in ($ass_ids) and ae.entity_id=$entity_id 
            group by sa.id, ae.assessment_id 
            order by sa.first_name";

        $result = $this->db_readonly->query($sql)->result();
        
        $marks = [];
        foreach ($result as $k => $res) {
            if(!array_key_exists($res->id, $marks)) {
                $marks[$res->id] = array(
                    'id' => $res->id,
                    'name' => $res->student_name,
                );
            }
            $marks[$res->id][$res->assessment_id] = $res->marks;
        }
        
        $data = [];
        foreach ($marks as $key => $mark) {
            $data[] = $mark;
        }

        return ['assessments' => $ass_total_marks, 'students' => $data];
    }

    public function get_student_marks($section_id, $assessment_id, $entity_id, $class_id) {
        // $ass_ids = implode(",", $assessment_id);
        $sql = "SELECT sa.id, concat(ifnull(sa.first_name,''), ifnull(sa.last_name,'')) as student_name, aems.marks  as marks, ae.assessment_id 
            from assessments_entities ae 
            join assessments_entities_marks_students aems on ae.id=aems.assessments_entities_id 
            right join student_admission sa on sa.id=aems.student_id 
            right join student_year sy on sy.student_admission_id=sa.id and class_section_id=$section_id
            where ae.assessment_id in ($assessment_id) and ae.entity_id=$entity_id
            group by sa.id, ae.assessment_id 
            order by aems.marks desc";

        $result = $this->db_readonly->query($sql)->result();
        return $result;
    }

    public function getGenStatus($entityId, $assId, $sectionId){
        $sql = "select count(id) as count from assessments_entities_marks_students where assessments_entities_id in (select id from assessments_entities where assessment_id=$assId and entity_id=$entityId) and student_id in (select student_admission_id from student_year where class_section_id=$sectionId)";
        $res = $this->db_readonly->query($sql)->row();
        if($res->count == 0)
            return 0;
        return 1;
    }

    private function _getStudentList($section_id, $entity_id) {
        $sql = "select id, elective_group_id, is_elective from assessment_entities_group where id=(select ass_entity_gid from assessment_entity_master where id=$entity_id)";
        $group = $this->db->query($sql)->row();
        $showAlumni = $this->settings->getSetting('examination_show_alumni_students_in_marks_entry');

        if ($showAlumni) {
            $student_status_arr = [2, 4, 5]; // Include active + alumni
            $promotion_status_arr = ['JOINED']; // Only current students
        } else {
            $student_status_arr = [2]; // Only active
            $promotion_status_arr = ['JOINED', '4', '5']; // Exclude alumni
        }
        $student_status_in = implode(",", array_map('intval', $student_status_arr));
        $promotion_status_not_in = "'" . implode("','", $promotion_status_arr) . "'";

        $section_std = "select sa.id as student_id from student_admission sa 
                join student_year sy on sy.student_admission_id=sa.id 
                WHERE sy.class_section_id = $section_id AND sa.admission_status IN ($student_status_in) AND sy.promotion_status NOT IN ($promotion_status_not_in)";
        $std_sql = $section_std;
        if($group->is_elective == 1) {
            $ele_gid = $group->elective_group_id;
            $entity_gid = $group->id;
            $std_sql = "select student_id from assessment_students_elective where ass_elective_gid=$ele_gid and ass_entity_gid=$entity_gid and student_id in ($section_std)";
        }
        $students = $this->db->query($std_sql)->result();
        return $students;
        // echo "<pre>"; print_r($students); die();
        
    }

    public function calculateDerivedMarksMultiple($students, $entity_id, $derived_entities_id, $formula, $set_values, $rounding = 2) {
        if(empty($set_values)) {
            return -10; //marks not added for all assessmets
        }
        $assessment_ids = implode(',', array_keys($set_values));
        $std_ids = implode(",", $students);
        /*$sql = "select student_id, (case when marks<0 then 0 else marks end) as marks, assessment_id, total_marks from assessments_entities_marks_students aems 
                join assessments_entities ae on ae.id=aems.assessments_entities_id 
                where assessment_id in ($assessment_ids) and entity_id=$entity_id and aems.student_id in ($std_ids)";*/
        $sql = "select student_id, (case when marks<0 then 0 else marks end) as marks, marks as actual_marks, assessment_id, total_marks from assessments_entities_marks_students aems 
                join assessments_entities ae on ae.id=aems.assessments_entities_id 
                where assessment_id in ($assessment_ids) and entity_id=$entity_id and aems.student_id in ($std_ids)";
        $stdMarks = $this->db->query($sql)->result();

        // $digit = $this->settings->getSetting('examination_marks_rounding_digits');//rounding length
        $std_marks = array();
        foreach ($stdMarks as $key => $std) {
            if(!array_key_exists($std->student_id, $std_marks)) {
                $std_marks[$std->student_id] = array();
            }
            // $marks_rounded = number_format($std->marks, $rounding);
            $std_marks[$std->student_id][$std->assessment_id] = array('marks' => $std->marks, 'total_marks' => $std->total_marks, 'actual_marks' => $std->actual_marks);
        }

        // 

        $total_assessments = count($set_values);

        $data = array();
        $not_added = 0;
        switch ($formula) {
            case 'percentage':
                foreach ($std_marks as $std_id => $std) {
                    //Manjukiran 5-4-23: Commenting this code to ignore assessments that are not entered at all and proceed
                    // if(count($set_values) != count($std) || empty($std)) {
                    //     $not_added++; //marks not added for all assessmets
                    //     continue;
                    // }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $derived_marks = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    foreach ($set_values as $ass_id => $val) {
                        //Manjukiran 5-4-23: Commenting this code to ignore assessments that are not entered at all and proceed
                        if (!isset($std[$ass_id]['actual_marks'])) continue;
// #4 => when total marks is zero, we shoulld not add it to derived marks
                       if($std[$ass_id]['total_marks'] > 0) {
                        $derived_marks += ($std[$ass_id]['marks']/$std[$ass_id]['total_marks'])*$val;
                       }
                        if($std[$ass_id]['actual_marks'] == '-3') {
                            $not_applicables++;
                        }
                        if($std[$ass_id]['actual_marks'] == '-1') {
                            $absents++;
                        }
                    }
                    $temp['marks'] = $derived_marks;
                    if(count($std) == $not_applicables) {
                        $temp['marks'] = '-3';
                    } else if(count($std) == $absents) {
                        $temp['marks'] = '-1';
                    }
                    array_push($data, $temp);
                }
                break;
            case 'aggregate_to_100':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $total = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    foreach ($set_values as $ass_id => $val) {
                        if($std[$ass_id]['actual_marks'] == '-1') {
                            $absents++;
                        }
                        if($std[$ass_id]['actual_marks'] == '-3') {
                            $not_applicables++;
                            continue;
                        }
                        // if($std[$ass_id]['actual_marks'] == -3) continue;
                        $marks += $std[$ass_id]['marks'];
                        $total += $std[$ass_id]['total_marks'];
                    }
                    if($total != 0) {
                        $temp['marks'] = ($marks/$total)*100;
                    } else {
                        $temp['marks'] = 0;
                    }

                    if(count($std) == $not_applicables) {
                        $temp['marks'] = '-3';
                    } else if(count($std) == $absents) {
                        $temp['marks'] = '-1';
                    }
                    array_push($data, $temp);
                }
                break;
            case 'average':
                foreach ($std_marks as $std_id => $std) {
                    $ass_count = $total_assessments;
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $minus_three = 0;
                    foreach ($set_values as $ass_id => $val) {
                        //Manjukiran 4/4/2022: Removing this logic which doesn't consider unentered assessments; If assessments are not added, average will still consider that as a valid assessment.
                        // if($std[$ass_id]['actual_marks'] == -3) {
                        //     $ass_count--;
                        //     continue;
                        // }
                        if($std[$ass_id]['actual_marks'] == -3) {
                            $minus_three ++;
                            continue;
                        }
                        $marks += $std[$ass_id]['marks'];
                    }

                    //Manjukiran 4/4/2022: If all asses are -3, then the derived ass is also -3
                    if ($minus_three === $ass_count) {
                        $temp['marks'] = -3;
                    } else {
                        $temp['marks'] = $marks/$ass_count;
                    }
                    array_push($data, $temp);
                }
                break;
            case 'best_2_sum':
                foreach ($std_marks as $std_id => $std) {
                    // if(count($set_values) != count($std) || empty($std)) {
                    //     $not_added++; //marks not added for all assessmets
                    //     continue;
                    // }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    // uasort($std, function($a, $b) { return $b['marks'] - $a['marks']; });
                    uasort($std, function($a, $b) { 
                        $result = 0;
                        if ($a['marks'] > $b['marks']) {
                            $result = -1;
                        } else if ($a['marks'] < $b['marks']) {
                            $result = 1;
                        }
                        return $result;
                    });
                    $i=1;
                    foreach ($std as $ass_id => $stdm) {
                        if($i++ == 3) break;
                        $marks += $stdm['marks'];
                    }
                    $temp['marks'] = $marks;
                    array_push($data, $temp);
                }
                break;
            case 'best_2_average':
                foreach ($std_marks as $std_id => $std) {
                    // if(count($set_values) != count($std) || empty($std)) {
                    //     $not_added++; //marks not added for all assessmets
                    //     continue;
                    // }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    uasort($std, function($a, $b) { 
                        $result = 0;
                        if ($a['marks'] > $b['marks']) {
                            $result = -1;
                        } else if ($a['marks'] < $b['marks']) {
                            $result = 1;
                        }
                        return $result;
                    });
                    $i=1;
                    foreach ($std as $ass_id => $stdm) {
                        if($i++ == 3) break;
                        $marks += $stdm['marks'];
                    }
                    $temp['marks'] = $marks/2;
                    array_push($data, $temp);
                }
                break;
                case 'best_of_all':
                    foreach ($std_marks as $std_id => $std) {
                        if (count($set_values) != count($std) || empty($std)) {
                            $not_added++; // Marks not added for all assessments
                            continue;
                        }
                
                        $temp = array();
                        $temp['student_id'] = $std_id;
                        $temp['assessments_entities_id'] = $derived_entities_id;
                
                        $valid_marks = array();
                        $na_present = false;
                        $absent_present = false;
                
                    
                        foreach ($std as $ass_id => $stdm) {
                            if ($stdm['actual_marks'] == '-3') {
                                $na_present = true;
                            } elseif ($stdm['actual_marks'] == '-1') {
                                $absent_present = true;
                            } else {
                                $valid_marks[] = $stdm['marks'];
                            }
                        }
                
                        
                        rsort($valid_marks);
                
                        if (!empty($valid_marks)) {
                            $temp['marks'] = $valid_marks[0]; 
                        } elseif ($na_present) {
                            $temp['marks'] = '-3'; 
                        } elseif ($absent_present) {
                            $temp['marks'] = '-1';
                        } else {
                            $temp['marks'] = '-1'; 
                        }
                
                        array_push($data, $temp);
                    }
                    break;
            case 'second_best_of_all':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    // uasort($std, function($a, $b) { return $b['marks'] - $a['marks']; });
                    
                    $i=0;
                    $std_marks_arr=[];
                    foreach ($std as $ass_id => $stdm) {
                        $std_marks_arr[]=$stdm['actual_marks'];
                        if($i++ == 2) break;
                        $marks = $stdm['marks'];
                    }
                    rsort($std_marks_arr); //sorting in descending order
                    if($std_marks_arr[1]!='-3' || $std_marks_arr[1]!='-1'){
                        $temp['marks'] = $std_marks_arr[1];
                    }else if($std_marks_arr[0]!='-3' || $std_marks_arr[0]!='-1'){
                        $temp['marks'] = $std_marks_arr[0];
                    }else {
                        $temp['marks'] = '-1';
                    }
                    
                    array_push($data, $temp);
                }
                break;
            case 'sum':
                foreach ($std_marks as $std_id => $std) {
                    //Manjukiran 5-4-23: Commenting this code to ignore assessments that are not entered at all and proceed
                    // if(count($set_values) != count($std) || empty($std)) {
                    //     $not_added++; //marks not added for all assessmets
                    //     continue;
                    // }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    $not_applicables = 0;
                    $absents = 0;
                    foreach ($set_values as $ass_id => $val) {
                        //Manjukiran 5-4-23: Commenting this code to ignore assessments that are not entered at all and proceed
                        if (!isset($std[$ass_id]['actual_marks'])) continue;

                        $marks += $std[$ass_id]['marks'];
                        if($std[$ass_id]['actual_marks'] == '-3') {
                            $not_applicables++;
                        }
                        if($std[$ass_id]['actual_marks'] == '-1') {
                            $absents++;
                        }
                    }
                    $temp['marks'] = $marks;
                    if(count($std) == $not_applicables) {
                        $temp['marks'] = '-3';
                    } else if(count($std) == $absents) {
                        $temp['marks'] = '-1';
                    }
                    array_push($data, $temp);
                }
                break;
                case 'sum_perc_ignore_na':
                    foreach ($std_marks as $std_id => $std) {
                        //Manjukiran 5-4-23: Commenting this code to ignore assessments that are not entered at all and proceed
                        // if(count($set_values) != count($std) || empty($std)) {
                        //     $not_added++; //marks not added for all assessmets
                        //     continue;
                        // }
                        $temp = array();
                        $temp['student_id'] = $std_id;
                        $temp['assessments_entities_id'] = $derived_entities_id;
                        $marks = 0;
                        $total_marks = 0;
                        $not_applicables = 0;
                        $absents = 0;
                        foreach ($set_values as $ass_id => $val) {
                            //Manjukiran 5-4-23: Commenting this code to ignore assessments that are not entered at all and proceed
                            if (!isset($std[$ass_id]['actual_marks'])) continue;
                            //If not applicable, then, do not consider for marks calculation
                            if($std[$ass_id]['actual_marks'] != '-3') {
                                $marks += $std[$ass_id]['marks'];
                                $total_marks += $std[$ass_id]['total_marks'];
                            }
                            if ($std[$ass_id]['actual_marks'] == '-3') {
                                $not_applicables ++;
                            }
                            if ($std[$ass_id]['actual_marks'] == '-2') {
                                $absents ++;
                            }
                        }
                        if ($total_marks > 0) {
                            $temp['marks'] = $marks / $total_marks * 100;
                        } else {
                            $temp['marks'] = '-3';
                        }
                        if(count($std) == $not_applicables) {
                            $temp['marks'] = '-3';
                        } else if(count($std) == $absents) {
                            $temp['marks'] = '-1';
                        }
                        array_push($data, $temp);
                    }
                    break;
            case 'multiply':
                foreach ($std_marks as $std_id => $std) {
                    if(count($set_values) != count($std) || empty($std)) {
                        $not_added++; //marks not added for all assessmets
                        continue;
                    }
                    $temp = array();
                    $temp['student_id'] = $std_id;
                    $temp['assessments_entities_id'] = $derived_entities_id;
                    $marks = 0;
                    foreach ($set_values as $ass_id => $val) {
                        $marks += ($std[$ass_id]['marks'] * $val);
                    }
                    $temp['marks'] = $marks;
                    array_push($data, $temp);
                }
                break;
            default:
                break;
        }

        $existing = $this->db->query("select id,student_id,assessments_entities_id from assessments_entities_marks_students where assessments_entities_id=$derived_entities_id and student_id in ($std_ids)")->result_array();
        $update_data = array();
        foreach ($existing as $key => $value) {
            $update_data[$value['student_id']] = $value;
        }

        foreach ($data as $key => $val) {
            $std_id = $val['student_id'];
            if(array_key_exists($std_id, $update_data)) {
                $update_data[$std_id]['marks'] = $val['marks'];
                unset($data[$key]);
            }
        }
        if(!empty($data)) {
            foreach($data as $kii => $vaal) {
                $data[$kii]['marks']= round($data[$kii]['marks'], $rounding);
            }
            $this->db->insert_batch('assessments_entities_marks_students', $data);
        }
        if(!empty($update_data)) {
            foreach($update_data as $kii => $vaal) {
                $update_data[$kii]['marks']= round($update_data[$kii]['marks'], $rounding);
            }

            $this->db->update_batch('assessments_entities_marks_students', $update_data, 'id');
        }
        return $not_added;
        // echo 'update:<pre>'; print_r($update_data); 
        // echo 'insert:<pre>'; print_r($data); die();
    }

    /*public function calculateDerivedMarks($student_id, $entity_id, $derived_entities_id, $formula, $set_values) {
        if(empty($set_values)) {
            return -10; //marks not added for all assessmets
        }
        $assessment_ids = implode(',', array_keys($set_values));
        $sql = "select (case when marks<0 then 0 else marks end) as marks, assessment_id, total_marks from assessments_entities_marks_students aems 
                join assessments_entities ae on ae.id=aems.assessments_entities_id 
                where assessment_id in ($assessment_ids) and entity_id=$entity_id and aems.student_id=$student_id";
        $stdMarks = $this->db->query($sql)->result();
        if(count($set_values) != count($stdMarks) || empty($stdMarks)) {
            return -10; //marks not added for all assessmets
        }
        // echo '<pre>'; print_r($set_values); 
        // echo '<pre>'; print_r($stdMarks); 

        $std_marks = array();
        foreach ($stdMarks as $key => $val) {
            $std_marks[$val->assessment_id] = $val;
        }

        $derived_marks = 0;
        switch ($formula) {
            case 'percentage':
                foreach ($set_values as $ass_id => $val) {
                    $derived_marks += ($std_marks[$ass_id]->marks/$std_marks[$ass_id]->total_marks)*$val;
                }
                break;
            case 'aggregate_to_100':
                foreach ($set_values as $ass_id => $val) {
                    $marks += $std_marks[$ass_id]->marks;
                    $total += $std_marks[$ass_id]->total_marks;
                }
                $derived_marks = ($marks/$total)*100;
                break;
            case 'average':
                foreach ($set_values as $ass_id => $val) {
                    $marks += $std_marks[$ass_id]->marks;
                }
                $derived_marks = ($marks/count($set_values));
                break;
            case 'best_2':
                usort($std_marks, function($a, $b) { return $b->marks - $a->marks; });
                $derived_marks = ($std_marks[0] + $std_marks[1]);
                break;
            case 'sum':
                foreach ($set_values as $ass_id => $val) {
                    $derived_marks += $std_marks[$ass_id]->marks;
                }
                break;
            case 'multiply':
                foreach ($set_values as $ass_id => $val) {
                    $derived_marks += ($std_marks[$ass_id]->marks * $val);
                }
                break;
            default:
                break;
        }
        
        // print_r($derived_marks); die();

        $derived_data = $this->db->query("select id from assessments_entities_marks_students where assessments_entities_id=$derived_entities_id and student_id=$student_id")->row();
        if(empty($derived_data)) {
            //new entry
            $data = array(
                'student_id' => $student_id,
                'assessments_entities_id' => $derived_entities_id,
                'marks' => $derived_marks,
                'status' => 2
            );
            return $this->db->insert('assessments_entities_marks_students', $data);
        } else {
            return $this->db->where('id', $derived_data->id)->update('assessments_entities_marks_students', array('marks' => $derived_marks));
        }

    }*/

    public function generatederivedMarks() {
        $input = $this->input->post();
        $section_id = $input['section'];
        $derived_assessment_id = $input['assId'];
        $class_id = $input['classId'];
        $type = $input['type'];
        $entity_id = $input['entityId'];
        // echo '<pre>'; print_r($input);

        $entity_data = $this->db->query("select id,name,grading_system_id from assessment_entity_master where id=$entity_id")->row();
        $data['status'] = 1;
        $data['return'] = '<h4><strong>'.$entity_data->name.'</strong></h4>';
        /*if(!$entity_data->grading_system_id) {
            $data['status'] = 0;
            $data['return'] .= '<p>Grading system not added.</p>';//grading system not added
            return $data;
        }*/
       
        $consData = $this->db->query("select * from assessments where id=$derived_assessment_id")->row();
        $derived_entities_id = $this->db->query("select id from assessments_entities where assessment_id=$derived_assessment_id and entity_id=$entity_id");
        if($derived_entities_id->num_rows() == 1) {
            $derived_entities_id= $derived_entities_id->row()->id;
        } else {
            $derived_entities_id= 0;
        }
        $jsonData = json_decode($consData->formula);
        $formula = $jsonData->merg_algorithm->name;
        $assessments = $jsonData->assessments;
        $merge_set = $jsonData->merg_values;
        $entity_data_set = array();
        foreach ($merge_set as $key => $merge) {
            if($merge->entity_id == $entity_id) {
                $entity_data_set = $merge->value_set;
            }
        }
        if(empty($entity_data_set)) {
            $data['status'] = 0;
            $data['return'] .= '<p>Entity is not added.</p>'; //entity is not added to derived
            return $data;
        }

        $set_values = array();
        foreach ($entity_data_set as $key => $set) {
            $set_values[$set->assId] = $set->value;
        }

        $student_list = $this->_getStudentList($section_id, $entity_id);
        $total_std = count($student_list);
        if($total_std == 0) {
            $data['status'] = 0;
            $data['return'] .= '<p>No data.</p>'; //entity is not added to derived
            return $data;
        }
        $students = array();
        foreach ($student_list as $key => $std) {
            $students[] = $std->student_id;
        }

        $not_added = $this->calculateDerivedMarksMultiple($students, $entity_id, $derived_entities_id, $formula, $set_values, $consData->rounding);

        /*$not_added = 0;
        foreach ($student_list as $key => $student) {
            $status = (int)$this->calculateDerivedMarks($student->student_id, $entity_id, $derived_entities_id, $formula, $set_values);
            if($status == -10) {
                $not_added++;
            }
        }*/
        
        $added = $total_std - $not_added;
        if($not_added == 0) {
            $data['status'] = 1;
            $data['return'] .= '<p>Successfully generated/refreshed.</p>'; //entity is not added to derived
            return $data;
        } else {
            if($not_added == $total_std) {
                $data['status'] = 0;
                $data['return'] .= '<p>Failed to generate/refresh.</p>'; //entity is not added to derived
                return $data;
            } else {
                $data['status'] = 0;
                // $data['return'] .= '<p>Successfully generated/refreshed '.$added.'/'.$total_std.'</p>'; //entity is not added to derived
                $data['return'] .= '<p>Failed to generate/refresh '.$not_added.'/'.$total_std.'</p>';
                return $data;
            }
        }
        /*echo '<pre>'; print_r($entity_data_set); die();*/
    }

    public function generateConsolidation(){
        $section = $_POST['section'];
        $assId = $_POST['assId'];
        $classId = $_POST['classId'];
        $type = $_POST['type'];
        $entId = $_POST['entityId'];
        
        $consData = $this->db->select('*')->where('id', $assId)->get('assessments')->row();
        $jsonData = json_decode($consData->formula);
        // echo "<pre>";print_r($jsonData->merg_values); die();
        foreach ($jsonData->merg_values as $key => $merg_values) {
            $entityId = $merg_values->entity_id;
            if($entId != $entityId)
                continue;
            $stdMarks = array();
            $totalArr = array();
            $formula = array();
            $avgTotal = 0;
            $arrayStd = array();
            $missingIds = 0;
            $totals = 0;
            foreach ($merg_values->value_set as $index => $valueSet) {
                $aId = $valueSet->assId;
                $value = $valueSet->value;
                $ent = (object)$this->db->select('id,ass_subject_gid as gId, total_marks')->where('assessment_id', $aId)->where('entity_id', $entityId)->get('assessments_entities')->row();
                $eId = 0;
                if(isset($ent->total_marks)) {
                    $totalMarks = $ent->total_marks;
                    $eId = $ent->id;
                    $stdMarks[$eId] = $this->db->select('aems.student_id, aems.marks, aems.percentage')->where('assessments_entities_id', $eId)->join('student_admission s', 's.id=aems.student_id')->join('student_year sy', 'sy.student_admission_id=s.id')->where('sy.class_section_id',$section)->get('assessments_entities_marks_students aems')->result();
                    // echo "<pre>"; print_r($stdMarks);die();
                    if(($jsonData->merg_algorithm)->name == 'percentage')
                        $avgTotal += ($totalMarks/$totalMarks)*$value; //(UT1 total/UT1 total)*percentage_part
                    else if(($jsonData->merg_algorithm)->name == 'average') {
                        if($totals == 0)
                            $avgTotal += $totalMarks;
                        else {
                            $avgTotal += $totalMarks;
                            $avgTotal = ($avgTotal/2);
                        }
                        $totals++;
                    }else if(($jsonData->merg_algorithm)->name == 'aggregate_to_100') {
                        $avgTotal = 100;
                    } else if(($jsonData->merg_algorithm)->name == 'sum') {
                        $avgTotal += $totalMarks;
                    } else if(($jsonData->merg_algorithm)->name == 'multiply') {
                        $avgTotal += $totalMarks * $value;
                    }
                    $totalArr[$eId] = $totalMarks;
                    if(empty($arrayStd)) {
                        $arrayStd = $stdMarks[$eId];
                    }
                } else {
                    $missingIds++;
                }
                $formula[$eId] = $value;
            }
            foreach ($stdMarks as $key => $value) {
                if(empty($value))
                    return -10; // return -10 if marks not added yet
                else 
                    break;
            }
            while($missingIds > 0){
                $stdMarks[-$missingIds] = array();
                foreach ($arrayStd as $key => $value) {
                    $std = new stdClass();
                    $std->student_id = $value->student_id;
                    $std->marks = -3; // dont consider for consolidation
                    array_push($stdMarks[-$missingIds], $std);
                    // $stdMarks[-$missingIds]->marks = -3; 
                }
                $missingIds--;
            }
            $enityData = array(
                            'assessment_id' => $assId,
                            'ass_subject_gid' => 0,
                            'entity_id' => $entityId,
                            'total_marks' => $avgTotal
                        );
            $this->db->trans_start();
            if($type == 'generate') {
                $res = $this->db->select('id as assEntId')->from('assessments_entities')->where('entity_id', $entId)->where('assessment_id',$assId)->get()->row();
                if (empty($res)) {
                    $this->db->insert('assessments_entities', $enityData);
                    $insertId = $this->db->insert_id();    
                } else {
                    $insertId = $res->assEntId;
                }
            } else {
                $insertId = $this->db->select('id')->where('entity_id', $entId)->where('assessment_id',$assId)->get('assessments_entities')->row()->id;
            }
            
            $consoleStdData = $this->_calculateMarks($stdMarks, $totalArr, $formula, $jsonData->merg_algorithm, $insertId, $entityId);
            if(empty($consoleStdData['mArray'])) {
                return -1; //if grades not added
            }
            // echo "<pre>"; print_r($consoleStdData);die();
            if($type == 'generate') {
                if(($jsonData->merg_algorithm)->name == 'best_2') {
                    $this->db->where('assessment_id', $assId);
                    $this->db->where('entity_id', $entityId);
                    $this->db->update('assessments_entities', array('total_marks' => $consoleStdData['total']));
                }
                $this->db->insert_batch('assessments_entities_marks_students', $consoleStdData['mArray']);
                $insId = $this->db->insert_id();
                $count = count($consoleStdData['mArray']);
                foreach ($consoleStdData['mArray'] as $key => $value) {
                    $action = 'Marks added '.$value['marks'];
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $insId++,
                        'action' => $action,
                        'action_by' => $this->authorization->getAvatarId()
                    );
                }
            } else {
                foreach ($consoleStdData['mArray'] as $key => $value) {
                    $marksData = array(
                        'marks' => $value['marks'],
                        'percentage' => $value['percentage'],
                        'grade' => $value['grade']
                    );
                    $this->db->where('student_id', $value['student_id']);
                    $this->db->where('assessments_entities_id', $value['assessments_entities_id']);
                    $this->db->update('assessments_entities_marks_students', $marksData);
                    $insId = $this->db->select('id')->where('student_id', $value['student_id'])->where('assessments_entities_id', $value['assessments_entities_id'])->get('assessments_entities_marks_students')->row()->id;
                    $marksHistory[] = array(
                        'assessments_entities_marks_students_id' => $insId,
                        'action' => 'Marks modified to '.$value['marks'],
                        'action_by' => $this->authorization->getAvatarId()
                    );
                }
            }
            $this->db->insert_batch('assessments_entities_marks_students_history',$marksHistory);
            $this->db->trans_complete();
        }
        return $this->db->trans_status();
    }

    private function _calculateMarks($stdArr, $totalArr, $formulaArr, $algorith, $assEid, $entityId){
        $conArr = array();
        $index = 0;
        foreach ($stdArr as $eId => $stdData) {
            $index = $eId;
            foreach ($stdData as $e => $std) {
                if(!array_key_exists($std->student_id, $conArr)){
                    $conArr[$std->student_id] = array();
                    $conArr[$std->student_id]['sum'] = 0.00;
                }
                // $conArr[$std->student_id]['sum'] += $std->marks;
                $conArr[$std->student_id]['marks'][] = $std->marks;
            }
        }
        // echo "<pre>"; print_r($conArr); die();
        $totalForBest2 = 0;
        $digit = $this->settings->getSetting('examination_marks_rounding_digits');
        switch ($algorith->name) {
            case 'percentage':
                foreach ($conArr as $stdId => $data) {
                    $total = 0.00;
                    $i = 0;
                    $sumMarks = 0;
                    $sumTotal = 0;
                    $noOfAssessments = count($data['marks']);
                    foreach ($totalArr as $k => $val) {
                        $marks = $data['marks'][$i];
                        if($marks == -3) //consider for consolidation - assessment not doen for this subject
                            continue;
                        if($noOfAssessments > 1 && $marks == '-1.00') // if he is absent consider marks as 0
                            $marks = 0.00;
                        $marks = round($marks, $digit);
                        $sumMarks += round(($marks/$val)*$formulaArr[$k], $digit);// (UT1 marks/ UT1 total)*percentage_part
                        $sumTotal += ($val/$val)*$formulaArr[$k];// (UT1 total/UT1 total)*percentage_part
                        $i++;
                    }
                    //make marks -1 for single assessment, so it will be taken as absent
                    if($noOfAssessments == 1 && $marks < 0) {
                        $percent = 0;
                        $sumMarks = -1.00;
                    } else {
                        $percent = ($sumMarks/$sumTotal) * 100;
                        $percent = round($percent);
                        $sumMarks = round($sumMarks, $digit); 
                    }
                    $grade = $this->__calculateGradeNew($percent, $entityId, 'component'); //always marks generated at 'component' level
                    if($grade == -1) {
                        return array(); //if grades not added for the entity, return empty array
                    }
                    $finalArr[] = array(
                                            'assessments_entities_id' => $assEid,
                                            'student_id' => $stdId,
                                            'marks' => $sumMarks,
                                            'percentage' => $percent,
                                            'grade' => $grade
                                        );
                }
                // echo "<pre>"; print_r($finalArr); die();
                break;
            case 'average':
                foreach ($conArr as $stdId => $data) {
                    $tMarks = 0.00;
                    $tObtained = 0.00;
                    $num = 0;
                    $i=0;
                    foreach ($totalArr as $k => $val) {
                        $marks = $data['marks'][$i];
                        if($marks == -3) //don't consider for consolidation - assessment not doen for this subject
                            continue;
                        if($marks == '-1.00') // if he is absent consider marks as 0
                            $marks = 0.00;
                        $marks = round($marks, $digit);
                        $tObtained += $marks;//add obtained marks
                        $tMarks += $val;//add total marks
                        $num++;//number of tests
                        $i++;
                    }
                    $average = $tObtained/$num;
                    $tAvg = $tMarks/$num;
                    $percent = ($average/$tAvg) * 100;
                    $percent = round($percent);
                    $grade = $this->__calculateGradeNew($percent, $entityId, 'component'); //always marks generated at 'component' level
                    if($grade == -1) {
                        return array(); //if grades not added for the entity, return empty array
                    }
                    $finalArr[] = array(
                                            'assessments_entities_id' => $assEid,
                                            'student_id' => $stdId,
                                            'marks' => round($average, $digit),
                                            'percentage' => $percent,
                                            'grade' => $grade
                                        );
                }
                break;
            
            case 'aggregate_to_100':
                foreach ($conArr as $stdId => $data) {
                    $total = 0.00;
                    $i = 0;
                    $sumMarks = 0;
                    $sumTotal = 0;
                    foreach ($totalArr as $k => $val) {
                        $marks = $data['marks'][$i];
                        if($marks == -3) //consider for consolidation - assessment not done for this subject
                            continue;
                        if($marks == '-1.00') // if he is absent consider marks as 0
                            $marks = 0.00;
                        $marks = round($marks, $digit);
                        $sumMarks += $marks;// sum of all marks
                        $sumTotal += $val;// sum of totals
                        $i++;
                    }
                    $percent = ($sumMarks/$sumTotal) * 100;
                    $aggMarks = round($percent, 2);
                    $percent = round($percent);
                    $grade = $this->__calculateGradeNew($percent, $entityId, 'component'); //always marks generated at 'component' level
                    if($grade == -1) {
                        return array(); //if grades not added for the entity, return empty array
                    }
                    $finalArr[] = array(
                                            'assessments_entities_id' => $assEid,
                                            'student_id' => $stdId,
                                            'marks' => round($aggMarks, $digit),
                                            'percentage' => $percent,
                                            'grade' => $grade
                                        );
                }
                // echo "<pre>"; print_r($finalArr); die();
                break;
            case 'best_2':
                foreach ($conArr as $stdId => $data) {
                    $total = 0.00;
                    $i = 0;
                    $sumMarks = 0;
                    $sumTotal = 0;
                    $mArr = array();
                    $tArr = array();
                    foreach ($totalArr as $k => $val) {
                        $marks = $data['marks'][$i];
                        if($marks == -3) //consider for consolidation - assessment not doen for this subject
                            continue;
                        if($marks == '-1.00') // if he is absent consider marks as 0
                            $marks = 0.00;
                        $marks = round($marks, $digit);
                        array_push($mArr, $marks);
                        array_push($tArr, $val);
                        $i++;
                    }
                    $mBest = 0.00;
                    $tBest = 0.00;
                    $count = 2;
                    if(count($mArr) < 2)
                        $count = count($mArr);
                    for ($t=0; $t < $count; $t++) {
                        $max = array_keys($mArr, max($mArr));
                        $mBest += floatval($mArr[$max[0]]);
                        $tBest += floatval($tArr[$max[0]]);
                        unset($mArr[$max[0]]);
                    }
                    $average = $mBest/$count;
                    $tAvg = $tBest/$count;
                    $totalForBest2 = $tBest;
                    $percent = ($average/$tAvg) * 100;
                    $percent = round($percent);
                    $grade = $this->__calculateGradeNew($percent, $entityId, 'component'); //always marks generated at 'component' level
                    if($grade == -1) {
                        return array(); //if grades not added for the entity, return empty array
                    }
                    $finalArr[] = array(
                                            'assessments_entities_id' => $assEid,
                                            'student_id' => $stdId,
                                            'marks' => round($average, $digit),
                                            'percentage' => $percent,
                                            'grade' => $grade
                                        );
                }
                // echo "<pre>"; print_r($finalArr); die();
                break;
            case 'sum':
                foreach ($conArr as $stdId => $data) {
                    $total = 0.00;
                    $i = 0;
                    $sumMarks = 0;
                    $sumTotal = 0;
                    $noOfAssessments = count($data['marks']);
                    foreach ($totalArr as $k => $val) {
                        $marks = $data['marks'][$i];
                        if($marks == -3) //consider for consolidation - assessment not doen for this subject
                            continue;
                        if($noOfAssessments > 1 && $marks == '-1.00') // if he is absent consider marks as 0
                            $marks = 0.00;
                        $sumMarks += $marks;// (UT1 marks/ UT1 total)*percentage_part
                        $sumTotal += $val;// (UT1 total/UT1 total)*percentage_part
                        $i++;
                    }
                    //make marks -1 for single assessment, so it will be taken as absent
                    if($noOfAssessments == 1 && $marks < 0) {
                        $percent = 0;
                        $sumMarks = -1.00;
                    } else {
                        $percent = round(($sumMarks/$sumTotal) * 100);
                    }
                    $grade = $this->__calculateGradeNew($percent, $entityId, 'component'); //always marks generated at 'component' level
                    if($grade == -1) {
                        return array(); //if grades not added for the entity, return empty array
                    }
                    $finalArr[] = array(
                                            'assessments_entities_id' => $assEid,
                                            'student_id' => $stdId,
                                            'marks' => $sumMarks,
                                            'percentage' => $percent,
                                            'grade' => $grade
                                        );
                }
                // echo "<pre>"; print_r($finalArr); die();
                break;
            case 'multiply':
                foreach ($conArr as $stdId => $data) {
                    $total = 0.00;
                    $i = 0;
                    $sumMarks = 0;
                    $sumTotal = 0;
                    foreach ($totalArr as $k => $val) {
                        $marks = $data['marks'][$i];
                        if($marks == -3) //consider for consolidation - assessment not doen for this subject
                            continue;
                        if($marks == '-1.00') // if he is absent consider marks as 0
                            $marks = '-1.00';
                        $marks = $marks * $formulaArr[$k];
                        $sumMarks += $marks;
                        $sumTotal += $val * $formulaArr[$k];// (UT1 total/UT1 total)*percentage_part
                        $i++;
                    }
                    //make marks -1 for single assessment, so it will be taken as absent
                    if($sumMarks < -1) {
                        $percent = 0;
                    } else {
                        $percent = ($sumMarks/$sumTotal) * 100;
                        $percent = round($percent);
                    }
                    $grade = $this->__calculateGradeNew($percent, $entityId, 'component'); //always marks generated at 'component' level
                    if($grade == -1) {
                        return array(); //if grades not added for the entity, return empty array
                    }
                    $finalArr[] = array(
                                            'assessments_entities_id' => $assEid,
                                            'student_id' => $stdId,
                                            'marks' => $sumMarks,
                                            'percentage' => $percent,
                                            'grade' => $grade
                                        );
                }
                // echo "<pre>"; print_r($finalArr); die();
                break;
            default: break;
        }

        return array('mArray' => $finalArr, 'total' => $totalForBest2);
    }

    public function __calculateGradeNew_v2($pm, $entityId, $type, $nameRequired='short_name', $grading_system_over_subjects, $grading_system_id_over_subjects) {
        // echo '<pre>'; print_r($grading_system_over_subjects);
        // echo '<pre>'; print_r($grading_system_id_over_subjects); die();
        if($grading_system_over_subjects == 0) {
            if($type == 'component')
            $gData = $this->getGradesByEntity($entityId);
            else if($type == 'group')
                $gData = $this->getGradesByGroup($entityId);
            if(empty($gData)) {
                return -1;
            }
        } else {
            $gData = $this->db_readonly->where('id', $grading_system_id_over_subjects)->get('assessment_grading_system')->row();
        }
        $grades = json_decode($gData->grades);
        foreach ($grades as $key => $value) {
            // if(bccomp($pm, $value->from, 2) >= 0 && bccomp($pm, $value->to, 2) <= 0) {
            $val1 = $pm - $value->from;
            $val2 = $value->to - $pm;
            if ($val1 >= 0 && $val2 >= 0) {
            // if($pm >= $value->from && $pm <= $value->to) {
                if ($nameRequired == 'long_name') {
                    return $value->long_name;
                } else if($nameRequired == 'short_name'){
                    return $value->grade;
                } else if($nameRequired == 'grade_point'){
                    return (isset($value->grade_point))?$value->grade_point:'';
                }
            }
        }
    }

    public function __calculateGradeNew($pm, $entityId, $type, $nameRequired='short_name') {
       
        if($type == 'component')
            $gData = $this->getGradesByEntity($entityId);
        else if($type == 'group')
            $gData = $this->getGradesByGroup($entityId);
        if(empty($gData)) {
            return -1;
        }
        $grades = json_decode($gData->grades);
        foreach ($grades as $key => $value) {
            // if(bccomp($pm, $value->from, 2) >= 0 && bccomp($pm, $value->to, 2) <= 0) {
            $val1 = $pm - $value->from;
            $val2 = $value->to - $pm;
            if ($val1 >= 0 && $val2 >= 0) {
            // if($pm >= $value->from && $pm <= $value->to) {
                if ($nameRequired == 'long_name') {
                    return $value->long_name;
                } else if($nameRequired == 'short_name'){
                    return $value->grade;
                } else if($nameRequired == 'grade_point'){
                    return (isset($value->grade_point))?$value->grade_point:'';
                }
            }
        }
    }

    public function getGradesByEntity($entityId) {
        return $this->db_readonly->select('grades')->where("id in (select grading_system_id from assessment_entity_master where id='$entityId')")->get('assessment_grading_system')->row();
    }

    public function getGradesByGroup($groupId){
        return $this->db_readonly->select('grades')->where("id in (select grading_system_id from assessment_entities_group where id='$groupId')")->get('assessment_grading_system')->row();
    }

    public function updateConsolidatedAssessment(){
        $input = $this->input->post();
        $json = $this->db->query("select formula from assessments where id=".$input['assId'])->row()->formula;
        $json = json_decode($json);
        // unset($json->merg_values);

        $entities = $input['entities'];
        $assIds = $input['assIds'];
        $entityArr = array();
        foreach ($entities as $e => $entId) {
            $tempArr = array();
            $tempArr['entity_id'] = $entId;
            foreach ($assIds as $i => $val) {
                $tempArr['value_set'][] = ['assId'=>$val, 'value'=>$input[$entId.'_'.$val]];
            }
            array_push($entityArr, $tempArr);
        }
        $json->merg_values = $entityArr;
        $data = array('formula' => json_encode($json));
        // echo "<pre>"; print_r($json); die();
        $this->db->where('id', $input['assId']);
        return $this->db->update('assessments', $data);
    }

    public function getStdDetails($stdId){
        $this->db_readonly->select("if(sa.gender = 'F', 'Female', 'Male') as gender_female, if(sa.gender = 'F', 'Girl', 'Boy') as gender_girl, ifnull(sa.gender, 'Not Specified') as gender, sa.id as stdId,s.alpha_rollnum, s.roll_no as rollNo,DATE_FORMAT(`sa`.`dob`, '%d-%m-%Y') as dob, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName, sa.admission_no, c.class_name, cs.section_name, c.id as classId,s.class_section_id, s.picture_url, s.student_house, s.combination, sa.enrollment_number, sa.sts_number, sa.registration_no");
        $this->db_readonly->from('student_year s');
        $this->db_readonly->join('student_admission sa', 'sa.id=s.student_admission_id');
        $this->db_readonly->join('class_section cs', 'cs.id=s.class_section_id');
        $this->db_readonly->join('class c', 'c.id=cs.class_id');
        $this->db_readonly->where('sa.id', $stdId);
        $this->db_readonly->where('s.acad_year_id', $this->yearId);
        return $this->db_readonly->get()->row();
    }

    public function getStdHealthDetails($stdId){
        $this->db_readonly->select("student_id as stdId,blood_group,height,weight");
        $this->db_readonly->from('student_health s');
        $this->db_readonly->where('student_id', $stdId);
        $this->db_readonly->order_by('id', 'desc');
        return $this->db_readonly->get()->row();
    }

    public function getStdrunninghltdata($stdId){
        $this->db_readonly->select("student_id as stdId,height_in_cm,weight_in_kg");
        $this->db_readonly->from('student_height_weight');
        $this->db_readonly->where('student_id', $stdId);
        $this->db_readonly->order_by('id', 'desc');
        return $this->db_readonly->get()->row();
    }



    public function getStdSubReport($entId, $assId, $stdId){
        $this->db_readonly->select('ae.assessment_id as assId, aem.marks, aem.status, ae.total_marks, aem.status');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where_in('ae.assessment_id', $assId);
        $this->db_readonly->where('ae.entity_id', $entId);
        $result = $this->db_readonly->get()->result();
        // echo "<pre>";print_r($result);die();
        return $result;
    }

    public function getStd($sectionId){
        $this->db_readonly->select("s.id as stdId, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as stdName");
        $this->db_readonly->from('student_year s');
        $this->db_readonly->join('student_admission sa', 'sa.id=s.student_admission_id');
        $this->db_readonly->where('s.class_section_id', $sectionId);
        $this->db_readonly->order_by('sa.first_name');
        return $this->db_readonly->get()->result();
    }

    public function updatePdfLinkStatus($path) {
        $this->db->where('pdf_link', $path);
        $this->db->update('assessments_marks_card_history', array('status' => 1));
    }

    public function getAttendanceSessionID() {
        $classId = $_POST['classId'];
        $sectionId = $_POST['sectionId'];
        $stdIds = $_POST['stdIds'];
        $from = date('Y-m-d', strtotime($_POST['from']));
        $to = date('Y-m-d', strtotime($_POST['to']));

        $result = $this->db_readonly->select("attstu.id,attstu.attendance_session_id,attstu.attendance_master_id,attstu.attendance_master_group_id,attstu.status,DATE_FORMAT(`attsess`.`day`, '%d-%m-%Y') as day,attstu.student_admission_id,attstu.reference_type,attstu.reference_id, attstu.reference_status")
        ->from('attendance_student attstu') 
        ->join('attendance_session attsess','attsess.id = attstu.`attendance_session_id`')
        ->where('`attsess`.`day` >=', $from)
        ->where('`attsess`.`day` <=', $to)
        ->where('attsess.class_id`',$classId)
        ->where('attsess.class_section_id',$sectionId)
        ->where_in('attstu.student_admission_id',$stdIds)
        ->get()->result_array();

        return $result;
    }

    public function generateAverage($classId, $assId, $ass_entity_ids) {
        //$ass_entity_ids = array('114');
        //Manjukiran: Changed the algo to take care of marks that are entered only. This is for electives.

        // $student_count = $this->db->select('count(sy.id) stdCount')->from('student_admission sa')->join('student_year sy', 'sy.student_admission_id=sa.id')->where('sy.class_id', $classId)->where('sa.admission_status', 2)->where('sy.promotion_status!=', '4')->where('sy.promotion_status!=', '5')->get()->row()->stdCount;

        $digit = $this->settings->getSetting('examination_marks_rounding_digits');
        if (empty($digit)) {
            $digit = 2;
        }
        $marks = $this->db->select('sum(case when aems.marks>0 then aems.marks else 0 end) as average_marks, sum(case when aems.marks>0 then 1 else 0 end) as student_count, max(aems.marks) as highest, aems.assessments_entities_id')
                ->from('assessments_entities_marks_students aems')
                ->where_in('aems.assessments_entities_id', $ass_entity_ids)
                ->group_by('aems.assessments_entities_id')
                ->get()->result();
        $data = array();
        foreach ($marks as $key => $mark) {
            $data[] = array(
                'id' => $mark->assessments_entities_id,
                'class_average' => round($mark->average_marks/$mark->student_count, $digit),
                'class_highest' => $mark->highest
            );
        }
        if (!empty($data)) {
            $this->db->update_batch('assessments_entities', $data, 'id');
        } else {
            return 0;
        }
        
        $section_marks = $this->db->select('sum(case when aems.marks>0 then aems.marks else 0 end) as average_marks, sum(case when aems.marks>0 then 1 else 0 end) as student_count, max(aems.marks) as highest, aems.assessments_entities_id, sy.class_section_id')
        ->from('assessments_entities_marks_students aems')
        ->where_in('aems.assessments_entities_id', $ass_entity_ids)
        ->join('student_year sy','aems.student_id=sy.student_admission_id and sy.acad_year_id = '.$this->yearId.'')
        ->group_by('sy.class_section_id')
        ->group_by('aems.assessments_entities_id')
        ->get()->result();
        $jsFormat = array();
        foreach ($section_marks as $key => $section_mark) {
            $jsFormat[] = array(
                'id' => $section_mark->assessments_entities_id,
                'section_id'=> $section_mark->class_section_id,
                'average'=> round($section_mark->average_marks/$section_mark->student_count, $digit),
                'highest'=> $section_mark->highest
            );
        }
        $jsEncode = json_encode($jsFormat);
        $dataSection = [];
        foreach ($section_marks as $key => $section_mark) {
            $dataSection[] = array(
                'id' => $section_mark->assessments_entities_id,
                'section_average_highest' => $jsEncode
            );
        }

        if (!empty($dataSection)) {
            return $this->db->update_batch('assessments_entities', $dataSection, 'id');
        } else {
            return 0;
        }
    }

    public function getCloningSubjects($clone_from) {
        return $this->db_readonly->select("aeg.id as group_id, aeg.entity_name as group_name, aem.id as entity_id, aem.name as entity_name, ae.total_marks, ae.is_editable")
        ->from('assessments_entities ae')
        ->join('assessment_entity_master aem', 'aem.id=ae.entity_id')
        ->join('assessment_entities_group aeg', 'aeg.id=aem.ass_entity_gid')
        ->where('assessment_id', $clone_from)
        ->order_by('aeg.id, aem.id')
        ->get()->result();
    }

    public function saveClonedSubjects(){
        $input = $this->input->post();
        $group_subjects = array();
        foreach ($input['group_ids'] as $i => $gId) {
            if(!array_key_exists($gId, $group_subjects)) {
                $group_subjects[$gId] = array();
            }
            $group_subjects[$gId][] = $input['entity_ids'][$i];
        }
        $subData = array(
            'assessment_id' => $input['assessment_id'],
            'portions' => NULL,
            'date' => NULL,
            'start_time' => NULL,
            'end_time' => NULL,
            'portions_at' => 0
        );

        $this->db->trans_start();
        foreach ($group_subjects as $gId => $entities) {
            $subData['ass_entity_gid'] = $gId;
            $this->db->insert('assessment_subject_group', $subData);
            $assSubGid = $this->db->insert_id();
            foreach ($entities as $entityId) {
                $entityData[] = array(
                    'assessment_id' => $input['assessment_id'],
                    'entity_id' => $entityId,
                    'portions' => NULL,
                    'date' => NULL,
                    'start_time' => NULL,
                    'end_time' => NULL,
                    'total_marks' => $input['totals'][$entityId],
                    'is_editable' => $input['is_editable'][$entityId],
                    'ass_subject_gid' => $assSubGid
                );
            }
        }

        // echo "<pre>"; print_r($entityData); die();
        $this->db->insert_batch('assessments_entities', $entityData);
        
        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        }
        $this->db->trans_commit();
        return 1;
    }

    public function getHallticketId($assessment_id) {
        $row = $this->db_readonly->select('id, template')->where('assessment_id', $assessment_id)->get('assessment_halltickets')->row();
        if(empty($row))
            return array();
        return $row;
    }

    public function saveHallticket() {
        $input = $this->input->post();
        $data = array(
            'assessment_id' => $input['assessment_id'],
            'template' => $input['template']
        );
        if($input['hallticket_id'] == 0)
            return $this->db->insert('assessment_halltickets', $data);
        return $this->db->where('id', $input['hallticket_id'])->update('assessment_halltickets', $data);
    }

    public function getSectionStudents($section_id) {
        $students = $this->db_readonly->select("sa.id as student_id, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name, cs.section_name, cs.class_name, sy.roll_no, sa.admission_no, sa.sts_number, sy.picture_url, sa.gender, DATE_FORMAT(sa.dob, '%d-%m-%Y') as dob, sy.picture_url, sa.enrollment_number,sa.registration_no, sy.combination, sy.alpha_rollnum")
        ->from('student_admission sa')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->where('cs.id', $section_id)
        ->where('sa.admission_status', 2)
        ->where('sy.acad_year_id', $this->yearId)
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where('sy.promotion_status!=', 'JOINED')
        ->order_by('sy.roll_no')
        ->get()->result();

        $f = $this->db_readonly->select("p.student_id, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as father_name")
        ->from('parent p')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->where('sr.relation_type', 'Father')
        ->where("sr.std_id in (select student_admission_id from student_year where class_section_id=$section_id)")
        ->get()->result();

        $fathers = array();
        foreach ($f as $key => $value) {
            $fathers[$value->student_id] = $value->father_name;
        }

        $m = $this->db_readonly->select("p.student_id, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as mother_name")
        ->from('parent p')
        ->join('student_relation sr', 'sr.relation_id=p.id')
        ->where('sr.relation_type', 'Mother')
        ->where("sr.std_id in (select student_admission_id from student_year where class_section_id=$section_id)")
        ->get()->result();

        $mothers = array();
        foreach ($m as $key => $value) {
            $mothers[$value->student_id] = $value->mother_name;
        }

        foreach ($students as $key => $student) {
            $student->father_name = $fathers[$student->student_id];
            $student->mother_name = $mothers[$student->student_id];
            $student->photo = '<img width="160px" style="float: right;margin-top: -4rem !important;" height="160px" src='.$this->filemanager->getFilePath($student->picture_url).'>';
        }
        return $students;
        // echo "<pre>"; print_r($students); die();
    }

    function getClassTeacherDetails($section_id){
        
        $result = $this->db_readonly->select('cs.class_teacher_id, cs.assistant_class_teacher_id, sm.staff_signature')->from('class_section cs')->join('staff_master sm', 'cs.class_teacher_id=sm.id')->where('cs.id', $section_id)->get()->row();
        $teachers = array(
            'class_teacher' => '',
            'assistant_class_teacher' => '',
            'staff_signature' => ''

        );
        if(!empty($result->class_teacher_id)) {
            $name = $this->_staffName($result->class_teacher_id);
            $teachers['class_teacher'] = $name->initial.'. '.$name->staffName;
            $teachers['class_teacher_woi'] = $name->staffName;
        } else {
            $teachers['class_teacher'] = '';
            $teachers['class_teacher_woi'] = '';
        }
        if(!empty($result->assistant_class_teacher_id)) {
            $name = $this->_staffName($result->assistant_class_teacher_id);
            $teachers['assistant_class_teacher'] = $name->initial.'. '.$name->staffName;
            $teachers['assistant_class_teacher_woi'] = $name->staffName;
        } else {
            $teachers['assistant_class_teacher'] = '';
            $teachers['assistant_class_teacher_woi'] = '';
        }
        if(!empty($result->staff_signature)){
            $teachers['staff_signature'] = $result->staff_signature;
        }else{
            $teachers['staff_signature'] = '';
        }
        

        return $teachers;
    }

       private function _staffName($staffId) {
        return $this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName, (case when sm.gender='F' then 'Ms' else 'Mr' end) as initial")
        ->where('id', "$staffId")->get('staff_master sm')->row();
    }

    public function getHallticketTemplate($hallticket_id) {
        return $this->db_readonly->select('template')->where('id', $hallticket_id)->get('assessment_halltickets')->row()->template;
    }

    public function viewMarksData($ass_entity_gid){
        return $this->db_readonly->where("ass_subject_gid",$ass_entity_gid)->get("assessments_entities")->result();
    }

    public function get_student_assessments_marks($section_id, $assessment_id, $entity_id) {
        $sql = "SELECT sa.id, concat(ifnull(sa.first_name,''), ifnull(sa.last_name,'')) as student_name, aems.marks  as marks, ae.assessment_id, a.ass_type as ass_type, ae.total_marks   
            from assessments_entities ae 
            join assessments_entities_marks_students aems on ae.id=aems.assessments_entities_id 
            right join student_admission sa on sa.id=aems.student_id 
            right join student_year sy on sy.student_admission_id=sa.id and class_section_id=$section_id
            right join assessments a on a.id= $assessment_id
            where ae.assessment_id= $assessment_id and ae.entity_id=$entity_id 
            group by sa.id, ae.assessment_id 
            order by aems.marks desc";

        $result = $this->db_readonly->query($sql)->result();

        // derived subjects
        $der_ids= $this->db_readonly->select('derived_formula')->where('id', $entity_id)->get('assessment_entity_master')->row();
        $der_ids_arr= [];
        foreach(json_decode($der_ids->derived_formula)->entityId as $key => $val) {
            array_push($der_ids_arr, $val->id);
        }
        $der_names_arr= $this->db_readonly->select('name')->where_in('id', $der_ids_arr)->get('assessment_entity_master')->result();
        
        return ['marks' => $result, 'derived_subjects' => $der_names_arr];
    }

    public function get_subjects_derived_formula($entity_id) {
        $sql_formula = "SELECT ifnull(derived_formula, '') as derived_formula 
        from assessment_entity_master 
        where id=$entity_id ";

        return $this->db_readonly->query($sql_formula)->row()->derived_formula;
    }

    public function get_student_section_and_class_ranks($section_id, $assessment_id, $entity_id) {
        
        $sql = "SELECT sa.id, concat(ifnull(sa.first_name,''), ' ', ifnull(sa.last_name,'')) as student_name, aems.entity_section_rank  as section_rank, aems.entity_grade_rank as class_rank, aems.marks as obtained_marks, ae.total_marks as total_marks  
            from assessments_entities ae 
            join assessments_entities_marks_students aems on ae.id=aems.assessments_entities_id 
            right join student_admission sa on sa.id=aems.student_id 
            right join student_year sy on sy.student_admission_id=sa.id and class_section_id=$section_id
            where ae.assessment_id in ($assessment_id) and ae.entity_id=$entity_id 
            group by sa.id, ae.assessment_id 
            order by sa.first_name";

        $result = $this->db_readonly->query($sql)->result();
       
        // echo '<pre>'; print_r($result); die();
        return $result;
    }

    public function delete_assessment_having_no_subjects($class_id, $assessment_id) {
        $this->db->where('id', $assessment_id);
        $this->db->where('class_id', $class_id);

        return $this->db->delete('assessments');
    }

    public function get_subjects_assessment_wise($ass_id, $class_id) {
        $res= $this->db_readonly->select('ae.id, aem.name, ae.show_marks_to_parents')
            ->from('assessments_entities ae')
            ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')
            ->join('assessments a', 'a.id= ae.assessment_id')
            ->where('a.id', $ass_id)
            ->where('a.class_id', $class_id)
            ->where('a.acad_year_id', $this->yearId)
            ->get()->result();

            return $res;
    }

    public function changeParentMarksPublishStatusSubjectWise($ass_id, $class_id, $sub_ids_arr_0, $sub_ids_arr_1, $change_mark_status, $computed_ids_arr_status0, $computed_ids_arr_status1){
        
        $disabled = [];
        foreach ($sub_ids_arr_0 as $key => $aeId) {
            $disabled[] = array('id' =>$aeId,'show_marks_to_parents'=>'0');
        }
        $enabled = [];
        foreach ($sub_ids_arr_1 as $key => $aeId) {
            $enabled[] = array('id' =>$aeId,'show_marks_to_parents'=>'1');
        }

        $disabled_comp = [];
        foreach ($computed_ids_arr_status0 as $key => $c_id) {
            $disabled_comp[] = array('id' =>$c_id,'show_result_to_parents'=> 0);
        }
        $enabled_comp = [];
        foreach ($computed_ids_arr_status1 as $key => $c_id) {
            $enabled_comp[] = array('id' =>$c_id,'show_result_to_parents'=> 1);
        }

        $this->db->trans_start();
            if(!empty($disabled)) {
                $this->db->update_batch('assessments_entities', $disabled, 'id');
            }
            if(!empty($enabled)) {
                $this->db->update_batch('assessments_entities', $enabled, 'id');
            }
            if(!empty($disabled_comp)) {
                $this->db->update_batch('assessment_computed_field_master', $disabled_comp, 'id');
            }
            if(!empty($enabled_comp)) {
                $this->db->update_batch('assessment_computed_field_master', $enabled_comp, 'id');
            }
            $this->db->where('id', $ass_id)->update('assessments', array('show_marks_to_parents'=>$change_mark_status));
        $this->db->trans_complete();

        if(!$this->db->trans_status()) {
            $this->db->trans_rollback();
        }
        return $this->db->trans_status();
    }

    public function get_subjects_ass_wise($assId) {
        $sql = "select distinct(id),ass_entity_gid as group_id, name from assessment_entity_master where id in (select entity_id from assessments_entities where assessment_id in (".$assId."))";
        $subs= $this->db_readonly->query($sql)->result();

        $formula= $this->db_readonly->select('formula')
                ->where('id', $assId)
                ->get('assessments')->row();

        $formula= json_decode($formula->formula);
        foreach($formula->merg_values as $key => $value) {
            foreach($subs as $sub => $val) {
                if($val->id == $value->entity_id) {
                    $subs[$sub]->reduced_value= $value->value_set[0]->value;
                    break;
                }
            }
        }

        return $subs;

    }

    public function edit_derived_assessment_formula() {
        $input = $this->input->post();
        $ass_id= $input['current_assessment_id'];
        $class_id= $input['current_class_id'];
        
        $sub_id_arr= array(); $i= 0;
        foreach($input as $key => $val) {
            if($i > 1) {
                $v1= explode('-', $key);
                if(isset($v1[1])) {
                    array_push($sub_id_arr, $v1[1]);// even index: sub ids
                    array_push($sub_id_arr, $val);// odd index: sub values
                    if($input['der_formula_input_id7'] == 'percentage') {
                        $this->db->where('assessment_id', $ass_id)->where('entity_id', $v1[1])->update('assessments_entities', ['total_marks' => $val]);
                    }
                }
            }
            $i++;
        }

        // previous sub values
        $formula= $this->db_readonly->select('formula')
                ->where('id', $ass_id)
                ->get('assessments')->row();

        $formula= json_decode($formula->formula);
        $j= 0;
        foreach($sub_id_arr as $k => $v) {
            if($j % 2 == 0) {
                foreach($formula->merg_values as $keys => $values) {
                    if($v == $values->entity_id) {
                        $values->value_set[0]->value= $sub_id_arr[$k+ 1];
                    }
                }
            }
            
            $j++;
        }
        $json_form= json_encode($formula);
        $success= $this->db->where('id', $ass_id)->update('assessments', array('formula' => $json_form));
        if($success)
            return 1;
        return 0;

    }

    public function update_time_boundation() {
        $inputs= $this->input->post();
        $open_date= date('Y-m-d H:i:s', strtotime($inputs['open']. ' ' .$inputs['time_open']. ':00'));
        $close_date= date('Y-m-d H:i:s', strtotime($inputs['close']. ' ' .$inputs['time_close']. ':00'));
        $ass_id= $inputs['ass_id'];
        return $this->db->where('id', $ass_id)->update('assessments', array('marks_entry_open_date' => $open_date, 'marks_entry_close_date' => $close_date));
    }

    public function get_assessments_class_wise($class_id, $generation_type) {
        $res= $this->db_readonly->select("id, ass_type, short_name, long_name, generation_type")->where('generation_type', $generation_type)->where('class_id', $class_id)->where('acad_year_id', $this->yearId)->get('assessments')->result();
        if(!empty($res)) {
            return $res;
        }
        return array();
    }

    public function get_student_marks_details($assessment_id, $student_id, $class_section_id, $class_id) {
        $res= $this->db_readonly->select("ae.total_marks, ae.is_editable, aems.marks, aems.id as aems_id, aems.grade, aem.id as aem_id, aem.evaluation_type, aem.name as aem_name, aem.mapping_string as aem_mapping_string, aeg.id as entities_group_id, aeg.mapping_string as entities_group_mapping_string, aeg.display_name as entities_group_group_name, aeg.entity_name as entities_group_group_name_2, ifnull(aeg.elective_group_id, '-') as elective_group_id, aeg.is_elective, if(aeg.is_elective = 1, a_ele_g.mapping_string, '-') as elective_mapping_string, if(aem.derived_formula is not null or aem.derived_formula != '', 1, 0) as is_derived_subject")
                ->from('assessments_entities ae')
                ->join('assessments_entities_marks_students aems', 'aems.assessments_entities_id= ae.id')
                ->join('assessment_entity_master aem', 'aem.id= ae.entity_id')
                ->join('assessment_subject_group asg', "asg.id= ae.ass_subject_gid and asg.assessment_id= $assessment_id")
                ->join('assessment_entities_group aeg', 'aeg.id= asg.ass_entity_gid')
                ->join('assessment_elective_group a_ele_g', 'a_ele_g.id= aeg.elective_group_id', 'left')
                ->where('aem.class_id', $class_id)
                // ->where('aems.acad_year_id', $this->yearId)
                ->where('aems.student_id', $student_id)
                ->where('ae.assessment_id', $assessment_id)
                ->get()->result();

        // echo '<pre>'; print_r($res); die();
        
                if(!empty($res)) {
                    return $res;
                }
                return array();
    }

    public function edit_marks_of_a_student() {
        $input= $this->input->post();
        $aems_id= $input['aems_id'];

        $evaluation_type= $input['evaluation_type'];
        $updated_marks= $input['updated_marks'];
        if($evaluation_type == 'marks') {
            $history= array(
                'assessments_entities_marks_students_id' => $aems_id,
                'action' =>  "Marks changed to -$updated_marks",
                'action_by' => $this->authorization->getAvatarId()
            );
        } else {
            $history= array(
                'assessments_entities_marks_students_id' => $aems_id,
                'action' => "Grade changed to -$updated_marks",
                'action_by' => $this->authorization->getAvatarId()
            );
        }
        
        $this->db->trans_start();
            if($evaluation_type == 'marks') {
                $this->db->where('id', $aems_id)->update('assessments_entities_marks_students', ['marks' => $updated_marks]);
            } else {
                $this->db->where('id', $aems_id)->update('assessments_entities_marks_students', ['grade' => $updated_marks]);
            }
            $this->db->insert('assessments_entities_marks_students_history', $history);
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    public function delete_marks_of_a_student() {
        $input= $this->input->post();
        $aems_id= $input['aems_id'];
        return $this->db->where('id', $aems_id)->delete('assessments_entities_marks_students');
    }

    public function edit_display_name() {
        $input= $this->input->post();
        return $this->db->where('id', $input['id'])->update('assessments', ['display_name' => $input['disp_name']]);
    }

    public function edit_rounding_parameter() {
        $input= $this->input->post();
        return $this->db->where('id', $input['id'])->update('assessments', ['rounding' => $input['selectedValue']]);
    }

    public function get_computed_class_wise($class_id, $ass_id) {
        $c= $this->db_readonly->select("id, name, show_result_to_parents, subjects")->where('fields_operation', 'Exam Result')->where('class_id', $class_id)->get('assessment_computed_field_master')->result();
        if(!empty($c)) {
            foreach( $c as $key => $val) {
                $isAdded= $this->db_readonly->where('ass_computed_field_master_id', $val->id)->get('assessment_computed_field_details')->result();
                if(empty($isAdded)) {
                    unset($c[$key]);
                }
            }
            foreach($c as $k1 => $v1) {
                $subs_and_computed= json_decode($v1->subjects);
                $is_exist= false;
                foreach($subs_and_computed as $id_key => $computed_id) {
                    foreach($computed_id  as $key1 => $val1) {
                        if($id_key == $ass_id) {
                            $is_exist= true;
                        }
                    }
                }
                if(!$is_exist) {
                    unset($c[$k1]);
                }
            }
        }
        return $c;
    }

    public function isPortionsPublished($assessment_id) {
        $assessment_portion= $this->db_readonly->where('assessment_id', $assessment_id)->get('assessment_portions')->row();
        if(!empty($assessment_portion)) {
            if($assessment_portion->publish_status == 1) {
                return 'Published';
            } else {
                return 'Not Published';
            }
        }
        return 'Not Yet Created';
    }

    public function getAssessmentsGenerationTypeWise($classId, $assessment_type) {
        if($classId == '') return array();
        $str = "a.generation_type='Manual' ";
        if ($assessment_type == 'auto') {
            $str = "";
        }

        $this->db_readonly->select("a.id, a.ass_type, a.short_name, a.long_name, a.description, a.publish_status, a.release_marks, a.generation_type, a.formula, a.sorting_order, a.acad_year_id, a.class_id, a.enable_subject_remarks, a.remarks_group_id, a.show_marks_to_parents, a.marks_entry_open_date, a.marks_entry_close_date, a.marks_release_type, a.rounding, a.display_name, ifnull(ap.publish_status, -1) as portion_publish_status, ifnull( GROUP_CONCAT( CONCAT(aeg.entity_name) ) , '-') AS subAdded")
                ->from('assessments a')
                ->join('assessment_portions ap', 'ap.assessment_id = a.id', 'left')
                ->join('assessment_subject_group asg', 'asg.assessment_id = a.id', 'left')
                ->join('assessment_entities_group aeg', 'aeg.id=asg.ass_entity_gid', 'left')
                ->where('a.class_id', $classId)
                ->where('a.acad_year_id', $this->yearId);
        if($str && !empty($str) && strlen($str)) {
            $this->db_readonly->where($str);
        }
        $result= $this->db_readonly->group_by('a.id')->get()->result();


        foreach($result as $key => $val) {
            if($val->subAdded == '-') {
                $val->is_assessment_deletable= '1';
            } else {
                $val->is_assessment_deletable= '-1';
            }


            if($val->portion_publish_status == '-1') {
                $val->isPortionsPublished= 'Not Created';
            } else if($val->portion_publish_status == '0') {
                $val->isPortionsPublished= 'Not Published';
            } else {
                $val->isPortionsPublished= 'Published';
            }



            if($val->marks_release_type == 'auto') {
                    $open= date('Y-m-d', strtotime($val->marks_entry_open_date));
                    $close= date('Y-m-d', strtotime($val->marks_entry_close_date));
                    if($open != '1970-01-01' && $close != '1970-01-01' && date('Y-m-d H:i:s') >= date('Y-m-d H:i:s', strtotime($val->marks_entry_open_date)) && date('Y-m-d H:i:s') <= date('Y-m-d H:i:s', strtotime($val->marks_entry_close_date))) {
                        $val->add_marks_enability= '1';
                    } else {
                        $val->add_marks_enability= '-1';
                    }
            } else {
                if($val->release_marks == 1) {
                    $val->add_marks_enability= '1';
                } else {
                    $val->add_marks_enability= '-1';
                }
            }
        }

        return $result;
    }

    public function getAgetAssessmentsGenerationTypeWisessessments($classId, $show_derived) {
        
        if ($classId == '') return array();

        $this->db_readonly->select("ae.assessment_id AS is_sub_added,a.id, a.ass_type, a.short_name, a.long_name, a.description, a.publish_status, a.release_marks, a.generation_type, a.formula, a.sorting_order, a.acad_year_id, a.class_id, a.enable_subject_remarks, a.remarks_group_id, a.show_marks_to_parents, a.marks_entry_open_date, a.marks_entry_close_date, a.marks_release_type, a.rounding, a.display_name,ifnull(ae.assessment_id, 0) as assessment_id")
            ->from('assessments a')
            ->join('assessments_entities ae', 'a.id = ae.assessment_id', 'left')
            ->where('a.class_id', $classId)
            ->where('a.acad_year_id', $this->yearId);
        if ($show_derived = 'manual') {
            $this->db_readonly->where('a.generation_type', 'Manual');
        }
        $result= $this->db_readonly->group_by('a.id, ae.assessment_id')->get()->result();

        foreach ($result as $key => $val) {
            $val->is_assessment_deletable = (!empty($val->is_sub_added)) ? '-1' : '1';
            if ($val->marks_release_type == 'auto') {
                $open = date('Y-m-d', strtotime($val->marks_entry_open_date));
                $close = date('Y-m-d', strtotime($val->marks_entry_close_date));
                $currentDate = date('Y-m-d H:i:s');
                if ($open != '1970-01-01' && $close != '1970-01-01' && 
                    $currentDate >= date('Y-m-d H:i:s', strtotime($val->marks_entry_open_date)) && 
                    $currentDate <= date('Y-m-d H:i:s', strtotime($val->marks_entry_close_date))) {
                    $val->add_marks_enability = '1';
                } else {
                    $val->add_marks_enability = '-1';
                }
            } else {
                $val->add_marks_enability = ($val->release_marks == 1) ? '1' : '-1';
            }
        }

        return $result;

    }

}
?>