<?php
defined('BASEPATH') OR exit('No direct script access allowed');
            
class Attendance_model extends CI_Model {

    public function __construct() {
		 parent::__construct();
  	}

    public function getAttendanceData($date, $student_id) {
    	$attendance_type = $this->settings->getSetting('student_attendance_type');
    	$attendance = [];
    	if($attendance_type == '' || $attendance_type == 'subject_wise') {
    		$sql = "SELECT sub.subject_name, s.status, m.taken_on, s.duration 
	    			FROM attendance_v2_master m 
	    			JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
	    			JOIN subject_master sub ON m.type_id=sub.id 
	    			WHERE m.date='$date' 
	    			AND s.student_admission_id=$student_id 
	    			AND m.type=0 AND s.status!=0 
	    			ORDER BY m.id";
	    	$attendance = $this->db_readonly->query($sql)->result();
    	}
    	foreach($attendance as $key => $value) {
    		$attendance[$key]->taken_on = local_time($value->taken_on, 'Y-m-d H:i:s');

			$statusMap = [
				1 => 'Present',
				2 => 'Absent',
				3 => 'Late'
			];

			$attendance[$key]->att_status = isset($statusMap[$value->status]) ? $statusMap[$value->status] : '-';
		}
    	return $attendance;
    }

    public function getAttendanceDataByDateRange($from_date, $to_date, $student_id) {
    	$attendance_type = $this->settings->getSetting('student_attendance_type');
    	$attendance = [];
    	if($attendance_type == '' || $attendance_type == 'subject_wise') {
    		$sql = "SELECT m.date, sub.subject_name, s.status, m.taken_on, s.duration 
	    			FROM attendance_v2_master m 
	    			JOIN attendance_v2_student s ON s.attendance_v2_master_id=m.id 
	    			JOIN subject_master sub ON m.type_id=sub.id 
	    			WHERE m.date>='$from_date' AND m.date<='$to_date' 
	    			AND s.student_admission_id=$student_id 
	    			AND m.type=0 AND s.status!=0 
	    			ORDER BY m.date DESC, m.taken_on ASC";
	    	$attendance = $this->db_readonly->query($sql)->result();
    	}
    	$data = [];
    	foreach($attendance as $key => $value) {
    		if(!array_key_exists($value->date, $data)) {
    			$data[$value->date] = array();
    			$data[$value->date]['present'] = 0;
    			$data[$value->date]['absent'] = 0;
    			$data[$value->date]['late'] = 0;
    		}
    		$value->taken_on = local_time($value->taken_on, 'Y-m-d H:i:s');
    		$data[$value->date]['date'] = $value->date;
    		$data[$value->date]['duration'] = $value->duration;
    		if($value->status == 1) {
    			$attendance[$key]->att_status = 'Present';
    			$data[$value->date]['present']++;
    		} else if($value->status == 2) {
    			$attendance[$key]->att_status = 'Absent';
    			$data[$value->date]['absent']++;
    		} else if($value->status == 3) {
    			$attendance[$key]->att_status = 'Late';
    			$data[$value->date]['late']++;
    		}
    		$data[$value->date]['attendance'][] = $value;
    	}
    	$attendance = [];
    	foreach ($data as $d) {
    		$attendance[] = $d;
    	}
    	return $attendance;
	    // echo "<pre>"; print_r($attendance); die();
    }
}
?>