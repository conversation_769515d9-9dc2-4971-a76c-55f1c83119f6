<?php
/**
 * Name:    OxygenV2
 * Author:  Anish
 *          <EMAIL>
 *
 * Created:  23 october, 2024
 *
 * Description: Controller for Budget Module. Entry point for Inentory Module
 *
 * Requirements: PHP5 or above
 *
 */

class Budget_model extends CI_Model {
  private $yearId;
  function __construct() {
      parent::__construct();
      $this->yearId = $this->acad_year->getAcadYearID();
  }

  private function __get_months_year_wise_by_range_format2($start_month, $end_month) {
    // echo $end_month; die();
      $start_date = DateTime::createFromFormat('Y-m', $start_month);
      $end_date = DateTime::createFromFormat('Y-m', $end_month);
      $months = array();

      while ($start_date <= $end_date) {
          $months[] = $start_date->format('M-Y');
          $start_date->modify('+1 month');
      }

      return $months;
  }


//   private function __get_months_year_wise_by_rage($start_month, $end_month) {
//     // Print and debug input values for validation (optional)
//     // echo '<pre>'; print_r($start_month); die();

//     // Convert input dates from 'January 2025' format
//     $start_date = DateTime::createFromFormat('F Y', $start_month);
//     $end_date = DateTime::createFromFormat('F Y', $end_month);

//     // Validate the input dates
//     if (!$start_date || !$end_date) {
//         throw new Exception('Invalid date format. Expected format is "Month YYYY" (e.g., "January 2025").');
//     }

//     $months = array();
//     // Generate the range of months
//     while ($start_date <= $end_date) {
//         $months[] = $start_date->format('F Y'); // Full month name and year
//         $start_date->modify('+1 month');
//     }

//     return $months;
// }

private function __get_months_year_wise_by_rage($start_month, $end_month) {
  // Convert input dates from 'January 2025' format
  $start_date = DateTime::createFromFormat('F Y', $start_month);
  $end_date = DateTime::createFromFormat('F Y', $end_month);

  // Validate the input dates
  if (!$start_date || !$end_date) {
      throw new Exception('Invalid date format. Expected format is "Month YYYY" (e.g., "January 2025").');
  }

  $months = array();
  // Generate the range of months
  while ($start_date <= $end_date) {
      $months[] = $start_date->format('M-Y'); // Abbreviated month name and year (e.g., 'Apr-2025')
      $start_date->modify('+1 month');
  }

  return $months;
}



  public function add_budget_year() {
    $inputs= $this->input->post();
    
    $provisional_amount= isset($inputs['provisional_amount']) ? $inputs['provisional_amount'] : [];
    $procurement_categories= isset($inputs['procurement_categories']) && $inputs['procurement_categories'] != '[]' ? $inputs['procurement_categories'] : ['all'];
    
    if($procurement_categories[0] == 'all' || $procurement_categories[0] == 'All') {
      $cats= $this->db->select('ec.id')
      ->join('expense_sub_category esc', 'esc.cat_id = ec.id')
      ->group_by('ec.id')
      ->get('expense_category ec')->result();
      
      if(!empty($cats)) {
        $procurement_categories= [];
        foreach($cats as $key => $val) {
          $procurement_categories[]= $val->id;
        }
      } else {
        return false;
      }
    }
    
    
    $data= array(
        'year' => $inputs['budget_year'],
        'start_month' => $inputs['start_month'],
        'end_month' => $inputs['end_month'],
        'status' => 'Draft',
        'approver_id' => $inputs['approver_id'],
        'created_by' => $this->authorization->getAvatarStakeHolderId(),
        'last_modified_by' => $this->authorization->getAvatarStakeHolderId(),
        'budget_categories_json' => json_encode($procurement_categories),
        // 'category_split_type' => $category_split_type
    );




    $start_date = DateTime::createFromFormat('F Y', $data['start_month']);
    $end_date = DateTime::createFromFormat('F Y', $data['end_month']);

    if ($start_date && $end_date) {
        $data['start_month'] = $start_date->format('Y-m');
        $data['end_month'] = $end_date->format('Y-m');
    } else {
        throw new Exception('Invalid date format for start_month or end_month. Expected format is "Month YYYY".');
    }

    

    $this->db->trans_start();
      $this->db->insert('procurement_budget_year', $data);
      $year_id= $this->db->insert_id();
      
      if($this->db->trans_status()) {
        // Pre-addition of choosed categories
        
        $master=[];
        foreach($procurement_categories as $key => $val) { 
          $provisional_amt= NULL;
          if(!empty($provisional_amount) && isset($provisional_amount[$val])) {
            $provisional_amt= $provisional_amount[$val];
          }
          $master[]= array( // Applying insert batch due to foreach loop
            'budget_category_id' => $val,
            'budget_year_id' => $year_id,
            'amount_allocated' => 0,
            'amount_available' => 0,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'created_on' => date('Y-m-d H:i:s'),
            'modified_by' => $this->authorization->getAvatarStakeHolderId(),
            'modified_on' => date('Y-m-d H:i:s'),
            'provisional_amount' => $provisional_amt
          );
        }
        $this->db->insert_batch('procurement_budget_master', $master);

         // Pre-addition of months and sub-cats splits

        
         $masters_categories = $this->db->select("pbm.id, pbm.budget_category_id, group_concat(ifnull(esc.id, 0)) as subCats")
         ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
         ->join('expense_sub_category esc', 'esc.cat_id = ec.id')
         ->where('pbm.budget_year_id', $year_id) // You are getting this from the budget master
         ->group_by('pbm.budget_category_id, pbm.id') // Group by both `budget_category_id` and `id`
         ->get('procurement_budget_master pbm')
         ->result();


        // $masters_categories= $this->db->select("pbm.id, pbm.budget_category_id, group_concat(ifnull(esc.id, 0)) as subCats")
        //     ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
        //     ->join('expense_sub_category esc', 'esc.cat_id = ec.id')
        //     ->where('pbm.budget_year_id', $year_id) // I am getting from the budget master because now only it was added fresh
        //     ->group_by('pbm.budget_category_id')->get('procurement_budget_master pbm')->result();
            

        if(!empty($masters_categories)) {
          $months_arr= $this->__get_months_year_wise_by_rage($inputs['start_month'], $inputs['end_month']);
          $months_split=[];
          $subcat_split=[];
          foreach($masters_categories as $key => $val) {
            $subCatsArr= [];
            if(!empty($val->subCats)) {
              $subCatsArr= explode(',', $val->subCats);
            }
            // // echo '<pre>'; print_r($subCatsArr); die();
            // if(!empty($months_arr)) {
            //   foreach($months_arr as $keyMonths => $valMonths) {
            //     $months_split[]= array(
            //       'procurement_budget_master_id' => $val->id,
            //       'month_name' => $valMonths,
            //       'amount_allocated' => 0
            //     );
            //   }
            // }
            if(!empty($subCatsArr)) {
              foreach($subCatsArr as $keySub => $valSub) {
                // $subcat_split[]= array(
                //   'procurement_budget_master_id' => $val->id,
                //   'subcategory_id' => $valSub,
                //   'amount_allocated' => 0,
                //   'subcategory_type' => 'Subcategory'
                // );



                if(!empty($months_arr)) {
                  foreach($months_arr as $keyMonths => $valMonths) {
                    $budget_split[]= array(
                      'procurement_budget_master_id' => $val->id,
                      'expense_subcategory_id' => $valSub,
                      'month_name' => $valMonths,
                      'allocated_amount' => 0
                    );
                  }
                }

                
              }
            }
          }

          // // Inserting splitting
          // if(!empty($subcat_split)) {
          //   $this->db->insert_batch('procurement_budget_master_category_wise_split', $subcat_split);
          // }
          // if(!empty($months_split)) {
          //   $this->db->insert_batch('procurement_budget_master_monthly_split', $months_split);
          // }

          if(!empty($budget_split)) {
            $this->db->insert_batch('procurement_budget_split', $budget_split);
          }

        }

       

        // History
        $year= $inputs['budget_year'];
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'New Budget Created',
          'action_description' => "Budget - $year has been initiated.",
          'action_table' => 'procurement_budget_year',
          'source_id' => $year_id // budget year id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function get_budget_years() {
    $years= $this->db_readonly->select("concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as approver, pby.id, pby.year, pby.start_month, pby.end_month, pby.status, pby.approver_id, pby.created_by, pby.created_on, pby.last_modified_by, pby.last_modified_on, pby.budget_categories_json, pby.release_for_allocation_status, pby.budget_approver_status, pby.CFO_approver_id, pby.CFO_approver_status, pby.budget_approver_comments, pby.CFO_approver_comments")
      ->join('staff_master sm', 'sm.id = pby.approver_id')
      ->get('procurement_budget_year pby')->result();

      // echo '<pre>'; print_r($years); die();

      if(!empty($years)) {
        foreach($years as $key => $val) {
          $val->isOthrBudgetActive= $this->isOthrBudgetActive($val->id);
        }
      }
      return $years;
  }

  public function change_budget_year_status() {
    $inputs= $this->input->post();
    $data= array(
        'status' => $inputs['status_input'],
        'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->trans_start();
        if($inputs['status_input'] == 'Active') { // if status need to make active, already active status should be in finished status
          $isActiveExist= $this->db->where('status', 'Active')->get('procurement_budget_year')->row();
          if(!empty($isActiveExist)) {
            return ['status' => '-1', 'errorMessage' => "A budget i.e. $isActiveExist->year is already active. Reach out to your budget Administrator to move it into 'Finished' status and visit again"];
          }
            // $this->db->where('status', 'Active')->update('procurement_budget_year', ['status' => 'Finished']);
        }
        $this->db->where('id', $inputs['id'])->update('procurement_budget_year', $data);
        // histoy
        if($this->db->trans_status()) {
          $sts= $inputs['status_input'];
          $year= $this->db->select('year')->where('id', $inputs['id'])->get('procurement_budget_year')->row();
          $data_audit_log= array(
            'action_on' => date('Y-m-d H:i:s'),
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'action_type' => 'Status Changed',
            'action_description' => "Budget ($year->year) has been transitioned to the '$sts' status.",
            'action_table' => 'procurement_budget_year',
            'source_id' => $inputs['id']
          );
          $this->db->insert('procurement_budget_audit_log', $data_audit_log);
        }
    $this->db->trans_complete();
    if(!$this->db->trans_status()) {
      $this->db->trans_rollback();
      return ['status' => '-2', 'errorMessage' => 'Something went wrong'];
    }
    return ['status' => '1', 'errorMessage' => 'Successfully status updated'];;
  }

  public function add_budget_category() {
    $inputs= $this->input->post();
    $data= array(
        'category_name' => $inputs['budget_category'],
        'description' => $inputs['description'],
        'status' => 1,
        'created_by' => $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->trans_start();
      $this->db->insert('procurement_budget_categories', $data);
      // history
      if($this->db->trans_status()) {
        $id= $this->db->insert_id();
        $selectedCategoryName= $inputs['budget_category'];
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'New Budget Category Introduced',
          'action_description' => "A new budget category, $selectedCategoryName, has been introduced.",
          'action_table' => 'procurement_budget_categories',
          'source_id' => $id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function get_budget_categorys() {
    return $this->db_readonly->get('procurement_budget_categories')->result();
  }

  public function change_budget_category_status() {
    $inputs= $this->input->post();
    // echo '<pre>'; print_r($inputs); die();
    $data= array(
        'status' => $inputs['status_input']
    );
    $this->db->trans_start();
        $this->db->where('id', $inputs['id'])->update('procurement_budget_categories', $data);
        if($this->db->trans_status()) {
          $selectedCategoryName= $this->db->select('category_name')->where('id', $inputs['id'])->get('procurement_budget_categories')->row();
          $sts= $inputs['status_input'] == '1' ? 'acivated' : 'de-activated';
          $data_audit_log= array(
            'action_on' => date('Y-m-d H:i:s'),
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'action_type' => 'Status Changed',
            'action_description' => "The budget category ($selectedCategoryName->category_name) has been $sts.",
            'action_table' => 'procurement_budget_categories',
            'source_id' => $inputs['id']
          );
          $this->db->insert('procurement_budget_audit_log', $data_audit_log);
        }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function get_active_budget_year($budget_year_id) {
    return $this->db_readonly->where('id', $budget_year_id)->get('procurement_budget_year')->row();
  }

  public function get_budget_categories_not_added_to_budget_master($activeBudgetYear) {
    $logged_in= $this->authorization->getAvatarStakeHolderId();
    $used_categories= $this->db_readonly->select("ifnull(group_concat(budget_category_id), 0) as categories_str")->where('budget_year_id', $activeBudgetYear->id)->get('procurement_budget_master')->row();
    $this->db_readonly->select("ec.id, ec.category_name, ec.department_id")
      ->join('expense_sub_category esc', 'ec.id = esc.cat_id') // Let's get the categories which have at-least one sub-category
      ->join('staff_departments sd', 'sd.id = ec.department_id') // Let the non-departmental category handle by Budget Admin only
      ->where("ec.id not in ($used_categories->categories_str)");
    // if(!$this->authorization->isSuperAdmin()) {
    //   $this->db_readonly->where("(sd.head_of_department_id is null OR sd.head_of_department_id = 0 OR sd.head_of_department_id = '$logged_in')"); // to get department wise category only
    // }
    $categorie= $this->db_readonly->group_by('ec.id')->get('expense_category ec')->result();

    // echo '<pre>'; print_r($this->db_readonly->last_query($categorie)); die();
    if($logged_in == $activeBudgetYear->approver_id) {
      return $categorie;
    } else {
      return [];
    }
  }

  public function create_budget_form() {
    $inputs= $this->input->post();
    // echo '<pre>'; print_r($inputs); die();
    $category_id= $inputs['category_id'];
    $activeBudgetYear= $inputs['activeBudgetYear'];
    $allocated_to_category= $inputs['allocated_to_category'];
    $description= $inputs['description'];
    $activeBudgetYearName= $inputs['activeBudgetYearName'];
    $selectedCategoryName= $inputs['selectedCategoryName'];
    $category_split_type= $inputs['category_split_type'];
    $months= $inputs['months'];

    // echo '<pre>'; print_r($months); die();

    // $subCats= isset($inputs['subCats']) && !empty($inputs['subCats']) ? $inputs['subCats'] : [];
    $subCats= $this->db->select('id')->where('cat_id', $category_id)->get('expense_sub_category')->result();
    $data_master= array(
      'budget_category_id' => $category_id,
      'budget_year_id' => $activeBudgetYear,
      'amount_allocated' => 0,
      'amount_available' => 0,
      'description' => $description,
      'created_by' => $this->authorization->getAvatarStakeHolderId(),
      'created_on' => date('Y-m-d H:i:s'),
      'modified_by' => $this->authorization->getAvatarStakeHolderId(),
      'modified_on' => date('Y-m-d H:i:s'),
      // 'category_split_type' => $category_split_type
    );
    $this->db->trans_start();
      $this->db->insert('procurement_budget_master', $data_master);
      $id= $this->db->insert_id();

      // Adding new category to the budget year as JSON
      $catJSONs= $this->db->select('budget_categories_json')->where('id', $activeBudgetYear)->get('procurement_budget_year')->row();
      // echo '<pre>'; print_r($catJSONs); 
      $JSON_arrayed_str= str_replace("{", "[", $catJSONs->budget_categories_json);
      $JSON_arrayed_str= str_replace("}", "]", $JSON_arrayed_str);
      // echo '<pre>'; print_r($JSON_arrayed_str); 

      $catJSON= json_decode($JSON_arrayed_str, true);

      


      if(!empty($catJSON)) {
        $catJSON[]= $category_id;
      } else {
        $catJSON= [$category_id];
      }
      $catJSON= json_encode($catJSON);
      // echo '<pre>'; print_r($catJSON);  die();
      $this->db->where('id', $activeBudgetYear)->update('procurement_budget_year', ['budget_categories_json' => $catJSON]);

      
      // echo '<pre>'; print_r($subCats);
      // echo '<pre>'; print_r($catJSON); die();

      if($id && !empty($subCats)) { // Subcategory filling
        foreach($subCats as $keySub => $valSub) {
// 
          if($id && !empty($months)) { // months filling
            foreach($months as $keyMon => $valMon) {
              $data_months[]= array(
                'procurement_budget_master_id' => $id,
                'expense_subcategory_id' => $valSub->id,
                'month_name' => $keyMon,
                'allocated_amount' => 0
              );
            }
          }
// 
        }
      }

     
      
      if(!empty($data_months)) {
        $this->db->insert_batch('procurement_budget_split', $data_months);
      }

      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Budget Created',
          'action_description' => "The budget category ($selectedCategoryName) has been added to the budget ($activeBudgetYearName).",
          'action_table' => 'procurement_budget_master',
          'source_id' => $id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    if(!$this->db->trans_status()) {
      $this->db->trans_rolback();
    }
    return $this->db->trans_status();
  }

  public function get_categories_budget_year_wise() {
    $budget_year= $this->input->post('budget_year');

    $details['categories']= $this->db_readonly->select("pbc.id,pbc.category_name,pbm.amount_allocated,pbm.amount_available")
      ->from('procurement_budget_categories pbc')
      ->join('procurement_budget_master pbm', 'pbc.id = pbm.budget_category_id')
      ->where('pbm.budget_year_id', $budget_year)
      ->group_by('pbc.id')
      ->order_by('pbc.category_name')
      ->get()->result();

      $details['categories_expense']= $this->db_readonly->select("pbc.category_name, ifnull(sum(pbm.amount_allocated), 0) as amount_allocated, ifnull(sum(pbe.spend_amount), 0) as spend_amount")
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_categories pbc', 'pbc.id = pbm.budget_category_id')
      ->join('procurement_budget_expense pbe', 'pbm.id = pbe.procurement_budget_master_id', 'left')
      ->where('pbm.budget_year_id', $budget_year)
      ->group_by('pbm.budget_category_id, pbc.id')
      ->order_by('pbm.id')
      ->get()->result();

      $details['mothly_expense']= $this->db_readonly->select("pbmms.month_name,ifnull(sum(pbmms.amount_allocated), 0) as amount_allocated, ifnull(sum(pbe.spend_amount), 0) as spend_amount")
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_master_monthly_split pbmms', 'pbm.id = pbmms.procurement_budget_master_id')
      ->join('procurement_budget_expense pbe', 'pbmms.id = pbe.budget_master_monthly_split_id', 'left')
      ->where('pbm.budget_year_id', $budget_year)
      ->group_by('pbmms.month_name')
      ->order_by('pbmms.id')
      ->get()->result();

      $oldestBudgetMasterDate= $this->db_readonly->select('created_on')
        ->where('budget_year_id', $budget_year)
        ->order_by('id', 'asc')
        ->limit(1)
        ->get('procurement_budget_master')->row();

        $details['budget_history']= [];

      if(!empty($oldestBudgetMasterDate) && isset($oldestBudgetMasterDate->created_on)) {
        $details['budget_history']= $this->db_readonly->select("pbal.id as audit_log_id, ifnull(TRIM(concat(sm.first_name, ' ', ifnull(sm.last_name, ''))), 'Admin') as createdBy, pbal.action_on, pbal.action_description, pbal.action_type")
        ->from('procurement_budget_audit_log pbal')
        ->join('staff_master sm', 'sm.id = pbal.action_by', 'left')
        ->where('pbal.action_on >=', $oldestBudgetMasterDate->created_on)
        ->order_by('pbal.action_on', 'desc')
        ->get()->result();
        if(!empty($details['budget_history'])) {
          foreach($details['budget_history'] as $key => $val) {
            $val->action_on= local_time($val->action_on, 'd-M-Y h:i A');
          }
        }
      }

    return $details;
  }

  public function get_months_budget_year_wise() {
    $budget_year= $this->input->post('budget_year');
    return $this->db_readonly->select("group_concat(pbm.id) as categoris_id_str, pbmms.month_name, sum(pbmms.amount_allocated) as amount_allocated, pbm.budget_year_id")
    ->from('procurement_budget_master pbm')
    ->join('procurement_budget_master_monthly_split pbmms', 'pbm.id = pbmms.procurement_budget_master_id')
    ->where('pbm.budget_year_id', $budget_year)
    ->group_by('pbmms.month_name')
    ->order_by('pbmms.id')
    ->get()->result();
  }

  public function one_category_details() {
    $inputs= $this->input->post();
    $budget_year_id= $inputs['budget_year_id'];
    $category_id= $inputs['category_id'];

    $AllDetails['category_details']= $this->__get_category_details($budget_year_id, $category_id);
    $AllDetails['category_splited_details']= $this->__get_category_splited_details($AllDetails['category_details']->budget_master_id); // for graph and table
    $AllDetails['audit_log_history']= $this->__get_audit_log_history($budget_year_id, $category_id, $AllDetails['category_details']->budget_master_id);
    $AllDetails['monthly_expense_details']= $this->__get_monthly_expense_details($budget_year_id, $category_id, $AllDetails['category_details']->budget_master_id);
    $AllDetails['category_expense_details']= $this->__get_category_expense_details($budget_year_id, $category_id, $AllDetails['category_details']->budget_master_id);

    return $AllDetails;
  }

  private function __get_category_details($budget_year_id, $category_id) {
    $x= $this->db_readonly->select("pbm.id as budget_master_id, ifnull(TRIM(concat(sm.first_name, ' ', ifnull(sm.last_name, ''))), 'Admin') as createdBy, ifnull(pbm.amount_allocated, 0) as amount_allocated, ifnull(pbm.amount_available, 0) as amount_available, ifnull(pbm.amount_commited, 0) as amount_commited, ifnull(pbm.amount_blocked, 0) as amount_to_be_commited, ifnull(pbm.description, '-') as description, ifnull(pbm.amount_used_additionally, 0) as amount_used_additionally, pbm.created_on, pbc.category_name")
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_categories pbc', 'pbc.id = pbm.budget_category_id')
      ->join('staff_master sm', 'sm.id = pbm.created_by', 'left')
      ->where('pbm.budget_category_id', $category_id)
      ->where('pbm.budget_year_id', $budget_year_id)
      ->get()->row();
    if(!empty($x)) {
      $x->created_on= local_time($x->created_on, 'd-M-Y h:i A');
    }
    return $x;
  }

  private function __get_category_splited_details($budget_master_id) {
    $x= $this->db_readonly->where('procurement_budget_master_id', $budget_master_id)->order_by('id')->get('procurement_budget_master_monthly_split')->result();
    $x1= [];
    if(!empty($x)) {
      foreach($x as $key => $val) {
        $x1[$val->id]= $val; // index is the monthly split id only
      }
    }
    // echo '<pre>'; print_r($x1); die();
    return $x1;
  }

  private function __get_audit_log_history($budget_year_id, $category_id, $budget_master_id) {
    $x= $this->db_readonly->select("pbal.id as audit_log_id, ifnull(TRIM(concat(sm.first_name, ' ', ifnull(sm.last_name, ''))), 'Admin') as createdBy, pbal.action_on, pbal.action_description, pbal.action_type")
      ->from('procurement_budget_audit_log pbal')
      ->join('staff_master sm', 'sm.id = pbal.action_by', 'left')
      ->where('pbal.source_id', $budget_master_id)
      ->where('pbal.action_table', 'procurement_budget_master')
      ->get()->result();
    if(!empty($x)) {
      foreach($x as $key => $val) {
        $val->action_on= local_time($val->action_on, 'd-M-Y h:i A');
      }
    }
    return $x;
  }

  private function __get_monthly_expense_details($budget_year_id, $category_id, $budget_master_id) {
    return [];
  }

  private function __get_category_expense_details($budget_year_id, $category_id, $budget_master_id) {
    return [];
  }

  function send_budget_for_approval() {
    // history need to store
    $id= $this->input->post('year_id');
    $comments= $this->input->post('comments');
    $this->db->trans_start();
     $this->db->where('id', $id)->update('procurement_budget_year', ['status' => 'Sent for Approval', 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()]);
     $this->db->insert('procurement_budget_approval_comments', array( // I think comments table is alrteady a history storing table
      'budget_year_id' => $id,
      'status' =>'Sent for Approval',
      'comments' => isset($comments) ? $comments : NULL,
      'created_by' => $this->authorization->getAvatarStakeHolderId()
     ));
    $this->db->trans_complete();

    return $this->db->trans_status();
  }

  function get_all_budget_approvers() {
   return $this->db_readonly->select("sm.id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as name")
      ->from('privileges p')
      ->join('privileges_sub ps', 'ps.privilege_id = p.id')
      ->join('roles_privileges_sub rps', 'rps.privilege_sub_id = ps.id')
      ->join('roles r', 'rps.role_id = r.id')
      ->join('roles_staff rs', 'rs.role_id = r.id')
      ->join('staff_master sm', 'rs.staff_id = sm.id')
      ->where('p.name', 'PROCUREMENT_BUDGET')
      ->where('ps.name', 'MANAGE_OVERALL_BUDGET')
      ->group_by('sm.id') // unique staff
      ->get()->result();
  }

  function get_categories_and_months_year_wise() {
    $budget_year= $this->input->post('budget_year');
    $categories= $this->db_readonly->select("ifnull(TRIM(pbm.description), '') as description,pbc.id,pbc.category_name,pbm.amount_allocated,pbm.amount_available, pbm.id as procurement_budget_master_id")
      ->from('procurement_budget_categories pbc')
      ->join('procurement_budget_master pbm', 'pbc.id = pbm.budget_category_id')
      ->where('pbm.budget_year_id', $budget_year)
      ->group_by('pbc.id')
      ->order_by('pbc.category_name')
      ->get()->result();

    if(!empty($categories)) {
      foreach($categories as $key => $val) {
        $mothly_expense= $this->db_readonly->select("pbmms.month_name,ifnull(sum(pbmms.amount_allocated), 0) as amount_allocated, ifnull(sum(pbe.spend_amount), 0) as spend_amount, pbmms.id as budget_master_monthly_split_id")
          ->from('procurement_budget_master_monthly_split pbmms')
          ->join('procurement_budget_expense pbe', 'pbmms.id = pbe.budget_master_monthly_split_id', 'left')
          ->where('pbmms.procurement_budget_master_id', $val->procurement_budget_master_id)
          ->group_by('pbmms.month_name')
          ->order_by('pbmms.id')
          ->get()->result();
        $val->monthly_expense= $mothly_expense;
      }
    }
    return $categories;
  }

  function get_budget_master_details_to_edit() {
    $procurement_budget_master_id= $this->input->post('procurement_budget_master_id');
    return $this->db_readonly->select("pbmms.id as pbmms_id, ifnull(pbm.description, '') as description, ifnull(pbm.amount_allocated, 0) as pbm_allocated, ifnull(pbm.amount_available, 0) as pbm_available, ifnull(pbm.amount_commited, 0) as pbm_commited, ifnull(pbm.amount_blocked, 0) as pbm_amount_to_be_commited, ifnull(pbm.amount_used_additionally, 0) as amount_used_additionally, pbmms.month_name, ifnull( pbmms.amount_allocated, 0) as pbmms_amount_allocated, ifnull( SUM(pbe.spend_amount) over(partition by pbmms.id, pbe.budget_master_monthly_split_id), 0 ) as monthly_spent")
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_master_monthly_split pbmms', 'pbmms.procurement_budget_master_id = pbm.id')
      ->join('procurement_budget_expense pbe', 'pbe.budget_master_monthly_split_id = pbmms.id', 'left')
      ->where('pbm.id', $procurement_budget_master_id)
      ->get()->result();
  }

  function submit_edit() {
   

    $inputs= $this->input->post();
    $budget_year= $inputs['budget_year'];
    $budget_year_name= $inputs['budget_year_name'];
    $category_name= $inputs['category_name'];
    $desc= $inputs['description'];
       // description for audit log history
       $master= $this->db->select("ifnull(TRIM(pbm.description), '') as description, ifnull(pbm.amount_allocated, 0) as amount_allocated, pbm.created_on, ifnull(TRIM(concat(sm.first_name, ' ', ifnull(sm.last_name, ''))), 'Admin') as created_by")
       ->join('staff_master sm', 'sm.id = pbm.created_by', 'left')
       ->where('pbm.id', $inputs['edit_budget_master_id'])
       ->get('procurement_budget_master pbm')->row();
     if(!empty($master)) {
       $master->created_on= local_time($master->created_on, 'd-M-Y h:i A');
        $all_cat= $inputs['allocated_category'];
       $action_description= "A component ($category_name) from the budget ($budget_year_name) has been updated. The previous and updated details are as follows:
 
   Allocated Amount = $master->amount_allocated and updated value = $all_cat
   Created By = $master->created_by 
   Created On = $master->created_on 

   Previous Description:
   $master->description
   
   Updated Description: 
   $desc
 
 ";
     }
 
     $months= $this->db
       ->where('procurement_budget_master_id', $inputs['edit_budget_master_id'])
       ->get('procurement_budget_master_monthly_split')->result();
 
     if(!empty($months)) {
       foreach($months as $key => $val) {
        $upd_mon= $inputs['months'][$val->id];
         $action_description .= "The allocation for $val->month_name has been updated:

Previous Allocated Amount: $val->amount_allocated
Updated Allocated Amount: $upd_mon";
       }
     }

    //  echo '<pre>'; print_r($action_description); die();


    if(!empty($inputs['months'])) {
      foreach($inputs['months'] as $month_id => $month_allocated) {
        $month_update[]= array(
          'id' => $month_id,
          'amount_allocated' => $month_allocated
        );
      }
    }
    $this->db->trans_start();
      $this->db->where('id', $inputs['edit_budget_master_id'])->update('procurement_budget_master', array('amount_allocated' => $inputs['allocated_category'], 'description' => $inputs['description']));
      if(!empty($month_update))
        $this->db->update_batch('procurement_budget_master_monthly_split', $month_update, 'id');

      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Category Updated in Budget',
          'action_description' => $action_description,
          'action_table' => 'procurement_budget_master',
          'source_id' => $inputs['edit_budget_master_id']
        );
        if(!empty($action_description))
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }

    $this->db->trans_complete();


    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();
  }

  function delete_category_from_budget_master() {
    // echo '<pre>'; print_r($_POST); die();
    $procurement_budget_master_id= $this->input->post('procurement_budget_master_id');
    $budget_year= $this->input->post('budget_year');;
    $budget_year_name= $this->input->post('budget_year_name');;
    $category_name= $this->input->post('category_name');;

    // description for audit log history
    $master= $this->db->select("ifnull(pbm.amount_allocated, 0) as amount_allocated, pbm.created_on, ifnull(TRIM(concat(sm.first_name, ' ', ifnull(sm.last_name, ''))), 'Admin') as created_by")
      ->join('staff_master sm', 'sm.id = pbm.created_by', 'left')
      ->where('pbm.id', $procurement_budget_master_id)
      ->get('procurement_budget_master pbm')->row();
    if(!empty($master)) {
      $master->created_on= local_time($master->created_on, 'd-M-Y h:i A');

      $action_description= "A component ($category_name) has been removed from the budget ($budget_year_name). The details of removed component and months were as follows.

  Allocated Amount = $master->amount_allocated 
  Created By = $master->created_by 
  Created On = $master->created_on 

";
    }

    $months= $this->db
      ->where('procurement_budget_master_id', $procurement_budget_master_id)
      ->get('procurement_budget_master_monthly_split')->result();

    if(!empty($months)) {
      foreach($months as $key => $val) {
        $action_description .= " $val->month_name = $val->amount_allocated 
";
      }
    }

    // echo '<pre>'; print_r($action_description); die();


    $this->db->trans_start();
    $this->db->where('id', $procurement_budget_master_id)->delete('procurement_budget_master');
    $this->db->where('procurement_budget_master_id', $procurement_budget_master_id)->delete('procurement_budget_master_monthly_split');

    if($this->db->trans_status()) {
      $data_audit_log= array(
        'action_on' => date('Y-m-d H:i:s'),
        'action_by' => $this->authorization->getAvatarStakeHolderId(),
        'action_type' => 'Component Removed from Budget',
        'action_description' => $action_description,
        'action_table' => 'procurement_budget_master',
        'source_id' => $procurement_budget_master_id // We are storing table_id even if the row is deleted from the table Because we can still fetch some details from history (i.e. procurement_budget_audit_log) table when the first time it was added into this budget year
      );
      if(!empty($action_description))
      $this->db->insert('procurement_budget_audit_log', $data_audit_log);
    }

  $this->db->trans_complete();


  if(! $this->db->trans_status()) {
    $this->db->trans_rollback();
  }
  return $this->db->trans_status();
    
  }

  function approve_or_sendForModify() {
    // history add also
    $approver_comments= $this->input->post('approver_comments');
    $response_type= $this->input->post('response_type');;
    $response_type= ucwords( str_replace('_', ' ', $response_type) );
    $budget_year_id= $this->input->post('budget_year_id');;
    $budget_year_name= $this->input->post('budget_year_name');;

    $approve_data= array(
      'budget_year_id' => $budget_year_id,
      'status' => $response_type,
      'comments' => $approver_comments,
      'created_by' => $this->authorization->getAvatarStakeHolderId()
    );

    $this->db->trans_start();
      $this->db->where('id', $budget_year_id)->update('procurement_budget_year', [ 'status' => $response_type, 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()]);
      $this->db->insert('procurement_budget_approval_comments', $approve_data);
      $id= $this->db->insert_id();
      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Budget Status Changed',
          'action_description' => "A budget, identified as $budget_year_name, has been transitioned to the $response_type state.",
          'action_table' => 'procurement_budget_approval_comments',
          'source_id' => $id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();

  }

  function get_previous_comments() {
    $budget_year_id= $this->input->post('year_id');
    return $this->db_readonly->select("ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff, pbac.*")
        ->join('staff_master sm', 'sm.id = pbac.created_by', 'left')
        ->where('pbac.budget_year_id', $budget_year_id)
        ->order_by('id, created_on')
        ->get('procurement_budget_approval_comments pbac')->result();
  }

  public function close_budget_or_finish() {
    $inputs= $this->input->post();
    $this->db->trans_start();
      $this->db->where('id', $inputs['year_id'])->update('procurement_budget_year', ['status' => 'Finished', 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()]);
        // histoy
        if($this->db->trans_status()) {
          $sts= 'Finished';
          $year= $inputs['year_name'];
          $data_audit_log= array(
            'action_on' => date('Y-m-d H:i:s'),
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'action_type' => 'Status Changed',
            'action_description' => "The budget ( i.e. $year ) has been moved to '$sts' status.",
            'action_table' => 'procurement_budget_year',
            'source_id' => $inputs['year_id']
          );
          $this->db->insert('procurement_budget_audit_log', $data_audit_log);
        }
    $this->db->trans_complete();
    return $this->db->trans_status();
  }

  public function get_budget_audit_log_year_wise($budget_year_id) {
    // Step 1: budget year related log ids
    $budgetYearsAuditLogsObj= $this->db_readonly->select('id')
      ->where('source_id', $budget_year_id)
      ->where('action_table', 'procurement_budget_year')
      ->get('procurement_budget_audit_log')->result();

    $auditLogIdsArr= [];
    if(!empty($budgetYearsAuditLogsObj)) {
      $auditLogIdsArr= $this->__push_log_ids_into_variable_array($auditLogIdsArr, $budgetYearsAuditLogsObj);
    }

     // Step 1: budget master related log ids
     $budgetMastersAuditLogsObj= $this->db_readonly->select('pba.id')
      ->join('procurement_budget_audit_log pba', "pba.source_id = pbm.id AND pba.action_table = 'procurement_budget_master'")
      ->where('pbm.budget_year_id', $budget_year_id)
      ->where('pba.action_table', 'procurement_budget_master')
      ->get('procurement_budget_master pbm')->result();

    if(!empty($budgetMastersAuditLogsObj)) {
      $auditLogIdsArr= $this->__push_log_ids_into_variable_array($auditLogIdsArr, $budgetMastersAuditLogsObj);
    }

    // Step 2: expenses related log ids
    $expensesAuditLogsObj= $this->db_readonly->select('pba.id')
    ->from('procurement_budget_master pbm')
    ->join('procurement_budget_expense pbe', "pbe.procurement_budget_master_id = pbm.id")
    ->join('procurement_budget_audit_log pba', "pba.source_id = pbe.id AND pba.action_table = 'procurement_budget_expense'")
    ->where('pbm.budget_year_id', $budget_year_id)
    ->where('pba.action_table', 'procurement_budget_expense')
    ->get()->result();

    if(!empty($expensesAuditLogsObj)) {
      $auditLogIdsArr= $this->__push_log_ids_into_variable_array($auditLogIdsArr, $expensesAuditLogsObj);
    }

    $log['budget_creations_log']= [];
    if(!empty($auditLogIdsArr)) {
      $log['budget_creations_log']= $this->db_readonly->select("ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff, pbal.*")->join('staff_master sm', 'sm.id = pbal.action_by', 'left')->where_in('pbal.id', $auditLogIdsArr)->order_by('pbal.action_on', 'desc')->get('procurement_budget_audit_log pbal')->result();
    }

    // Step 3: Approver's comments
    $log['budget_approvals_log']= $this->db_readonly->select("ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff, pbac.*")->join('staff_master sm', 'sm.id = pbac.created_by', 'left')->where('pbac.budget_year_id', $budget_year_id)->order_by('pbac.created_on', 'desc')->get('procurement_budget_approval_comments pbac')->result();

    return $log;
  }

  private function __push_log_ids_into_variable_array($varibaleArr, $sorceObj) {
    foreach($sorceObj as $key => $val) {
      $varibaleArr[]= $val->id;
    }
    return $varibaleArr;
  }

  function activate_budget($budget_year_id, $year) {
    $isActiveExist= $this->db->where('status', 'Active')->get('procurement_budget_year')->row();
    if(!empty($isActiveExist)) {
      return ['status' => '-1', 'errorMessage' => "A budget i.e. $isActiveExist->year is already active. Reach out to your budget Administrator to move it into 'Finished' state and visit again"];
    }
    $this->db->trans_start();
      $this->db->where('id', $budget_year_id)->update('procurement_budget_year', array('status' => 'Active', 'last_modified_by' => $this->authorization->getAvatarStakeHolderId()));
      if($this->db->trans_status()) {
        $sts= 'Active';
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Status Changed',
          'action_description' => "The budget ( i.e. $year ) has been moved to '$sts' state.",
          'action_table' => 'procurement_budget_year',
          'source_id' => $budget_year_id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
      $this->db->trans_complete();
      if(! $this->db->trans_status()) {
        $this->db->trans_rollback();
        return ['status' => '-1', 'errorMessage' => "Something went wrong"];
      }
      return ['status' => '1', 'errorMessage' => "Successfully budget activated."];
  }

  function year_and_approver_details($year, $approver) {
    $dollar['year'] = $this->db_readonly->where('id', $year)->get('procurement_budget_year')->row();
    $dollar['approver'] = $this->db_readonly->select("sm.id, ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff, ifnull(u.email, '') as email")
    ->join('avatar a', 'a.stakeholder_id = sm.id')
    ->join('users u', 'u.id = a.user_id')
    ->where('sm.id', $approver)
    ->where('a.avatar_type', 4)
    ->get('staff_master sm')->row();
    
    $dollar['approval_request_sender'] = $this->db_readonly->select("ifnull(dsg.designation, 'NA Designation') as designation, ifnull(dpt.department, 'NA Department') as department, sm.id, ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff")
      ->join('staff_departments dpt', 'sm.department = dpt.id', 'left')
      ->join('staff_designations dsg', 'sm.designation = dsg.id', 'left')
      ->where('sm.id', $this->authorization->getAvatarStakeHolderId())->get('staff_master sm')->row();
    if(empty( $dollar['approval_request_sender'])) {
      $dummy= new stdClass();
      $dummy->designation= 'Administrator';
      $dummy->department= 'Budget Department';
      $dummy->staff = 'Admin';
      $dollar['approval_request_sender']= $dummy;
    }
    return $dollar;
  }

  public function get_circular_data($name){
    return $this->db_readonly->where('name', $name)->get('email_template')->row();
  }

  function get_procurement_categories() {
    return $this->db_readonly->where('status', 1)->get('procurement_itemmaster_category')->result();
  }

  function get_expenseCategories() {
    return $this->db_readonly->select("ec.*")
    ->join('expense_sub_category esc', 'ec.id = esc.cat_id')
    ->group_by('ec.id')
    ->get('expense_category ec')
    ->result();
  }

  function update_status_of_release_for_allocation() {
    $inputs= $this->input->post();
    $data= array(
      'release_for_allocation_status' => $inputs['status']
    );
    $this->db->trans_start();
      $this->db->where('id', $inputs['year_id'])->update('procurement_budget_year', $data);
      $this->db->where('budget_year_id', $inputs['year_id'])->update('procurement_budget_master', array('approval_status' => 'Approved', 'approval_comments' => 'It is approved.'));
      if($this->db->trans_status()) {
        $year= $inputs['year_name'];
        $sts= 'released for allocation';
        if($inputs['status'] == 'Stop') {
          $this->db->where('id', $inputs['year_id'])->update('procurement_budget_year', array('status' => 'In Review'));
          $sts= 'stoped for allocation';
        }
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Status Changed',
          'action_description' => "A budget ( i.e. $year ) has been $sts",
          'action_table' => 'procurement_budget_year',
          'source_id' => $inputs['year_id']
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();

  }

  function get_procurement_budget_categories_loggedIn_wise($year_id) {
    
    $categoriesJson= $this->db_readonly->where('id', $year_id)->get('procurement_budget_year')->row();
    if(empty($categoriesJson)) {
      // echo '<pre>Ap '; print_r($this->db_readonly->last_query($categoriesJson)); die();
      return [];
    }
    $categories_arr= json_decode($categoriesJson->budget_categories_json);
    if($categories_arr[0] == 'all') {
      $categories_arr= [];
      $x= $this->db_readonly->where('status', 1)->get('procurement_itemmaster_category')->result();
      if(!empty($x)) {
        foreach($x as $key => $val) {
          $categories_arr[]= $val->id;
        }
      }
    }
    
    if(!$this->authorization->isSuperAdmin()) {
      $loggedInId= $this->authorization->getAvatarStakeHolderId();
      $allCategories= $this->__query_to_get_category_and_months_user_wise($categories_arr, $year_id, $loggedInId);
    } else {
      $allCategories= $this->__query_to_get_categories($categories_arr, $year_id);
      // echo '<pre>'; print_r($allCategories); die();
    }

   
    if(empty($allCategories)) {
      return [];
    }

    foreach($allCategories as $key => $val) {
      if(false && trim($val->procurement_budget_master_id) == '-') {
        $val->isAllocated= 0;
        $val->monthsSplittingAmount= [];
        $val->categoriesSplittingAmount= [];
      } else {
        $val->isAllocated= 1;
        // Months de-structuring
        $monthsArr= explode(',', $val->monthly_split);
        $monthsSpliting= [];
        if(!empty($monthsArr)) {
          foreach($monthsArr as $keyMonth => $valMonth) {
            $month_value= explode(':', $valMonth);
            $newObj= new stdClass();
            $newObj->month_name= $month_value[0];
            $newObj->month_allocated= $month_value[1];
            $monthsSpliting[]= $newObj;
          }
        }
        // Category de-structuring
        $categoriesArr= explode(',', $val->category_split);
        $categoriesSplitting= [];
        if(!empty($categoriesArr)) {
          foreach($categoriesArr as $keyCategory => $valCategory) {
            $category_value= explode(':', $valCategory);
            $newObj= new stdClass();
            $newObj->category_name= $category_value[0];
            $newObj->category_allocated= $category_value[1];
            $categoriesSplitting[]= $newObj;
          }
        }
        $val->monthsSplittingAmount= $monthsSpliting;
        $val->categoriesSplittingAmount= $categoriesSplitting;
      }
    }
    // echo '<pre>'; print_r($allCategories); die();
   return $allCategories;
  }

  private function __query_to_get_categories($categories_arr, $year_id) {
    $this->db_readonly->select("
      ifnull(pbm.id, '-') AS procurement_budget_master_id,
      pic.id AS catId,
      pic.category_name AS catName,
      IFNULL(pbm.amount_allocated, 0) AS amount_allocated,
      IFNULL(pbm.amount_available, 0) AS amount_available,
      IFNULL(pbm.amount_commited, 0) AS amount_commited,
      IFNULL(pbm.amount_blocked, 0) AS amount_to_be_commited,
      IFNULL(pbm.amount_used_additionally, 0) AS amount_used_additionally,
      IFNULL(pbm.description, '-') AS description,
      -- Concatenate monthly split details
      GROUP_CONCAT(
          CONCAT(
              IFNULL(pbms.month_name, '-'), ':',
              IFNULL(pbms.amount_allocated, '-'), ':',
              IFNULL(pbms.id, '-')
          ) 
          ORDER BY pbms.id 
          SEPARATOR '; '
      ) AS monthly_split,
      -- Concatenate category split details
      GROUP_CONCAT(
          CONCAT(
              IFNULL(pbmiws.proc_im_subcategory_id, '-'), ':',
              IFNULL(pbmiws.amount_allocated, '-'), ':',
              IFNULL(pis.subcategory_name, '-'), ':',
              IFNULL(pbmiws.proc_im_items_id, '-'), ':',
              IFNULL(pii.item_name, '-')
              ) 
              ORDER BY pbmiws.proc_im_subcategory_id 
              SEPARATOR '; '
          ) AS category_split
    ");
    $this->db_readonly->from('procurement_itemmaster_category pic');
    $this->db_readonly->join('procurement_budget_master pbm', 'pbm.budget_category_id = pic.id', 'left'); // Changed from LEFT JOIN to INNER JOIN
    $this->db_readonly->join('procurement_budget_master_monthly_split pbms', 'pbms.procurement_budget_master_id = pbm.id', 'left'); // Left join for monthly split
    $this->db_readonly->join('procurement_budget_master_item_wise_split pbmiws', 'pbmiws.procurement_budget_master_id = pbm.id', 'left'); // Left join for item-wise split
    $this->db_readonly->join('procurement_itemmaster_subcategory pis', 'pis.id = pbmiws.proc_im_subcategory_id', 'left');
    $this->db_readonly->join('procurement_itemmaster_items pii', 'pii.id = pbmiws.proc_im_items_id', 'left');
    $this->db_readonly->where_in('pic.id', $categories_arr);
    $this->db_readonly->where("(pbm.budget_year_id IS NULL OR pbm.budget_year_id = '$year_id')");
    $this->db_readonly->group_by('pic.id, pic.category_name, pbm.id');
    return $this->db_readonly->get()->result();

  }

  private function __query_to_get_category_and_months_user_wise($categories_arr, $year_id, $loggedInId) {
    $this->db_readonly->select("
        ifnull(pbm.id, '-') AS procurement_budget_master_id,
        pic.id AS catId,
        pic.category_name AS catName,
        IFNULL(pbm.amount_allocated, 0) AS amount_allocated,
        IFNULL(pbm.amount_available, 0) AS amount_available,
        IFNULL(pbm.amount_commited, 0) AS amount_commited,
        IFNULL(pbm.amount_blocked, 0) AS amount_to_be_commited,
        IFNULL(pbm.amount_used_additionally, 0) AS amount_used_additionally,
        IFNULL(pbm.description, '-') AS description,
        -- Monthly split details concatenated
        GROUP_CONCAT(
            CONCAT(
                IFNULL(pbms.month_name, '-'), ':',
                IFNULL(pbms.amount_allocated, '-'), ':',
                IFNULL(pbms.id, '-')
            ) 
            ORDER BY pbms.id 
            SEPARATOR '; '
        ) AS monthly_split,
        -- Category split details concatenated
        GROUP_CONCAT(
            CONCAT(
                IFNULL(pbmiws.proc_im_subcategory_id, '-'), ':',
                IFNULL(pbmiws.amount_allocated, '-'), ':',
                IFNULL(pis.subcategory_name, '-'), ':',
                IFNULL(pbmiws.proc_im_items_id, '-'), ':',
                IFNULL(pii.item_name, '-')
            ) 
            ORDER BY pbmiws.proc_im_subcategory_id 
            SEPARATOR '; '
        ) AS category_split
    ");
    $this->db_readonly->from('procurement_itemmaster_category pic');
    $this->db_readonly->join('staff_departments sd', 'sd.id = pic.staff_department_id');
    $this->db_readonly->join('procurement_budget_master pbm', 'pbm.budget_category_id = pic.id', 'left'); // Changed to INNER JOIN
    $this->db_readonly->join('procurement_budget_master_monthly_split pbms', 'pbms.procurement_budget_master_id = pbm.id', 'left'); // Left join for monthly split
    $this->db_readonly->join('procurement_budget_master_item_wise_split pbmiws', 'pbmiws.procurement_budget_master_id = pbm.id', 'left'); // Left join for item-wise split
    $this->db_readonly->join('procurement_itemmaster_subcategory pis', 'pis.id = pbmiws.proc_im_subcategory_id', 'left');
    $this->db_readonly->join('procurement_itemmaster_items pii', 'pii.id = pbmiws.proc_im_items_id', 'left');
    $this->db_readonly->where('sd.head_of_department_id', $loggedInId); // Filter by logged-in user
    $this->db_readonly->where_in('pic.id', $categories_arr); // Filter by category IDs
    $this->db_readonly->where("(pbm.budget_year_id IS NULL OR pbm.budget_year_id = '$year_id')"); // Filter by budget year
    $this->db_readonly->group_by('pic.id, pic.category_name, pbm.id'); // Group by relevant fields
    return $this->db_readonly->get()->result();
  }

  function get_procurement_budget_categories_loggedIn_wise_v2($year_id) {
    $isAccountant= $this->db_readonly->select('approver_id')->where('id', $year_id)->get('procurement_budget_year')->row();
    $accountantId= 0;
    if(!empty($isAccountant)) {
      $accountantId= $isAccountant->approver_id;
    }
    
    $categoriesJson= $this->db_readonly->where('id', $year_id)->get('procurement_budget_year')->row();
    // 
    if(empty($categoriesJson)) {
      
      return [];
    }
    $categories_arr= json_decode($categoriesJson->budget_categories_json);
    
    $this->db_readonly
        ->select(
            "pbm.budget_category_id, 
            '$accountantId' as budget_owner_id, 
            pbm.lock_status, 
            pbm.lock_comments, 
            ifnull(pbm.approval_status, 'Not Respond') as approval_status, 
            ifnull(pbm.approval_comments, '-') as approval_comments, 
            ifnull(pbm.provisional_amount, 0) as provisional_amount, 
            pbm.amount_allocated, 
            ifnull(pbm.description, '-') as description, 
            ifnull(pbm.category_split_type, 'Category') as category_split_type, 
            pbm.created_on AS pbm_created_on, 
            pbm.modified_on AS pbm_modified_on, 
            IFNULL(CONCAT(smCreater.first_name, ' ', IFNULL(smCreater.last_name, '')), '-') AS pbm_created_by, 
            IFNULL(CONCAT(smModifier.first_name, ' ', IFNULL(smModifier.last_name, '')), '-') AS pbm_modified_by, 
            IFNULL(CONCAT(sm.first_name, ' ', IFNULL(sm.last_name, '')), '-') AS hod, 
            IFNULL(sd.department, '-') AS department, 
            ec.category_name,
            '$year_id' as year_id, 
            pbm.id AS pbm_id"
        )
        ->from('procurement_budget_master pbm')
        ->join('expense_category ec', 'pbm.budget_category_id = ec.id')
        ->join('staff_master smCreater', 'smCreater.id = pbm.created_by', 'left')
        ->join('staff_master smModifier', 'smModifier.id = pbm.modified_by', 'left')
        ->join('staff_departments sd', 'sd.id = ec.department_id', 'left')
        ->join('staff_master sm', 'sm.id = sd.head_of_department_id', 'left')
        ->where('pbm.budget_year_id', $year_id)
        ->where_in('pbm.budget_category_id', $categories_arr);

    
    if (!$this->authorization->isSuperAdmin() && ($accountantId != $this->authorization->getAvatarStakeHolderId()) && !$this->authorization->isAuthorized('PROCUREMENT_BUDGET.CFO_CEO_APPROVER')) { // If not superadmin and not accountant and not CFO
        $loggedInId = $this->authorization->getAvatarStakeHolderId();
        $this->db_readonly->where('sd.head_of_department_id', $loggedInId);
        
    }

    $allCategories = $this->db_readonly->get()->result();

    // echo '<pre>'; print_r($this->db_readonly->last_query($categoriesJson)); die();

    if(empty($allCategories)) {
      return [];
    }

    foreach($allCategories as $key => $val) {
      if($val->pbm_created_on) {
        $val->pbm_created_on = local_time($val->pbm_created_on, 'd-M-Y h:i A');
      }
      if($val->pbm_modified_on) {
        $val->pbm_modified_on = local_time($val->pbm_modified_on, 'd-M-Y h:i A');
      }
    }
    // echo '<pre>'; print_r($allCategories); die();
   return $allCategories;
  }
  
  function get_subcategories_splitting_loggedIn_wise() {
    $inputs= $this->input->post();
    // echo '<pre>'; print_r($inputs); die();
    $category_id= $inputs['category_id'];
    $budget_year_id= $inputs['budget_year_id'];
    if(empty($category_id) || empty($budget_year_id)) {
      return false;
    }
    $cats_json= $this->db_readonly->select('budget_categories_json')->where('id', $budget_year_id)->get('procurement_budget_year')->row();
    
    if(empty($cats_json)) {
      return false;
    }
    $cats_arr= json_decode($cats_json->budget_categories_json);
    if(empty($cats_arr) || !in_array($category_id, $cats_arr)) {
      return false;
    }

    $cats_arr= [$category_id];

    $this->db_readonly->select("ec.id, ec.category_name, ifnull( group_concat(ifnull(esc.sub_category, '-')), '-') as subcategories_name, ifnull( group_concat(ifnull(esc.id, '-')), '-') as subcategories_id")
        ->from('expense_category ec')
        ->join('staff_departments sd', 'sd.id = ec.department_id')
        ->join('expense_sub_category esc', 'esc.cat_id = ec.id', 'left')
        ->where_in('ec.id', $cats_arr);
    if(!$this->authorization->isSuperAdmin()) {
      $this->db_readonly->where("sd.head_of_department_id", $this->authorization->getAvatarStakeHolderId());
    }
    $this->db_readonly->group_by('ec.id');
    $categories= $this->db_readonly->get()->result();

    if(!empty($categories)) {
      foreach($categories as $key => $val) {
        if($val->subcategories_id != '-') {
          $sub_cats= explode(',', $val->subcategories_name);
          $sub_cat_ids= explode(',', $val->subcategories_id);
          $arr_storer= [];
          if(!empty($sub_cats)) {
            foreach($sub_cats as $keySub => $valSub) {
              $obj= new stdClass();
              $obj->subCatId= $sub_cat_ids[$keySub];
              $obj->subCatName= $valSub;
              $arr_storer[]= $obj;
            }
          }
          $val->subcats= $arr_storer;
        } else {
          $val->subcats= [];
        }
      }
    }

    return $categories;
  }

  function get_monthly_report() {
    $inputs= $this->input->post();
    $budget_year_id= $inputs['budget_year_id'];
    $yearInformation= $this->__get_year_details($budget_year_id);

    $categoriesMonths = $this->db_readonly->select("
          ec.category_name, 
          MAX(pbm.amount_allocated) as category_month_allocated, 
          IFNULL(GROUP_CONCAT(IFNULL(pbs.month_name, '-')), '-') as month_names, 
          IFNULL(GROUP_CONCAT(IFNULL(pbs.allocated_amount, 0)), 0) as amount_allocated
      ")
      ->from('procurement_budget_master pbm')
      ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
      ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
      ->where('pbm.budget_year_id', $yearInformation->id)
      ->group_by(['pbm.budget_category_id', 'ec.category_name'])
      ->get()
      ->result();



// Process each object in the array
foreach ($categoriesMonths as $item) {
  // Initialize result array
  $categoryWiseMonthlyAllocated = [];
    $months = explode(',', $item->month_names); // Split month_names into an array
    $amounts = explode(',', $item->amount_allocated); // Split amount_allocated into an array

    // Initialize category if not already set
    if (!isset($categoryWiseMonthlyAllocated[$item->category_name])) {
        $categoryWiseMonthlyAllocated = [];
    }

    // Combine months and amounts for the category
    foreach ($months as $index => $month) {
        if (!isset($categoryWiseMonthlyAllocated[$month])) {
            $categoryWiseMonthlyAllocated[$month] = 0; // Initialize if not already set
        }
        $categoryWiseMonthlyAllocated[$month] += (float)$amounts[$index]; // Add amount for the month
    }
    $item->monthly= $categoryWiseMonthlyAllocated;
}


        

    // if(!empty($categoriesMonths)) {
    //   foreach($categoriesMonths as $key => $val) {
    //     $months_names= [];
    //     $months_amounts= [];
    //     if($val->month_names && $val->month_names != '-') {
    //       $names_arr= explode(',', $val->month_names);
    //       foreach($names_arr as $k => $v) {
    //         $months_names[]= $v;
    //       }
    //       $val->months_names= $months_names;
    //     }
    //     if($val->amount_allocated && $val->amount_allocated != '-') {
    //       $amounts_arr= explode(',', $val->amount_allocated);
    //       foreach($amounts_arr as $k => $v) {
    //         $months_amounts[]= $v;
    //       }
    //       $val->months_amounts= $months_amounts;
    //     }
    //   }
    // }

    // echo '<pre>'; print_r($categoriesMonths); die();

    $reports['yearInformation'] = $yearInformation;
    $reports['categoriesMonths'] = $categoriesMonths;

    return $reports;
  }

  private function __get_year_details($budget_year_id) {
    $x= $this->db_readonly->select("pby.id, pby.year, pby.start_month, pby.end_month, pby.status, pby.created_on, pby.last_modified_on, pby.release_for_allocation_status, ifnull(concat(smc.first_name, ' ', ifnull(smc.last_name, '')), 'Admin') as creater, ifnull(concat(smm.first_name, ' ', ifnull(smm.last_name, '')), 'Admin') as last_modifier, pby.budget_categories_json, ifnull(sum(ifnull(pbm.amount_allocated, 0)), 0) as total, ifnull(sum(ifnull(pbm.amount_available, 0)), 0) as available")
      ->join('procurement_budget_master pbm', 'pbm.budget_year_id = pby.id', 'left')
      ->join('staff_master smc', 'smc.id = pby.created_by', 'left')
      ->join('staff_master smm', 'smm.id = pby.last_modified_by', 'left')
      ->where('pby.id', $budget_year_id)
      ->group_by('pby.id')
      ->get('procurement_budget_year pby')->row();

    $x->created_on= local_time($x->created_on, 'd-M-Y');
    $x->last_modified_on= local_time($x->last_modified_on, 'd-M-Y');

    return $x;
  }

  function get_categories_and_months_for_edit() {
    $budget_master_id= $this->input->post('budget_master_id');

    $category= $this->db_readonly->select("pbm.id, ifnull(pbm.provisional_amount, 0) as provisional_amount, pbm.budget_category_id, ifnull(pbm.description, '') as description, ec.category_name, ifnull(pbm.amount_allocated, 0) as amount_allocated, ifnull(pbm.amount_available, 0) as amount_available")
    ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
    ->where('pbm.id', $budget_master_id)
    ->get('procurement_budget_master pbm')->row();

    if(empty($category)) {
      return [ 'status' => -1, 'message' => 'Category details not found', 'category' => [], 'budgetSplit' => []];
    }

    $budgetSplit= $this->db_readonly->select("pbs.id as budgetSplit_id, pbs.allocated_amount, ifnull(pbs.description, '') as description, esc.sub_category, pbs.expense_subcategory_id, pbs.month_name")
    ->join('expense_sub_category esc', 'pbs.expense_subcategory_id = esc.id')
    ->where('pbs.procurement_budget_master_id', $budget_master_id)
    ->get('procurement_budget_split pbs')->result();

    return [ 'status' => 1, 'message' => 'All the necessary data found', 'category' => $category, 'budgetSplit' => $budgetSplit];
  }

  function save_single_month_edit() {
    $amount_allocated_single_month= $this->input->post('amount_allocated_single_month');
    $description_single_month= $this->input->post('description_single_month');
    $single_month_id= $this->input->post('single_month_id');

    // echo '<pre>'; print_r($_POST); die();

    $month_data= array(
      'allocated_amount' => $amount_allocated_single_month,
      'description' => $description_single_month
    );

    $this->db->trans_start();
      $this->db->where('id', $single_month_id)->update('procurement_budget_split', $month_data);
      if($this->db->trans_status()) {
          $data_audit_log= array(
            'action_on' => date('Y-m-d H:i:s'),
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'action_type' => 'Month Details Updated',
            'action_description' => "The details of the month have been updated.",
            'action_table' => 'procurement_budget_split',
            'source_id' => $single_month_id
          );
          $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }

    $this->db->trans_complete();
    if(!$this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();
  }

  function save_single_subcategory_edit() {
    $amount_allocated_single_subcategory= $this->input->post('amount_allocated_single_subcategory');
    $description_single_subcategory= $this->input->post('description_single_subcategory');
    $single_subcategory_id= $this->input->post('single_subcategory_id');

    // echo '<pre>'; print_r($_POST); die();

    $subcategory_data= array(
      'amount_allocated' => $amount_allocated_single_subcategory,
      'description' => $description_single_subcategory
    );

    $this->db->trans_start();
      $this->db->where('id', $single_subcategory_id)->update('procurement_budget_master_category_wise_split', $subcategory_data);
      if($this->db->trans_status()) {
          $data_audit_log= array(
            'action_on' => date('Y-m-d H:i:s'),
            'action_by' => $this->authorization->getAvatarStakeHolderId(),
            'action_type' => 'subcategory Details Updated',
            'action_description' => "The details of the subcategory have been updated.",
            'action_table' => 'procurement_budget_master_subcategoryly_split',
            'source_id' => $single_subcategory_id
          );
          $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }

    $this->db->trans_complete();
    if(!$this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();
  }

  function update_budget_allocation() {
    $input= $this->input->post();
    // echo '<pre>'; print_r($input); die();
    $category_id_edit= $this->input->post('category_id_edit');
    $allocated_to_category_edit= $this->input->post('allocated_to_category_edit');
    $description_edit= $this->input->post('description_edit');

    $data= array(
      'description' => $description_edit,
      'amount_allocated' => $allocated_to_category_edit,
      'amount_available' => $allocated_to_category_edit
    );

    $this->db->trans_start();

    $this->db->where('id', $category_id_edit)->update('procurement_budget_master', $data);

    $months_edit= $input['months_edit'];
    if(!empty($months_edit)) {
      foreach($months_edit as $key => $val) {
        $mth_upd[]= array(
          'id' => $key,
          'allocated_amount' => $val,
        );
      }
      $this->db->update_batch('procurement_budget_split', $mth_upd, 'id');
    }
    
    $this->db->trans_complete();


    return $this->db->trans_status();
  }

  function get_subcategories_not_added_yet() {
    
    $budget_year_id= $this->input->post('budget_year_id');
    $budget_category_id= $this->input->post('budget_category_id');
    $budget_master_id= $this->input->post('budget_master_id');

    $used_subs= $this->db_readonly->select("group_concat(ifnull(subcategory_id, '0')) as subs_str")
        ->where('procurement_budget_master_id', $budget_master_id)
        ->where('subcategory_type', 'Subcategory')
        ->group_by('procurement_budget_master_id')
        ->get('procurement_budget_master_category_wise_split')->row();

        // echo '<pre>'; print_r($this->db_readonly->last_query($used_subs)); die();
        

    $used_subs_arr= [];
    if(!empty($used_subs))
      $used_subs_arr= explode(',', $used_subs->subs_str);

      
      
    return $this->db_readonly->where('cat_id', $budget_category_id)->where_not_in("id", $used_subs_arr)->get('expense_sub_category')->result();
    // $all_subs_arr= [];
    // if(!empty($all_subs))
    //   $all_subs_arr= explode(',', $all_subs->all_subs_str);

      
    //   $unused_subs_arr = array_diff($all_subs_arr, $used_subs_arr);

    // if(empty($unused_subs_arr)) {
    //   return [];
    // }

    // return $this->db_readonly->where_in('id', $unused_subs_arr)->get('expense_sub_category')->result();
  }

  function save_other_subcategory_split() {
    $inputs= $this->input->post();

    $other_subcategory_type= 'Subcategory';
    $other_subcategory_id= isset($inputs['other_subcategory_id']) ? $inputs['other_subcategory_id'] : NULL;
    if($other_subcategory_id == 'Other') {
      $other_subcategory_id= NULL;
      $other_subcategory_type= 'Other';
    }
    $other_name_of_others= isset($inputs['other_name_of_others']) ? $inputs['other_name_of_others'] : NULL;
    $other_description= $inputs['other_description'];
    $other_allocated_amount= $inputs['other_allocated_amount'];
    $budget_master_id_edit= $inputs['budget_master_id_edit'];

    if($other_subcategory_id == 'Other') {
      $other_subcategory_id= NULL;
      $other_subcategory_type= 'Other';
    }

    $data= array(
      'procurement_budget_master_id' => $budget_master_id_edit,
      'amount_allocated' => $other_allocated_amount,
      'subcategory_type' => $other_subcategory_type,
      'description' => $other_description,
      'subcategory_id' => $other_subcategory_type == 'Subcategory' ? $other_subcategory_id : NULL,
      'other_subcategory_name' =>$other_subcategory_type == 'Subcategory' ? NULL : $other_name_of_others
    );

    $this->db->trans_start();
      $this->db->insert('procurement_budget_master_category_wise_split', $data);
      $id= $this->db->insert_id();
      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Subcategory Added',
          'action_description' => "A subcategory of type $other_subcategory_type has been added.",
          'action_table' => 'procurement_budget_master_subcategoryly_split',
          'source_id' => $id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
      return  $this->db->trans_status();
    }

    return $id;
  }

  function delete_a_category_from_a_year() {
    $inputs= $this->input->post();

    $budget_master_id= $inputs['budget_master_id'];
    $category_name= $inputs['category_name'];
    $year_name= $inputs['year_name'];

    $this->db->trans_start();
      $cat_id= $this->db->select('budget_category_id, budget_year_id')->where('id', $budget_master_id)->get('procurement_budget_master')->row();
      if(!empty($cat_id)) {
        // echo '<pre>cat_id'; print_r($cat_id);
        // Deleting category from the budget year as JSON
        $catJSONs= $this->db->select('budget_categories_json')->where('id', $cat_id->budget_year_id)->get('procurement_budget_year')->row();
        // echo '<pre>cat_id'; print_r($catJSONs);
        $JSON_arrayed_str= str_replace('{', '[', $catJSONs->budget_categories_json);
        $JSON_arrayed_str= str_replace('}', ']', $JSON_arrayed_str);
        // echo '<pre>cat_id'; print_r($JSON_arrayed_str);
        $catJSON= json_decode($JSON_arrayed_str, true);
        // echo '<pre>cat_id'; print_r($catJSON);
        if(!empty($catJSON)) {
          $catJSON= array_unique($catJSON); 
          $catJSON= array_keys(array_flip($catJSON));
          foreach($catJSON as $key => $val) {
            if($val == 0) {
              unset($catJSON[$key]);
            }
            else if($val == $cat_id->budget_category_id) {
              unset($catJSON[$key]);
            }
          }
          $catJSON= array_keys(array_flip($catJSON));
        }
        $catJSON= json_encode($catJSON);
        // echo '<pre>cat_id'; print_r($catJSON);
        $catJSON= str_replace(':', ",", $catJSON);
        $catJSON= str_replace('{', '[', $catJSON);
        $catJSON= str_replace('}', ']', $catJSON);
        // echo '<pre>'; print_r($catJSON); die();
        $this->db->where('id', $cat_id->budget_year_id)->update('procurement_budget_year', ['budget_categories_json' => $catJSON]);
      }

      $this->db->where('id', $budget_master_id)->delete('procurement_budget_master');
      $this->db->where('procurement_budget_master_id', $budget_master_id)->delete('procurement_budget_split');
      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Cmponent Removed',
          'action_description' => "A component ($category_name) from the budget ($year_name) has been deleted.",
          'action_table' => NULL,
          'source_id' => NULL
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }
    $this->db->trans_complete();
    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
    }

    return  $this->db->trans_status();
  }

  function details_of_a_budget_component() {
    $inputs= $this->input->post();

    $budget_master_id= $inputs['budget_master_id'];
    $year_id= $inputs['year_id'];
    
    $category= $this->db_readonly->select("pbm.id, ifnull(pbm.provisional_amount, 0) as provisional_amount, pbm.budget_category_id, ifnull(pbm.description, '-') as description, ec.category_name, ifnull(pbm.amount_allocated, 0) as amount_allocated, ifnull(pbm.amount_available, 0) as amount_available, ifnull(pbm.amount_commited, 0) as amount_commited, ifnull(pbm.amount_blocked, 0) as amount_to_be_commited, ifnull(pbm.amount_used_additionally, 0) as amount_used_additionally")
      ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
      ->where('pbm.id', $budget_master_id)
      ->get('procurement_budget_master pbm')->row();

    if(empty($category)) {
      return [ 'status' => -1, 'message' => 'Category details not found', 'category' => [], 'months' => [] ];
    }

    $budgetSplit= $this->db_readonly->select("pbs.id as budgetSplit_id, pbs.expense_subcategory_id, pbs.month_name, pbs.allocated_amount, ifnull(pbs.description, '-') as description, esc.sub_category")
      ->join('expense_sub_category esc', 'pbs.expense_subcategory_id = esc.id')
      ->where('pbs.procurement_budget_master_id', $budget_master_id)
      ->get('procurement_budget_split pbs')->result();

    $itemWiseChartData= $this->db_readonly->select("sum(pbs.allocated_amount) as allocated_amount, esc.sub_category, pbs.expense_subcategory_id")
      ->from('procurement_budget_split pbs')
      ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
      ->where('pbs.procurement_budget_master_id', $budget_master_id)
      ->group_by('pbs.expense_subcategory_id')
      ->get()->result();

    if(empty($budgetSplit)) {
      return [ 'status' => 1, 'message' => 'budgetSplit details not found', 'category' => $category, 'budgetSplit' => [], 'itemWiseChartData' => $itemWiseChartData, 'monthWiseChartData' => [] ];
    }

    $monthWiseChartData=$this->db_readonly->select("sum(pbs.allocated_amount) as allocated_amount, pbs.month_name")
      ->from('procurement_budget_split pbs')
      ->where('pbs.procurement_budget_master_id', $budget_master_id)
      ->group_by('pbs.month_name')
      ->get()->result();

    // echo '<pre>'; print_r($monthWiseChartData); die();

    return [ 'status' => 1, 'message' => 'All the necessary data found', 'category' => $category, 'budgetSplit' => $budgetSplit, 'itemWiseChartData' => $itemWiseChartData, 'monthWiseChartData' => $monthWiseChartData ];
  }

  function remove_single_month() {
    $id= $this->input->post('id');
    $this->db->where('id', $id)->delete('procurement_budget_split');
    return 1;
  }

  function remove_single_subcategory() {
    $id= $this->input->post('id');
    $this->db->where('id', $id)->delete('procurement_budget_master_category_wise_split');
    return 1;
  }

  function distribute_amounts() {
    $input= $this->input->post();
    // echo '<pre>'; print_r($input); die();

    

    $months_edit= $input['months_edit'];
    if(!empty($months_edit)) {
      foreach($months_edit as $key => $val) {
        $mth_upd[]= array(
          'id' => $key,
          'allocated_amount' => $val,
        );
      }
      $this->db->update_batch('procurement_budget_split', $mth_upd, 'id');
    }

    $this->db->where('id', $input['category_id_edit'])->update('procurement_budget_master', array('amount_allocated' => $input['allocated_to_category_edit'], 'amount_available' => $input['allocated_to_category_edit'], 'description' => $input['description_edit']));
    return 1;
  }

  function get_data_for_graphs() {
    $budget_year_id= $this->input->post('budget_year_id');
    // $budget_year= $this->db_readonly->where('id', $budget_year_id)->get('procurement_budget_year')->row();
    // if(!empty($budget_year)) {
    //   $budget_months= $this->__get_months_year_wise_by_range_format2($budget_year->start_month, $budget_year->end_month);
      
    // }
    $piChartData['category_tab']= $this->db_readonly->select("ifnull(pbm.amount_allocated, 0) as amount_allocated, ec.category_name as name")
      ->from('procurement_budget_master pbm')
      ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
      ->where('pbm.budget_year_id', $budget_year_id)
      ->get()->result();
    $month= $this->db_readonly->select("sum(ifnull(pbs.allocated_amount, 0)) as amount_allocated, pbs.month_name as name")
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
      ->where('pbm.budget_year_id', $budget_year_id)
      ->group_by('pbs.month_name')
      ->get()->result();

      

    $piChartData['quarter_tab']= $this->get_quarterly_allocation($month);
    $piChartData['month_tab']= $month;
      return $piChartData;
  }

  private function get_quarterly_allocation($data) {
      $total_months = count($data);
      $total_quarters = ceil($total_months / 3); // Calculate the total number of quarters
      $quarter_months = ceil($total_months / $total_quarters); // Months per quarter dynamically
      $quarters = [];

      // Generate dynamic quarter names
      $quarter_names = array_map(function ($index) {
          return "Quarter " . ($index + 1);
      }, range(0, $total_quarters - 1));

      $current_quarter = 0;
      $current_allocation = 0;
      foreach ($data as $index => $item) {
          $current_allocation += $item->amount_allocated;

          // Check if it's the end of the quarter or last element
          if (($index + 1) % $quarter_months == 0 || $index == $total_months - 1) {
              $quarters[] = [
                  'name' => $quarter_names[$current_quarter],
                  'amount_allocated' => $current_allocation,
              ];
              $current_quarter++;
              $current_allocation = 0;
          }
      }

      return $quarters;
  }

  function getBudgetYearApprover($approver_id) {
    if($approver_id > 0) {
      $staff= $this->db_readonly->select("id, concat(first_name, ' ', ifnull(last_name, '')) as name")->where('id', $approver_id)->get('staff_master')->row();
      if(!empty($staff)) {
        return ['name' => $staff->name, 'id' => $staff->id];
      } else {
        return ['name' => 'Budget Admin', 'id' => 0];
      }
    } else {
      return ['name' => 'Budget Admin', 'id' => 0];
    }
  }

  function getBudgetYearCfoCeoApprover() {
    $CFOs= $this->db_readonly->select("sm.id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as name")
      ->from('privileges p')
      ->join('privileges_sub ps', 'p.id = ps.privilege_id')

      ->join('roles_privileges_sub rps', 'ps.id = rps.privilege_sub_id')
      ->join('roles r', 'rps.role_id = r.id')
      ->join('roles_staff rs', 'rs.role_id = r.id')
      ->join('staff_master sm', 'sm.id = rs.staff_id')
      ->where('p.name', 'PROCUREMENT_BUDGET')
      ->where('ps.name', 'CFO_CEO_APPROVER')
      ->group_by('sm.id')
      ->get()->result();
    return $CFOs;
  }

  function getAccountantNameAndId($budget_year_id) {
    return $this->db_readonly->select("sm.id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as staff, ifnull(pby.budget_approver_status, 'Not Respond') as budget_approver_status, ifnull(pby.CFO_approver_status, 'Not Respond') as CFO_approver_status")
      ->from('procurement_budget_year pby')
      ->join('staff_master sm', 'sm.id = pby.approver_id')
      ->where('pby.id', $budget_year_id)
      ->get()->row();
  }

  function approveSendForModifyAccountsTeam() {
    $budget_year_id= $this->input->post('budget_year_id');
    $approval_status= $this->input->post('approval_status');
    $description_approver_side= $this->input->post('description_approver_side');
    $budget_year_name= $this->input->post('budget_year_name');

    $this->db->trans_start();
      $this->db->where('id', $budget_year_id)->update('procurement_budget_year', array('budget_approver_status' => $approval_status, 'budget_approver_comments' => $description_approver_side) );

      $data_audit_log= array(
        'action_on' => date('Y-m-d H:i:s'),
        'action_by' => $this->authorization->getAvatarStakeHolderId(),
        'action_type' => 'Status Changed',
        'action_description' => "The Accounts Team has changed the budget approval status to $approval_status for the budget $budget_year_name.",
        'action_table' => 'procurement_budget_year',
        'source_id' => $budget_year_id // budget year id
      );
      $this->db->insert('procurement_budget_audit_log', $data_audit_log);

      $comments_data= array(
        'budget_year_id' => $budget_year_id,
        'status' => $approval_status,
        'comments' => "Comments from Accounts Team: $description_approver_side",
        'created_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_budget_approval_comments', $comments_data);
    $this->db->trans_complete();
    
    return  $this->db->trans_status();;
  }

  function approveSendForModifyCFOs() {
    $budget_year_id= $this->input->post('budget_year_id');
    $approval_status= $this->input->post('approval_status');
    $description_approver_side= $this->input->post('description_approver_side');
    $budget_year_name= $this->input->post('budget_year_name');

    $this->db->trans_start();
      $this->db->where('id', $budget_year_id)->update('procurement_budget_year', array(
          'CFO_approver_id' =>  $this->authorization->getAvatarStakeHolderId(),
          'CFO_approver_status' => $approval_status,
          // 'budget_approver_status' => $approval_status,
          'CFO_approver_comments' => $description_approver_side
        ) );

      $data_audit_log= array(
        'action_on' => date('Y-m-d H:i:s'),
        'action_by' => $this->authorization->getAvatarStakeHolderId(),
        'action_type' => 'Status Changed',
        'action_description' => "The CFO has changed the budget approval status to $approval_status for the budget $budget_year_name.",
        'action_table' => 'procurement_budget_year',
        'source_id' => $budget_year_id // budget year id
      );
      $this->db->insert('procurement_budget_audit_log', $data_audit_log);

      $comments_data= array(
        'budget_year_id' => $budget_year_id,
        'status' => $approval_status,
        'comments' => "Comments from CFO: $description_approver_side",
        'created_by' => $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->insert('procurement_budget_approval_comments', $comments_data);
    $this->db->trans_complete();
    
    return  $this->db->trans_status();;
  }

  function approeModifyAccountsTeamCategoryWise() {
    $pbm_id= $this->input->post('pbm_id');
    $category_name= $this->input->post('category_name');
    $budget_year_name= $this->input->post('budget_year_name');
    $year_id= $this->input->post('year_id');
    $responsive_description= $this->input->post('responsive_description');
    $type= $this->input->post('type');

    

    $data= array(
      'approval_status' => $type,
      'approval_comments' => $responsive_description
    );


    $this->db->trans_start();
    $x= $this->db->where('id', $pbm_id)->update('procurement_budget_master', $data);

    if(!empty($responsive_description)) {
      $responsive_description= "And left the following comments: $responsive_description";
    }

    if($type == 'Approved') {
      $data_audit_log= array(
        'action_on' => date('Y-m-d H:i:s'),
        'action_by' => $this->authorization->getAvatarStakeHolderId(),
        'action_type' => 'Allocation Approved',
        'action_description' => "Accounts team has been approved the category - $category_name from the budget - $budget_year_name. $responsive_description",
        'action_table' => 'procurement_budget_master',
        'source_id' => $pbm_id
      );
    } else {
      $data_audit_log= array(
        'action_on' => date('Y-m-d H:i:s'),
        'action_by' => $this->authorization->getAvatarStakeHolderId(),
        'action_type' => 'Allocation Sent for Modify',
        'action_description' => "Accounts team has been sent for modify the category - $category_name from the budget - $budget_year_name. $responsive_description",
        'action_table' => 'procurement_budget_master',
        'source_id' => $pbm_id
      );
    }
    $this->db->insert('procurement_budget_audit_log', $data_audit_log);
    $this->db->trans_complete();

    return $this->db->trans_status();
  }

  function __customize_our_data($data, $budget_year_id, $locked_budget_category_names) {
    $locked_budget_category_names= explode(',', $locked_budget_category_names);
    $unique_category_array= [];
    $unique_item_array= [];

    $unique_category_not_matched_array= [];
    $unique_item_not_matched_array= [];





    $logged_id_user_id= $this->authorization->getAvatarStakeHolderId();
    $isBudgetAdmin= $this->authorization->isAuthorized('PROCUREMENT_BUDGET.MANAGE_OVERALL_BUDGET');
    $departmentCategoriesArr= [0];
    if(!$isBudgetAdmin) {
      $departmentCategories= $this->db_readonly->select("group_concat(ec.id) as categories")
        ->from('expense_category ec')
        ->join('staff_departments sd', 'sd.id = ec.department_id')
        ->where('sd.head_of_department_id', $logged_id_user_id)
        ->get()->row();
        if(!empty($departmentCategories) && isset($departmentCategories->categories))
        $departmentCategoriesArr= explode(',', $departmentCategories->categories);
    }

//  echo '<pre>'; print_r(($isBudgetAdmin)); die();



    
    foreach($data as $key => $val) {
      if(in_array($val['Category'], $locked_budget_category_names)) {
        unset($data[$key]);
        continue;
      }
      // Categories
      if (empty($unique_category_array) || !array_key_exists($val['Category'], $unique_category_array)) {
          $haiKya = $this->db_readonly->select("id")
              ->where("LOWER(category_name)", strtolower(trim($val['Category'])))
              ->get('expense_category')
              ->row();

          if (!empty($haiKya)) {
              $unique_category_array[$val['Category']] = $haiKya->id; // if not exist
              $data[$key]['category_id'] = $haiKya->id;
          } else {
              $unique_category_not_matched_array[] = $val['Category'];
              $unique_category_array[$val['Category']] = 0;
              $data[$key]['category_id'] = 0;
          }
      } else {
          $data[$key]['category_id'] = $unique_category_array[$val['Category']]; // if already exist
      }
  
      // Sub-categories
      if (empty($unique_item_array) || !array_key_exists($val['Subcategory'], $unique_item_array)) {
          $haiKya = $this->db_readonly->select("id")
              ->where("LOWER(sub_category)", strtolower(trim($val['Subcategory'])))
              ->get('expense_sub_category')
              ->row();
          // $preValue= $this->db_readonly->select()
          //     ->from()
          //     ->join()
          //     ->join()
          //     ->join()
          //     ->where()
          //     ->where()
          //     ->where()
          //     ->get()->row();
          if (!empty($haiKya)) {
              $unique_item_array[$val['Subcategory']] = $haiKya->id; // if not exist
              $data[$key]['sub_category_id'] = $haiKya->id;
          } else {
              $unique_item_not_matched_array[] = $val['Subcategory'];
              $unique_item_array[$val['Subcategory']] = 0;
              $data[$key]['sub_category_id'] = 0;
          }
      } else {
          $data[$key]['sub_category_id'] = $unique_item_array[$val['Subcategory']]; // if already exist
      }
    }

    // $budget_year_id= $this->input->post('budget_year');
    $PreValueMatcher= $this->downloadCSVFormatWithAllPreFilledData($budget_year_id);
    // if(!empty($PreValueMatcher)) {
    //   foreach($PreValueMatcher as $key => $val) {
        
    //   }
    // }
    $FORMATED_MATCHER= [];
    if(!empty($PreValueMatcher)) {
      foreach($PreValueMatcher as $key => $val) {
        if(in_array($val->Category, $locked_budget_category_names)) {
          unset($PreValueMatcher[$key]);
          continue;
        }
        $FORMATED_MATCHER["$val->Category $val->Subcategory $val->Month"]= $val->Amount."___" .$val->Narration;
      }
    }

    // If not budget admin then display only department wise categories
    if(!$isBudgetAdmin && !empty($data)) {
      foreach($data as $key => $val) {
        if(!in_array($val['category_id'], $departmentCategoriesArr)) {
          $unique_category_not_matched_array[] = $val['Category'];
          unset($data[$key]);
        }
      }
    }
    $unique_category_not_matched_array= array_unique($unique_category_not_matched_array);
    
    // echo '<pre>'; print_r(($unique_category_not_matched_array)); die();
    return ['mathed' => $data, 'PreValueMatcher' => $FORMATED_MATCHER, 'unmatched_category' => $unique_category_not_matched_array, 'unmatched_subcategory' => $unique_item_not_matched_array, 'unique_category_array' => $unique_category_array, 'unique_item_array' => $unique_item_array];
  }

  function csv_form_submit() {
    $input= $this->input->post();

    $item_allocation_csv= $input['item_allocation_csv'];
    $category_id_csv= $input['category_id_csv'];
    $sub_category_id_csv= $input['sub_category_id_csv'];
    $Month_csv= $input['Month_csv'];
    $description_from_csv= $input['description_from_csv'];
    $budget_year_id_csv= $input['budget_year_id_csv'];
    $budget_year_name_csv= $input['budget_year_name_csv'];

    $unique_cats = new stdClass();
    foreach ($category_id_csv as $index => $category_id) {
        if (!isset($unique_cats->$category_id)) {
            $unique_cats->$category_id = 0;
        }
        if(isset($item_allocation_csv[$index])) {
          $unique_cats->$category_id += 1*$item_allocation_csv[$index];
        }
    }

    // echo '<pre>'; print_r($unique_cats); die();

    $budget_master_acc_to_cat_id= []; // key = cat_id and value = budget_master_id
    foreach($unique_cats as $key => $val) {
      $budget_m= $this->db->select('id')->where('budget_year_id', $budget_year_id_csv)->where('budget_category_id', $key)->get('procurement_budget_master')->row();
      if(!empty($budget_m)) {
        $budget_master_acc_to_cat_id[$key]= $budget_m->id;
        $budget_master_update[]= array( // 1
          'id' => $budget_m->id,
          'amount_allocated' => $val,
          'amount_available' => $val
        );
      }
    }

    // 

    foreach($item_allocation_csv as $keyIndex => $amount) {
      $budget_split= $this->db->select('id')->where('procurement_budget_master_id', $budget_master_acc_to_cat_id[$category_id_csv[$keyIndex]])->where('expense_subcategory_id', $sub_category_id_csv[$keyIndex])->where('month_name', $Month_csv[$keyIndex])->get('procurement_budget_split')->row();
      if(!empty($budget_split)) {
        $budget_split_update[]= array(
          'id' => $budget_split->id,
          'allocated_amount' => $amount,
          'description' => $description_from_csv[$keyIndex]
        );
      }
    }

    // echo '<pre>'; print_r($budget_master_update); die();

    $this->db->trans_start();
      // if(!empty($budget_master_update)) {
      //   $this->db->update_batch('procurement_budget_master', $budget_master_update, 'id');
      // }
      if(!empty($budget_split_update)) {
        $this->db->update_batch('procurement_budget_split', $budget_split_update, 'id');
      }
      
    $this->db->trans_complete();
    
    return $this->db->trans_status();
  }

  function calculate_category_wise_budget() {
    $budget_year_id_csv= $this->input->post('budget_year_id_csv');
    $budget_year_name_csv= $this->input->post('budget_year_name_csv');
    $budget_split_calculation= $this->db_readonly->select("pbm.id, pbm.budget_category_id, sum(pbs.allocated_amount) as allocated_amount")
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
      ->where('pbm.budget_year_id', $budget_year_id_csv)
      ->group_by('pbm.id')
      ->get()->result();
    
    $budget_master_update= [];
    if(!empty($budget_split_calculation)) {
      foreach($budget_split_calculation as $key => $val) {
        $budget_master_update[]= array(
          'id' => $val->id,
          'amount_allocated' => $val->allocated_amount,
          'amount_available' => $val->allocated_amount
        );
      }
    }

    $status= false;
    $this->db->trans_start();
    if(!empty($budget_master_update)) {
      $status= $this->db->update_batch('procurement_budget_master', $budget_master_update, 'id');
    }
    if($this->db->trans_status()) {
      $data_audit_log= array(
        'action_on' => date('Y-m-d H:i:s'),
        'action_by' => $this->authorization->getAvatarStakeHolderId(),
        'action_type' => 'Mass Update - CSV',
        'action_description' => "Bulk updates were successfully applied for the budget : $budget_year_name_csv. All necessary changes have been applied as per the provided data.",
        'action_table' => 'procurement_budget_year',
        'source_id' => $budget_year_id_csv // budget year id
      );
      $this->db->insert('procurement_budget_audit_log', $data_audit_log);
    }
    $this->db->trans_complete();

    return $this->db->trans_status();

  }

  function downloadCSVFormatWithAllPreFilledData($budget_year_id) {
      
      $logged_id= $this->authorization->getAvatarStakeHolderId();

     $this->db_readonly->select('ec.id , ec.category_name')
          ->from('expense_category ec');
      if(!$this->authorization->isSuperAdmin() && !$this->authorization->isAuthorized('PROCUREMENT_BUDGET.MANAGE_OVERALL_BUDGET')) {
        $this->db_readonly->join('staff_departments sd', 'ec.department_id= sd.id');
      }
          
      $this->db_readonly->join('procurement_budget_master pbm', 'pbm.budget_category_id= ec.id');
      if(!$this->authorization->isSuperAdmin() && !$this->authorization->isAuthorized('PROCUREMENT_BUDGET.MANAGE_OVERALL_BUDGET')) {
        $this->db_readonly->where('sd.head_of_department_id', $logged_id);
      }
      $cat_is= $this->db_readonly->where('pbm.budget_year_id', $budget_year_id)
          ->get()->result();

          // echo '<pre>'; print_r($cat_is); die();

      $cat_ids_arr= [];
      if(!empty($cat_is)) {
        foreach($cat_is as $key => $val) {
          $cat_ids_arr[]= $val->id;
        }
      }

      if (!empty($cat_ids_arr)) {
        $details = $this->db_readonly->select("ec.category_name as Category, esc.sub_category as Subcategory, pbs.month_name as Month, pbs.allocated_amount as Amount, ifnull(pbs.description, '') as Narration")
            ->distinct()
            ->from('procurement_budget_master pbm')
            ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
            ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
            ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
            ->where_in('pbm.budget_category_id', $cat_ids_arr)
            ->where_in('ec.id', $cat_ids_arr)
            ->where('pbm.budget_year_id', $budget_year_id)
            ->get()->result();
            
      } else {
          $details = [];
      }

      // echo '<pre>'; print_r($details); die();

      return $details;
  }

  function lock_category() {
    $budget_master_id= $this->input->post('budget_master_id');
    $category_name= $this->input->post('category_name');
    $year_name= $this->input->post('year_name');

    $this->db->trans_start();
      $this->db->where('id', $budget_master_id)->update('procurement_budget_master', array('lock_status' => 'Locked'));

      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Lock a Category',
          'action_description' => "The budget category - $category_name from budget year - $year_name has been locked.",
          'action_table' => 'procurement_budget_master',
          'source_id' => $budget_master_id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
      }

    $this->db->trans_complete();


    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();

  }

  function unlock_category() {
    $budget_master_id= $this->input->post('budget_master_id');
    $category_name= $this->input->post('category_name');
    $year_name= $this->input->post('year_name');
    $unlock_comments= $this->input->post('unlock_comments');

    $loggedInId= $this->authorization->getAvatarStakeHolderId();
    $checkIfLoggedIdIdIsAuthorized= $this->db_readonly->select('pby.approver_id')
      ->from('procurement_budget_master pbm')
      ->join('procurement_budget_year pby', 'pby.id = pbm.budget_year_id')
      ->where('pbm.id', $budget_master_id)
      ->get()->row();
      // echo '<pre>'; print_r($checkIfLoggedIdIdIsAuthorized);
      if(!empty($checkIfLoggedIdIdIsAuthorized) && $checkIfLoggedIdIdIsAuthorized->approver_id != $loggedInId) {
        // echo '<pre>'; print_r($loggedInId); die();
        return '-2';
      }

    $this->db->trans_start();
      $this->db->where('id', $budget_master_id)->update('procurement_budget_master', array('lock_status' => 'Un-locked', 'lock_comments' => $unlock_comments));

      if($this->db->trans_status()) {
        $data_audit_log= array(
          'action_on' => date('Y-m-d H:i:s'),
          'action_by' => $this->authorization->getAvatarStakeHolderId(),
          'action_type' => 'Un-lock a Category',
          'action_description' => "The budget category - $category_name from budget year - $year_name has been un-locked. The following remarks have been noted: $unlock_comments",
          'action_table' => 'procurement_budget_master',
          'source_id' => $budget_master_id
        );
        $this->db->insert('procurement_budget_audit_log', $data_audit_log);
        $year_id= $this->db->select("budget_year_id")->where('id', $budget_master_id)->get('procurement_budget_master')->row();
        $data_audit_log= array(
          'budget_year_id' => $year_id->budget_year_id,
          'created_on' => date('Y-m-d H:i:s'),
          'status' => 'Un-locked- '.$category_name,
          'comments' => "Un-locked the budget category - $category_name from budget year - $year_name. The following remarks have been noted: $unlock_comments",
          'created_by' => $this->authorization->getAvatarStakeHolderId()
        );
        $this->db->insert('procurement_budget_approval_comments', $data_audit_log);
        
      }

    $this->db->trans_complete();


    if(! $this->db->trans_status()) {
      $this->db->trans_rollback();
    }
    return $this->db->trans_status();

  }

  function checkTheStatusBeforeStoping() {
    $year_id= $this->input->post('year_id');
   return $this->db_readonly->select("ec.category_name")
        ->from('procurement_budget_year pby')
        ->join('procurement_budget_master pbm', 'pbm.budget_year_id = pby.id')
        ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
        ->where('pby.id', $year_id)
        ->where('pbm.lock_status', 'Un-locked')
        ->get()->result();

  }

  function getRelevantStaffForEmailNotification() {
    $year_id= $this->input->post('year_id');
    

    $staffs= $this->db_readonly->distinct()->select("ifnull(sm.id, 0) as id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff, group_concat(ec.category_name) as categories, ifnull(sm.personal_mail_id, '') as personal_mail_id")
      ->from('procurement_budget_master pbm')
      ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
      ->join('staff_departments sd', 'sd.id = ec.department_id')
      ->join('staff_master sm', 'sm.id = sd.head_of_department_id')
      ->where('pbm.budget_year_id', $year_id)
      ->group_by('sm.id')
      ->get()->result();

    return $staffs;
  }

  function getRelevantStaffForEmailNotificationUnlock() {
    $budget_master_id= $this->input->post('budget_master_id');
    $staffs= $this->db_readonly->distinct()->select("ifmull(sm.id, 0) as id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff, group_concat(ec.category_name) as categories, ifnull(sm.personal_mail_id, '') as personal_mail_id")
    ->from('procurement_budget_master pbm')
    ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
    ->join('staff_departments sd', 'sd.id = ec.department_id')
    ->join('staff_master sm', 'sm.id = sd.head_of_department_id')
    ->where('pbm.id', $budget_master_id)
    ->group_by('sm.id')
    ->get()->result();

    return $staffs;
  }

  function get_budget_subcategories($budget_year_id, $budget_master_id) {
    return $this->db_readonly->select("pbs.expense_subcategory_id, ifnull(sum(ifnull(pbs.allocated_amount, 0)), 0) as subcategory_allocated_amount, esc.sub_category")
        ->from('procurement_budget_split pbs')
        ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
        ->where('pbs.procurement_budget_master_id', $budget_master_id)
        ->group_by('pbs.expense_subcategory_id')
        ->get()->result();
  }

  function get_budget_category_details($budget_year_id, $budget_master_id) {
    return $this->db_readonly->select("ec.category_name, ifnull(pbm.amount_allocated, 0) as amount_allocated, ifnull(pbm.amount_available, 0) as amount_available, ifnull(pbm.amount_commited, 0) as amount_commited, ifnull(pbm.amount_blocked, 0) as amount_blocked, ifnull(pbm.amount_consumed, 0) as amount_consumed, pbm.lock_status")
        ->from('procurement_budget_master pbm')
        ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
        ->where('pbm.id', $budget_master_id)
        // ->where('procurement_budget_master_id', $budget_master_id)
        // ->group_by('pbs.expense_subcategory_id')
        ->get()->row();
  }

  function get_sub_category_details() {
    $budget_master_id= $this->input->post('budget_master_id');
    $expense_subcategory_id= $this->input->post('expense_subcategory_id');
    return $this->db_readonly->select("pbs.id as procurement_budget_split_id, pbs.month_name, ifnull(pbs.allocated_amount, 0) as allocated_amount, ifnull(pbs.description, '') as description, esc.sub_category")
          ->from('procurement_budget_split pbs')
          ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
          ->where('pbs.procurement_budget_master_id', $budget_master_id)
          ->where('pbs.expense_subcategory_id', $expense_subcategory_id)
          ->get()->result();
  }

  function save_single_subcategory_submit() {
    $input= $this->input->post();
    $budget_master_id= isset($input['budget_master_id']) ? $input['budget_master_id'] : 0;
    if(empty($budget_master_id) || $budget_master_id == 0) {
      return false;
    }
    $subcategory_edit_amount= isset($input['subcategory_edit_amount']) ? $input['subcategory_edit_amount'] : 0;
    $subcategory_allocation= isset($input['subcategory_allocation']) ? $input['subcategory_allocation'] : [];
    $subcategory_narration= isset($input['subcategory_narration']) ? $input['subcategory_narration'] : [];
    if(empty($subcategory_allocation)) {
      return false;
    }
    $subCatsAmt= 0;
    if(!empty($subcategory_allocation)) {
      foreach($subcategory_allocation as $key => $val) {
        $subCatsAmt += floatval($val);
      }
    }

    if($subCatsAmt > 0) {
      $indexKey= 0;
      $amountMissDistributed= 0;
      foreach($subcategory_allocation as $key => $val) {
        if($subCatsAmt < $subcategory_edit_amount && $indexKey == 0) {
          // echo 'yes'; die();
          $update_split[]= array(
            'id' => $key,
            'allocated_amount' => $val,
            'description' => $subcategory_narration[$key]
          );
        } else {
          $amountMissDistributed += $val;
          $update_split[]= array(
            'id' => $key,
            'allocated_amount' => $val,
            'description' => $subcategory_narration[$key]
          );
        }
        $indexKey ++;
      }
      if($subCatsAmt < $subcategory_edit_amount) {
        $update_split[0]['allocated_amount']= $subcategory_edit_amount - $amountMissDistributed;
      }
    } else {
      $indexKey= 0;
      foreach($subcategory_allocation as $key => $val) {
        $update_split[]= array(
          'id' => $key,
          'allocated_amount' => $indexKey == 0 ? $subcategory_edit_amount : 0,
          'description' => $subcategory_narration[$key]
        );
        $indexKey ++;
      }
    }
    $this->db->trans_start();
    if(!empty($update_split)) {
      $this->db->update_batch('procurement_budget_split', $update_split, 'id');
      $budget_category_amount= $this->db->select("ifnull( sum( ifnull(allocated_amount, 0) ), 0 ) as budget_category_amount")->where('procurement_budget_master_id', $budget_master_id)->group_by('procurement_budget_master_id')->get('procurement_budget_split')->row();
      if(!empty($budget_category_amount)) {
        $update_budget_master= array(
          'amount_allocated' => $budget_category_amount->budget_category_amount,
          'amount_available' => $budget_category_amount->budget_category_amount
        );
        $this->db->where('id', $budget_master_id)->update('procurement_budget_master', $update_budget_master);
      }
    }
    $this->db->trans_complete();

    return $this->db->trans_status();
  }

  function getRelevantsendorReceiverEmail($year_id) {
    return $this->db_readonly->select("sm.id, concat(sm.first_name, ' ', ifnull(sm.last_name, '')) as staff, sm.personal_mail_id, pby.year")
        ->from('procurement_budget_year pby')
        ->join('staff_master sm', 'sm.id = pby.approver_id')
        ->where('pby.id', $year_id)
        ->get()->row();
  }

  function getRelevantCategoryHOD($budget_master_id) {
    return $this->db_readonly->select("ifnull(sm.id, 0) as id, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff, ifnull(personal_mail_id, '') as personal_mail_id")
        ->from('procurement_budget_master pbm')
        ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
        ->join('staff_departments sd', 'sd.id = ec.department_id')
        ->join('staff_master sm', 'sm.id = sd.head_of_department_id')
        ->where('pbm.id', $budget_master_id)
        ->get()->row();
  }

  function isOthrBudgetActive($budget_year_id) {
    $findOtherAvtiveBudget= $this->db_readonly->select("id")
      ->from('procurement_budget_year')
      ->where('status', 'Active')
      ->where('id !=', $budget_year_id)
      ->get()->row();
      if(!empty($findOtherAvtiveBudget)) {
        return 'Yes';
      }
      return 'No';
  }

  function getBudgetAllocationReport() {
    $budget_year_id= $this->input->post('budget_year_id');
    $categoryAllocation= $this->db_readonly->select("pbm.id, ec.category_name, pbm.budget_category_id as expense_category_id, ifnull(pbm.amount_allocated, 0) as category_amount_allocated, 0 as requisition, 0 as po, 0 as invoiced, ifnull(pbm.amount_allocated, 0) as available")
          ->from('procurement_budget_master pbm')
          ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
          ->where('pbm.budget_year_id', $budget_year_id)
          ->get()->result();
    $categoryConsuption= $this->getCategoryMovementRecordsForCategory($budget_year_id);
    $indentCategory= $categoryConsuption['indents'];
    $allCategoriesUsedInIndent= $categoryConsuption['allCategoriesUsedInIndent'];
    if(!empty($categoryConsuption) && !empty($indentCategory)) {
      foreach($categoryAllocation as $key => $val) {
        if(in_array($val->expense_category_id, $allCategoriesUsedInIndent)) {

          // echo '<pre>'; print_r($allCategoriesUsedInIndent); die();

          $val->requisition= $indentCategory[$val->expense_category_id]->requisition;
          $val->po= $indentCategory[$val->expense_category_id]->po;
          $val->invoiced= $indentCategory[$val->expense_category_id]->invoiced;
          $val->available= 1*$val->category_amount_allocated - (1*$indentCategory[$val->expense_category_id]->requisition + 1*$indentCategory[$val->expense_category_id]->po + 1*$indentCategory[$val->expense_category_id]->invoiced);

        }
      }
    }

    return $categoryAllocation;
  }

  function getBudgetAmountMovement() {
    $budget_year_id= $this->input->post('budget_year_id');
    $data= $this->db_readonly->select("pbm.id, ec.category_name, ifnull(pbm.amount_allocated, 0) as amount_allocated, ifnull(pbm.amount_available, 0) as amount_available, ifnull(pbm.amount_commited, 0) as amount_commited, ifnull(pbm.amount_blocked, 0) as amount_blocked, ifnull(pbm.amount_consumed, 0) as amount_consumed, ifnull(sm.first_name, 'Super') as firstName, ifnull(sm.last_name, 'Admin') as lastName, ifnull(trim(pim.indent_name), '-') as indent_name, if(pim.is_active = 1, 'Active', 'De-active') as is_active, pim.status as indent_status, esc.sub_category, ec.category_name, DATE_FORMAT(pitil.action_on, '%d-%b-%Y') as action_on, ifnull(pitil.invoice_transaction_mode, '-') as invoice_transaction_mode, ifnull(pitil.action_type, '-') as action, ifnull(pitil.description, '-') as description, ifnull(pitil.total_amount, 0) as indent_total_amount, ifnull(pitil.used_amount, 0) as indent_used_amount, ifnull(pitil.remaining_amount, 0) as indent_remaining_amount, pitil.source_type as indent_source_type, pitil.amount_status as indent_amount_status")
        ->from('procurement_indent_to_invoice_ledger pitil')
        ->join('staff_master sm', 'sm.id = pitil.action_by', 'left')
        ->join('procurement_indents_master pim', 'pim.id = pitil.indent_master_id', 'left')
        ->join('expense_sub_category esc', 'esc.id = pitil.expense_subcategory_id')
        ->join('expense_category ec', 'ec.id = esc.cat_id')
        ->join('procurement_budget_master pbm', 'pbm.budget_year_id = pitil.budget_year_id and pbm.budget_category_id = pitil.expense_category_id')
        ->where('pitil.budget_year_id', $budget_year_id)
        ->get()->result();
    return $data;
  }

  function getSubCategoryMovementRecords() {
    $budget_year_id= $this->input->post('budget_year_id');

    $allSubCategories= $this->db_readonly->select("pbs.expense_subcategory_id, sum(pbs.allocated_amount) as subcategory_allocated_amount, ec.category_name, esc.sub_category")
        ->from('procurement_budget_master pbm')
        ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
        ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
        ->join('expense_category ec', 'ec.id = esc.cat_id')
        ->where('pbm.budget_year_id', $budget_year_id)
        ->group_by('pbs.expense_subcategory_id')
        ->get()->result();

    $indent_PO_data= $this->db_readonly->select("pitil.source_type, ifnull(pitil.indent_master_id, 0) as indent_master_id, ifnull(pitil.procurement_requisition_id, 0) as procurement_requisition_id, if(pitil.indent_master_id is null OR pitil.indent_master_id = '' OR pitil.indent_master_id = 0, 'Purchase Order', 'Indent') as isComeFromIndent, pitil.expense_subcategory_id, pitil.expense_category_id, ifnull(trim(pim.indent_name), '-') as indent_name, ifnull(pr.request_number, '-') as request_number, pitil.total_amount as indent_total_amount, ifnull(pitil.used_amount, 0) as indent_used_amount, ifnull(pitil.remaining_amount, 0) as indent_remaining_amount, pitil.amount_status as indent_amount_status, pitil.amount_moving_type as indent_amount_moving_type, pitil.original_amount as indent_original_amount, ifnull(pitil.action_type, '-') as action_type, ifnull(pitil.description, '-') as description")
        ->from('procurement_indent_to_invoice_ledger pitil')
        ->join('procurement_indents_master pim', 'pim.id = pitil.indent_master_id', 'left')
        ->join('procurement_requisition pr', 'pr.id = pitil.procurement_requisition_id', 'left')
        ->where('pitil.budget_year_id', $budget_year_id)
        ->get()->result();

      $indents= [];
      $allSubCategoriesUsedInIndent= [];
      if(!empty($indent_PO_data)) {
        foreach($indent_PO_data as $key => $val) {
          $indexKey= "ExpenseSubCategory---$val->expense_subcategory_id"; // Because PO have (Active, Partially-Used, Fully-Used) status and indent have (Active, De-active) status. So we need to check the indent with PO. If fresh PO are coming then also we need to check the indent with PO. So we are using this key.
          if(($val->indent_amount_status != 'DE-ACTIVE')) {
            if(!isset($indents[$indexKey]) && empty($indents[$indexKey])) {
              $allSubCategoriesUsedInIndent[]= $val->expense_subcategory_id;
              $subcategoryData= $this->db_readonly->select("esc.sub_category, ec.category_name, pbs.expense_subcategory_id, sum(pbs.allocated_amount) as subcategory_allocated_amount")
              ->from('procurement_budget_master pbm')
              ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
              ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
              ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
              ->where('pbm.budget_year_id', $budget_year_id)
              ->where('pbm.budget_category_id', $val->expense_category_id)
              ->where('pbs.expense_subcategory_id', $val->expense_subcategory_id)
              ->group_by('pbs.expense_subcategory_id')
              ->get()->row();
              $calculatedData= $this->__calculateMoneyMovementIndent($subcategoryData, $val);
              $indents[$indexKey] = $calculatedData;
            } else {
              $indents[$indexKey]->indent_used_amount= 1*$indents[$indexKey]->indent_used_amount + 1*$val->indent_used_amount;
              $indents[$indexKey]->indent_remaining_amount= 1*$val->indent_remaining_amount;
              $indents[$indexKey]->indent_amount_moving_type= $val->indent_amount_moving_type;
              $indents[$indexKey]->action_type= $val->action_type;
              $indents[$indexKey]->description= $val->description;
              $indents[$indexKey]->indent_original_amount= $val->indent_original_amount;
// amt movements
              $po= 0;
              $requisition= 0;
              $invoiced= 0;
          
              if($val->source_type == 'PO') {
                $po= $val->indent_remaining_amount;
              } else if($val->source_type == 'Indent' && $val->indent_amount_status != 'DE-ACTIVE') {
                $requisition= $val->indent_remaining_amount;
              } else if($val->source_type == 'Invoice') {
                $invoiced= $val->indent_total_amount;
              }

              $indents[$indexKey]->po= $indents[$indexKey]->po + $po;
              $indents[$indexKey]->requisition= $indents[$indexKey]->requisition + $requisition;
              $indents[$indexKey]->invoiced= $indents[$indexKey]->invoiced + $invoiced;
// End amt movements

            }
          }
        }
      }

      if(!empty($allSubCategories)) {
        foreach($allSubCategories as $key => $val) {
          $subCategoryId= $val->expense_subcategory_id;
          $indexKey= "ExpenseSubCategory---$subCategoryId";
          if(!in_array($subCategoryId, $allSubCategoriesUsedInIndent)) {
            $obj= new stdClass();

            $obj->subCategory= $val->sub_category;
            $obj->category_name= $val->category_name;
            $obj->subcategory_allocated_amount= $val->subcategory_allocated_amount;
            $obj->po= 0;
            $obj->requisition= 0;
            $obj->invoiced= 0;

            $indents[$indexKey] = $obj;
          }
        }
      }

      // echo '<pre>'; print_r($indents); die();
    return $indents;
  }

  function __calculateMoneyMovementIndent($subcategoryData, $indentData) {
    $obj= new stdClass();

    $obj->subCategory= $subcategoryData->sub_category;
    $obj->category_name= $subcategoryData->category_name;
    $obj->subcategory_allocated_amount= $subcategoryData->subcategory_allocated_amount;

    $obj->indent_master_id= $indentData->indent_master_id;
    $obj->procurement_requisition_id= $indentData->procurement_requisition_id;
    $obj->indent_name= $indentData->indent_name;
    $obj->request_number= $indentData->request_number;
    $obj->indent_original_amount= $indentData->indent_original_amount;
    $obj->indent_total_amount= $indentData->indent_total_amount;
    $obj->invoiced= 0;

    $obj->indent_used_amount= $indentData->indent_used_amount;
    $obj->indent_remaining_amount= $indentData->indent_remaining_amount;
    
    $obj->isComeFromIndent= $indentData->isComeFromIndent;
    $obj->indent_amount_moving_type= $indentData->indent_amount_moving_type;
    $obj->action_type= $indentData->action_type;
    $obj->description= $indentData->description;


    $obj->po= 0;
    $obj->requisition= 0;
    $obj->invoiced= 0;

    if($indentData->source_type == 'PO') {
      $obj->po= $indentData->indent_remaining_amount;
    } else if($indentData->source_type == 'Indent' && $indentData->indent_amount_status != 'DE-ACTIVE') {
      $obj->requisition= $indentData->indent_remaining_amount;
    } else if($indentData->source_type == 'Invoice') {
      $obj->invoiced= $indentData->indent_total_amount;
    }
    
    return $obj;
  }

  function getCategoryMovementRecordsForCategory($budget_year_id) {

    // $allSubCategories= $this->db_readonly->select("pbs.expense_subcategory_id, sum(pbs.allocated_amount) as subcategory_allocated_amount, ec.category_name, esc.sub_category, ec.id as expense_category_id")
    //     ->from('procurement_budget_master pbm')
    //     ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
    //     ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
    //     ->join('expense_category ec', 'ec.id = esc.cat_id')
    //     ->group_by('pbs.expense_subcategory_id')
    //     ->get()->result();

    $indent_PO_data= $this->db_readonly->select("pitil.expense_subcategory_id, pitil.source_type, pitil.expense_category_id, pitil.total_amount as indent_total_amount, ifnull(pitil.used_amount, 0) as indent_used_amount, ifnull(pitil.remaining_amount, 0) as indent_remaining_amount, pitil.amount_status as indent_amount_status, pitil.amount_moving_type as indent_amount_moving_type, pitil.original_amount as indent_original_amount")
        ->from('procurement_indent_to_invoice_ledger pitil')
        ->join('procurement_indents_master pim', 'pim.id = pitil.indent_master_id', 'left')
        ->join('procurement_requisition pr', 'pr.id = pitil.procurement_requisition_id', 'left')
        ->where('pitil.budget_year_id', $budget_year_id)
        ->get()->result();

      $indents= [];
      $allCategoriesUsedInIndent= [];
      if(!empty($indent_PO_data)) {
        foreach($indent_PO_data as $key => $val) {
          $indexKey= $val->expense_category_id; // Because PO have (Active, Partially-Used, Fully-Used) status and indent have (Active, De-active) status. So we need to check the indent with PO. If fresh PO are coming then also we need to check the indent with PO. So we are using this key.
          if(($val->indent_amount_status != 'DE-ACTIVE')) {
            if(!isset($indents[$indexKey]) && empty($indents[$indexKey])) {
              $allCategoriesUsedInIndent[]= $val->expense_category_id;
              $subcategoryData= $this->db_readonly->select("ec.category_name, sum(pbs.allocated_amount) as subcategory_allocated_amount")
              ->from('procurement_budget_master pbm')
              ->join('procurement_budget_split pbs', 'pbs.procurement_budget_master_id = pbm.id')
              // ->join('expense_sub_category esc', 'esc.id = pbs.expense_subcategory_id')
              ->join('expense_category ec', 'ec.id = pbm.budget_category_id')
              ->where('pbm.budget_year_id', $budget_year_id)
              ->where('pbm.budget_category_id', $val->expense_category_id)
              // ->where('pbs.expense_subcategory_id', $val->expense_subcategory_id)
              ->group_by('pbs.expense_subcategory_id')
              ->get()->row();
              $calculatedData= $this->__calculateMoneyMovementFromIndentAndPO($subcategoryData, $val);
              $indents[$indexKey] = $calculatedData;
            } else {
              // $indents[$indexKey]->indent_used_amount= 1*$indents[$indexKey]->indent_used_amount + 1*$val->indent_used_amount;
              // $indents[$indexKey]->indent_remaining_amount= 1*$val->indent_remaining_amount;
              // $indents[$indexKey]->indent_amount_moving_type= $val->indent_amount_moving_type;
              // $indents[$indexKey]->action_type= $val->action_type;
              // $indents[$indexKey]->description= $val->description;
              // $indents[$indexKey]->indent_original_amount= $val->indent_original_amount;
// amt movements
              $po= 0;
              $requisition= 0;
              $invoiced= 0;
          
              if($val->source_type == 'PO') {
                $po= $val->indent_remaining_amount;
              } else if($val->source_type == 'Indent' && $val->indent_amount_status != 'DE-ACTIVE') {
                $requisition= $val->indent_remaining_amount;
              } else if($val->source_type == 'Invoice') {
                $invoiced= $val->indent_total_amount;
              }

              $indents[$indexKey]->po= $indents[$indexKey]->po + $po;
              $indents[$indexKey]->requisition= $indents[$indexKey]->requisition + $requisition;
              $indents[$indexKey]->invoiced= $indents[$indexKey]->invoiced + $invoiced;
// End amt movements

            }
          }
        }
      }

      // if(!empty($allSubCategories)) {
      //   foreach($allSubCategories as $key => $val) {
      //     $subCategoryId= $val->expense_subcategory_id;
      //     $indexKey= "ExpenseCategory---$subCategoryId";
      //     if(!in_array($subCategoryId, $allCategoriesUsedInIndent)) {
      //       $obj= new stdClass();

      //       $obj->subCategory= $val->sub_category;
      //       $obj->category_name= $val->category_name;
      //       $obj->subcategory_allocated_amount= $subcategoryData->subcategory_allocated_amount;
      //       $obj->po= 0;
      //       $obj->requisition= 0;
      //       $obj->invoiced= 0;

      //       $indents[$indexKey] = $obj;
      //     }
      //   }
      // }

      // echo '<pre>'; print_r($indents); die();
    return ['indents' => $indents, 'allCategoriesUsedInIndent' => $allCategoriesUsedInIndent];
  }

  function __calculateMoneyMovementFromIndentAndPO($subcategoryData, $indentData) {
    $obj= new stdClass();

    // $obj->subCategory= $subcategoryData->sub_category;
    $obj->category_name= $subcategoryData->category_name;
    $obj->subcategory_allocated_amount= $subcategoryData->subcategory_allocated_amount;

    // $obj->indent_master_id= $indentData->indent_master_id;
    // $obj->procurement_requisition_id= $indentData->procurement_requisition_id;
    // $obj->indent_name= $indentData->indent_name;
    // $obj->request_number= $indentData->request_number;
    // $obj->indent_original_amount= $indentData->indent_original_amount;
    // $obj->indent_total_amount= $indentData->indent_total_amount;
    // $obj->invoiced= 0;

    // $obj->indent_used_amount= $indentData->indent_used_amount;
    // $obj->indent_remaining_amount= $indentData->indent_remaining_amount;
    
    // $obj->isComeFromIndent= $indentData->isComeFromIndent;
    // $obj->indent_amount_moving_type= $indentData->indent_amount_moving_type;
    // $obj->action_type= $indentData->action_type;
    // $obj->description= $indentData->description;


    $obj->po= 0;
    $obj->requisition= 0;
    $obj->invoiced= 0;

    if($indentData->source_type == 'PO') {
      $obj->po= $indentData->indent_remaining_amount;
    } else if($indentData->source_type == 'Indent' && $indentData->indent_amount_status != 'DE-ACTIVE') {
      $obj->requisition= $indentData->indent_remaining_amount;
    } else if($indentData->source_type == 'Invoice') {
      $obj->invoiced= $indentData->indent_total_amount;
    }
    
    return $obj;
  }

}
?>