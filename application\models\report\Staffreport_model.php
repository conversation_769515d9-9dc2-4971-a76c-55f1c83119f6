<?php


defined('BASEPATH') OR exit('No direct script access allowed');
            
	class Staffreport_model extends CI_Model {
	        
    public function __construct() {
      parent::__construct();
      $this->load->library('filemanager');

	  }

    public $mandatoryFields = [
      [
        'displayName'=>'Id',
        'columnNameWithTable'=>'sm.id',
        'columnName'=>'id',
        'varName'=>'smId',
        'table'=>'staff_master',
        'displayType'=>'hidden',
        'dataType'=>'string'
      ],
      // [
      //   'displayName' => 'Name',
      //   'columnNameWithTable' => 'concat(ifnull(sm.first_name,""), " ", ifnull(sm.last_name,""))',
      //   'columnName' => 'first_name',
      //   'varName' => 'sFirstName',
      //   'table' => 'staff_master',
      //   'displayType' => 'text',
      //   'dataType' => 'string'
      // ],
      [
        'displayName' => 'First Name',
        'columnNameWithTable' => 'ifnull(sm.first_name,"")',
        'columnName' => 'first_name',
        'varName' => 'sFirstName',
        'table' => 'staff_master',
        'displayType' => 'text',
        'dataType' => 'string'
      ],
      [
        'displayName' => 'Last Name',
        'columnNameWithTable' => 'ifnull(sm.last_name,"")',
        'columnName' => 'last_name',
        'varName' => 'slastName',
        'table' => 'staff_master',
        'displayType' => 'text',
        'dataType' => 'string'
      ],
      [
        'displayName'=>'Employee Code',
        'columnNameWithTable'=>'sm.employee_code',
        'columnName'=>'employee_code',
        'varName'=>'employee_code',
        'table'=>'staff_master',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
      [
        'displayName'=>'Contact Number',
        'columnNameWithTable'=>'sm.contact_number',
        'columnName'=>'contact_number',
        'varName'=>'cNumber',
        'table'=>'staff_master',
        'displayType'=>'text',
        'dataType'=>'string'
      ],
    ];

		public function get_staff_details(){
    return $this->db->select("sp. *, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName")
    ->from('staff_payslip sp')
    ->join('staff_master sm','sm.id=sp.staffid')
    ->get()->result();
    }

    public function allstaffslip(){

     return $this->db->select("sp. *, date_format(sp.created_on,'%d-%m-%Y') as slip_date, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName")
    ->from('staff_payslip sp')
    ->join('staff_master sm','sm.id=sp.staffid')
    ->get()->result();
    }

    public function getmonthwisepaylistdetails($staff_type)
    {
        $this->db->select('*');
        $this->db->where('staff_type', $staff_type);
    return $this->db->get('staff_payslip')->result();
    }

    public function getstaffnamewisemonth($staff_name = 0,$month_name){
      $this->db->select("sp. *, ssd.total_salary as scale_of_pay,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName");
      $this->db->from('staff_payslip sp');
      $this->db->join('staff_master sm','sm.id=sp.staffid');
      $this->db->join('staff_salary_details ssd','ssd.staff_id=sp.staffid');
      if($staff_name){
        $this->db->where('staffid', $staff_name);
      } 
      if($month_name) {
        $this->db->where('month_name', $month_name);
      } 
      $query = $this->db->get()->result();
      return $query;
        
    }
  public function printdetails($id)
  {
    $this->db->select('*');
   $this->db->from('staff_payslip');
   $this->db->where('id', $id);
   $query = $this->db->get()->row();
   return $query;
  }

  public function getstaffwise()
  {
    $this->db->select('*');
   $this->db->from('staff_payslip');
   $query = $this->db->get()->row();
   return $query;
  }

  public function getColumns($name) {
    $query = $this->db->query('SHOW COLUMNS FROM '.$name);
    $columns = $query->result();
    
    //Adding qualification to column set
    $qual = new stdClass();
    $qual->Field = 'qualification';

    $columns = array_merge($columns, [$qual]);
    return $columns;
    //echo '<pre>';print_r($columns);die();
  }

  public function viewStaffReport($fields, $name,$status){
  
    
    if (!empty(strpos($fields,'qualification'))) {

       $fields = $fields .',staffId';
      //Get view data
      $fields = str_replace(',qualification','',$fields);
      $fields = str_replace('qualification','',$fields);
      
      $result = $this->db->select($fields)
        ->from($name)
        ->where('status',$status) //2: Approved
        ->get()->result();

      //Get qualification data
      $qual = $this->db->select('staff_id,qualification')
          ->from('staff_qualification')
          ->get()->result();

      //Merge qualifcation data
      foreach ($result as $key => &$val) {
       $temp = '';
        foreach ($qual as $key => $value) {
           if ($val->staffId == $value->staff_id) {
            if (!empty($temp)) $temp .= ', ';
            $temp .= $value->qualification;
          }
        }
        if (empty($temp)) $temp = 'Not specified';
        $val->qualification = $temp;
        unset($val->staffId);
       }
    } else {
      $result = $this->db->select($fields)
        ->from($name)
        ->where('status',$status) //2: Approved
        ->get()->result();
    }
    return $result;
  }


  public function getStaffData($fields, $staff_status_selected_id, $staff_type){
    // echo "<pre>";print_r($fields);die();
    $fields = str_replace('degn.designation as designation','(CASE WHEN degn.status = 1 THEN degn.designation ELSE "-" END) as designation',$fields);
    $fields = str_replace('dept.department as department','(CASE WHEN dept.status = 1 THEN dept.department ELSE "-" END) as department',$fields);
    $fields = str_replace('previous_designation_name as previous_designation_name','(CASE WHEN prev_degn.status = 1 THEN prev_degn.designation ELSE "-" END) as previous_designation_name',$fields);
    $fields = str_replace('sm.staff_age as staff_age', '(CASE WHEN sm.dob IS NULL OR sm.dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.dob, CURDATE()) END) as staff_age', $fields);
    $fields = str_replace('sm.father_age as father_age', '(CASE WHEN sm.father_dob IS NULL OR sm.father_dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.father_dob, CURDATE()) END) as father_age', $fields);
    $fields = str_replace('sm.mother_age as mother_age', '(CASE WHEN sm.mother_dob IS NULL OR sm.mother_dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.mother_dob, CURDATE()) END) as mother_age', $fields);
    $fields = str_replace('sm.spouse_age as spouse_age', '(CASE WHEN sm.spouse_dob IS NULL OR sm.spouse_dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.spouse_dob, CURDATE()) END) as spouse_age', $fields);
    $fields = str_replace('sm.child1_age as child1_age', '(CASE WHEN sm.child1_dob IS NULL OR sm.child1_dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.child1_dob, CURDATE()) END) as child1_age', $fields);
    $fields = str_replace('sm.child2_age as child2_age', '(CASE WHEN sm.child2_dob IS NULL OR sm.child2_dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.child2_dob, CURDATE()) END) as child2_age', $fields);
    $fields = str_replace('sm.child3_age as child3_age', '(CASE WHEN sm.child3_dob IS NULL OR sm.child3_dob = "1970-01-01" THEN "-" ELSE TIMESTAMPDIFF(YEAR, sm.child3_dob, CURDATE()) END) as child3_age', $fields);
    // echo "<pre>";print_r($staff_type);die();
    $staffResult =  $this->db->select($fields)
    ->from('staff_master sm')
    ->join('staff_departments dept', "dept.id= sm.department",'left')
    ->join('staff_designations degn', "degn.id= sm.designation",'left')
    ->join('staff_attendance_code sac','sm.id=sac.staff_id','left')
    ->join('staff_designations prev_degn', "prev_degn.id = sm.previous_designation_name", 'left')
    ->join('staff_master sm_exit_update_by', "sm.exit_updated_by=sm_exit_update_by.id", 'left');
    if($staff_status_selected_id != 0) {
      $this->db->where('sm.status', $staff_status_selected_id);
    }
    if(!empty($staff_type) && (count($staff_type) == 1 && $staff_type[0] != -1)) {
      $this->db->where_in('sm.staff_type', $staff_type);
    }
    $staffResult = $this->db->join('avatar a', 'sm.id=a.stakeholder_id and a.avatar_type=4')
    ->join('users u', 'a.user_id=u.id')
    ->join('new_payroll_master npm','npm.staff_id=sm.id','left')
    ->where('sm.is_primary_instance', 1)
    ->order_by('sm.first_name')
    ->get()->result();

    foreach ($staffResult as $a => $b) {
      if(isset($b->resignation_letter_doc)){
          $b->resignation_letter_doc= $this->filemanager->getSignedUrlWithExpiry($b->resignation_letter_doc);
      }
    }
    return $staffResult;
  }

   public function getAndMergeAddresses($StaffResult, $addressOf,  $addressType) {
      $addResult = $this->__getAddresses($StaffResult, $addressOf, $addressType);
    return $StaffResult;
  }

  public function getStaffData_msm($selectedColumns, $staff_status_selected_id){
    $allColumns = array_merge($this->mandatoryFields, $selectedColumns);
    $colString = '';
		foreach ($allColumns as $col) {
			if ($colString != '') {
				$colString .= ',';
			}
			$colString .= $col['columnNameWithTable'] . ' as ' . $col['varName'];
		}
    $staffResult =  $this->db->select($colString)
     ->from('staff_master sm');
     if($staff_status_selected_id != 0) {
       $this->db->where('sm.status', $staff_status_selected_id);
     }
     $staffResult = $this->db->join('avatar a', 'sm.id=a.stakeholder_id and a.avatar_type=4')
     ->join('users u', 'a.user_id=u.id')
     ->join('new_payroll_master npm','npm.staff_id=sm.id','left')
     ->order_by('sm.first_name')
     ->get()->result();
     return $staffResult;
   }

   public function get_display_columns($displayColumns){
    $displayColumns = array_merge($this->mandatoryFields, $displayColumns);
    return $displayColumns;
   }

  public function __getAddresses($StaffResult, $addressOf, $addressType){
    
     $addResult =  $this->db->select('sm.id as sId, add.address_type as addType, concat(add.Address_Line1, " ", add.Address_Line2, " ", add.area, " ", add.district, " ", add.state, " ", add.pin_code) as address')
    ->from('staff_master sm')
    ->where('sm.status','2')
    ->join('avatar a', 'sm.id=a.stakeholder_id and a.avatar_type=4')
    ->join('users u', 'a.user_id=u.id')
    ->where('add.avatar_type','4')
    ->where('add.address_type',$addressType)
    ->join('address_info add', 'sm.id=add.stakeholder_id')
    ->get()->result();

    foreach ($StaffResult as &$std) {
      $found = 0;
      // if (!empty($addResult)) {
         foreach ($addResult as $add) {
          if ($std->smId == $add->sId) {
            $found = 1;
            break;
          }
        }
        $key = $addressType . '_' . 'address';
        if ($found) {
          $std->$key = $add->address;
        } else {
          $std->$key = '<span style="color:red" >No Address given<span>';
        }
    }

    return $StaffResult;
  }
  public function staff_audit_report($from_date,$to_date)
  {
    $fromdate = date('Y-m-d', strtotime($from_date));
    $todate = date('Y-m-d', strtotime($to_date));
    $this->db_readonly->select("al.table_name,al.old_values,al.new_values,al.event_type,date_format(al.modified_on,'%d-%m-%Y') as date,ifnull(sm1.first_name,'-') as modified_by, concat(ifnull(sm.first_name,''),'',ifnull(sm.last_name,'')) as staff_name");
    $this->db_readonly->from('audit_log al');
    $this->db_readonly->join('staff_master sm', 'sm.id=al.source_id', 'left');
    $this->db_readonly->join('staff_master sm1', 'sm1.id=al.modified_by', 'left');
    if ($fromdate && $todate) {
      $this->db_readonly->where('date_format(al.modified_on,"%Y-%m-%d") BETWEEN "' . $fromdate . '" and "' . $todate . '"');
      $this->db_readonly->where_in('table_name', ['staff_master']);
    }
    $result = $this->db_readonly->get()->result();
    return $result;

  }

   public function add_staff_document_types($doc_name, $visibility) {
      // check if exist
      $duplicate= $this->db->select('*')->where('document_name', $doc_name)->get('staff_document_types');
      if($duplicate->num_rows() == 0){
        return $this->db->insert('staff_document_types', array('document_name' => $doc_name, 'visible_to_staff' => $visibility));
      }
      return false;
    }

    public function edit_staff_document_types($visibility, $doc_name_old) {
      $this->db->where('document_name', $doc_name_old)->update('staff_document_types', array('visible_to_staff' => $visibility));
      return $this->db->affected_rows();
    }

    public function get_staff_document_types() {
      return $this->db_readonly->select('*')->order_by('document_name')->get('staff_document_types')->result();
    }

    public function delete_document_type($primary_id) {
      $this->db->where('id', $primary_id)->delete('staff_document_types');
      return $this->db->affected_rows();
    }

    public function get_staff_missing_data($fData){
      // echo '<pre>';print_r($fData);die();
      $result = $this->db->select("$fData,id,concat(ifnull(first_name,''),' ', ifnull(last_name,'')) as staff_name")->from('staff_master')->where('status',2)->order_by('first_name','asc')->get()->result();

      $temp_arr = [];  
      if(!empty($result)){
        foreach($result as $key=>$val){
          if(!array_key_exists($val->id, $temp_arr)){
            $temp_arr[$val->id]['id'] = $val->id;
            $temp_arr[$val->id]['Sname'] = $val->staff_name;
            $temp_arr[$val->id]['staff_data'] = $val;
          }
        }
      }

      return $temp_arr;
    }

    public function get_staff_names(){
        return $this->db_readonly->select("id as sId, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as sName, identification_code,'staff' as type")
          ->from('staff_master')
          ->where('status', 2)
          ->order_by('first_name','asc')
          ->get()->result();
    }

    public function get_staff_history_data($from_date,$to_date,$staff_name){
      $this->db->select("sh.*,date_format(sh.edited_on,'%d-%m-%Y %h:%i %p') as edited_on, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name,concat(ifnull(sm1.first_name,''),' ',ifnull(sm1.last_name,'')) as edited_by")
          ->from('staff_edit_history sh')
          ->join('staff_master sm','sh.staff_id = sm.id')
          ->join('staff_master sm1','sh.edited_by=sm1.id','left');
          if(empty($staff_name)){
            $this->db->where('date_format(sh.edited_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ');
          }
          
          if($staff_name){
            $this->db->like('sm.first_name',$staff_name);
            $this->db->or_like('sm.last_name',$staff_name);
          }
          // ->where('sm.id', $staff_id)
          $this->db->order_by('sh.edited_on','desc');
          $res =  $this->db->get()->result();
          $search = ['m_','_',"{",'}','"'];
          $replace   = ['mother ',' ','','',''];
        foreach($res as $key=>$val){
          if($val->edited_by == " "){
            $val->edited_by='Admin';
          }
          $encoded_old_data = json_decode($val->old_data);
          $encoded_new_data = json_decode($val->new_data);

          if(empty($encoded_old_data) && empty($encoded_new_data)){
            $val->old_data = ucwords(str_replace($search,$replace,$val->old_data));
            $val->new_data = ucwords(str_replace($search,$replace,$val->new_data));
            continue;
          }
          if(isset($encoded_old_data->department_name) && !empty($encoded_old_data->department_name)){
            $encoded_old_data->department_name = $this->get_department($encoded_old_data->department_name);
          }
          if(isset($encoded_old_data->designation_name) && $encoded_old_data->designation_name != null){
            $encoded_old_data->designation_name = $this->get_designation($encoded_old_data->designation_name);
          }
          if(isset($encoded_old_data->previous_designation_name) && $encoded_old_data->previous_designation_name != null){
            $encoded_old_data->previous_designation_name = $this->get_designation($encoded_old_data->previous_designation_name);
          }

       
          if(isset($encoded_new_data->department_name) && $encoded_new_data->department_name != null){
            $encoded_new_data->department_name = $this->get_department($encoded_new_data->department_name);
          }
          if(isset($encoded_new_data->designation_name) && $encoded_new_data->designation_name != null){
            $encoded_new_data->designation_name = $this->get_designation($encoded_new_data->designation_name);
          }
          if(isset($encoded_new_data->previous_designation_name) && $encoded_new_data->previous_designation_name != null){
            $encoded_new_data->previous_designation_name = $this->get_designation($encoded_new_data->previous_designation_name);
          }
          $val->old_data = ucwords(str_replace($search,$replace,json_encode($encoded_old_data)));
          $val->new_data = ucwords(str_replace($search,$replace,json_encode($encoded_new_data))); 
        }

        return $res;
    }

    public function get_department($dept_id){
      $result = $this->db->where('id',$dept_id)->get('staff_departments')->row();
      return $result ? $result->department : $dept_id;
    }
    public function get_designation($id){
      $result = $this->db->where('id',$id)->get('staff_designations')->row();
      return $result ? $result->designation : $id;
    }

    public function get_config_display_fields(){
      $this->db_readonly->select('value');
      $this->db_readonly->where('name', 'staff_fields_enabled_for_school');
      return $this->db_readonly->get('config')->result();
    }

    public function save_staff_report_template($input){
      $data = array(
        'saved_report_name'=>$input['title'],
        'filters_selected'=>json_encode($input),
        'master_report_name'=>'staff_report_filters_template',
        'created_by'=>$this->authorization->getAvatarStakeHolderId(),
        'created_on'=>$this->Kolkata_datetime(),
        'acad_year_id'=>$this->acad_year->getAcadYearId()
      );
      if(isset($input['template_id'])){
               $this->db->where('id',$input['template_id']);
        return $this->db->update('predefined_reports',$data);
      }else{
        return $this->db->insert('predefined_reports',$data);
      }
    }

    public function Kolkata_datetime(){
      $timezone = new DateTimeZone("Asia/Kolkata" );
      $date = new DateTime();
      $date->setTimezone($timezone );
      $dtobj = $date->format('Y-m-d H:i:s');
      return $dtobj;
    }

    public function get_predefined_names($template_id=''){
      $this->db->select('id as template_id,saved_report_name,filters_selected,master_report_name')
      ->from('predefined_reports')
      ->where('master_report_name','staff_report_filters_template')
      ->where('master_report_name','staff_report_filters_template')
      ->where('acad_year_id',$this->acad_year->getAcadYearId());
      if(!empty($template_id)){
        $this->db->where('id',$template_id);
      }
      $result = $this->db->get()->result();

      foreach($result as $key => $val){
        $val->filters_selected = json_decode($val->filters_selected);
      }
      // echo '<pre>';print_r($result);die();
      return $result;
    }
 }
 ?>