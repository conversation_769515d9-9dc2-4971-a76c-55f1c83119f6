<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  02 May 2018
 *
 * Description: Model for Mass Update.
 *
 * Requirements: PHP5 or above
 *
 */

/**
 * Description of Student_Model
 *
 * <AUTHOR>
 */
class Student_report_model extends CI_Model {
    private $yearId;
    private $current_branch;
  public function __construct() {
      // $this->load->library('Settings');
      parent::__construct();
      $this->yearId =  $this->acad_year->getAcadYearId();
      $this->current_branch = $this->authorization->getCurrentBranch();
  }

  public function getColumns($name) {
    $query = $this->db->query('SHOW COLUMNS FROM '.$name);
    return $query->result();
  }

  public function getFeeStudent_details($feeStatus)
  {
    $this->db->select('fcs.student_id, fcs.fee_collect_status');
    $this->db->from('feev2_cohort_student fcs');
    $this->db->join('feev2_blueprint fb',"fcs.blueprint_id=fb.id and fb.acad_year_id=$this->yearId");
    $this->db->group_by('student_id');
    $stdIds =$this->db->get()->result();

    $stdIdsArray = [];
    foreach ($stdIds as $key => $std) {
      if ($feeStatus == $std->fee_collect_status) {
        array_push($stdIdsArray, $std->student_id);
      }
    }
    if (!empty($stdIdsArray)) {
      return $stdIdsArray;
    }
    return 0;
  }
  public function getStudentData($fields, $classId, $sectionId, $admission_type, $rte_non_rte, $feeStdIds, $admission_status){
    $this->db->select($fields.',(case when sy.promotion_status = 4 or sy.promotion_status = 5  then sy.promotion_status else sa.admission_status end) as admission_status')
      ->from('student_admission sa')
      // ->where('sa.admission_status','2')
      ->join('avatar a', 'sa.id=a.stakeholder_id and a.avatar_type=1')
      ->join('users us', 'a.user_id=us.id')
      ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      ->where("sy.promotion_status!='JOINED'")
      //->where('a.avatar_type', '1') //1->Student
      ->order_by('sy.class_section_id,sa.first_name')

      ->join('student_relation srf', 'sa.id=srf.std_id')
      ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
      //->where('srf.relation_type','Father')

      ->join('student_relation srm', 'sa.id=srm.std_id')
      ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
      //->where('srm.relation_type','Mother')

      ->join('avatar fa', 'f.id=fa.stakeholder_id and fa.avatar_type=2')
      ->join('users fus', 'fa.user_id=fus.id')
      //->where('fa.avatar_type', '2') //2->Parent

      ->join('class c', "sy.class_id=c.id and c.acad_year_id = $this->yearId")
      ->join('class_section cs', 'sy.class_section_id=cs.id','left')

      ->join('avatar ma', 'm.id=ma.stakeholder_id and ma.avatar_type=2')
      ->join('users mus', 'ma.user_id=mus.id');
      //->where('ma.avatar_type', '2'); //2->Parent
    if ($feeStdIds) {
      $this->db->where_in('sa.id',$feeStdIds);
    }
    // if ($admission_status) {
    //   $this->db->where('sa.admission_status',$admission_status);
    // }
    if ($sectionId != '9999') {
      //Show all students in a section
      $this->db->where('sy.class_section_id',$sectionId);
    }
    if ($classId) {
      $this->db->where('sy.class_id',$classId);
    }
    if ($admission_type) {
      $this->db->where('sy.admission_type',$admission_type);
    }
    if ($rte_non_rte) {
      $this->db->where('sy.is_rte',$rte_non_rte);
    }
    $stdResult = $this->db->get()->result();
    if ($admission_status) {
      $array = [];
      foreach ($stdResult as $key => $val) {
        if ($admission_status == $val->admission_status) {
          array_push($array, $val);
        }
      }
      return $array;      
    }
    return $stdResult;
  }

  public function getStudentReportData($fields, $student_ids) {
    $this->db->select($fields.',(case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status,(case when sa.gender = "M" then "Male" else "Female" end) as sGender,(CASE WHEN sa.has_transport = 1 THEN "Yes" WHEN sa.has_transport = 0 THEN "No" ELSE "" END) as shas_transport')
      ->from('student_admission sa')
      ->join('admission_status as','sa.admission_form_id=as.af_id','left')
      ->join('avatar a', 'sa.id=a.stakeholder_id and a.avatar_type=1')
      ->join('users us', 'a.user_id=us.id')
      ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      ->join('class_master_combinations cmc','sy.combination_id=cmc.id','left')
      ->where("sy.promotion_status!='JOINED'")
      ->join('student_relation srf', 'sa.id=srf.std_id')
      ->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'")
      ->join('student_relation srm', 'sa.id=srm.std_id')
      ->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'")
      // ->join('student_relation grm', 'sa.id=grm.std_id')
      // ->join('parent g', "g.id=grm.relation_id and grm.relation_type='Guardian'",'left')
      ->join('avatar fa', 'f.id=fa.stakeholder_id and fa.avatar_type=2')
      ->join('users fus', 'fa.user_id=fus.id')
      ->join('class c', "sy.class_id=c.id and c.acad_year_id = $this->yearId")
      ->join('class_section cs', 'sy.class_section_id=cs.id','left')
      ->join('avatar ma', 'm.id=ma.stakeholder_id and ma.avatar_type=2')
      ->join('users mus', 'ma.user_id=mus.id')
      ->join('semester sem', 'sy.semester=sem.id','left')
      ->join('staff_master sm', 'sy.single_window_approved_by=sm.id','left')
      ->join('staff_master sm1', 'sy.id_card_issued_by=sm1.id','left')
      ->join('staff_master sm2', 'f.id_card_issued_by=sm2.id','left')
      ->join('staff_master sm3', 'm.id_card_issued_by=sm3.id','left')
      ->where_in('sa.id',$student_ids)
      ->order_by('c.id')
      ->order_by('cs.id')
      ->order_by('sa.first_name');
    $result = $this->db->get()->result();
    foreach($result as $res){
      if(isset($res->sStudentHouse) && $res->sStudentHouse == '0'){
          $res->sStudentHouse = "-";
      }
      if(isset($res->combination) && empty($res->combination)){
          $res->combination = "-";
      }
    }
    return $result;
  }

    public function get_prev_school_report($student_ids){
      $this->db_readonly->select('sa.id as saId, sa.first_name, sps.year_id, sps.school_name, sps.class as class_name, sps.board, spsm.sub_name, spsm.grade, spsm.percentage, spsm.marks, spsm.marks_scored, sps.total_marks, sps.total_marks_scored, sps.total_percentage, sps.report_card');
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->join('student_prev_school sps','sa.id = sps.student_id');
      $this->db_readonly->join('student_prev_school_marks spsm', 'sps.id = spsm.sps_id', 'left');
      $this->db_readonly->where_in('sa.id',$student_ids);
      $result = $this->db_readonly->get()->result();
      return $result;
  }

  public function getStudentList($classId, $sectionId, $admission_type, $rte_non_rte, $admission_status, $category, $staff_kid, $active_status,$gender){
    $this->db->select('sa.id, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status,c.class_name,cs.section_name')
      ->from('student_admission sa')
      ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      ->join('class_section cs', 'sy.class_section_id=cs.id','left')
      ->join('class c', "sy.class_id=c.id")
      ->where("sy.promotion_status!='JOINED'")
      ->order_by('c.id','cs.id, sa.first_name')
      ->order_by('cs.id')
      ->order_by('sa.first_name');
    if ($staff_kid !='all') {
      $this->db->where('sa.has_staff',$staff_kid);
    }
    if ($sectionId != '9999') {
      //Show all students in a section
      $this->db->where('sy.class_section_id',$sectionId);
    }
    if ($classId) {
      $this->db->where('sy.class_id',$classId);
    }
    if ($admission_type) {
      $this->db->where('sy.admission_type',$admission_type);
    }
    if ($category) {
      $this->db->where('sa.category',$category);
    }
    if ($rte_non_rte) {
      $this->db->where('sy.is_rte',$rte_non_rte);
    }
    if (!$active_status) {
       $this->db->where('sa.temp_deactivation',1);
    }
    if($gender != ''){
      $this->db->where('sa.gender',$gender);
    }
    // if($admission_status){
    //   $this->db->where('sy.promotion_status',$admission_status);
    // }
    $stdResult = $this->db->get()->result();
    $student_ids = [];
    if ($admission_status) {
      foreach ($stdResult as $key => $val) {
        if (in_array($val->admission_status,$admission_status)) {
          array_push($student_ids, $val->id);
        }
      }
    } else {
      foreach ($stdResult as $key => $val) {
        array_push($student_ids, $val->id);
      }
    }
    return $student_ids;
  }


    public function getAndMergeAddressesReport($stdData,$addressOf,$addressType, $student_ids, $classId, $sectionId, $admission_type){
      if ($addressOf == 'Father')
      $addResult = $this->__getAddresses ($stdData, 'Father', $addressType, $student_ids, $classId, $sectionId, $admission_type);

    if ($addressOf == 'Mother')
      $addResult = $this->__getAddresses ($stdData, 'Mother', $addressType, $student_ids, $classId, $sectionId, $admission_type);

    if ($addressOf == 'Student')
      $addResult = $this->__getAddresses ($stdData, 'Student', $addressType, $student_ids, $classId, $sectionId, $admission_type);

    return $addResult;

  }

  // public function getAndMergeAddresses($stdResult, $addressOf, $addressType, $sectionId, $classId, $admission_type) {

  //   if ($addressOf == 'Father')
  //     $addResult = $this->__getAddresses ($stdResult, 'Father', $addressType, $classId, $sectionId, $admission_type);

  //   if ($addressOf == 'Mother')
  //     $addResult = $this->__getAddresses ($stdResult, 'Mother', $addressType, $classId, $sectionId,$admission_type);

  //   if ($addressOf == 'Student')
  //     $addResult = $this->__getAddresses ($stdResult, 'Student', $addressType,$classId, $sectionId, $admission_type);

  //   return $stdResult;
  // }

  public function getAndMergeHealthData($stdData, $healthCol, $colName, $classId, $sectionId, $admission_type) {
    $this->db->select('sd.id as sId, sh.*')
      ->from('student_admission sd')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->join('student_health sh','sd.id=sh.student_id')
      ->where('sd.admission_status','2');
      if ($sectionId != '9999') {
        //Show all students in a section
        $this->db->where('sy.class_section_id',$sectionId);
      } 
       if ($classId) {
        $this->db->where('sy.class_id',$classId);
      }
      if ($admission_type) {
        $this->db->where('sy.admission_type',$admission_type);
      }
      $healthResult = $this->db->get()->result();
      foreach ($stdData as &$std) {
        $found = 0;
        foreach ($healthResult as $add) {
          if ($std->saId == $add->sId) {
              $found = 1;
              break;
            }
          }
          if ($found) {
            $std->$healthCol = $add->$colName;
          } else {
            $std->$healthCol = '';
          }
      }
      return $stdData;
  }

  public function getAndMergeHealthDataReport($stdData, $healthCol, $colName, $student_ids) {
    $this->db->select('sd.id as sId, sh.*')
      ->from('student_admission sd')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->join('student_health sh','sd.id=sh.student_id')
      ->where('sd.admission_status','2')
      ->where_in('sd.id',$student_ids);
      $healthResult = $this->db->get()->result();
      foreach ($stdData as &$std) {
        $found = 0;
        foreach ($healthResult as $add) {
          if ($std->saId == $add->sId) {
              $found = 1;
              break;
            }
          }
          if ($found) {
            $std->$healthCol = $add->$colName;
          } else {
            $std->$healthCol = '';
          }
      }
      return $stdData;
  }

  public function getAndMergeGuardianDataReport($stdData, $guardianCol, $colName, $student_ids){
    $this->db->select('sa.id as sId, g.*,concat(sm.first_name,ifnull(sm.last_name,"")) as id_card_issued_by')
    ->from('student_admission sa')
    ->where('sa.admission_status','2')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->where("sy.promotion_status!='JOINED'")
    ->join('student_relation grm', 'sa.id=grm.std_id')
    ->join('parent g', "g.id=grm.relation_id and grm.relation_type='Guardian'") 
    ->join('staff_master sm', "g.id_card_issued_by=sm.id",'left') 
    ->where_in('sa.id',$student_ids);
    $guardianList =  $this->db->get()->result();
    foreach ($stdData as &$std) {
      $found = 0;
      foreach ($guardianList as $guard) {
        if ($std->saId == $guard->student_id) {
            $found = 1;
            break;
          }
        }
        if ($found) {
          $std->$guardianCol = $guard->$colName;
        } else {
          $std->$guardianCol = '';
        }
    }
    return $stdData;
  }

  private function __getAddressesData($stdResult, $addressOf, $addressType, $student_ids) {
    //echo '<pre>';print_r($addressOf);die();
    $addresses = $this->settings->getSetting('address_types');

    $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1, " ", add.Address_Line2, " ", add.area, " ", add.district, " ", add.state, " ", add.pin_code) as address, add.unformatted_address')
      ->from('student_admission sd')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->where('sd.admission_status','2')
      ->where('add.address_type', $addressType);

    switch ($addressOf) {
    case 'Father':
      $this->db->join('student_relation srf', 'sd.id=srf.std_id')
      ->join('parent f', 'f.id=srf.relation_id')
      ->where('srf.relation_type','Father')

      ->join('address_info add', 'f.id=add.stakeholder_id')
      ->where('add.avatar_type', '2');
      break;
    case 'Mother':
      $this->db->join('student_relation srf', 'sd.id=srf.std_id')
      ->join('parent f', 'f.id=srf.relation_id')
      ->where('srf.relation_type','Mother')

      ->join('address_info add', 'f.id=add.stakeholder_id')
      ->where('add.avatar_type', '2');
      break;
    case 'Student':
      $this->db->join('address_info add', 'sd.id=add.stakeholder_id')
      ->where('add.avatar_type', '1');
      break;
    }
    $this->db->where('sd.id',$student_ids);
    $addResult = $this->db->get()->result();

    foreach ($stdResult as &$std) {
      $found = 0;
      // if (!empty($addResult)) {
         foreach ($addResult as $add) {
          if ($std->saId == $add->sId) {
            $found = 1;
            break;
          }
        }
        $key = $addressOf . '_' . $addressType . '_' . 'address';
        $unkey = $addressOf . '_' . $addressType . '_' . 'unaddress';
        if ($found) {
          $std->$key = $add->address;
          $std->$unkey = $add->unformatted_address;
        } else {
          $std->$key = '<No Address given>';
          $std->$unkey = '<No Unformatted Address given>';
        }
      // }
    }
    return $addResult;
  }

  private function __getAddresses ($stdData, $addressOf, $addressType, $student_ids, $classId, $sectionId, $admission_type){
    $addresses = $this->settings->getSetting('address_types');

    switch ($addressOf) {
      case 'Father':
        $father_ids = $this->db->select('relation_id')
        ->from('student_relation')
        ->where('relation_type','Father')
        ->where_in('std_id', $student_ids)
        ->get()->result();
        $final_ids = array();

        foreach($father_ids as $key => $id){
          array_push($final_ids,$id->relation_id);
        }
        break;
      case 'Mother':

        $mother_ids = $this->db->select('relation_id')
        ->from('student_relation')
        ->where('relation_type','Mother')
        ->where_in('std_id', $student_ids)
        ->get()->result();
        $final_ids = array();

        foreach($mother_ids as $key => $id){
          array_push($final_ids,$id->relation_id);
        }

        break;

        case 'Student':
          $final_ids = $student_ids;
        break;

      }

    $this->db->select('')
      ->from('student_admission sd')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->where('sd.admission_status','2')
      ->where('add.address_type', $addressType);

    switch ($addressOf) {
    case 'Father':
      $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1, " ", add.Address_Line2, " ", add.area, " ", add.district, " ", add.state, " ", add.pin_code) as Father_address')
      ->join('student_relation srf', 'sd.id=srf.std_id')
      ->join('parent f', 'f.id=srf.relation_id')
      ->where('srf.relation_type','Father')

      ->join('address_info add', 'f.id=add.stakeholder_id')
      ->where('add.avatar_type', '2');
      break;
    case 'Mother':
      $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1, " ", add.Address_Line2, " ", add.area, " ", add.district, " ", add.state, " ", add.pin_code) as Mother_address')
      ->join('student_relation srf', 'sd.id=srf.std_id')
      ->join('parent f', 'f.id=srf.relation_id')
      ->where('srf.relation_type','Mother')

      ->join('address_info add', 'f.id=add.stakeholder_id')
      ->where('add.avatar_type', '2');
      break;
    case 'Student':
      $this->db->select('sd.id as sId, add.address_type as addType, concat(add.Address_Line1, " ", add.Address_Line2, " ", add.area, " ", add.district, " ", add.state, " ", add.pin_code) as Student_address')
      ->join('address_info add', 'sd.id=add.stakeholder_id')

      ->where('add.avatar_type', '1');
      break;
    }

    $this->db->where_in('add.stakeholder_id', $final_ids);
       if ($sectionId != '9999') {
      //Show all students in a section
      $this->db->where('sy.class_section_id',$sectionId);
    }
     if ($classId) {
      $this->db->where('sy.class_id',$classId);
    }
    if ($admission_type) {
      $this->db->where('sy.admission_type',$admission_type);
    }
    // if ($rte_non_rte) {
    //   $this->db->where('sa.is_rte',$rte_non_rte);
    // }
    $addResult = $this->db->get()->result();
      // echo $addressOf;
      // echo '<pre>'; print_r($addResult);
      // echo $this->db->last_query();
      // die();
    // foreach ($stdResult as &$std) {
    //   $found = 0;
    //   // if (!empty($addResult)) {
    //      foreach ($addResult as $add) {
    //       if ($std->saId == $add->sId) {
    //         $found = 1;
    //         break;
    //       }
    //     }
    //     $key = $addressOf . '_' . $addressType . '_' . 'address';
    //     $unkey = $addressOf . '_' . $addressType . '_' . 'unaddress';
    //     if ($found) {
    //       $std->$key = $add->address;
    //       $std->$unkey = $add->unformatted_address;
    //     } else {
    //       $std->$key = '<No Address given>';
    //       $std->$unkey = '<No Unformatted Address given>';
    //     }
    //   // }
    // }
    return $addResult;
  }

  public function getClassName ($classId) {
    return $this->db->select('class_name')->from('class')->where('id',$classId)->get()->row()->class_name;
  }

  public function getSectionName ($sectionId) {
    $result = $this->db->select('section_name')->from('class_section')->where('id',$sectionId)->get()->row();

    if (!empty($result))
      return $result->section_name;
    else
      return '';
  }

  public function getStaffDistinctColumn($column_name,$table_name) {
    $result = $this->db->select("distinct(" . $column_name . ") as name," . $column_name. " as value")
    ->from($table_name)
    ->order_by($column_name)
    ->where($column_name.' !=', "")
    ->get()->result();

    return $result;
  }

  public function getSectionOptions($classId) {
    $result = $this->db->select("cs.id as value, cs.section_name as name")
      ->from('class_section cs')
      ->where('cs.class_id', $classId)
      ->get()->result();
    
    return $result;
  }

  public function getClassOptions() {
    $result = $this->db->select("c.id as value, c.class_name as name")
      ->from('class c')
      ->get()->result();
    
    return $result;
  }

  public function checkIfUnique($table_name, $column_name, $value) {
    $this->db->where($column_name,$value);
    $query = $this->db->get($table_name);
    if ($query->num_rows() > 0)
      return true;
    else
      return false;
  }

  public function get_class_wise_student_list(){

    $master = $this->db->select("cs.id as class_section_id, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name,  count(if(sa.gender='M',1,NULL)) 'boys', count(if(sa.gender='F',1,NULL)) 'girls', count(if(sa.admission_status='2',1,NULL)) 'approved',count(if(sa.admission_status='1',1,NULL)) 'pending', count(if(sy.is_rte='1',1,NULL)) 'rte_student', count(if(sy.is_rte='2',1,NULL)) 'non_rte_student',c.principal_id,c.coordinator_id ,c.academic_director_id,c.admin_id,c.viceprincipal,c.transport_manager,c.facilities_manager,c.head_of_the_boarding,c.it_support,c.accountant,cs.assistant_class_teacher_id,cs.assistant_class_teacher_2")
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class_section cs', 'sy.class_section_id=cs.id')
    ->join('class c', "sy.class_id=c.id and c.acad_year_id=$this->yearId")
    ->join('staff_master sm','cs.class_teacher_id=sm.id','left')
    ->where_in('sa.admission_status', [1, 2]) 
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->where("sy.promotion_status!='JOINED'")
    ->group_by('cs.id')
    ->order_by('cs.display_order')
    ->get()->result();

    $this->db->distinct('sss.subject_id');
    $this->db->distinct('s.id');
    $list = $this->db->select("s.short_name as sub_name, sss.class_section_id,  concat(ifnull(sm.first_name,''), ' ' ,ifnull(sm.last_name,'')) as teacher_name, sm.id as staff_id")
    ->from('staff_subject_section sss')
    ->where('sss.acad_year_id',$this->yearId)
    ->join('stafflist_subject_section stss', 'stss.sss_id=sss.id')
    ->join('staff_master sm','stss.staff_id=sm.id','left')
    ->join('subjects s','sss.subject_id=s.id')
    ->order_by('sm.first_name')
    ->get()->result();

      foreach ($master as $key => $value) {
      // echo "<pre>"; print_r($master);die();
        $value->principal_id = $this->_getAvatarNameById($value->principal_id);
        $value->coordinator_id = $this->_getAvatarNameById($value->coordinator_id);
        $value->academic_director_id = $this->_getAvatarNameById($value->academic_director_id);
        $value->admin_id = $this->_getAvatarNameById($value->admin_id);
        $value->viceprincipal = $this->_getAvatarNameById($value->viceprincipal);
        $value->transport_manager = $this->_getAvatarNameById($value->transport_manager);
        $value->facilities_manager = $this->_getAvatarNameById($value->facilities_manager);
        $value->head_of_the_boarding = $this->_getAvatarNameById($value->head_of_the_boarding);
        $value->it_support = $this->_getAvatarNameById($value->it_support);
        $value->accountant = $this->_getAvatarNameById($value->accountant);
        $value->assistant_class_teacher_id = $this->_getAvatarNameById($value->assistant_class_teacher_id);
        $value->assistant_class_teacher_2 = $this->_getAvatarNameById($value->assistant_class_teacher_2);

    }
    
    
    foreach ($master as $key => &$ma) {
      $teachers = [];
      foreach ($list as $key => $val) {
        if ($ma->class_section_id == $val->class_section_id) {
          $teachers[trim($val->teacher_name)][] = $val->sub_name;
        }
      }
      $ma->teacher_subject = $teachers;
     }
     
    return $master;
  }
  private function _getAvatarNameById($avatarId) {
    $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')    
        ->where('id',$avatarId)
        ->get()->row();
    if (!empty($collected)) {
      return $collected->staffName;
    }else{
      return '-';
    }
  }

  public function get_student_acadyearSelectionWise($acad_year_id){
    $classMaster = $this->db->select('id as classId, class_name, academic_budget')
    ->from('class')
    ->where('is_placeholder!=1')
    ->where('acad_year_id',$acad_year_id)
    ->get()->result();

    $result = $this->db->select("sy.class_id as class_id, c.class_name, '0' as academic_budget,
      count(if(sa.gender='M' and sy.admission_type='2',1,NULL)) 'New_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='2',1,NULL)) 'New_Admission_girls', 
      count(if(sa.gender='M' and sy.admission_type='1',1,NULL)) 'Re_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='1',1,NULL)) 'Re_Admission_girls', 
      count(if(sy.admission_type='2' and sy.is_rte='1',1,NULL)) 'NeRTE', 
      count(if(sy.admission_type='1' and sy.is_rte='1',1,NULL)) 'ReRTE', 
      count(if(sa.admission_status='2',1,NULL)) 'approved', ")
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acad_year_id")
    ->join('class c','c.id=sy.class_id')
    ->where('sa.admission_status','2')
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->group_by('sy.class_id')
    ->order_by('sy.class_id')
    ->get()->result();
    foreach ($classMaster as $key => &$val) {
        $val->Rboys = 0;
        $val->Rgirls = 0;
        $val->Nboys = 0;
        $val->Ngirls = 0;
        $val->approved =0;
        $val->ReRTE =0;
        $val->NeRTE =0;
      foreach ($result as $key => $res) {
        if ($val->classId == $res->class_id) {
          $val->Rboys = $res->Re_Admission_boys;
          $val->Rgirls = $res->Re_Admission_girls;
          $val->Nboys = $res->New_Admission_boys;
          $val->Ngirls = $res->New_Admission_girls;
          $val->approved = $res->approved;
          $val->ReRTE = $res->ReRTE;
          $val->NeRTE = $res->NeRTE;
        }
      }
    }

    if (!empty($classMaster)) {
      return $classMaster;
    }else{
      return $result;
    }
  }

  public function get_fee_count_acadyearClassWise($blueprintId, $acadYearId){
    return $this->db->select("
      count(if(sa.gender='M' and sy.admission_type='2' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_New_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='2' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_New_Admission_girls', 
      count(if(sa.gender='M' and sy.admission_type='1' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_Re_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='1' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_Re_Admission_girls',
      count(if(sa.gender='M' and sy.admission_type='2' and fcs.fee_collect_status='COHORT_CONFIRM',1,NULL)) 'Pending_Fee_New_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='2' and fcs.fee_collect_status='COHORT_CONFIRM',1,NULL)) 'Pending_Fee_New_Admission_girls', 
      count(if(sa.gender='M' and sy.admission_type='1' and fcs.fee_collect_status='COHORT_CONFIRM',1,NULL)) 'Pending_Fee_Re_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='1' and fcs.fee_collect_status='COHORT_CONFIRM',1,NULL)) 'Pending_Fee_Re_Admission_girls', 
      count(if(sa.admission_status='2',1,NULL)) 'approved', sy.class_id
      ")
    ->from('feev2_blueprint fb')
    ->where('fb.id',$blueprintId)
    ->where('fb.acad_year_id',$acadYearId)
    ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
    ->join('student_admission sa','sa.id=fcs.student_id')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acadYearId")
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->where('sa.admission_status','2')
    ->group_by('sy.class_id')
    ->get()->result();
  }

  public function get_blueprints_acad_year_wise(){
    $this->db->select('id, name, acad_year_id');
    $this->db->from('feev2_blueprint');
    $this->db->where('acad_year_id',$this->yearId);
   return $this->db->get()->result();
  }

 
  public function get_student_count_admission_wise(){
    return $this->db->select("
      count(if(sa.gender='M' and sy.admission_type='2',1,NULL)) 'New-Admission-boys', 
      count(if(sa.gender='F' and sy.admission_type='2',1,NULL)) 'New-Admission-girls', 
      count(if(sa.gender='M' and sy.admission_type='1',1,NULL)) 'Re-Admission-boys', 
      count(if(sa.gender='F' and sy.admission_type='1',1,NULL)) 'Re-Admission-girls', 
      count(if(sa.admission_status='2',1,NULL)) 'approved', 
      sy.acad_year_id, ay.acad_year")
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.promotion_status !='JOINED'")
    ->join('academic_year ay','ay.id=sy.acad_year_id')
    ->where('sa.admission_status','2')
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->group_by('sy.acad_year_id')
    ->order_by('sy.acad_year_id', 'DESC')
    ->get()->result_array();
  }
  public function get_fees_count_admission_wise($acadYearId){
    $result =  $this->db->select("
      count(if(sa.gender='M' and sy.admission_type='2' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_New_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='2' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_New_Admission_girls', 
      count(if(sa.gender='M' and sy.admission_type='1' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_Re_Admission_boys', 
      count(if(sa.gender='F' and sy.admission_type='1' and fcs.fee_collect_status='STARTED',1,NULL)) 'Fee_Re_Admission_girls', 
      count(if(sa.admission_status='2',1,NULL)) 'approved', 
      sy.acad_year_id, fcs.blueprint_id, fb.name as blueprint_name
      ")
    ->from('feev2_blueprint fb')
    ->where('fb.acad_year_id',$acadYearId)
    ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
    ->join('student_admission sa','sa.id=fcs.student_id and sa.admission_status=2')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$acadYearId")
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->group_by('fb.acad_year_id')
    ->group_by('fb.id')
    ->get()->result();
    $resArray = [];
    foreach ($result as $key => $val) {
      if (!array_key_exists($val->blueprint_id,  $resArray)) {
        $resArray[$val->blueprint_id] =0;
      }
      $resArray[$val->blueprint_id] += ($val->Fee_New_Admission_boys + $val->Fee_New_Admission_girls + $val->Fee_Re_Admission_boys + $val->Fee_Re_Admission_girls);
    }
    foreach ($result as $key => &$value) {
      foreach ($resArray as $blueId => $res) {
        if ($value->blueprint_id == $blueId) {
          $value->totalBlueprintTotal = $res;
        }
      }
    }
    return $result;
  }

  public function saveAdmissionClassBudget($class_id, $budget) {
    return $this->db->where('id', $class_id)->update('class', array('academic_budget' => $budget));
  }

  public function get_blueprints_by_selected_year($yearId){
    return $this->db->select('*')->where('acad_year_id',$yearId)->from('feev2_blueprint')->get()->result();
  }

  public function get_all_alumni_student(){

    $this->db_readonly->select("sd.id, sd.admission_no, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as student_name, cs.section_name, c.class_name ");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
    $this->db_readonly->where('ss.acad_year_id',$this->yearId);
    $this->db_readonly->where('sd.admission_status','4');
    $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
    $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
    if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    return $this->db_readonly->get()->result();
  }

  public function getTransportStudentList($classId, $stops, $kilometerId, $pickup_mode){
    $this->db_readonly->select('sa.id')
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class_section cs', 'sy.class_section_id=cs.id')
    ->join('class c', "sy.class_id=c.id")
    ->where("sy.promotion_status!='JOINED'")
    ->order_by('c.id','cs.id, sa.first_name')
    ->order_by('cs.id')
    ->order_by('sa.first_name');
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    // if ($sectionId != '9999') {
    //   $this->db_readonly->where('sy.class_section_id',$sectionId);
    // }
    if ($classId) {
      $this->db_readonly->where_in('sy.class_id',$classId);
    }
    if ($stops) {
      $this->db_readonly->where('sy.stop',$stops);
    }
    if ($pickup_mode) {
      $this->db_readonly->where('sy.pickup_mode',$pickup_mode);
    }
    if ($kilometerId) {
      $this->db_readonly->where('sy.has_transport_km',$kilometerId);
    }
    $stdResult = $this->db_readonly->get()->result();
    $student_ids = [];
    foreach ($stdResult as $key => $val) {
      array_push($student_ids, $val->id);
    }
    return $student_ids;
  }

  public function getTransportSudentReportData($student_ids,$installmentId) {
    $this->db_readonly->select("concat(ifnull(c.class_name,''), '' ,ifnull(cs.section_name,'')) as classSection, concat(ifnull(sa.first_name,''), ' ' ,ifnull(sa.last_name,'')) as student_name, ifnull(sy.has_transport_km,'') as  has_transport_km, sy.stop, sy.pickup_mode, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, ifnull(p.mobile_no,'') as father_mobile_no, sa.id as stdId, ifnull(p1.mobile_no,'') as mother_mobile_no, ifnull(p1.email,'') as mother_email_id, ifnull(p.email,'') as father_email_id,concat(ifnull(pr.first_name,''),' ',ifnull(pr.last_name,'')) as created_by,ifnull(date_format(sy.transport_details_created_on,'%d-%m-%Y'),'') as transport_details_created_on,ifnull(sy.nearest_land_mark,'') as nearest_land_mark")
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id")
    ->join('avatar a','sy.transport_details_created_by=a.id','left')
    ->join('parent pr','a.stakeholder_id=pr.id and avatar_type=2','left')
    ->where('sy.acad_year_id',$this->yearId)
    ->where("sy.promotion_status!='JOINED'")
    ->join('class c', "sy.class_id=c.id")
    ->join('class_section cs', 'sy.class_section_id=cs.id','left')
    ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
    ->join("student_relation sr1", "sr1.std_id=sa.id and sr1.relation_type='Mother'")
    ->join("parent p", "p.id=sr.relation_id")
    ->join("parent p1", "p1.id=sr1.relation_id")
    ->where_in('sa.id',$student_ids)
    ->order_by('c.id')
    ->order_by('cs.id')
    ->order_by('sa.first_name');
    $stdDetails = $this->db_readonly->get()->result();

    $this->db_readonly->select("(case when fcs.fee_collect_status ='STARTED' THEN 'PAID' ELSE 'NOT-PAID' END) AS trans_status, fcs.student_id, 
      (case when fsi.status ='FULL' THEN 'PAID' ELSE 'NOT-PAID' END) AS ins_trans_status,fi.name as insName,fi.id as insId, date_format(ft.paid_datetime,'%d-%m-%Y') as paid_date")
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_transaction_installment_component ftic','fsi.id=ftic.fee_student_installments_id')
    ->join('feev2_transaction ft','ftic.fee_transaction_id=ft.id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->where_in('fcs.student_id',$student_ids)
    ->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id')
    ->where('fb.is_transport',1)
    ->where('fb.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if ($installmentId) {
      $this->db_readonly->where('fi.id',$installmentId);
    }
    $feeDetails = $this->db_readonly->get()->result();
    foreach ($stdDetails as $key => &$value) {
      $value->trans_status = [];
      if (!empty($feeDetails)) {
         foreach ($feeDetails as $key => $val) {
          if ($value->stdId == $val->student_id) {
            $value->trans_status[$val->insId] = $val->ins_trans_status;
            $value->trans_date[$val->insId] = $val->paid_date;
            $value->installment_name[$val->insId] = $val->insName;
          }
        }
      }
    }
    return $stdDetails;
  }

  public function get_admission_student_list_ids($classes_id, $selected_acad_yearId){
    $this->db_readonly->select('sa.id as stdId')
    ->from('student_admission sa')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->where('sy.acad_year_id',$selected_acad_yearId)
    ->where('sa.admission_status',2)
    ->where('sy.promotion_status!=', '4') 
    ->where('sy.promotion_status!=', '5');
    if ($classes_id) {
      $this->db_readonly->where('sy.class_id',$classes_id);
    }
    // if ($fee_status !='' && $fee_status !='NOT_ASSIGNED') {
    //   $this->db_readonly->join('feev2_cohort_student fcs','sa.id=fcs.student_id');
    //   $this->db_readonly->where('fcs.fee_collect_status',$fee_status);
    //   $this->db_readonly->where('fcs.blueprint_id',1);
    // }
    // if ($admission_status) {
    //   $this->db_readonly->where('sy.admission_type',$admission_status);
    // }
    // if ($gender != 'all') {
    //   $this->db_readonly->where('sa.gender',$gender);
    // }
    $this->db_readonly->order_by('c.id, sa.first_name');
    $result = $this->db_readonly->get()->result();
    $stdIds = [];
    foreach ($result as $key => $res) {
      array_push($stdIds, $res->stdId);
    }
    return $stdIds;
  }

  public function get_admission_student_list_data($student_ids, $selected_blueprint_id, $selected_acad_yearId){
    $this->db_readonly->select("sa.id as stdId,  concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name, concat(ifnull(c.class_name,''), '' ,ifnull(cs.section_name,'')) as classSection, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, case gender when 'M' then 'Male' when 'F' then 'Female' else 'Other' end as gender, case when fcs.fee_collect_status = 'STARTED' then 'Paid' when fcs.fee_collect_status = 'COHORT_CONFIRM' then 'Not-Paid' else 'Not-Assigned' end as fee_status, p.mobile_no, sy.admission_type, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status")
    ->from('student_admission sa')
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.acad_year_id',$selected_acad_yearId)
    ->where('sa.admission_status',2)
    ->join('feev2_cohort_student fcs',"sa.id=fcs.student_id and fcs.blueprint_id = $selected_blueprint_id",'left')
    ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
    ->join("parent p", "p.id=sr.relation_id")
    ->order_by('c.id, sa.first_name')
    ->where_in('sa.id',$student_ids);
    $result =  $this->db_readonly->get()->result();
    foreach ($result as &$std) {
      $std->admission_status_name = $this->settings->getSetting('admission_status')[$std->admission_status];
    }
    return $result;
  }

  public function get_class_list_all($selected_acadYearId){
    return $this->db->select("c.id as cId, ifnull(c.class_name,'') as class_name")
    ->from('class c')
    ->where('c.acad_year_id',$selected_acadYearId)
    ->where('c.is_placeholder!=1')
    ->get()->result();
  }

  public function student_admission_deactive_user_by_id($student_id, $remarks){

    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $time = new DateTime();
    $time->setTimezone($timezone);
    $merge = new DateTime($date->format('Y-m-d') .' ' .$time->format('H:i:s'));
    $modifiedDate =  $merge->format('Y-m-d H:i:s');

    $users = $this->db->select('u.username, sr.relation_type, u.id as user_id')
    ->from('student_relation sr')
    ->where('sr.std_id',$student_id)
    ->join('parent p','sr.relation_id=p.id')
    ->join('avatar a','p.id=a.stakeholder_id')
    ->join('users u','a.user_id=u.id')
    ->where('a.avatar_type','2')
    ->get()->result();
    $usersIds = [];
    $fatherUserName = '';
    $motherUserName = '';
    foreach ($users as $key => $val) {
      array_push($usersIds, $val->user_id);
      if ($val->relation_type =='Father') {
        $fatherUserName = $val->username;
      }
      if ($val->relation_type =='Mother') {
        $motherUserName = $val->username;
      }
    }


    $sql = "SELECT id FROM ci_sessions cs WHERE data LIKE '%$fatherUserName%' or data LIKE '%$motherUserName%'";
    $query = $this->db->query($sql);
    $sessionIds = [];
    foreach ($query->result() as $key => $val) {
        array_push($sessionIds, $val->id);
    }
    $this->db->trans_start();
    
    $this->db->where('id', $student_id);
    $this->db->update('student_admission',array('admission_status'=>'3')); // 3 Rejected
        
    $this->db->where('student_admission_id', $student_id);
    $this->db->where('acad_year_id',$this->yearId);
    $this->db->update('student_year', array('status_modified_by'=>$this->authorization->getAvatarId(),'terminate_date'=>date('Y-m-d', strtotime($modifiedDate)), 'terminate_remarks'=>$remarks));
    
    if (!empty($sessionIds)) {
      $this->db->where_in('id',$sessionIds);
      $this->db->delete('ci_sessions');
    }
        
    if (!empty($usersIds)) {
      $this->db->where_in('id',$usersIds);
      $this->db->update('users',array('token'=>''));
    }
    $this->db->trans_complete();
    if ($this->db->trans_status()) {
      return true;
    }else{
      return false;
    }
  }

  public function get_student_category_wise_count(){
   
    $result = $this->db->select("sy.class_id as class_id,  count(if(sa.gender='M',1,NULL)) 'boys', count(if(sa.gender='F',1,NULL)) 'girls', count(if(sa.admission_status='2',1,NULL)) 'approved', category")
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class c','c.id=sy.class_id')
    ->where('c.is_placeholder!=1')
    ->where('sa.admission_status','2')
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->group_by('sy.class_id')
    ->group_by('sa.category')
    ->order_by('sy.class_id')
    ->get()->result();

    $categoryWise = $this->db->select("sy.class_id as class_id,  count(if(sa.gender='M',1,NULL)) 'boys', count(if(sa.gender='F',1,NULL)) 'girls', count(if(sa.admission_status='2',1,NULL)) 'approved', category")
    ->from('student_admission sa')
    ->join('student_year sy', "sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
    ->join('class c','c.id=sy.class_id')
    ->where('c.is_placeholder!=1')
    ->where('sa.admission_status','2')
    ->where('sy.promotion_status!=','4')
    ->where('sy.promotion_status!=','5')
    ->group_by('sa.category')
    ->get()->result();


    $classWiseArry = [];
    foreach ($result as $key => $value) {
      if ($value->category != 0 && $value->category !='') {
        $classWiseArry[$value->class_id][$value->category] = $value;
      }
    }
    $catWiseArry = [];
    foreach ($categoryWise as $key => $value) {
      if ($value->category != 0 && $value->category !='') {
        $catWiseArry[$value->category] = $value;
      }
    }

    return array('classWise'=>$classWiseArry, 'catWiseArry'=>$catWiseArry);
  }

  public function get_student_category_class(){
    return $this->db->select('c.id as classId ,class_name,count(sy.id) as total_student')
    ->from('student_year sy')
    ->join('class c','sy.class_id=c.id')
    ->join('student_admission sa','sa.id=sy.student_admission_id')
    ->where('is_placeholder!=','1')
    ->where('promotion_status !=','4')
    ->where('promotion_status !=','5')
    ->where('sa.admission_status','2')
    ->where('sy.acad_year_id',$this->yearId)
    ->group_by('c.id')
    ->order_by('c.id')
    ->get()->result();

  }

  public function get_transport_blueprints_list(){
    $this->db->select('fit.id, fit.name as installmentsType');
    $this->db->from('feev2_blueprint fb');
    $this->db->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id');
    $this->db->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id');
  
    $this->db->where('fb.acad_year_id',$this->yearId);
    $this->db->where('fb.is_transport',1);
    if($this->current_branch) {
      $this->db->where('fb.branches',$this->current_branch);
    }
    return $this->db->get()->result();

    // $this->db->select('id, name as blueprint_name');
    // $this->db->from('feev2_blueprint');
    // $this->db->where('acad_year_id',$this->yearId);
    // $this->db->where('is_transport',1);
    // if($this->current_branch) {
    //   $this->db->where('branches',$this->current_branch);
    // }
    // return $this->db->get()->row();
  }

  public function get_transport_installment_list($installment_type, $installmentId){
    $this->db->select('fi.id, fi.name as installment_name');
    $this->db->from('feev2_blueprint fb');
    $this->db->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id');
    $this->db->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id');
    $this->db->join('feev2_installments fi','fit.id=fi.feev2_installment_type_id');
    $this->db->where('fb.acad_year_id',$this->yearId);
    $this->db->where('fb.is_transport',1);
    if($this->current_branch) {
      $this->db->where('fb.branches',$this->current_branch);
    }
    if ($installment_type) {
      $this->db->where('fit.id',$installment_type);
    }
    if ($installmentId) {
      $this->db->where('fi.id',$installmentId);
    }
    $result =  $this->db->get()->result();
    $insListArry = [];
    foreach ($result as $key => $value) {
      $insListArry[$value->id] = $value->installment_name;
    }

    return $insListArry;
  }

  public function get_document_by_student($class_id,$admission_type, $section_id = '',$document_names,$admission_status) {
    $this->db_readonly->select("sa.id as stdId, sa.admission_no, sd.document_other, sd.document_url, sd.document_type, CONCAT(IFNULL(sa.first_name, ''), ' ', IFNULL(sa.last_name, '')) as std_name, cs.section_name, p.mobile_no")
    ->from('student_admission sa')
    ->join('student_relation sr', 'sa.id = sr.std_id AND relation_type = "Father"')
    ->join('parent p', 'p.id = sr.relation_id')
    ->join('student_year sy', 'sy.student_admission_id = sa.id')
    ->join('class_section cs', 'cs.id = sy.class_section_id', 'left');

    if (!empty($document_names)) {
        $doc_names = array_map('strtolower', $document_names);
        $escaped_doc_names = array_map(function($val) {
            return $this->db_readonly->escape($val);
        }, $doc_names);
        $this->db_readonly->join(
            "(SELECT * FROM student_documents WHERE LOWER(document_type) IN (" . implode(',', $escaped_doc_names) . ")) sd",
            "sa.id = sd.student_id",
            "left"
        );
    } else {
        $this->db_readonly->join('student_documents sd', 'sa.id = sd.student_id', 'left');
    }

    // Apply filters
    $this->db_readonly->where('sa.admission_status', $admission_status);

    if ($admission_status != 3) {
        $this->db_readonly->where('sy.promotion_status !=', '4');
        $this->db_readonly->where('sy.promotion_status !=', '5');
    }

    if (!empty($section_id)) {
        $this->db_readonly->where('sy.class_section_id', $section_id);
    }

    if (!empty($class_id)) {
        $this->db_readonly->where('sy.class_id', $class_id);
    }

    if (!empty($admission_type)) {
        $this->db_readonly->where('sy.admission_type', $admission_type);
    }

    $this->db_readonly->where('sy.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('sa.first_name', 'asc');
    $this->db_readonly->order_by('sa.id', 'asc');

    $document_objects = $this->db_readonly->get()->result();

    $std_docs_array = [];
    foreach ($document_objects as $key => $val) {
      $val->document_type = strtolower($val->document_type);
      if (!array_key_exists($val->stdId, $std_docs_array)) {
        $stdObj = new stdClass();
        $stdObj->student_name = $val->std_name;
        $stdObj->stdId = $val->stdId;
        $stdObj->section_name = $val->section_name;
        $stdObj->admission_no = $val->admission_no;
        $stdObj->p_mobile_no = $val->mobile_no;
        $stdObj->documents = [];
      } 

      if($val->document_type !='Others' && $val->document_other =='NA'){
        if (!empty($val->document_url))
          $stdObj->documents['document'][$val->document_type] = $this->filemanager->getSignedUrlWithExpiry($val->document_url);
        else
          $stdObj->documents['document'][$val->document_type] = '';
      }
      else if($val->document_type == 'Others' && $val->document_other !='NA'){
        if (!empty($val->document_url))
          $stdObj->documents['other'][$val->document_other] = $this->filemanager->getSignedUrlWithExpiry($val->document_url);
        else 
          $stdObj->documents['other'][$val->document_other] = '';
      }

      if($val->document_type !='Others' && $val->document_other !='NA'){
        if (!empty($val->document_url))
          $stdObj->documents['document'][$val->document_type] = $this->filemanager->getSignedUrlWithExpiry($val->document_url);
        else
          $stdObj->documents['document'][$val->document_type] = '';
      }

      $std_docs_array[$val->stdId] = $stdObj;
    }
    
    return $std_docs_array;
    
  }

  public function get_class_names(){
    return  $this->db_readonly->select("c.id as cId, cs.id as csId,c.class_name, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection")
    ->from('class c')
    ->where('c.acad_year_id',$this->yearId)
    ->join('class_section cs','c.id=cs.class_id')
    ->where('cs.is_placeholder!=1')
    ->get()->result();
    // echo '<pre>';print_r($result);die();
  }

  public function get_class_section_student_data($cId, $csId) {
    $query = $this->db_readonly->select("sd.id as sId, CONCAT(IFNULL(sd.first_name,''), ' ', IFNULL(sd.last_name,'')) as sName")
        ->from('student_admission sd')
        ->join('student_year sy', 'sd.id = sy.student_admission_id')
        ->where('sy.acad_year_id', $this->yearId)
        ->where('sy.class_id', $cId);

    if (!empty($csId)) {
        $query->where('sy.class_section_id', $csId);
    }

    $query->where('sd.admission_status', 2)
        ->where('sy.promotion_status !=', '4')
        ->where('sy.promotion_status !=', '5')
        ->order_by('sd.first_name', 'asc');

    $result = $query->get()->result();
    return $result;
}

  public function get_stu_parent_documents($input){
    $this->db_readonly->select("sa.id as stdId,sd.id as doc_id,ifnull(relation_type,'Student') as relation,sd.document_other, sd.document_url, sd.document_type, concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name, cs.section_name,ifnull(sd.name_as_per_aadhar,'-') as name_as_per_aadhar,ifnull(sd.remarks,'-') as remarks,ifnull(sd.aadhar_number,'-') as aadhar_number,ifnull(sd.document_status,'-') as document_status")
    ->from('student_admission sa')
    ->join('student_documents sd','sa.id=sd.student_id')
    ->join('student_year sy','sy.student_admission_id=sa.id')
    ->join('class_section cs',"cs.id=sy.class_section_id");
   
    if(!empty($input['student_id'])){
      $this->db_readonly-> where('sa.id',$input['student_id']);
    }

    if ($input['section_id'] != ''){
      $this->db_readonly->where('sy.class_section_id',$input['section_id']);
    }
    if ($input['class_id'] != ''){
      $this->db_readonly->where('sy.class_id',$input['class_id']);
    }
  
    $this->db_readonly->where('sy.acad_year_id',$this->yearId);
    $this->db_readonly->like('sd.document_type','Aadhar');
    $this->db_readonly->order_by('sa.first_name','asc');
    $this->db_readonly->order_by('sa.id','asc');
    $document_objects =  $this->db_readonly->get()->result();

    $std_docs_array = [];
    foreach ($document_objects as $key => $val) {
      if (!array_key_exists($val->stdId, $std_docs_array)) {
        $stdObj = array();
        $stdObj['student_name'] = $val->std_name;
        $stdObj['stud_id'] = $val->stdId;
        // $stdObj->documents[$val->relation] = [];
        // $stdObj['document'][] = array();       
      }
        $stdObj['document'][$val->relation]['document_id'] = $val->doc_id;
        $stdObj['document'][$val->relation]['name_as_per_aadhar'] = $val->name_as_per_aadhar;
        $stdObj['document'][$val->relation]['aadhar_number'] = $val->aadhar_number;
        $stdObj['document'][$val->relation]['remarks'] = $val->remarks;
        $stdObj['document'][$val->relation]['document_status'] = $val->document_status;
        $stdObj['document'][$val->relation]['relation'] = $val->relation;

      if(!empty($val->document_url)){
        if (!empty($val->document_url))
          $stdObj['document'][$val->relation]['document_url'] = $this->filemanager->getSignedUrlWithExpiry($val->document_url);
        else 
          $stdObj['document'][$val->relation]['document_url'] = '';
      }
      $std_docs_array[$val->stdId] = $stdObj;
    }
    // echo '<pre>';print_r($std_docs_array);die();
    return $std_docs_array;
  }

  public function get_pan_card_documnets($input){
   
    $this->db_readonly->select("sa.id as stdId,sd.id as doc_id,ifnull(relation_type,'Student') as relation,sd.document_other, sd.document_url, sd.document_type, concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as std_name, cs.section_name,ifnull(sd.remarks,'-') as remarks,ifnull(sd.document_status,'-') as document_status,ifnull(sd.pancard_number,'-') as pancard_number")
    ->from('student_admission sa')
    ->join('student_documents sd','sa.id=sd.student_id')
    ->join('student_year sy','sy.student_admission_id=sa.id')
    ->join('class_section cs',"cs.id=sy.class_section_id");
   
    if(!empty($input['student_id'])){
      $this->db_readonly-> where('sa.id',$input['student_id']);
    }

    if ($input['section_id'] != ''){
      $this->db_readonly->where('sy.class_section_id',$input['section_id']);
    }
    if ($input['class_id'] != ''){
      $this->db_readonly->where('sy.class_id',$input['class_id']);
    }
  
    $this->db_readonly->where('sy.acad_year_id',$this->yearId);
    $this->db_readonly->like('sd.document_type','pan');
    $this->db_readonly->order_by('sa.first_name','asc');
    $this->db_readonly->order_by('sa.id','asc');
    $document_objects =  $this->db_readonly->get()->result();

    $std_docs_array = [];
    foreach ($document_objects as $key => $val) {
      if (!array_key_exists($val->stdId, $std_docs_array)) {
        $stdObj = array();
        $stdObj['student_name'] = $val->std_name;
        // $stdObj->documents[$val->relation] = [];
        // $stdObj['document'][] = array();       
      }
        $stdObj['document'][$val->relation]['document_id'] = $val->doc_id;
        $stdObj['document'][$val->relation]['pancard_number'] = $val->pancard_number;
        $stdObj['document'][$val->relation]['remarks'] = $val->remarks;
        $stdObj['document'][$val->relation]['document_status'] = $val->document_status;
        $stdObj['document'][$val->relation]['relation'] = $val->relation;

      if(!empty($val->document_url)){
        if (!empty($val->document_url))
          $stdObj['document'][$val->relation]['document_url'] = $this->filemanager->getSignedUrlWithExpiry($val->document_url);
        else
          $stdObj['document'][$val->relation]['document_url'] = '';
      }
      $std_docs_array[$val->stdId] = $stdObj;
    }
    // echo '<pre>';print_r($std_docs_array);die();
    return $std_docs_array;
  }

  public function update_aadhar_status($input){
    if($input['aadhar_status'] == 'Approved'&& $input['relation'] == 'Student' && $input['aadhar_number'] !='' && $input['aadhar_number'] !='-'){
        $this->db->where('id',$input['stud_id']);
        $this->db->update('student_admission', array('aadhar_no'=>$input['aadhar_number'],) );   
    }
    $data=array(
      'document_status' =>$input['aadhar_status']
    );
    $this->db->where('id',$input['id']);
    return $this->db->update('student_documents',$data);
  }

  public function update_parent_pancard_status($input){
    $data=array(
      'document_status' =>$input['pancard_status']
    );
    $this->db->where('id',$input['doc_id']);
    return $this->db->update('student_documents',$data);
}

  public function get_student_documnets($input){
    $this->db_readonly->select("sd.id as sdId,sd.id as saId,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name,concat(ifnull(cs.class_name,'-'), ' ' , ifnull(cs.section_name,'')) as class_section,sd.document_url,ifnull(sd.aadhar_number,'-') as aadhar_number,ifnull(sd.aadhar_remarks,'-') as aadhar_document_remarks,ifnull(sd.document_status,'-') as document_status,ifnull(sd.aadhar_approved_status,'-') as aadhar_approved_status")
    ->from('student_documents sd')
    ->join('student_admission sa','sd.student_id=sa.id')
    ->join('student_year sy',"sa.id=sy.student_admission_id")
    ->join('class_section cs','sy.class_section_id=cs.id')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('sd.document_type','Aadhar Card');
    if($input['status'] != 'all'){
      $this->db_readonly->where('p.aadhar_approved_status',$input['status']);
    }
    $result = $this->db_readonly->get()->result();

    foreach($result as $key => $val){
        if (!empty($val->document_url)) {
        $val->document_url = $this->filemanager->getFilePath($val->document_url);
      }
    }
    return $result;
  }
  
  public function insert_student_configure_fields(){
    foreach ($this->input->post() as $name => $value) {
     $quiry = $this->db->where('name',$name)->get('config');
     if ($quiry->num_rows() > 0) {
         $UpdaterFields = array(
             'name' =>$name,
             'value' => json_encode($value),
             'type' => 'multiple'
         );
         $this->db->where('name',$name);
         return $this->db->update('config',$UpdaterFields);
     }else{
         $rFields = array(
             'name' =>$name,
             'value' => json_encode($value),
             'type' => 'multiple'
         );
         return $this->db->insert('config',$rFields);
     }
    }
 }

 public function insert_student_edit_fields(){
  foreach ($this->input->post() as $name => $value) {
   $quiry = $this->db->where('name',$name)->get('config');
   if ($quiry->num_rows() > 0) {
       $UpdaterFields = array(
           'name' =>$name,
           'value' => json_encode($value),
           'type' => 'multiple'
       );
       $this->db->where('name',$name);
       return $this->db->update('config',$UpdaterFields);
   }else{
       $rFields = array(
           'name' =>$name,
           'value' => json_encode($value),
           'type' => 'multiple'
       );
       return $this->db->insert('config',$rFields);
   }
  }
}

  public function get_config_student_display_fileds(){
    $this->db->select('value');
    $this->db->where('name','student_profile_display_columns');
    return  $this->db->get('config')->result();
  }

  public function get_config_student_edit_fileds(){
    $this->db->select('value');
    $this->db->where('name','student_profile_edit_columns');
    return  $this->db->get('config')->result();
}

  public function insert_student_document_types($input){
    // echo "<pre>";print_r($_POST);die();
    $doc_name = $input['document_name'];
    $relation_type = $input['relation_type'];
    $sql = "SELECT * from student_document_types where document_name = '$doc_name' AND  for_relation = '$relation_type'";
    $doc_data = $this->db->query($sql)->result();

    if(sizeof($doc_data) == 0){
      $data = array(
        'document_name' => $input['document_name'],
        'for_relation' => $input['relation_type'],
        'visibile_for_parents' => $input['visibility'],
        'document_type' => $input['document_type'],
        'need_approval' => $input['need_approval'],
        'is_mandatory' => $input['is_mandatory'],
        'document_size_in_mb' => $input['document_size'],
        'based_on_nationality' => $input['based_on_nationality']
      );
      return $this->db->insert('student_document_types',$data);
    }
    return 0;
  }

  public function get_student_document_types(){
    return $this->db_readonly->select("*,if(visibile_for_parents = 1 , 'Yes' , 'No') as visibile_for_parents,ifnull(document_type ,'-') as document_type,ifnull(need_approval,'-') as need_approval, if(is_mandatory = 1, 'Yes', 'No') as is_mandotary_display,ifnull(sd.document_size_in_mb,0) as document_size_in_mb,ifnull(based_on_nationality,'-') as based_on_nationality")
    ->from('student_document_types sd')
    ->get()->result();
  }

  public function delete_document_type($input){
      $this->db->where('id', $input['primary_id'])->delete('student_document_types');
      return $this->db->affected_rows();
  }

  public function edit_student_document_types($input){
    $data = array(
      'visibile_for_parents' => $input['visibility'],
      'document_type' => $input['edit_doc_type'],
      'need_approval' => $input['edit_approval'],
      'is_mandatory' => $input['edit_is_mandatory'],
      'document_size_in_mb' => $input['edit_document_size'],
      'based_on_nationality'=>$input['edit_based_on_nationality']
    );
    $this->db->where('id', $input['doc_id'])->update('student_document_types', $data);
    return $this->db->affected_rows();
  }

  public function get_document_types(){
		$doc_types =  $this->db_readonly->select("document_name")
		->from('student_document_types')
		->get()->result();

    $document_names = $this->db->select("distinct(document_type) as document_name")
    ->from('student_documents')
    ->get()->result();
    $merge = array_merge($doc_types,$document_names);

    $object = [];
    foreach ($merge as $key => $value)
    {
      if(!in_array(strtolower($value->document_name),$object))
        $object []= strtolower($value->document_name);
    }
    return array_unique($object);
	}
  
  public function get_student_missing_data($input,$s_fields,$f_fields,$m_fields){
      $class_id = $input['class_id'];
      $section_id = $input['section_id'];
      $this->db->select("$s_fields,$f_fields,$m_fields,sa.id,concat(ifnull(sa.first_name,''),' ', ifnull(sa .last_name,'')) as student_name,if(gender='M','Male','Female') as gender,CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as father_name,CONCAT(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) as mother_name,cs.section_name")
      ->from('student_admission sa')
      ->join('student_year sy','sa.id=sy.student_admission_id','left')
      ->join('class c','sy.class_id=c.id','left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('student_relation sr',"sa.id=sr.std_id")
      ->join('parent p','sr.relation_id=p.id')
      ->join('student_relation sr1',"sa.id=sr1.std_id")
      ->join('parent p1','sr1.relation_id=p1.id')
      ->where('sr.relation_type','Father')
      ->where('sr1.relation_type','Mother')
      ->where('sy.acad_year_id', $this->yearId);
      if(!empty($input['stu_id'])){
        $this->db->where('sa.id',$input['stu_id']);
      }
      if(!empty($class_id)){
        $this->db->where('c.id',$class_id);
      }
      if(!empty($section_id)){
        $this->db->where('cs.id',$section_id);
      }
      $res =  $this->db->get()->result();
      $temp_arr = [];
      if(!empty($res)){
        foreach($res as $key => $val){
          if(!array_key_exists($val->id,$temp_arr)){
            $temp_arr[$val->id]['id'] = $val->id;
            $temp_arr[$val->id]['student_name'] = $val->student_name;
            $temp_arr[$val->id]['section_name'] = $val->section_name;
            $temp_arr[$val->id]['student_data'] = $val;
          }
        }
      }

      return $temp_arr;
  }

  public function get_student_edit_history_data($from_date,$to_date,$student_name){
    $this->db->select("sh.*,date_format(sh.edited_on,'%d-%m-%Y %h:%i %p') as edited_on,concat(ifnull(sa.first_name,''),' ', ifnull(sa .last_name,'')) as student_name,if(sh.source = 'Parent',concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')),concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,''))) as edited_by,concat(ifnull(cs.class_name,'-'), ' ' , ifnull(cs.section_name,'')) as class_section")
    ->from('student_edit_history sh')
    ->join('parent p','sh.edited_by=p.id and sh.source="Parent"','left')
    ->join('staff_master sm', "sh.edited_by = sm.id AND (sh.source IS NULL OR sh.source = 'staff')", 'left')
    ->join('student_admission sa','sh.student_id=sa.id')
    ->join('student_year sy','sa.id=sy.student_admission_id','left')
    ->join('class c','sy.class_id=c.id','left')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->where('sy.acad_year_id', $this->yearId);
    if(empty($student_name)){
      $this->db->where('date_format(sh.edited_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ');
    }
    if($student_name){
        $this->db->group_start()
            ->like('sa.first_name', $student_name)
            ->or_like('sa.last_name', $student_name)
        ->group_end();
    }

    $this->db->order_by('sh.edited_on','desc');
    $res =  $this->db->get()->result();
    $search = ['f_','m_','_',"{",'}','"'];
    $replace   = ['father ','mother ',' ','','',''];
   
    foreach($res as $key => $val){
      if($val->edited_by == " "){
        $val->edited_by = 'Admin';
      }
      $encoded_old_data = json_decode($val->old_data);
      $encoded_new_data = json_decode($val->new_data);

      if(empty($encoded_new_data) && empty($encoded_old_data)){
        $val->old_data = ucwords(str_replace($search,$replace,$val->old_data));
        $val->new_data = ucwords(str_replace($search,$replace,$val->new_data));
        continue;
      }
      // echo '<pre>';print_r($encoded_old_data);die();
      
      if (isset($encoded_old_data->boardingid)) {
        $boarding_settings = $this->settings->getSetting('boarding');
    
        if (isset($boarding_settings[$encoded_old_data->boardingid])) {
            $encoded_old_data->boardingid = $boarding_settings[$encoded_old_data->boardingid];
        } else {
            // Optional fallback value
            $encoded_old_data->boardingid = $encoded_old_data->boardingid; // or retain original
        }
    }
    
      if(isset($encoded_old_data->admidType) && $encoded_old_data->admidType != null){
        $encoded_old_data->admidType = ($encoded_old_data->admidType == 1)? 'Re-admission':'New Admission';
      }
      if(isset($encoded_old_data->rteid)){
        // $encoded_old_data->rteid = $this->settings->getSetting('rte')[$encoded_old_data->rteid];
        $rte_settings = $this->settings->getSetting('rte');
        if (isset($rte_settings[$encoded_old_data->rteid])) {
          $encoded_old_data->rteid = $rte_settings[$encoded_old_data->rteid];
        } else {
          // Optional: handle missing key
          $encoded_old_data->rteid = $encoded_old_data->rteid; // or keep it as is
        }
      }
      if (isset($encoded_old_data->category) && $encoded_old_data->category != 0) {
        $category_settings = $this->settings->getSetting('category');
    
        if (isset($category_settings[$encoded_old_data->category])) {
            $encoded_old_data->category = $category_settings[$encoded_old_data->category];
        } else {
            // Optional fallback value
            $encoded_old_data->category = $encoded_old_data->category; // or leave it as-is
        }
      }
      if(isset($encoded_old_data->board) && !empty($encoded_old_data->board)){
        if (isset($encoded_old_data->board) && !empty($encoded_old_data->board)) {
          $board_settings = $this->settings->getSetting('board');
      
          if (isset($board_settings[$encoded_old_data->board])) {
              $encoded_old_data->board = $board_settings[$encoded_old_data->board];
          } else {
              // Optional: fallback value
              $encoded_old_data->board = $encoded_old_data->board; // or retain original value
          }
      }
      }
      if(isset($encoded_old_data->minority) && $encoded_old_data->minority != null){
        $encoded_old_data->minority = ($encoded_old_data->minority == 1) ? 'Yes' : 'No';
      }
      if(isset($encoded_old_data->single_child) && $encoded_old_data->single_child != null){
        $encoded_old_data->single_child = ($encoded_old_data->single_child == 1) ? 'Yes' : 'No';
      }
      if(isset($encoded_old_data->classsection) && !empty($encoded_old_data->classsection)){
        $encoded_old_data->classsection = $this->get_section_name($encoded_old_data->classsection);
      }
      if(isset($encoded_old_data->classid) && !empty($encoded_old_data->classid)){
        $encoded_old_data->classid = $this->get_class_name($encoded_old_data->classid);
      }
      if(isset($encoded_old_data->class_admitted_to) && !empty($encoded_old_data->class_admitted_to)){
        $encoded_old_data->class_admitted_to = $this->get_class_name($encoded_old_data->class_admitted_to);
      }
      if (isset($encoded_old_data->quota) && !empty($encoded_old_data->quota)) {
        $quota_settings = $this->settings->getSetting('quota');
    
        if (isset($quota_settings[$encoded_old_data->quota])) {
            $encoded_old_data->quota = $quota_settings[$encoded_old_data->quota];
        } else {
            // Optional fallback
            $encoded_old_data->quota = $encoded_old_data->quota; // or retain original value
        }
    }
    
      if(isset($encoded_old_data->add_status)){
        switch ($encoded_old_data->add_status) {
          case 1:
            $encoded_old_data->add_status = 'Pending';
            break;
          case 2:
            $encoded_old_data->add_status = 'Approved';
            break;
          case 3:
            $encoded_old_data->add_status = 'Rejected';
            break;
          case 4:
            $encoded_old_data->add_status = 'Alumni';
            break;
          default:
           break;
        }       
      }

      if(isset($encoded_new_data->boardingid)){
        // $encoded_new_data->boardingid = $this->settings->getSetting('boarding')[$encoded_new_data->boardingid];
        $boarding = $this->settings->getSetting('boarding');
        if(isset($boarding[$encoded_new_data->boardingid])){
          $encoded_new_data->boardingid = $boarding[$encoded_new_data->boardingid];
        }else{
          $encoded_new_data->boardingid = $encoded_new_data->boardingid;
        }
      }
      if(isset($encoded_new_data->admidType) && $encoded_new_data->admidType != null){
        $encoded_new_data->admidType = ($encoded_new_data->admidType == 1)? 'Re-admission':'New Admission';
      }
      if(isset($encoded_new_data->rteid)){
        // $encoded_new_data->rteid = $this->settings->getSetting('rte')[$encoded_new_data->rteid];

        $rte_settings = $this->settings->getSetting('rte');
        if (isset($rte_settings[$encoded_new_data->rteid])) {
          $encoded_new_data->rteid = $rte_settings[$encoded_new_data->rteid];
        } else {
          // Optional: handle missing key
          $encoded_new_data->rteid = $encoded_new_data->rteid; // or keep it as is
        }
      }
      if(isset($encoded_new_data->category) && !empty($encoded_new_data->category)){
        $category = $this->settings->getSetting('category');
        if(isset($category[$encoded_new_data->category])){
          $encoded_new_data->category = $category[$encoded_new_data->category];
        }else{
          $encoded_new_data->category = $encoded_new_data->category;
        }
      }
      if (isset($encoded_new_data->board) && !empty($encoded_new_data->board)) {
        $board_settings = $this->settings->getSetting('board');
    
        if (isset($board_settings[$encoded_new_data->board])) {
            $encoded_new_data->board = $board_settings[$encoded_new_data->board];
        } else {
            // Optional fallback
            $encoded_new_data->board = $encoded_new_data->board; // or keep the original value
        }
      }
      if(isset($encoded_new_data->minority) && $encoded_new_data->minority != null){
        $encoded_new_data->minority = ($encoded_new_data->minority == 1)? 'Yes' : 'No';
      }
      if(isset($encoded_new_data->single_child) && $encoded_new_data->single_child != null){
        $encoded_new_data->single_child = ($encoded_new_data->single_child == 1) ? 'Yes' : 'No';
      }
      if(isset($encoded_new_data->classsection) && !empty($encoded_new_data->classsection)){
        $encoded_new_data->classsection = $this->get_section_name($encoded_new_data->classsection);
      }
      if(isset($encoded_new_data->classid) && !empty($encoded_new_data->classid)){
        $encoded_new_data->classid = $this->get_class_name($encoded_new_data->classid);
      }
      if(isset($encoded_new_data->class_admitted_to) && !empty($encoded_new_data->class_admitted_to)){
        $encoded_new_data->class_admitted_to = $this->get_class_name($encoded_new_data->class_admitted_to);
      }
      if (isset($encoded_new_data->quota) && !empty($encoded_new_data->quota)) {
        $quota_settings = $this->settings->getSetting('quota');
    
        if (isset($quota_settings[$encoded_new_data->quota])) {
            $encoded_new_data->quota = $quota_settings[$encoded_new_data->quota];
        } else {
            // Optional fallback if the key is missing
            $encoded_new_data->quota = $encoded_new_data->quota; // or leave as-is
        }
    }
    
      if(isset($encoded_new_data->add_status)){
        switch ($encoded_new_data->add_status) {
          case 1:
            $encoded_new_data->add_status = 'Pending';
            break;
          case 2:
            $encoded_new_data->add_status = 'Approved';
            break;
          case 3:
            $encoded_new_data->add_status = 'Rejected';
            break;
          case 4:
            $encoded_new_data->add_status = 'Alumini';
            break;
          default:
           break;
        }
        // $tem_obj = new stdClass();
        // foreach($encoded_old_data as $k => $v){
        //   $new_key = ucwords(str_replace($search,$replace,$k));pare
        //   $tem_obj->$new_key = $v;
        //   unset($encoded_old_data->$k);
        //   // echo '<pre>';print_r($encoded_old_data);die();
        // }
        // $encoded_old_data = $tem_obj;
      }

      $val->old_data = ucwords(str_replace($search,$replace,json_encode($encoded_old_data)));
      $val->new_data = ucwords(str_replace($search,$replace,json_encode($encoded_new_data))); 
    }
    // echo '<pre>';print_r($res);die();
    return $res;

  }

  public function get_section_name($section_id){
    $result = $this->db->where('id',$section_id)->get('class_section')->row();
    return $result ? $result->section_name : $section_id;
  }

  public function get_class_name($class_id) {
    $result = $this->db->where('id', $class_id)->get('class')->row();
    return $result ? $result->class_name : $class_id; // or return 'N/A' or ''
  }

  public function insert_student_configure_required_fields(){
    foreach ($this->input->post() as $name => $value) {
      $quiry = $this->db->where('name',$name)->get('config');
      if ($quiry->num_rows() > 0) {
          $UpdaterFields = array(
              'name' =>$name,
              'value' => json_encode($value),
              'type' => 'multiple'
          );
          $this->db->where('name',$name);
          return $this->db->update('config',$UpdaterFields);
      }else{
          $rFields = array(
              'name' =>$name,
              'value' => json_encode($value),
              'type' => 'multiple'
          );
          return $this->db->insert('config',$rFields);
      }
      }
  }

  public function get_student_config_required_fields()
  {
      $this->db->select('value');
      $this->db->where('name', 'student_mandatory_fields');
      $result = $this->db->get('config')->result();
      return $result;
  }

  public function getClassNames(){
  
    $this->db_readonly->select("cs.id, CONCAT(ifnull(cs.class_name,''),' ', ifnull(cs.section_name,'')) as class_name");
    $this->db_readonly->from('class_section as cs');
    $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
    $this->db_readonly->order_by('c.id, cs.id');
    $this->db_readonly->where('cs.is_placeholder!=1');

    $result = $this->db_readonly->get()->result();

    return $result;
  }

public function  load_student_data($input){
      $this->db_readonly->select("sy.id,admission_no,CONCAT(ifnull(c.class_name,''),' ', ifnull(cs.section_name,'')) as class,CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as student_name,profile_confirmed,profile_confirmed_date, date_format(sy.profile_confirmed_date,'%d-%b-%Y %h:%i %p') as profile_confirmed_date,sy.profile_confirmed_by,sy.profile_status_changed_by, date_format(sy.profile_status_changed_date,'%d-%b-%Y %h:%i %p') as profile_status_changed_date,sy.student_admission_id,sy.class_section_id,sy.profile_status");
      $this->db_readonly->from('student_admission sd');
      $this->db_readonly->join('student_year sy','sy.student_admission_id=sd.id');
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->join('class_section cs','cs.id=sy.class_section_id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->where('sd.admission_status','2');        
      $this->db_readonly->where('sy.promotion_status!=', '4');
      $this->db_readonly->where('sy.promotion_status!=', '5');
      if($input['class_section_id']){
        $this->db_readonly->where_in('sy.class_section_id',$input['class_section_id']);
      }
      if($input['confirm_status'] !="All"){
        $this->db_readonly->where_in('sy.profile_confirmed',$input['confirm_status']);
      }
      if($input['status'] !="All"){
        $this->db_readonly->where('sy.profile_status',$input['status']);
      }
      $data['data']= $this->db_readonly->get()->result();
      foreach ($data['data'] as $key => $value) {
        $value->profile_confirmed_by = $this->_getParentNameById($value->profile_confirmed_by);
        $value->profile_status_changed_by = $this->_getAvatarNameById($value->profile_status_changed_by);
      }

    $this->db_readonly->select("sy.profile_status,count(*) as count");
    $this->db_readonly->from('student_admission sd');
    $this->db_readonly->join('student_year sy','sy.student_admission_id=sd.id');
    $this->db_readonly->join('class c','sy.class_id=c.id');
    $this->db_readonly->join('class_section cs','cs.id=sy.class_section_id');
    $this->db_readonly->where('sy.acad_year_id',$this->yearId);
    $this->db_readonly->where('sd.admission_status','2');
    $this->db_readonly->where('sy.promotion_status!=', '4');
    $this->db_readonly->where('sy.promotion_status!=', '5');
    if($input['class_section_id']){
      $this->db_readonly->where_in('sy.class_section_id',$input['class_section_id']);
    }
    if($input['status'] !="All"){
      $this->db_readonly->where('sy.profile_status',$input['status']);
    }
    if($input['confirm_status'] !="All"){
      $this->db_readonly->where_in('sy.profile_confirmed',$input['confirm_status']);
    }
    $this->db_readonly->group_by('sy.profile_status');
    $data2= $this->db_readonly->get()->result();
    $locked_count=0;
    $unlocked_count=0;
    foreach($data2 as $key => $value){
      if($value->profile_status == 'Unlock'){
        $unlocked_count=$value->count;
      }else if($value->profile_status == 'Lock'){
        $locked_count=$value->count;

      }
    }
    $data['locked_count']=$locked_count ? $locked_count :0  ;
    $data['unlocked_count']=$unlocked_count ? $unlocked_count :0 ;
    return $data;
  }

  public function change_confirm_status($input){
    if($input['status']=='Unlock'){
      $data= array(
        'profile_status'=> $input['status'],
        'profile_confirmed'=> 'No',
        'profile_status_changed_date'=> date('Y-m-d H:i:s'),
        'profile_status_changed_by'=> $this->authorization->getAvatarStakeHolderId()
      );
      $this->db->where_in('id',$input['id']);
      return $this->db->update('student_year', $data);
    }
    $data= array(
      'profile_status'=> $input['status'],
      'profile_status_changed_date'=> date('Y-m-d H:i:s'),
      'profile_status_changed_by'=> $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->where_in('id',$input['id']);
    return $this->db->update('student_year', $data);
  }

  private function _getParentNameById($id){
    $this->db_readonly->select('CONCAT(ifnull(p.first_name," "), " ", ifnull(p.last_name," ")) as parentName');
    $this->db_readonly->from('parent p');
    $this->db_readonly->where('p.id',$id);
    $data= $this->db_readonly->get()->row();
    if (!empty($data)) {
      return $data->parentName;
    }else{
      return '-';
    }
  }

  public function get_all_student_profile_fields(){
    $this->db_readonly->select('value');
    $this->db_readonly->from('config');
    $this->db_readonly->where('name','parent_profile_display_columns');
    $result= $this->db_readonly->get()->result();
    echo json_encode($result);
  }

  public function student_profile_display_config(){
    $input=$this->input->post();
    
    $view_input = isset($input['parent_profile_display_columns']) ? $input['parent_profile_display_columns'] : [];
    $quiry1 = $this->db->where('name','parent_profile_display_columns')->get('config');
    if($quiry1->num_rows() > 0){
      $view = array(
          'value' => json_encode($view_input),
      );
      $this->db->where('name','parent_profile_display_columns');
      $this->db->update('config',$view);
    }else{
      $mandatory = array(
        'name' =>'parent_profile_display_columns',
        'value' => json_encode($view_input),
        'type' => 'multiple'
      );
    $this->db->insert('config',$mandatory);
  }

    $edit_input = isset($input['profile_edit_columns']) ? $input['profile_edit_columns'] : [];
    $quiry2 = $this->db->where('name','profile_edit_columns')->get('config');
    if($quiry2->num_rows() > 0){
    $edit = array(
      'value' => json_encode($edit_input),
      );
    $this->db->where('name','profile_edit_columns');
    $this->db->update('config',$edit);
    }else{
      $mandatory = array(
        'name' =>'profile_edit_columns',
        'value' => json_encode($edit_input),
        'type' => 'multiple'
    );
    $this->db->insert('config',$mandatory);
    }


    $man_input = isset($input['profile_mandatory_columns']) ? $input['profile_mandatory_columns'] : [];
    $quiry = $this->db->where('name','profile_mandatory_columns')->get('config');
    if($quiry->num_rows() > 0){
      $mandatory = array(
        'value' => json_encode($man_input) ,
        );
      $this->db->where('name','profile_mandatory_columns');
      $this->db->update('config',$mandatory);
    }else{
        $mandatory = array(
          'name' =>'profile_mandatory_columns',
          'value' => json_encode($man_input),
          'type' => 'multiple'
      );
      $this->db->insert('config',$mandatory);
    }
    
    return 1;
  }
  public function configure_parent_side_fields_view(){
    $this->db->select('value');
    $this->db->where('name','parent_profile_display_columns');
    return  $this->db->get('config')->result();
  }

  public function configure_parent_side_fields_edit(){
    $this->db->select('value');
    $this->db->where('name','profile_edit_columns');
    return  $this->db->get('config')->result();
  }

  public function configure_parent_side_fields_Mandatory(){
    $this->db->select('value');
    $this->db->where('name','profile_mandatory_columns');
    return  $this->db->get('config')->result();
  }

  public function get_sections_names_by_class_id($class_id){
    return $this->db->select('section_name,id')->from('class_section')->where('class_id',$class_id)->get()->result();
  }

  public function update_documents_status(){
    $id = $_POST['id'];
    $status = $_POST['status'];
    $data = array(
      'status' => $status,
    );
    return $this->db->where('id', $id)->update('student_document_types', $data);
  }

  public function fetch_photos($class_id, $section_id, $admission_type, $admission_status, $photo_types) {
    $select = [
        'CONCAT(IFNULL(sa.first_name,"")," ",IFNULL(sa.last_name,"")) as student_name',
        'sa.admission_no',
        'cs.section_name'
    ];

    // Determine which joins are needed
    $need_father = $need_mother = $need_guardian = false;
    foreach ($photo_types as $pt) {
        if (strpos($pt, 'f.') === 0) $need_father = true;
        if (strpos($pt, 'm.') === 0) $need_mother = true;
        if (strpos($pt, 'g.') === 0) $need_guardian = true;
        // Add select for each photo type
        if (strpos($pt, '.') !== false) {
            list($table, $column) = explode('.', $pt, 2);
            // $select[] = "$table.$column AS " . $table . '_' . $column . "";
            $select[] = "`$table`.`$column` AS `" . $table . '_' . $column . "`";
        }
    }

    $this->db_readonly->select(implode(',', $select));
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id', 'left');
    $this->db_readonly->join('class c', 'sy.class_id=c.id');
    $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');

    if ($need_father) {
        $this->db_readonly->join('student_relation srf', 'sa.id=srf.std_id');
        $this->db_readonly->join('parent f', "f.id=srf.relation_id and srf.relation_type='Father'");
    }
    if ($need_mother) {
        $this->db_readonly->join('student_relation srm', 'sa.id=srm.std_id');
        $this->db_readonly->join('parent m', "m.id=srm.relation_id and srm.relation_type='Mother'");
    }
    if ($need_guardian) {
        $this->db_readonly->join('student_relation srg', 'sa.id=srg.std_id');
        $this->db_readonly->join('parent g', "g.id=srg.relation_id and srg.relation_type='Guardian'");
    }

    if ($class_id) { $this->db_readonly->where('c.id', $class_id); }
    if ($section_id) { $this->db_readonly->where('cs.id', $section_id); }
    if ($admission_type) { $this->db_readonly->where('sa.admission_type', $admission_type); }
    if ($admission_status) { $this->db_readonly->where('sa.admission_status', $admission_status); }
    $this->db_readonly->order_by('student_name');
    $query = $this->db_readonly->get();
    return $query->result();
  }
}