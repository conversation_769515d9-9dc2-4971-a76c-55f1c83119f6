<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Shifts_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    public function getShifts() {
    	$shifts = $this->db->select("id,is_active,name, shift_type, is_shift_over_midnight, DATE_FORMAT(start_time, '%h:%i %p') as start_time, DATE_FORMAT(end_time, '%h:%i %p') as end_time")->order_by("id", "desc")->get('st_attendance_shifts_master')->result();
    	foreach ($shifts as $k => $shift) {
    		$shifts[$k]->start_time = local_time($shift->start_time, 'h:i a');
    		$shifts[$k]->end_time = local_time($shift->end_time, 'h:i a');
    	}
    	return $shifts;
    }

	public function delete_staff_shift($shiftId,$action){
		$this->db->where('id', $shiftId);
		return $this->db->update('st_attendance_shifts_master',['is_active'=>$action]);
	}

	public function get_shift_assigned_staff($shiftId,$action){
		return  $this->db_readonly->query("select stm.id as staff_id, concat(ifnull(stm.first_name,''),' ',ifnull(stm.last_name,'')) as staff_name, group_concat(date_format(ss.date,'%b %D'))
											from st_attendance_staff_shifts ss
											join st_attendance_shifts_master sm on sm.id=ss.shift_master_id
											join staff_master stm on stm.id=ss.staff_id
											where shift_master_id='$shiftId' and date>=curdate() and is_active=1
											group by staff_id")->result();
	}

    public function saveShift($data) {
    	return $this->db->insert('st_attendance_shifts_master', $data);
    }

    public function getStaffShifts() {
    	$sql = "select sm.id as staff_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, sm.designation, sh.name as shift_name, sh.id as shift_id, ss.week_day 
    			from staff_master sm 
    			left join st_attendance_staff_shifts ss on ss.staff_id=sm.id 
    			left join st_attendance_shifts_master sh on sh.id=ss.shift_master_id 
    			where sm.status=2 order by sm.first_name";
    	$result = $this->db->query($sql)->result();
    	$staff_shifts = array();
    	foreach ($result as $key => $res) {
    		if(!array_key_exists($res->staff_id, $staff_shifts)) {
    			$staff_shifts[$res->staff_id] = array();
    			$staff_shifts[$res->staff_id]['staff_name'] = $res->staff_name;
    			$staff_shifts[$res->staff_id]['staff_id'] = $res->staff_id;
    			$staff_shifts[$res->staff_id]['designation'] = $res->designation;
    		}
    		if($res->week_day) {
    			$staff_shifts[$res->staff_id][$res->week_day] = array(
    				'shift_id' => $res->shift_id,
    				'shift_name' => $res->shift_name
    			);
    		}
    	}

    	$data = array();
    	foreach ($staff_shifts as $key => $value) {
    		$data[] = $value;
    	}
    	return $data; 
    }

    public function saveStaffShift() {
    	$staff_id = $_POST['staff_id'];
    	$shifts = $_POST['shifts'];
    	$last_modified_by = $this->authorization->getAvatarId();
    	$data = array();
    	foreach ($shifts as $day => $shift) {
    		if($shift['shift_id']) {
    			$data[] = array(
    				'shift_master_id' => $shift['shift_id'],
    				'staff_id' => $staff_id,
    				'week_day' => $day,
    				'last_modified_by' => $last_modified_by
    			);
    		}
    	}
    	// echo '<pre>'; print_r($data); die();

    	if(!empty($data)) {
    		$this->db->trans_start();
    		//remove existing shifts
    		$this->db->where('staff_id', $staff_id)->delete('st_attendance_staff_shifts');
    		//add new shifts
    		$this->db->insert_batch('st_attendance_staff_shifts', $data);
    		$this->db->trans_complete();
    		if($this->db->trans_status() === FALSE) {
    			$this->db->trans_rollback();
    			return 0;
    		}
    		$this->db->trans_commit();
    		return 1;
    	}
    	return 0;
    }

    public function getStaffList($data) {
		$staff_type=$data["staff_type"];
	    $this->db->select("id as staff_id,CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name, joining_date, date_format(joining_date,'%M-%Y') as joining_date_month_year");
		if($staff_type!=-1){
			$this->db->where('staff_type', $staff_type);
		}
		return $this->db->where('status', 2)->where('is_primary_instance', 1)->order_by('first_name')->get('staff_master')->result();
	}

	public function __getShiftsData() {
		$result = $this->db->select("id, name, shift_type")->get('st_attendance_shifts_master')->result();
		$shifts = array();
		foreach ($result as $res) {
			$shifts[$res->id] = $res;
		}
		return $shifts;
	}

	private function isStaffShiftExist($date,$staffId){
		$shiftExistQuery="select id from st_attendance_staff_shifts where staff_id='$staffId' and date=cast('$date' AS date);";
		return $this->db_readonly->query($shiftExistQuery)->row();
	}

	private function _createORUpdateSingleStaffShift($input) {
		$action_by = $this->authorization->getAvatarId();
		$new_entries = array();
		$audit_data = array();
		$update_entries = array();
		$shift_master = $input['shift_master'];
		$shifts = $input['shifts'];
		$previous = $input['previous'];
		$staff_id = $input['staff_id'];
		$month = $input['month'];
		$staff_shifts = $input['staff_shifts'];
		$staffsWithJoiningDate = $input['staffsWithJoiningDate'];
		$this->db->trans_start();

		$isSuperAdmin=$this->authorization->isSuperAdmin();
		$canStaffModifyPreviousShifts=(int)$this->authorization->isAuthorized("STAFF_ATTENDANCE.MODIFY_PREVIOUS_SHIFTS");
		
		foreach ($shifts as $date => $shift_master_id) {
			if(array_key_exists($date,$staff_shifts)){
				$staff_shift_id=$staff_shifts[$date];
			}else{
				$staff_shift_id = 0;
			}
			// Check shift
			// Assign shifts for current staff iff his/ her Date Of Joining
			if(array_key_exists($staff_id,$staffsWithJoiningDate) && strtotime($date)>=strtotime($staffsWithJoiningDate[$staff_id])){
				if($isSuperAdmin || $canStaffModifyPreviousShifts || strtotime($date) >= strtotime(date('Y-m-d'))){
					if (array_key_exists($date, $shifts)) {
						$curr_shift = $shifts[$date];
					} else {
						$curr_shift = 0;
					}

					$checkingStaffShiftExist=$this->isStaffShiftExist($date,$staff_id);

					if(!$staff_shift_id && empty($checkingStaffShiftExist)) {
						// avoiding empty-shifts or 'No-shifts'
						if($curr_shift>0){
							//new entry
							$new_entries = array(
								'shift_master_id' => $curr_shift,
								'staff_id' => $staff_id,
								'date' => $date,
								'month' => $month,
								'type' => $shift_master[$curr_shift]->shift_type
							);
							$this->db->insert('st_attendance_staff_shifts', $new_entries);
							$st_shift_id = $this->db->insert_id();
							$audit_data[] = array(
								'staff_shift_id' => $st_shift_id,
								'action' => 1,
								'action_by' => $action_by,
								'old_shift' => '',
								'new_shift' => $shift_master[$curr_shift]->name
							);
						}
					} else {
						//update old entry
						$prev_shift = $previous[$date];
						//if changes are done then only update data
						if($prev_shift != $curr_shift) {
							$staff_shift_id=$checkingStaffShiftExist->id;
							$update_entries[] = array(
								'id' => $staff_shift_id,
								'shift_master_id' => $curr_shift,
								'type' => $shift_master[$curr_shift]->shift_type
							);

							$audit_data[] = array(
								'staff_shift_id' => $staff_shift_id,
								'action' => 2,
								'action_by' => $action_by,
								'old_shift' => $shift_master[$prev_shift]->name,
								'new_shift' => $shift_master[$curr_shift]->name
							);
						}
					}
				}
			}
		}

		if(!empty($update_entries)) {
			$this->db->update_batch('st_attendance_staff_shifts', $update_entries, 'id');
		}
		if(!empty($audit_data)) {
			$this->db->insert_batch('st_attendance_staff_shift_history', $audit_data);
		}

		$this->db->trans_complete();
		if($this->db->trans_status() === FALSE) {
			$this->db->trans_rollback();
			return 0;
		}
		$this->db->trans_commit();
		return 1;
	}

	private function _getAssignedShiftsData($staff_id, $staff_shifts, $type=0) {
		if($type) {
			foreach ($staff_shifts as $key => $value) {
				$staff_shifts[$key] = 0;
			}
			return $staff_shifts;
		}
		$dates = array_keys($staff_shifts);
		$shifts = $this->db->select("id, date")->where('staff_id', $staff_id)->where_in('date', $dates)->get('st_attendance_staff_shifts')->result();
		$shift_data = array();
		foreach ($shifts as $key => $shift) {
			$shift_data[$shift->date] = $shift->id;
		}
		return $shift_data;
	}

	private function _getPreviousShiftsData($staff_id, $previous, $type=0) {
		if($type) {
			foreach ($previous as $key => $value) {
				$previous[$key] = 0;
			}
			return $previous;
		}
		$dates = array_keys($previous);
		$shifts = $this->db->select("shift_master_id, date")->where('staff_id', $staff_id)->where_in('date', $dates)->get('st_attendance_staff_shifts')->result();
		$shift_data = array();
		foreach ($shifts as $key => $shift) {
			$shift_data[$shift->date] = $shift->shift_master_id;
		}
		return $shift_data;
	}

	private function get_staff_with_joining_dates(){
		$staffs=$this->db_readonly->select("*")
		->from("staff_master")
		->get()->result();

		if(!empty($staffs)){
			$staffsWithJoiningDate=[];
			foreach($staffs as $key => $val){
				if(!empty($val->joining_date) and !array_key_exists($val->id,$staffsWithJoiningDate)){
					$staffsWithJoiningDate[$val->id]=$val->joining_date;
				}
			}
			return $staffsWithJoiningDate;
		}else{
			return [];
		}
	}

	public function saveStaffMonthShift() {
		$staff_id = $_POST['staff_id'];
		$month = $_POST['month'];
		$staff_shifts = $_POST['staff_shift'];
		$previous = $_POST['previous'];
		$shifts = $_POST['shifts'];
		$staffsWithJoiningDate = $this->get_staff_with_joining_dates();

		$is_clone = 0;
		$clone_to = [];
		if(isset($_POST['clone_shift'])) {
			$is_clone = 1;
			$clone_to = $_POST['staffId'];
			if(empty($clone_to)) {
				return -1;
			}
			$clone_to[] = $staff_id;
		}

		$shift_master = $this->__getShiftsData();
		$status = array();
		if($is_clone) {
			//cloning shifts
			$assigned_staff = explode(",", $_POST['updating_staff']);
			foreach ($clone_to as $clone_staff_id) {
				if(in_array($clone_staff_id, $assigned_staff)) {
					//update
					$assigned_staff_shifts = $this->_getAssignedShiftsData($clone_staff_id, $staff_shifts);
					$previous_shifts = $this->_getPreviousShiftsData($clone_staff_id, $previous);
				} else {
					//insert
					$assigned_staff_shifts = $this->_getAssignedShiftsData($clone_staff_id, $staff_shifts, 1);
					$previous_shifts = $this->_getPreviousShiftsData($clone_staff_id, $previous, 1);
				}
				$input = array(
					'staff_shifts' => $assigned_staff_shifts,
					'shifts' => $shifts,
					'shift_master' => $shift_master,
					'staff_id' => $clone_staff_id,
					'month' => $month,
					'previous' => $previous_shifts,
					'staffsWithJoiningDate' => $staffsWithJoiningDate
				);
				$status[$clone_staff_id] = $this->_createORUpdateSingleStaffShift($input);
			}
		} else {
			//creating or updating single staff shift
			$input = array(
				'staff_shifts' => $staff_shifts,
				'shifts' => $shifts,
				'shift_master' => $shift_master,
				'staff_id' => $staff_id,
				'month' => $month,
				'previous' => $previous,
				'staffsWithJoiningDate' => $staffsWithJoiningDate
			);
			$status[$staff_id] = $this->_createORUpdateSingleStaffShift($input);
		}

		return $status;
	}

	private function getTodaysAllocatedStaffShift($staff_id){
		// get shift master id to shift master name
		$staff_shift = $this->db_readonly->select("shift_master_id")
			->from("st_attendance_staff_shifts")
			->where("staff_id", $staff_id)
			->where("date", date("Y-m-d"))
			->where("shift_master_id!=", 0)
			->get()->row();

		if (!empty($staff_shift)) {
			// sending shift master name
			return $this->db_readonly->select("id as shift_master_id,name as allocated_shift_name")
				->from("st_attendance_shifts_master")
				->where("id", $staff_shift->shift_master_id)
				->get()->row();
		} else {
			return 0;
		}
	}

	public function getSingleStaffMonthShift($staff_id, $month) {
		$sql = "select ss.id as staff_shift_id, ss.date, sm.id as shift_id, sm.name as shift_name, sm.shift_type 
				from st_attendance_staff_shifts ss 
				join st_attendance_shifts_master sm on sm.id=ss.shift_master_id 
				where ss.month='$month' and sm.is_active=1
				and ss.staff_id=$staff_id and shift_master_id!=0";
		$staff_shifts = $this->db->query($sql)->result();
		return ["staff_shifts"=>$staff_shifts,"allocatedShiftName"=>$this->getTodaysAllocatedStaffShift($staff_id)];
	}

	private  function getDatesFromRange($start, $end, $format = 'Y-m-d'){
        // Declare an empty array
        $array = array();
        // Variable that store the date interval
        // of period 1 day
        $interval = new DateInterval('P1D');
        $realEnd = new DateTime($end);
        $realEnd->add($interval);
        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);
        
        // Use loop to store date into array
        foreach($period as $date){
            $array[] = $date->format($format);
        }
        // Return the array elements
        return $array;
    }

	public function getListOfHolidaysFromCalender($staff_id,$month){
		if(empty($month)){
			return [];
		}
		
		$this->yearId = $this->acad_year->getAcadYearId();

		$month_number=date("m",strtotime($month));
		// getting holidays from calenderV1
		$holidays_list_1=$this->db_readonly->select("from_date, to_date")
		->from("school_calender")
		->where_in("event_type",[2,3]) // 2 -> Holiday, 3 -> Holiday range
		->where_in("applicable_to",[1,3]) //1 All staff, 3 All staffs and All parents
		->where("acad_year_id",$this->yearId)
		->get()->result();

		// get all staff types
		$all_staff_types = $this->settings->getSetting("staff_type");
		// get staff type
		$staff_details=$this->db_readonly->select("staff_type")
		->from("staff_master")
		->where("id",$staff_id)
		->get()->row();

		$staff_type="";
		if(!empty($all_staff_types) && !empty($staff_details)) {
			$staff_type_id=$staff_details->staff_type;
			$staff_type=$all_staff_types[$staff_type_id];
		}
		
		// getting holidays from calenderV2
		$holidays_list_2 = $this->db_readonly->select("cal.from_date, cal.to_date")
			->from("calendar_events_v2 cal")
			->join("calendar_events_v2_assigned cal_assi","cal_assi.calendar_v2_master_id=cal.calendar_v2_master_id")
			->where("cal_assi.assigned_type","STAFF")
			->where("cal_assi.assigned_staff_type",$staff_type)
			->where_in("cal.event_type", ["holiday", "holiday_range"])
			->get()->result();

		$holidays=array_merge($holidays_list_1,$holidays_list_2);

		if(empty($holidays)){
			return [];
		}

		$holiday_list=[];
		foreach($holidays as $key => $hol){
			if(strtotime($hol->from_date)==strtotime($hol->to_date)){
				// It is single day Holiday
				$holidayMonth=date('m',strtotime($hol->from_date));
				if($holidayMonth==$month_number){
					$holiday_list[(int)date('d',strtotime($hol->from_date))]="Holiday";
				}
			}else{
				// Holiday >1 days
				$dates_from_date_range=$this->getDatesFromRange($hol->from_date, $hol->to_date);
				if(!empty($dates_from_date_range)){
					foreach($dates_from_date_range as $index => $date){
						$holidayMonth = date('m', strtotime($date));
						if($holidayMonth==$month_number){
							$holiday_list[(int)date('d', strtotime($date))] = "Holiday";
						}
					}
				}
			}
		}
		return $holiday_list;
	}

	public function getAllStaffMonthShiftsStatus($staff_ids, $month) {
		$staff_ids_string = implode(", ", $staff_ids);
		$sql = "select ss.staff_id, ss.month 
				from st_attendance_staff_shifts ss 
				where ss.staff_id in ($staff_ids_string) 
				and ss.month='$month' 
				group by ss.staff_id";
		$staff_shifts = $this->db->query($sql)->result();
		$staff_assigned = array();
		foreach ($staff_shifts as $shift) {
			$staff_assigned[] = $shift->staff_id;
		}
		return $staff_assigned;
	}

	public function getShiftsByDate($staff_id, $date) {
		$attendance = $this->db->select("id as attendance_id, staff_shift_id, first_check_in_time, last_check_out_time, status, shift_start_time, shift_end_time")->from('st_attendance a')->where("a.staff_shift_id in (select id from st_attendance_staff_shifts where staff_id=$staff_id and date='$date')")->get()->result();

		$att_data = array();
		foreach ($attendance as $key => $att) {
			$att_data[$att->staff_shift_id] = $att;
		}

		$shifts = $this->db->select("sm.id as shift_master_id, ss.id as staff_shift_id, sm.name as shift_name, sm.shift_type, sm.start_time, sm.end_time, sm.is_shift_over_midnight, ss.date, TIMESTAMPDIFF(MINUTE, sm.start_time , sm.end_time) as minute_diff")->from('st_attendance_staff_shifts ss')->join('st_attendance_shifts_master sm', 'sm.id=ss.shift_master_id')->where('staff_id', $staff_id)->where('date', $date)->order_by('sm.start_time')->get()->result();

		$staff_shift_ids = array();
		foreach ($shifts as $i => $shift) {
			$shifts[$i]->attendance_id = 0;
			$shifts[$i]->check_in_status = 0;//0: Not checked-in, 1: checked-in, 2: checked-out (finished)
			if(array_key_exists($shift->staff_shift_id, $att_data)) {
				$shifts[$i]->attendance_id = $att_data[$shift->staff_shift_id]->attendance_id;
				$shifts[$i]->start_time = local_time($att_data[$shift->staff_shift_id]->shift_start_time, 'd-m-Y h:i a');
				// $shifts[$i]->minute_diff = $att_data[$shift->staff_shift_id]->minute_diff;
				$shifts[$i]->end_time = local_time($att_data[$shift->staff_shift_id]->shift_end_time, 'd-m-Y h:i a');
				$shifts[$i]->check_in_status = 1;
				if($att_data[$shift->staff_shift_id]->last_check_out_time) {
					$shifts[$i]->check_in_status = 2;
				}
			} else {
				$staff_shift_ids[] = $shift->staff_shift_id;
				$shifts[$i]->start_time = local_time($shift->date.' '.$shift->start_time, 'd-m-Y h:i a');
				// $shifts[$i]->minute_diff = $att_data[$shift->staff_shift_id]->minute_diff;
				$end_time = local_time($shift->date.' '.$shift->end_time, 'd-m-Y h:i a');
				if($shift->is_shift_over_midnight) {
					$next_day = date("d-m-Y", strtotime("$shift->date +1 day"));
					$end_time = local_time($next_day.' '.$shift->end_time, 'd-m-Y h:i a');
				}
				$shifts[$i]->end_time = $end_time;
			}
		}

		// echo "<pre>"; print_r($shifts); die();
		return $shifts;
	}

	public function getHolidays($from_date, $to_date) {
        $sql = "SELECT `id` FROM `school_calender` WHERE `event_type`!=1 AND `event_type`!=4 AND (`from_date`='$date' OR `to_date`='$date' OR (`from_date`<='$date' AND `to_date`>='$date')) AND applicable_to!=2";
        $query = $this->db->query($sql);
        return $query->num_rows();
    }

	private function _getDatesByRange($from_date, $to_date, $format = "Y-m-d"){
		$dates = [];
		$from = $from_date;
		while ($from != $to_date) {
			$dates[] = array(
				'date' => date('Y-m-d', strtotime($from)),
			);
			$from = date('Y-m-d', strtotime("$from +1 days"));
		}
		$dates[] = array(
			'date' => date('Y-m-d', strtotime($from)),
		);
		return $dates;
	}

	private function checkIsAnyShiftExistInThisMonth($staff_id){
		$staff_shift=$this->db_readonly->select("id")
			->from("st_attendance_staff_shifts")
			->where("staff_id",$staff_id)
			->get()->num_rows();

		if($staff_shift>0){
			return 1;
		}else{
			return 0;
		}
	}

	public function remove_staff_shift($data){
		// remove shifts for the give month
		$this->db->where("staff_id",$data["staff_id"]);

		if($data["type"]=="month"){
			$this->db->where("month",$data["month"]);
			// check if staff shift exists for the current day
			$dates=$this->_getDatesByRange(date('Y-m-01'),date('Y-m-d'));
			if(!empty($dates)){
				foreach($dates as $date){
					$this->db->where("date!=", $date['date']);
				}
			}
		}else{
			$this->db->where("date",$data["month"]);
		}
		$is_staff_shift_removed=$this->db->delete("st_attendance_staff_shifts");

		if($is_staff_shift_removed){
			return $this->checkIsAnyShiftExistInThisMonth($data["staff_id"]);
		}else{
			return -1;
		}
	}

	public function check_is_shift_exits($data){
		$this->db_readonly->select("id")->from("st_attendance_staff_shifts")
		->where("staff_id", $data["staff_id"]);

		if($data["type"]=="month"){
			$this->db_readonly->where("month",$data["month"]);
		}else{
			$this->db_readonly->where("date", $data["month"]);
		}
		$result = $this->db_readonly->get()->result();

		if(count($result)){
			return 1;
		}else{
			return 0;
		}
	}
}