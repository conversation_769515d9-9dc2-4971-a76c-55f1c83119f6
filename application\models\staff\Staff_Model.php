<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Staff_model
 *
 * <AUTHOR>
 */
class Staff_model extends CI_model
{
  private $yearId;
  public function __construct()
  {
    // $this->load->library('Settings');
    parent::__construct();
    $this->yearId = $this->acad_year->getAcadYearId();
    $this->load->library('filemanager');
  }

  public function Kolkata_datetime(){
    $timezone = new DateTimeZone("Asia/Kolkata" );
    $date = new DateTime();
    $date->setTimezone($timezone );
    $dtobj = $date->format('Y-m-d H:i:s');
    return $dtobj;
}

  private function _insertIntoUsers($username, $email)
  {
    $ci = &get_instance();
    //Remove all space (left, right and in-between); convert to lower case
    $username = strtolower(str_replace(' ', '', $username));

    //username check for exists
    $flag = true;
    do {
      $this->db->where('username', $username);
      $query = $this->db->get('users');
      if ($query->num_rows() > 0) {
        $username = $username . rand(10, 99);
      } else {
        $flag = false;
      }
    } while ($flag);

    $userId = $ci->ion_auth->register_1($username, 'welcome123', $email);
    return $userId;
  }

  private function _insertIntoAvatar($student_userIdu, $avatar_type, $insert_id, $friendly_name)
  {
    $ci = &get_instance();
    $param = array(
      'user_id' => $student_userIdu,
      'avatar_type' => $avatar_type,
      'stakeholder_id' => $insert_id,
      'friendly_name' => $friendly_name,
      'last_modified_by' => $this->authorization->getAvatarId()
    );

    return $ci->db->insert('avatar', $param);
  }

  private function _prepareStaffData()
  {
    $input = $this->input->post();
    // echo "<pre>";print_r($input);
    if (!empty($this->input->post('date_of_birth'))) {
      $dob =  date("Y-m-d", strtotime($this->input->post('date_of_birth')));
    } else {
      $dob = null;
    }
    if (!empty($this->input->post('joining_date'))) {
      $doj =  date("Y-m-d", strtotime($this->input->post('joining_date')));
    } else {
      $doj = null;
    }

    return array(
      'nationality' => ($this->input->post('nationality') == '') ? null : $this->input->post('nationality'),
      'staff_type' => $this->input->post('staff_type'),
      'short_name' => ($this->input->post('short_name') == '') ? null : $this->input->post('short_name'),
      'department' => ($this->input->post('department_name') == '') ? null : $this->input->post('department_name'),
      'designation' => ($this->input->post('designation_name') == '') ? null : $this->input->post('designation_name'),
      'first_name' => $this->input->post('first_name'),
      'last_name' => ($this->input->post('last_name') == '') ? null : $this->input->post('last_name'),
      'father_first_name' => ($this->input->post('father_first_name') == '') ? null : $this->input->post('father_first_name'),
      'father_last_name' => ($this->input->post('father_last_name') == '') ? null : $this->input->post('father_last_name'),
      'mother_first_name' => ($this->input->post('mother_first_name') == '') ? null : $this->input->post('mother_first_name'),
      'mother_last_name' => ($this->input->post('mother_last_name') == '') ? null : $this->input->post('mother_last_name'),
      'marital_status' => $this->input->post('marital_status'),
      'dob' => $dob,
      'gender' => $this->input->post('gender'),
      'contact_number' => ($this->input->post('contact_number') == '') ? null : $this->input->post('contact_number'),
      'spouse_name' => ($this->input->post('spouse_name') == '') ? null : $this->input->post('spouse_name'),
      'alternative_number' => ($this->input->post('alternative_no') == '') ? null : $this->input->post('alternative_no'),
      'aadhar_number' => ($this->input->post('adhaar_number') == '') ? null : $this->input->post('adhaar_number'),
      'qualification' => ($this->input->post('qualification_name') == '') ? null : $this->input->post('qualification_name'),
      'subject_specialization' => ($this->input->post('specialization') == '') ? null : $this->input->post('specialization'),
      'total_education_experience' => ($this->input->post('education_exp') == '') ? null : $this->input->post('education_exp'),
      'total_experience' => ($this->input->post('experience') == '') ? null : $this->input->post('experience'),
      'status' => $this->input->post('status'),
      'spouse_contact_no' => ($this->input->post('spouse_contact_no') == '') ? null : $this->input->post('spouse_contact_no'),
      'emergency_info' => ($this->input->post('emergency_info') == '') ? null : $this->input->post('emergency_info'),
      'employee_code' => ($this->input->post('employee_code') == '') ? null : $this->input->post('employee_code'),
      'blood_group' => ($this->input->post('blood_group') == '') ? null : $this->input->post('blood_group'),
      'joining_date' => $doj,
      'last_modified_by' =>  $this->authorization->getAvatarId(),
      'custom1' => (!isset($input['custom1']) || $input['custom1'] == '')? null : $input['custom1'],
      'custom2' => (!isset($input['custom2']) || $input['custom2'] == '')? null : $input['custom2'],
      'custom3' => (!isset($input['custom3']) || $input['custom3'] == '')? null : $input['custom3'],
      'custom4' => (!isset($input['custom4']) || $input['custom4'] == '')? null : $input['custom4'],
      'custom5' => (!isset($input['custom5']) || $input['custom5'] == '')? null : $input['custom5'],
      'custom6' => (!isset($input['custom6']) || $input['custom6'] == '')? null : $input['custom6'],
      'custom7' => (!isset($input['custom7']) || $input['custom7'] == '')? null : $input['custom7'],
      'custom8' => (!isset($input['custom8']) || $input['custom8'] == '')? null : $input['custom8'],
      'custom9' => (!isset($input['custom9']) || $input['custom9'] == '')? null : $input['custom9'],
      'custom10' => (!isset($input['custom10']) || $input['custom10'] == '')? null : $input['custom10'],
      'custom11' => (!isset($input['custom11']) || $input['custom11'] == '')? null : $input['custom11'],
      'custom12' => (!isset($input['custom12']) || $input['custom12'] == '')? null : $input['custom12'],
      'custom13' => (!isset($input['custom13']) || $input['custom13'] == '')? null : $input['custom13'],
      'custom14' => (!isset($input['custom14']) || $input['custom14'] == '')? null : $input['custom14'],
      'custom15' => (!isset($input['custom15']) || $input['custom15'] == '')? null : $input['custom15'],
      'custom16' => (!isset($input['custom16']) || $input['custom16'] == '')? null : $input['custom16'],
      'custom17' => (!isset($input['custom17']) || $input['custom17'] == '')? null : $input['custom17'],
      'custom18' => (!isset($input['custom18']) || $input['custom18'] == '')? null : $input['custom18'],
      'custom19' => (!isset($input['custom19']) || $input['custom19'] == '')? null : $input['custom19'],
      'custom20' => (!isset($input['custom20']) || $input['custom20'] == '')? null : $input['custom20'],
      'math_high_grade' => (!isset($input['math_high_grade']) || $input['math_high_grade'] == '')? null : $input['math_high_grade'],
      'english_high_grade' => (!isset($input['english_high_grade']) || $input['english_high_grade'] == '')? null : $input['english_high_grade'],
      'social_high_grade' => (!isset($input['social_high_grade']) || $input['social_high_grade'] == '')? null : $input['social_high_grade'],
      'trained_to_teach' => (!isset($input['trained_to_teach']) || $input['trained_to_teach'] == '')? null : $input['trained_to_teach'],
      'appointed_subject' => (!isset($input['appointed_subject']) || $input['appointed_subject'] == '')? null : $input['appointed_subject'],
      'classes_taught' => (!isset($input['classes_taught']) || $input['classes_taught'] == '')? null : $input['classes_taught'],
      'main_sub_taught' => (!isset($input['main_sub_taught']) || $input['main_sub_taught'] == '')? null : $input['main_sub_taught'],
      'add_sub_taught' => (!isset($input['add_sub_taught']) || $input['add_sub_taught'] == '')? null : $input['add_sub_taught'],
      'boarding' => (!isset($input['boarding']) || $input['boarding'] == '')? null : $input['boarding'],
      'voter_id' => (!isset($input['voter_id']) || $input['voter_id'] == '')? null : $input['voter_id'],
      'height' => (!isset($input['height']) || $input['height'] == '')? null : $input['height'],
      'weight' => (!isset($input['weight']) || $input['weight'] == '')? null : $input['weight'],
      'allergies' => (!isset($input['allergies']) || $input['allergies'] == '')? null : $input['allergies'],
      'medical_issues' => (!isset($input['medical_issues']) || $input['medical_issues'] == '')? null : $input['medical_issues'],
      'identification_mark' => (!isset($input['identification_mark']) || $input['identification_mark'] == '')? null : $input['identification_mark'],
      'person_with_disability' => (!isset($input['person_with_disability']) || $input['person_with_disability'] == '')? null : $input['person_with_disability'],
      'religion' => (!isset($input['religion']) || $input['religion'] == '')? null : $input['religion'],
      'category' => (!isset($input['category']) || $input['category'] == '')? null : $input['category'],
      'caste' => (!isset($input['caste']) || $input['caste'] == '')? null : $input['caste'],
      'passport_number' => (!isset($input['passport_number']) || $input['passport_number'] == '')? null : $input['passport_number'],
      'passport_place_of_issue' => (!isset($input['passport_place_of_issue']) || $input['passport_place_of_issue'] == '')? null : $input['passport_place_of_issue'],
      'passport_date_of_issue' => (!isset($input['passport_date_of_issue']) || $input['passport_date_of_issue'] == '')? null : date("Y-m-d", strtotime($this->input->post('passport_date_of_issue'))),
      'passport_expiry_date' => (!isset($input['passport_expiry_date']) || $input['passport_expiry_date'] == '')? null : date("Y-m-d", strtotime($this->input->post('passport_expiry_date'))),
      'visa_details' => (!isset($input['visa_details']) || $input['visa_details'] == '')? null : $input['visa_details'],
      'has_completed_any_bed' => (!isset($input['has_completed_any_bed']) || $input['has_completed_any_bed'] == '')? null : $input['has_completed_any_bed'],
      'nature_of_appointment' => (!isset($input['nature_of_appointment']) || $input['nature_of_appointment'] == '')? null : $input['nature_of_appointment'],
      'personal_mail_id' =>(!isset($input['personal_mail_id']) || $input['personal_mail_id'] == '')? null : $input['personal_mail_id'],
      'father_occupation' =>(!isset($input['father_occupation']) || $input['father_occupation'] == '') ? null:$input['father_occupation'],
      'spouse_occupation' =>(!isset($input['spouse_occupation']) || $input['spouse_occupation'] == '') ? null:$input['spouse_occupation'],
      'father_contact_no' =>(!isset($input['father_contact_no']) || $input['father_contact_no'] == '') ? null:$input['father_contact_no'],
      'staff_reference_code' =>(!isset($input['staff_reference_code']) || $input['staff_reference_code'] == '') ? null:$input['staff_reference_code'],
      'previous_designation_name' =>(!isset($input['previous_designation_name']) || $input['previous_designation_name'] == '') ? null:$input['previous_designation_name'],
      'salutation'=>(!isset($input['salutation']) || $input['salutation'] == '') ? null:$input['salutation'],
      'has_completed_any_pgdei' => (!isset($input['has_completed_pgde']) || $input['has_completed_pgde'] == '')? null : $input['has_completed_pgde'],
      'child1_first_name' => (!isset($input['child1_first_name']) || $input['child1_first_name'] == '')? null : $input['child1_first_name'],
      'child2_first_name' => (!isset($input['child2_first_name']) || $input['child2_first_name'] == '')? null : $input['child2_first_name'],
      'child3_first_name' => (!isset($input['child3_first_name']) || $input['child3_first_name'] == '')? null : $input['child3_first_name'],
      'spouse_gender' => (!isset($input['spouse_gender']) || $input['spouse_gender'] == '')? null : $input['spouse_gender'],
      'child1_gender' => (!isset($input['child1_gender']) || $input['child1_gender'] == '')? null : $input['child1_gender'],
      'child2_gender' => (!isset($input['child2_gender']) || $input['child2_gender'] == '')? null : $input['child2_gender'],
      'child3_gender' => (!isset($input['child3_gender']) || $input['child3_gender'] == '')? null : $input['child3_gender'],
      'father_dob' => (!isset($input['father_dob']) || $input['father_dob'] == '')? null : date("Y-m-d", strtotime($input['father_dob'])),
      'mother_dob' => (!isset($input['mother_dob']) || $input['mother_dob'] == '')? null : date("Y-m-d", strtotime($input['mother_dob'])),
      'spouse_dob' => (!isset($input['spouse_dob']) || $input['spouse_dob'] == '')? null : date("Y-m-d", strtotime($input['spouse_dob'])),
      'child1_dob' => (!isset($input['child1_dob']) || $input['child1_dob'] == '')? null : date("Y-m-d", strtotime($input['child1_dob'])),
      'child2_dob' => (!isset($input['child2_dob']) || $input['child2_dob'] == '')? null : date("Y-m-d", strtotime($input['child2_dob'])),
      'child3_dob' => (!isset($input['child3_dob']) || $input['child3_dob'] == '')? null : date("Y-m-d", strtotime($input['child3_dob'])),
      'father_is_dependent' => (!isset($input['father_is_dependent']) || $input['father_is_dependent'] == '')? null : $input['father_is_dependent'],
      'mother_is_dependent' => (!isset($input['mother_is_dependent']) || $input['mother_is_dependent'] == '')? null : $input['mother_is_dependent'],
      'spouse_is_dependent' => (!isset($input['spouse_is_dependent']) || $input['spouse_is_dependent'] == '')? null : $input['spouse_is_dependent'],
      'child1_is_dependent' => (!isset($input['child1_is_dependent']) || $input['child1_is_dependent'] == '')? null : $input['child1_is_dependent'],
      'child2_is_dependent' => (!isset($input['child2_is_dependent']) || $input['child2_is_dependent'] == '')? null : $input['child2_is_dependent'],
      'child3_is_dependent' => (!isset($input['child3_is_dependent']) || $input['child3_is_dependent'] == '')? null : $input['child3_is_dependent'],
      'spouse_last_name' => (!isset($input['spouse_last_name']) || $input['spouse_last_name'] == '')? null : $input['spouse_last_name'],
      'child1_last_name' => (!isset($input['child1_last_name']) || $input['child1_last_name'] == '')? null : $input['child1_last_name'],
      'child2_last_name' => (!isset($input['child2_last_name']) || $input['child2_last_name'] == '')? null : $input['child2_last_name'],
      'child3_last_name' => (!isset($input['child3_last_name']) || $input['child3_last_name'] == '')? null : $input['child3_last_name'],
      'is_primary_instance' => (!isset($input['is_primary_instance']) || $input['is_primary_instance'] == '')? 1 : $input['is_primary_instance'],
      'staff_house' => (!isset($input['staff_house']) || $input['staff_house'] == '')? null : $input['staff_house'],
      'include_father_insurance' => $this->input->post('include_father_insurance'),
      'include_mother_insurance' => $this->input->post('include_mother_insurance'),
      'include_spouse_insurance' => $this->input->post('include_spouse_insurance'),
      'include_child1_insurance' => $this->input->post('include_child1_insurance'),
      'include_child2_insurance' => $this->input->post('include_child2_insurance'),
      'include_child3_insurance' => $this->input->post('include_child3_insurance'),
      'profile_status' =>'unlocked'
    );
  }

  public function addStaffDetails($path,$employee_id = '')
  {

    $addStaff = $this->_prepareStaffData();
    // echo "<pre>";print_r($addStaff);die();
    if ($path['file_name'] != '') {
      $addStaff = array_merge($addStaff, ['picture_url' => $path['file_name']]);
    }
    if(!empty($employee_id)){
      $addStaff['employee_code'] = $employee_id;
    }
    $this->db->insert('staff_master', $addStaff);

    if ($this->db->affected_rows() != 1) {
      return 0;
    } else {
      $suid = $this->db->insert_id();

      $firstName = $this->input->post('first_name');
      $lastName = $this->input->post('last_name');
      $pUsername = $this->generateUsername($firstName, $lastName);
      $staff_userId = $this->_insertIntoUsers($pUsername, $this->input->post('mail_id'));

      if (!$staff_userId) {
        return 0;
      }
      //Avatar Type for Staff is 4
      $avatarId = $this->_insertIntoAvatar($staff_userId, '4', $suid, $this->input->post('first_name') . ' ' . $this->input->post('last_name'));
      if (!$avatarId) {
        return 0;
      }
      return $suid;
    }
  }

  private function first($users)
  {
    do {
      $username = $this->generateRandomCode(4, 0); //generating random string
      $username .= $this->generateRandomCode(2, 1); //generating random number
    } while (in_array($username, $users));

    return $username;
  }

  private function second($name, $users)
  {
    $name = substr($name, 0, 6);
    if (!in_array($name, $users))
      return $name;
    $len = strlen($name);
    $random = '';
    $num = 6 - $len;
    do {
      $times = pow(10, $num);
      for ($i = 0; $i < $times; $i++) {
        $random = $this->generateRandomCode($num, 1);
      }
      $num++;
    } while (in_array($name . $random, $users));
    return $name . $random;
  }

  private function third($firstName, $lastName, $users)
  {
    $username = substr($firstName, 0, 4) . substr($lastName, 0, 2);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 2) . substr($lastName, 0, 4);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 4) . substr($firstName, 0, 2);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 2) . substr($firstName, 0, 4);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 3) . substr($lastName, 0, 3);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 3) . substr($firstName, 0, 3);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 5) . substr($lastName, 0, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 1) . substr($lastName, 0, 5);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 5) . substr($firstName, 0, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 1) . substr($firstName, 0, 5);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 6);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 6);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 4) . substr($lastName, 0, 2) . $this->generateRandomCode(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 4) . substr($firstName, 0, 2) . $this->generateRandomCode(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 3) . substr($lastName, 0, 3) . $this->generateRandomCode(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 3) . substr($firstName, 0, 3) . $this->generateRandomCode(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($firstName, 0, 5) . substr($lastName, 0, 1) . $this->generateRandomCode(2, 1);
    if (!in_array($username, $users))
      return $username;

    $username = substr($lastName, 0, 5) . substr($firstName, 0, 1) . $this->generateRandomCode(2, 1);
    if (!in_array($username, $users)) {
      return $username;
    } else {
      $username = substr($lastName, 0, 3) . substr($firstName, 0, 2) . $this->generateRandomCode(3, 1);
    }
    return $username;
  }

  private function generateRandomCode($length = 6, $isNumber = 1)
  {
    if ($isNumber)
      return substr(str_shuffle(str_repeat($x = '1234567890', ceil($length / strlen($x)))), 1, $length);
    else
      return substr(str_shuffle(str_repeat($x = 'abcdefghijklmnopqrstuvwxyz', ceil($length / strlen($x)))), 1, $length);
  }

  private function generateUsername($firstName, $lastName)
  {
    $names = $this->db->select('username')->get('users')->result();
    $users = array();
    if (!empty($names)) {
      foreach ($names as $val) {
        array_push($users, $val->username);
      }
    }
    $firstName = preg_replace('/\s+/', '', $firstName);
    $lastName = preg_replace('/\s+/', '', $lastName);
    $firstName = preg_replace('/[^A-Za-z0-9]/', '', $firstName);
    $lastName = preg_replace('/[^A-Za-z0-9]/', '', $lastName);
    $name = '';
    $fullName = $firstName . $lastName;
    if ($firstName == '' && $lastName == '') {
      $username = $this->first($users);
    } else if ($firstName == '') {
      if (!in_array($lastName, $users)) {
        $username = substr($lastName, 0, 6);
      } else {
        $username = $this->second($lastName, $users);
      }
    } else if ($lastName == '') {
      if (!in_array($firstName, $users)) {
        $username = substr($firstName, 0, 6);
      } else {
        $username = $this->second($firstName, $users);
      }
    } else {
      $username = $this->third($firstName, $lastName, $users);
    }
    return $username;
  }

  public function getStaffDetails()
  {
    $this->db_readonly->select('*');
    $this->db_readonly->from('staff_master');
    return $this->db_readonly->get()->result();
  }

  public function getStaffDetails_staff_view($status, $staffType, $isPrimaryInstance)
  {
    $this->db_readonly->select("sm.id, sm.employee_code, sm.first_name, sm.last_name, sm.short_name, sm.contact_number, sm.status, sm.staff_type, u.email, if(sm.joining_date = '1970-01-01' OR isnull(sm.joining_date), '-', DATE_FORMAT(sm.joining_date, '%d-%b-%Y')) as doj,picture_url,gender");
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('avatar a', "sm.id=a.stakeholder_id and avatar_type = 4");
    $this->db_readonly->join('users u', 'a.user_id=u.id');
    $this->db_readonly->where('status', $status);
    if ($staffType != '-1') {
      $this->db_readonly->where('sm.staff_type', $staffType);
    }
    if ($isPrimaryInstance != '') {
      $this->db_readonly->where('sm.is_primary_instance', $isPrimaryInstance);
    }
    $this->db_readonly->order_by('sm.first_name');
    $result = $this->db_readonly->get()->result();

    $staff_type_config = $this->settings->getSetting('staff_type');
    $keys = [];
    if (!empty($staff_type_config)) {
        $keys = array_keys($staff_type_config);
    }
    
    foreach ($result as &$staff) {
        if(!empty($staff->picture_url)){
          $staff->picture_url = $this->filemanager->getFilePath($staff->picture_url);
        }
        if (empty($staff_type_config)) {
            $staff->staff_type = 'Not Enabled';
        } elseif (!in_array($staff->staff_type, $keys)) {
            $staff->staff_type = 'NA';
        } else if($staff->staff_type == null || $staff->staff_type == ''){
          $staff->staff_type = 'Unknown';
        }else{
            $staff->staff_type = $staff_type_config[$staff->staff_type];
        }
    }
    // echo "<pre>";print_r($result);die();

    return $result;
  }

  public function getStaffDetailById($staffId)
  {
    $this->db_readonly->select('sm.*, u.email as email, u.id as staffUserId');
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('avatar a', 'sm.id=a.stakeholder_id');
    $this->db_readonly->join('users u', 'a.user_id=u.id');
    $this->db_readonly->where('a.avatar_type', '4'); //4: Staff
    $this->db_readonly->where('sm.id', $staffId);
    return $this->db_readonly->get()->row();
  }
  public function getStaffTimetableById($staffId)
  {
    $this->db_readonly->select('allocation_data');
    $this->db_readonly->from('staff_timetable');
    return $this->db_readonly->get()->row();
  }

  public function updateStaff($staff_id, $input_form, $staffUserId, $path)
  {
    $updateStaff = $this->_prepareStaffDataForUpadte();
    if (!empty($path) && $path['file_name'] != '') {
      $updateStaff = array_merge($updateStaff, ['picture_url' => $path['file_name']]);
    }

    if($this->input->post('staff_photo') != '') {
      $updateStaff = array_merge($updateStaff,['high_quality_picture_url' => $this->input->post('staff_photo')]);
    }

    if($this->input->post('staff_signature') != '') {
      $updateStaff = array_merge($updateStaff,['staff_signature' => $this->input->post('staff_signature')]);
    }

    $status = $this->input->post('status');
    if ($status != 2) {
      $active = 0;
    } else {
      $active = 1;
    }
    // echo '<pre>';print_r($staff_id);
    // echo '<pre>';print_r($updateStaff);die();
    $this->db->where('id', $staff_id);
    $result1 = $this->db->update('staff_master', $updateStaff);

    $userData = array('email' => $input_form['mail_id'], 'active' => $active);
    $this->db->where('id', $staffUserId);
    $result = $this->db->update('users', $userData);

    return $result;
  }

  public function deleteStaff($param)
  {
    $this->db->where('id', $param);
    $this->db->delete('staff_master');
  }


  public function get_dairyStaffIdwise($staffId)
  {
    $this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as staff_id");
    $this->db_readonly->from('circular_master');
    $this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
    $this->db_readonly->where('circular_sent_to.stakeholder_id', $staffId);
    $this->db_readonly->where('circular_sent_to.avatar_type', 4);
    $this->db_readonly->where('is_published', 1);
    $this->db_readonly->where('acad_year_id', $this->yearId);
    $this->db_readonly->order_by('circular_master.circular_date', 'desc');
    return $this->db_readonly->get()->result();
    //echo "<pre>";print_r($return);die();
  }

  public function getCountOfCirculars($staffId)
  {
    $this->db_readonly->select("count(ct.id) as circular_count, category");
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id');
    $this->db_readonly->where('ct.stakeholder_id', $staffId);
    $this->db_readonly->where('ct.avatar_type', 4);
    $this->db_readonly->where('cm.visible', 1);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->group_by('cm.category');
    $result = $this->db_readonly->get()->result();
    $res = array();
    foreach ($result as $key => $value) {
      $res[$value->category] = $value->circular_count;
    }
    return $res;
  }

  public function getCountOfOldCirculars($staffId)
  {
    $this->db_readonly->select("count(ct.id) as circular_count, category");
    $this->db_readonly->from('circular_master cm');
    $this->db_readonly->join('circular_sent_to ct', 'ct.circular_id=cm.id');
    $this->db_readonly->where('ct.stakeholder_id', $staffId);
    $this->db_readonly->where('ct.avatar_type', 4);
    $this->db_readonly->where('cm.is_published', 1);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->group_by('cm.category');
    $result = $this->db_readonly->get()->result();
    $res = array();
    foreach ($result as $key => $value) {
      $res[$value->category] = $value->circular_count;
    }
    return $res;
  }

  public function latestCirculars($staffId)
  {
    $fromDate = date("Y-m-d", strtotime("-7 days"));
    $today = date("Y-m-d");
    $this->db_readonly->select("count(ct.id) as circular_count, category");
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->join('circularv2_sent_to ct', 'ct.circularv2_master_id=cm.id');
    $this->db_readonly->where('ct.stakeholder_id', $staffId);
    $this->db_readonly->where('ct.avatar_type', 4);
    $this->db_readonly->where('cm.visible', 1);
    $this->db_readonly->where('date_format(sent_on,"%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $today . '"');
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->group_by('cm.category');
    $result = $this->db_readonly->get()->result();
    $res = array();
    foreach ($result as $key => $value) {
      $res[$value->category] = $value->circular_count;
    }
    return $res;
  }

  public function getCircularCount($category, $staffId)
  {
    $this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as staff_id");
    $this->db_readonly->from('circular_master');
    $this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
    $this->db_readonly->where('circular_sent_to.stakeholder_id', $staffId);
    $this->db_readonly->where('circular_sent_to.avatar_type', 4);
    $this->db_readonly->where('is_published', 1);
    $this->db_readonly->where('acad_year_id', $this->yearId);
    $this->db_readonly->where('category', $category);
    $num_rows = $this->db_readonly->count_all_results();
    // echo '<pre>'; print_r($num_rows); die();
    return $num_rows;
  }

  public function newCount($category, $staffId)
  {
    //$fromDate = date("Y-m-d",strtotime("-7 days"));
    $fromDate = date("Y-m-d", strtotime("-7 days"));
    $today = date("Y-m-d");
    //echo $today; die();
    $this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as staff_id");
    $this->db_readonly->from('circular_master');
    $this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
    $this->db_readonly->where('circular_sent_to.stakeholder_id', $staffId);
    $this->db_readonly->where('circular_sent_to.avatar_type', 4);
    $this->db_readonly->where('is_published', 1);
    $this->db_readonly->where('acad_year_id', $this->yearId);
    $this->db_readonly->where('date_format(circular_date,"%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $today . '"');

    $this->db_readonly->where('category', $category);
    $new_rows = $this->db_readonly->count_all_results();
    // echo '<pre>'; print_r($num_rows); die();
    return $new_rows;
  }

  public function getStaffCircularCategoryWise($staffId, $category)
  {
    $this->db_readonly->select("circular_master.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date ,circular_title,circular_content,stakeholder_id as staff_id");
    $this->db_readonly->from('circular_master');
    $this->db_readonly->join('circular_sent_to', 'circular_sent_to.circular_id=circular_master.id');
    $this->db_readonly->where('circular_sent_to.stakeholder_id', $staffId);
    $this->db_readonly->where('circular_sent_to.avatar_type', 4);
    $this->db_readonly->where('is_published', 1);
    $this->db_readonly->where('category', $category);
    $this->db_readonly->order_by('circular_master.circular_date', 'desc');
    return $this->db_readonly->get()->result();
  }

  public function getCircularsAndEmails($category, $staffId)
  {
    $this->db_readonly->select("cm.id,date_format(sent_on,'%d-%m-%Y %h:%i %p') as date ,title,body,category, cm.file_path, cm.mode");
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->where("cm.id in (select circularv2_master_id from circularv2_sent_to where stakeholder_id=$staffId and avatar_type=4)");
    $this->db_readonly->where('cm.category', $category);
    $this->db_readonly->where('cm.visible', 1);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('cm.sent_on', 'desc');
    return $this->db_readonly->get()->result();
  }

  public function getOldCirculars($category, $staffId)
  {
    $this->db_readonly->select("cm.id,date_format(circular_date,'%d-%m-%Y %h:%i %p') as date,cm.circular_title as title, cm.circular_content as body,category, cm.file_path, 'circular' as mode");
    $this->db_readonly->from('circular_master cm');
    $this->db_readonly->where("cm.id in (select circular_id from circular_sent_to where stakeholder_id=$staffId and avatar_type=4)");
    $this->db_readonly->where('cm.category', $category);
    $this->db_readonly->where('cm.is_published', 1);
    $this->db_readonly->where('cm.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('cm.circular_date', 'desc');
    return $this->db_readonly->get()->result();
  }

  public function getCircularDeatils($id)
  {
    $this->db_readonly->select("cm.id as circular_id, cm.title,cm.body,date_format(cm.sent_on,'%d-%m-%Y %h:%i %p') as date,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdb_readonlyy,cm.file_path, cm.mode");
    $this->db_readonly->from('circularv2_master cm');
    $this->db_readonly->where('cm.id', $id);
    $this->db_readonly->join('avatar a', 'cm.sent_by=a.id', 'left');
    $this->db_readonly->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left');
    return $this->db_readonly->get()->row();
  }

  public function getOldCircularDeatils($id)
  {
    $this->db_readonly->select("cm.circular_title as title,cm.circular_content as body,date_format(cm.circular_date,'%d-%m-%Y %h:%i %p') as date,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdb_readonlyy,cm.file_path, 'circular' as mode");
    $this->db_readonly->from('circular_master cm');
    $this->db_readonly->where('cm.id', $id);
    $this->db_readonly->join('avatar a', 'cm.created_by=a.id', 'left');
    $this->db_readonly->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left');
    return $this->db_readonly->get()->row();
  }

  public function getStaffCircularData($id)
  {
    $this->db_readonly->select("cm.circular_title,cm.circular_content,date_format(cm.circular_date,'%d-%m-%Y %h:%i %p') as date,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as createdb_readonlyy,cm.file_path");
    $this->db_readonly->from('circular_master cm');
    $this->db_readonly->where('cm.id', $id);
    $this->db_readonly->join('avatar a', 'cm.created_by=a.id', 'left');
    $this->db_readonly->join('staff_master sm', 'a.stakeholder_id=sm.id', 'left');
    return $this->db_readonly->get()->row();
  }


  public function get_smsStaffIdwise($staffId)
  {
    $date = date('Y-m-d', strtotime('-30 days'));
    $this->db_readonly->select("distinct(sms_master.id),date_format(sms_date,'%d-%m-%Y') as date ,sms_date as smsDate, sms_content,student_staff_id as staff_id");
    $this->db_readonly->from('sms_master');
    $this->db_readonly->join('sms_sent_to', 'sms_sent_to.sms_id=sms_master.id');
    $this->db_readonly->where('sms_sent_to.student_staff_id', $staffId);
    $this->db_readonly->where('DATE_FORMAT(sms_master.sms_date, "%Y-%m-%d") >', $date);
    $this->db_readonly->where('sms_master.user_type', 'Staff');
    $this->db_readonly->where('sms_master.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('sms_master.sms_date', 'desc');
    return $this->db_readonly->get()->result();
  }

  public function getSMSDetail($id)
  {
    $this->db_readonly->select("sms_master.id,date_format(sms_date,'%d-%m-%Y') as date ,sms_date as smsDate, sms_content");
    $this->db_readonly->where('id', $id);
    return $this->db_readonly->get('sms_master')->row();
  }

  public function getStaffDistinctColumn($column_name)
  {
    $result = $this->db_readonly->select("distinct(" . $column_name . ") as name")
      ->from('staff_master')
      ->order_by($column_name)
      ->where($column_name . ' !=', "")
      ->order_by('qualification')
      ->get()->result();

    return $result;
  }

  public function getStaffDepartmentList()
  {
    return $this->db_readonly->select('*')
    ->from('staff_departments')
    ->where('status', 1)
    ->order_by('department')
    ->get()->result();
  }

  public function getStaffDesignationList()
  {
    return $this->db_readonly->where('status', 1)->order_by('designation')->get('staff_designations')->result();
  }


  public function getApprovedStaffDetails()
  {
    $this->db_readonly->select("id,concat(ifnull(first_name, ''), ' ', ifnull(last_name, '')) as staff_name, ifnull(employee_code, '-') as employee_code,department,designation,contact_number");
    $this->db_readonly->where('status', '2');
    $this->db_readonly->from('staff_master');
    $this->db_readonly->order_by('first_name');
    return $this->db_readonly->get()->result();
  }

  public function insert_staffSalaryDetailsy($staffId)
  {

    $data = array(
      'total_salary' => $this->input->post('total_salary'),
      'basic_salary' => $this->input->post('basic_salary'),
      'staff_hra' => $this->input->post('staff_hra'),
      'staff_da' => $this->input->post('staff_da'),
      'total_gross' => $this->input->post('total_gross'),
      'staff_esi' => $this->input->post('staff_esi'),
      'staff_pf' => $this->input->post('staff_pf'),
      'staff_pt' => $this->input->post('staff_pt'),
      'bank_account' => $this->input->post('bank_account'),
      'esi_number' => $this->input->post('esi_number'),
      'salary_profile_id' => $this->input->post('profileId'),
      'pf_uan_number' => $this->input->post('pf_uan_number'),
      'pan_number' => $this->input->post('pan_number'),
      'last_modified_by' =>  $this->authorization->getAvatarId(),
      'staff_id' => $staffId,
    );

    return $this->db->insert('staff_salary_details', $data);
  }

  public function get_SallaryDetailsbyStaffId($staffId)
  {
    return $this->db_readonly->select("ssd. *, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName")
      ->from('staff_master sm')
      ->where('sm.id', $staffId)
      ->join('staff_salary_details ssd', 'sm.id=ssd.staff_id', 'left')
      ->get()->row();
  }

  public function update_staffSalaryDetailsy($staffId)
  {

    $data = array(
      'total_salary' => $this->input->post('total_salary'),
      'basic_salary' => $this->input->post('basic_salary'),
      'staff_hra' => $this->input->post('staff_hra'),
      'staff_da' => $this->input->post('staff_da'),
      'total_gross' => $this->input->post('total_gross'),
      'staff_esi' => $this->input->post('staff_esi'),
      'staff_pf' => $this->input->post('staff_pf'),
      'staff_pt' => $this->input->post('staff_pt'),
      'bank_account' => $this->input->post('bank_account'),
      'esi_number' => $this->input->post('esi_number'),
      'salary_profile_id' => $this->input->post('profileId'),
      'pf_uan_number' => $this->input->post('pf_uan_number'),
      'pan_number' => $this->input->post('pan_number'),
      'last_modified_by' =>  $this->authorization->getAvatarId(),
    );
    $this->db->where('staff_id', $staffId);
    return $this->db->update('staff_salary_details', $data);
  }

  public function generate_staffSalaryDetailsy($staffId)
  {
    $data = array(
      'staffid' => $staffId,
      'year' => $this->input->post('year'),
      'month_name' => $this->input->post('month_name'),
      'no_of_present' => $this->input->post('no_of_present'),
      'no_of_absent' => $this->input->post('no_of_absent'),
      'no_of_leave' => $this->input->post('no_of_leave'),
      'basic_salary' => $this->input->post('basic_salary'),
      'staff_hra' => $this->input->post('staff_hra'),
      'staff_da' => $this->input->post('staff_da'),
      'esi' => $this->input->post('esi'),
      'epf' => $this->input->post('epf'),
      'vpf' => $this->input->post('vpf'),
      'total_earning' => $this->input->post('total_earning'),
      'net_pay' => $this->input->post('net_pay'),
      'total_deduct' => $this->input->post('total_deduct'),
      'created_by' =>  $this->authorization->getAvatarId(),
    );
    return $this->db->insert('payroll_payslip', $data);
  }

  public function get_payslipGenereted($staffId)
  {

    return $this->db_readonly->select('*')
      ->from('payroll_payslip')
      ->where('staffid', $staffId)
      ->get()->row();
  }

  public function Update_staffPaySlipDetailsy($staffId)
  {
    $data = array(
      'year' => $this->input->post('year'),
      'month_name' => $this->input->post('month_name'),
      'no_of_present' => $this->input->post('no_of_present'),
      'no_of_absent' => $this->input->post('no_of_absent'),
      'no_of_leave' => $this->input->post('no_of_leave'),
      'basic_salary' => $this->input->post('basic_salary'),
      'staff_hra' => $this->input->post('staff_hra'),
      'staff_da' => $this->input->post('staff_da'),
      'esi' => $this->input->post('esi'),
      'epf' => $this->input->post('epf'),
      'vpf' => $this->input->post('vpf'),
      'total_earning' => $this->input->post('total_earning'),
      'net_pay' => $this->input->post('net_pay'),
      'total_deduct' => $this->input->post('total_deduct'),
      'created_by' =>  $this->authorization->getAvatarId(),
    );

    $this->db->where('staffid', $staffId);
    $this->db->where('year', $this->input->post('year'));
    return $this->db->update('payroll_payslip', $data);
  }

  public function print_payslipGenerate($staffid, $month, $year)
  {
    return $this->db_readonly->select("sp. *, date_format(sp.created_on,'%d-%m-%Y') as slip_date, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName")
      ->from('payroll_payslip sp')
      ->where('sp.staffid', $staffid)
      ->where('sp.month_name', $month)
      ->where('sp.year', $year)
      ->join('staff_master sm', 'sm.id=sp.staffid')
      ->get()->row();
  }

  public function getUid($staffId)
  {
    $this->db_readonly->select('a.user_id');
    $this->db_readonly->from('avatar a');
    $this->db_readonly->where('a.stakeholder_id', $staffId);
    $this->db_readonly->where('a.avatar_type', 4);
    return $this->db_readonly->get()->row();
  }

  public function checkUsernamebystafwise($username)
  {
    $res = $this->db_readonly->query("select * from users where username ='$username'")->num_rows();
    return $res;
  }

  public function getStaffuserDetails($staffId)
  {
    $this->db_readonly->select('a.user_id,u.username,u.active,a.stakeholder_id as staffId,u.restore_password');
    $this->db_readonly->from('avatar a');
    $this->db_readonly->where('a.stakeholder_id', $staffId);
    $this->db_readonly->where('a.avatar_type', 4);
    $this->db_readonly->join('users u', 'u.id=a.user_id');

    $result = $this->db_readonly->get()->row();

    //echo '<pre>';print_r($result);die();

    return $result;
  }

  // public function getAll_Staff(){
  // 	$result = $this->db->select("id, concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
  // 			->from('staff_master')
  // 			->get()->result();
  // 	return $result;
  // }

  public function getAll_Staff()
  {
    return $this->db_readonly->select("id, concat(ifnull(first_name,''),' ', ifnull(last_name,'')) as staff_name, ifnull(designation, '') as designation, ifnull(department, '') as department, ifnull(staff_type, '') as staff_type")
      ->from('staff_master')
      ->where('status', '2') //Approved staff only
      ->order_by('staff_name')
      ->get()->result();
  }

  public function getObservedData($staffId = 0)
  {
    $CURRENT_DATE = date('Y-m-d');
    $this->db_readonly->select("so.id,so.staff_id,so.taken_by,so.created_on,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS takenBy,so.observation,so.file_input");
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('staff_observation so', 'so.staff_id=sm.id','right');
    $this->db_readonly->join('staff_master sm1', 'so.taken_by=sm1.id','left');
    $this->db_readonly->where('date_format(so.created_on,"%Y-%m-%d") >= date_format(DATE_ADD("' . $CURRENT_DATE . '", INTERVAL -7 DAY),"%Y-%m-%d")');
    $this->db_readonly->where('so.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('so.id','desc');
    if ($staffId) {
      $this->db_readonly->where('so.taken_by', $staffId);
    }
    // echo '<pre>';print_r($this->db_readonly->get()->result());die();
    $result= $this->db_readonly->get()->result();
      foreach ($result as $a => $b) {
        if(!empty($b->file_input)){
        $b->file_input= $this->filemanager->getFilePath($b->file_input);
        }
    }
    return $result;
  }

  public function getStaffObservation($staffId = 0)
  {
    $this->db_readonly->select("so.id,so.staff_id,so.taken_by,so.created_on,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS takenBy,so.observation");
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('staff_observation so', 'so.staff_id=sm.id');
    $this->db_readonly->join('staff_master sm1', 'so.taken_by=sm1.id');
    $this->db_readonly->where('so.acad_year_id', $this->yearId);
    $this->db_readonly->order_by('sm.first_name');
    if ($staffId) {
      $this->db_readonly->where('so.staff_id', $staffId);
    }
    // echo '<pre>';print_r($this->db_readonly->get()->result());die();
    return $this->db_readonly->get()->result();
  }

  public function submitObservation($staffId,$file_input)
  {
    $staff_id=$this->input->post('user_selection');
    if( ! $staff_id){
      $staff_id="";
    }
    $data = array(
      'staff_id' => $staff_id,
      'taken_by' => $staffId,
      'observation' => $this->input->post('observation'),
      'last_modified_by' => $this->authorization->getAvatarId(),
      'acad_year_id' => $this->yearId,
      'file_input' => $file_input['file_name']
    );
    return $this->db->insert('staff_observation', $data);
  }

  public function deleteObservation($id)
  {
    $this->db->where('id', $id);
    return $this->db->delete('staff_observation');
  }

  public function getObservationData($id)
  {
    $this->db_readonly->select("so.id,so.staff_id,so.created_on,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS takenBy,so.observation");
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('staff_observation so', 'so.staff_id=sm.id');
    $this->db_readonly->join('staff_master sm1', 'so.taken_by=sm1.id');
    $this->db_readonly->order_by('sm.first_name');
    $this->db_readonly->where('so.id', $id);
    return $this->db_readonly->get()->row();
  }

  public function updateObservation($id)
  {
    $data = array(
      'observation' => $this->input->post('observation'),
      'last_modified_by' => $this->authorization->getAvatarId()
    );
    $this->db->where('id', $id);
    return $this->db->update('staff_observation', $data);
  }

  public function getStaff($staffId)
  {
    return $this->db_readonly->select('id,CONCAT(first_name,' . 'last_name) as staffName')
      ->from('staff_master')
      ->where('id', $staffId)
      ->get()->row();
  }

  //Create Staff Logs
  public function create_log()
  {
    $avatar_id = $this->authorization->getAvatarId();
    $id = $this->db->select("a.stakeholder_id")
      ->from("avatar a")
      ->where("a.id", $avatar_id)
      ->get()->row();



    $data = array(
      'staff_id' => $id->stakeholder_id,
      'log' => $this->input->post('log'),
      'task' => $this->input->post('task')
    );
    $this->db->insert('staff_logs', $data);
    $id = $this->db->insert_id();
    $teacher = array();
    $team = $this->input->post('team');
    foreach ($team as $key => $v) {
      $data1[] = array(
        'staff_logs_id' => $id,
        'staff_id' => $team[$key]
      );
    }
    return $this->db->insert_batch('staff_logs_team', $data1);
  }

  public function getLogsDistinctColumn()
  {
    $this->db_readonly->distinct();
    $result = $this->db->select("task as name")
      ->from('staff_logs')
      ->order_by('task')
      ->where('task' . ' !=', "")
      ->get()->result();
    return $result;
  }

  public function getStaffLogs()
  {
    $this->db_readonly->select("sl.id,sl.staff_id,sl.date,sl.log,sl.task,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName");
    $this->db_readonly->from("staff_logs sl");
    $this->db_readonly->join("staff_master sm", "sl.staff_id=sm.id", 'left');
    $this->db_readonly->order_by("sl.date", 'desc');
    return $this->db_readonly->get()->result();
  }

  public function getStaffNames($id)
  {
    $staff = $this->db_readonly->select("slt.staff_id,sm.id,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName")
      ->from('staff_logs_team slt')
      ->join('staff_master sm', 'slt.staff_id=sm.id')
      ->where('slt.staff_logs_id', $id)
      ->get()->result();
    return $staff;
    //echo "<pre>"; print_r($staff); die();
  }

  public function filterStaffLogs()
  {
    $from_date = $this->input->post('from_date');
    if (empty($from_date)) {
      $from_date = "2000-01-01";
    }
    $to_date = $this->input->post('to_date');
    $this->db_readonly->select("sl.id,sl.staff_id,sl.date,sl.log,sl.task,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staffName");
    $this->db_readonly->from("staff_logs sl");
    $this->db_readonly->join("staff_master sm", "sl.staff_id=sm.id", 'left');
    $this->db_readonly->where('DATE_FORMAT(sl.date,"%y-%m-%d") >= DATE_FORMAT("' . $from_date . '","%y-%m-%d")');
    $this->db_readonly->where('DATE_FORMAT(sl.date,"%y-%m-%d") <= DATE_FORMAT("' . $to_date . '","%y-%m-%d")');
    $this->db_readonly->order_by("sl.date", 'desc');
    return  $this->db_readonly->get()->result();
    //echo "<pre>"; print_r($a); die();

  }

  public function filterObservedData($staffId = 0)
  {
    $from_date = $this->input->get('from_date');
    $to_date = $this->input->get('to_date');
    if (empty($from_date)) {
      $from_date = "2000-01-01";
    }
    $this->db_readonly->select("so.id,so.staff_id,so.taken_by,so.created_on,CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) AS staffName,CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS takenBy,so.observation");
    $this->db_readonly->from('staff_master sm');
    $this->db_readonly->join('staff_observation so', 'so.staff_id=sm.id');
    $this->db_readonly->where("DATE_FORMAT(so.created_on,'%Y-%m-%d') >= DATE_FORMAT('" . $from_date . "','%Y-%m-%d')");
    $this->db_readonly->where("DATE_FORMAT(so.created_on,'%Y-%m-%d') <= DATE_FORMAT('" . $to_date . "','%Y-%m-%d')");
    $this->db_readonly->where('so.acad_year_id', $this->yearId);
    $this->db_readonly->join('staff_master sm1', 'so.taken_by=sm1.id');
    $this->db_readonly->order_by('sm.first_name');
    if ($staffId) {
      $this->db_readonly->where('so.taken_by', $staffId);
    }
    // echo '<pre>';print_r($this->db_readonly->get()->result());die();
    return $this->db_readonly->get()->result();
  }
  public function getStaffName($staff_id)
  {
    $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS staffName");
    $this->db_readonly->where('id', $staff_id);
    return $this->db_readonly->get('staff_master')->row_array()['staffName'];
  }

  public function getStaffSalary($staff_id)
  {
    $this->db_readonly->select("salary");
    $this->db_readonly->where('id', $staff_id);
    return $this->db_readonly->get('staff_master')->row_array()['salary'];
  }

  public function getStaffData() {
    $this->db_readonly->select("s.id as staffId, CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) AS staffName, s.contact_number, u.username, u.id as userId, if(u.active=1, 'Activated', 'Not Activated') as status,u.loggedin_atleast_once as logged_in, u.email, u.loggedin_atleast_once, u.active");
    $this->db_readonly->from('staff_master s');
    $this->db_readonly->join('avatar a', 'a.stakeholder_id=s.id');
    $this->db_readonly->join('users u', 'a.user_id=u.id');
    $this->db_readonly->where('a.avatar_type', '4');
    $this->db_readonly->where('s.status', 2);
    $this->db_readonly->order_by('s.first_name');
    return $this->db_readonly->get()->result();
  }

  public function getActivationPreview($staffIds) {
    return $this->db_readonly->select("u.username,s.contact_number,s.id as staffId, CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) AS staffName, u.id as userId, s.id as staffId, u.email, a.avatar_type")
      ->from('staff_master s')
      ->join('avatar a ', 'a.stakeholder_id=s.id')
      ->join('users u', 'a.user_id=u.id')
      ->where('a.avatar_type', '4')
      ->where('s.status', '2')
      ->where_in('s.id', $staffIds)
      ->order_by('s.first_name')
      ->get()->result();
  }

  public function activateStaffAcount($userIds)
  {
    return $this->db->where_in('id', $userIds)->update('users', array('active' => 1));
  }

  public function deactivateStaff($userId)
  {
    return $this->db->where('id', $userId)->update('users', array('active' => 0, 'loggedin_atleast_once' => 0));
  }

  public function getStaffUserTokens($staff_ids)
  {
    $users = $this->db_readonly->select('u.id as user_id, u.token')
      ->from('users u')
      ->join('avatar a', 'a.user_id=u.id')
      ->join('staff_master sm', 'sm.id=a.stakeholder_id')
      ->where_in('sm.id', $staff_ids)
      ->where('a.avatar_type', 4)
      ->where('u.token is not null')
      ->get()->result_array();
    return $users;
  }

  public function getStaffIds($staff_ids)
  {
    $users = $this->db_readonly->select('sm.id, if(u.token is null, 0, 1) as tokenState')
      ->from('users u')
      ->join('avatar a', 'a.user_id=u.id')
      ->join('staff_master sm', 'sm.id=a.stakeholder_id')
      ->where_in('sm.id', $staff_ids)
      ->where('a.avatar_type', 4)
      ->get()->result();
    return $users;
  }

  public function reset_session_by_staff($staffId)
  {

    $staffSession = '%\"staffId\";s:' . strlen($staffId) . ':\"' . $staffId . '\"%';
    $sql = "DELETE FROM ci_sessions WHERE data LIKE '" . $staffSession . "' ";
    return $this->db->query($sql);
  }

  public function getStaffReportingData($staff_type)
  {
    $sql = "select sm1.id as staff_id, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS staff_name, sm2.id as reporting_staff_id, CONCAT(ifnull(sm2.first_name,''),' ', ifnull(sm2.last_name,'')) AS reporting_to, sm1.is_reporting_manager, sm1.designation 
            from staff_master sm1 
            left join staff_master sm2 on sm2.id=sm1.reporting_manager_id 
            WHERE sm1.status = 2";
            
            if ($staff_type != -1) {
                $sql .= " AND sm1.staff_type = " . intval($staff_type);
            }
            
            $sql .= " ORDER BY sm1.first_name";
    return $this->db_readonly->query($sql)->result();
  }

  public function getReportingManagers()
  {
    $sql = "select sm1.id as staff_id, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS staff_name 
            from staff_master sm1 
            where sm1.status=2 
            and sm1.is_reporting_manager=1 
            order by sm1.first_name";
    return $this->db_readonly->query($sql)->result();
  }

  public function changeStaffManagementRole($staff_id, $status)
  {
    $this->db->trans_start();
    $this->db->where('id', $staff_id)->update('staff_master', ['is_reporting_manager' => $status]);
    if ($status == 0) {
      $this->db->where('reporting_manager_id', $staff_id)->update('staff_master', ['reporting_manager_id' => NULL]);
    }
    return $this->db->trans_complete();
  }

  public function assignReportingManager($staff_id, $reporting_staff_id)
  {
    $res = $this->db->select('id')->where('reporting_manager_id', $staff_id)->where('id', $reporting_staff_id)->get('staff_master')->row();
    if (!empty($res)) {
      return -1;
    }
    return $this->db->where('id', $staff_id)->update('staff_master', ['reporting_manager_id' => $reporting_staff_id]);
  }

  public function getStaffCodeData()
  {
    $sql = "select sm1.id as staff_id, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS staff_name, ac.id, ac.staff_code 
            from staff_master sm1 
            left join staff_attendance_code ac on ac.staff_id=sm1.id 
            where sm1.status=2 
            order by sm1.first_name";
    return $this->db_readonly->query($sql)->result();
  }

  public function getSingleStaffCodeData($staff_id)
  {
    $sql = "SELECT sm1.id as staff_id, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS staff_name, ac.id, ac.staff_code 
            from staff_master sm1 
            left join staff_attendance_code ac on ac.staff_id=sm1.id 
            where sm1.status=2 and ac.staff_id=$staff_id
            order by sm1.first_name";
    return $this->db_readonly->query($sql)->row();
  }

  public function assignNewStaffCode($staff_id, $staff_code)
  {
    $res = $this->db->select('id')->where('staff_id', $staff_id)->get('staff_attendance_code')->row();
    if (empty($res)) {
      return $this->db->insert('staff_attendance_code', ['staff_id' => $staff_id, 'staff_code' => $staff_code]);
    }
    return $this->db->where('staff_id', $staff_id)->update('staff_attendance_code', ['staff_code' => $staff_code]);
  }

  public function addManagedDocuments($input, $staff_id, $path)
  {
    $data = array(
      'document_type' => $input['document_for'],
      'document_other'  => $input['document_name'],
      "staff_id" => $staff_id,
      'document_url'  => ($path['file_name'] == '') ? null : $path['file_name'],
      "created_by" => $this->authorization->getAvatarId()
    );
    return $this->db->insert('staff_documents', $data);
  }
  public function getStaffStatus($staff_id){
    $result = $this->db_readonly->select('status, date_format(last_date_of_work,"%d-%m-%Y") as last_date_of_work,date_format(resignation_date,"%d-%m-%Y") as resignation_date , exit_remarks, resignation_letter_doc, date_format(exit_update_on,"%d-%m-%Y") as exit_update_on,exit_updated_by')->from('staff_master')->where('id', $staff_id)->get()->row();
    if(!empty($result)){
      if($result->exit_updated_by =='0'){
        $result->exit_updated_by = 'Admin';
      }else{
        $result->exit_updated_by = $this->get_staff_name_by_id($result->exit_updated_by);
      }
      return $result;
    }else{
      return 0;
    }    
  }
  public function get_exited_document_row($staff_id)
  {
    return $this->db->select('resignation_letter_doc')->where('id', $staff_id)->get('staff_master')->row();
  }


  public function terminate_staff($input, $staff_id, $path, $isExitDateNotFuture, $old_val, $new_val) {
    // Begin DB Transaction
    $staff_termination_enable_future_date_deactivation = $this->settings->getSetting('staff_termination_enable_future_date_deactivation');
    $this->db->trans_start();

    $terminationResponse = $this->store_staff_termination_details($input, $staff_id, $path, $isExitDateNotFuture);
    $isEditHistoryStored = $this->store_staff_edit_history($staff_id, $old_val, $new_val);
    if (!$terminationResponse || !$isEditHistoryStored) {
        $this->db->trans_rollback();
        return 0;
    }

    if ($staff_termination_enable_future_date_deactivation==0 || $isExitDateNotFuture) {
      $isStaffDeactivated = $this->deactivate_staff($staff_id);

      if (!$isStaffDeactivated) {
          $this->db->trans_rollback();
          return 0;
      }

      $this->delete_manage_group_assinged_staff($staff_id);

    }

    // Complete transaction
    $this->db->trans_complete();

    if ($this->db->trans_status() === FALSE) {
        return 0;
    } else {
        return 1;
    }
}

  public function store_staff_termination_details($input, $staff_id, $path,$isExitDateNotFuture){
    $staff_termination_enable_future_date_deactivation = $this->settings->getSetting('staff_termination_enable_future_date_deactivation');

    $data = array(
      'last_date_of_work' => date('Y-m-d',strtotime($input['staff_exit_date'])),
      'resignation_date' => date('Y-m-d',strtotime($input['resignation_date'])),
      'exit_update_on' => $this->Kolkata_datetime(),
      "exit_remarks" => $input['exit_remarks'],
      'resignation_letter_doc'  => ($path == '') ? null : $path,
      "exit_updated_by" => $this->authorization->getAvatarStakeHolderId()
      // 'status'=> 4
    );

    if($staff_termination_enable_future_date_deactivation==0 || $isExitDateNotFuture){
      $data['status'] = 4;
    }
    $this->db->where('id', $staff_id);
    return $this->db->update('staff_master', $data);
  }

  public function deactivate_staff($staff_id) {
    $user_names= array();
    $user_id= $this->db->select("user_id")
      ->where('stakeholder_id', $staff_id)
      ->where('avatar_type', 4)
      ->get('avatar');
    
    if( $user_id->num_rows() > 0 ) {
      $user_names= $this->db->select('username')
        ->where('id', $user_id->row()->user_id)
        ->get('users')->row();
    } else {
      return 0;
    }

    if( !empty($user_names) ) {
      $sql = "SELECT id FROM ci_sessions cs WHERE data LIKE '%$user_names->username%' ";
      $query = $this->db->query($sql);
    }

    $sessionIds = [];
    foreach ($query->result() as $key => $val) {
        array_push($sessionIds, $val->id);
    }

    $this->db->trans_start();

    if (!empty($sessionIds)) {
        $this->db->where_in('id',$sessionIds);
        $this->db->delete('ci_sessions');
    }
  
    if (!empty($user_id->row())) {
      $this->db->where('id',$user_id->row()->user_id);
      $this->db->update('users',array('token'=>'', 'active'=>0));
    }
    $this->db->trans_complete();

    if ($this->db->trans_status()) {
      return true;
    }
    return 0;

  }


  public function get_docs_list($staff_id)
  {
    // $result=$this->db_readonly->where('staff_id', $staff_id)->order_by('document_type')->get('staff_documents')->result();
    $result = $this->db->select("date_format(sd.created_on,'%d-%m-%Y') as created_on, (case when sd.document_type = 'Others' then document_other else document_type end) as document_name, sd.document_url, sd.id as doc_id, sd.staff_id, sd.approved_status, sd.created_by as created_by, sd.approved_rejected_comments as approved_rejected_comments")
                ->from('staff_documents sd')
                ->join('staff_document_types sdt', 'sdt.document_name= sd.document_type', 'left')
                ->where('sd.staff_id', $staff_id)
                ->order_by('document_name')
                ->get()->result();
    foreach ($result as $key => $val) {
      $val->create_name = $this->get_staff_name_from_avatar_id($val->created_by);
      $val->document_path = '';
      if(!empty($val->document_url)){
        $val->document_path = $this->filemanager->getFilePath($val->document_url);
      }
    }
    return $result;
  }

  
  public function get_staff_name_from_avatar_id($avatar_id) {
    // echo $avatar_id; die();
    $staff_obj = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff        
        ->where('a.id',$avatar_id)
        ->get()->row();
    if (!empty($staff_obj)) {
      return $staff_obj->staff_name;
    }else{
      return 'Admin';
    }
  }

  public function get_staff_name_by_id($staff_id){
     // echo $avatar_id; die();
     $staff_obj = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
     ->from('staff_master sm')
     ->where('sm.id',$staff_id)
     ->get()->row();
 if (!empty($staff_obj)) {
   return $staff_obj->staff_name;
 }else{
   return 'Admin';
 }
  }

  public function delete_documentsbyid($doc_id){
    $this->db->where("id",$doc_id);
    return $this->db->delete("staff_documents");
  }

  public function download_staff_document($id){
    return $this->db->select('document_url')->where('id', $id)->get('staff_documents')->row()->document_url;
  }

  public function get_document_row($document_id){
    return $this->db->select('document_url, document_type')->where('id', $document_id)->get('staff_documents')->row();
  }

  public function get_workshop_certificate_row($cer_id){
    return $this->db->select('certificate_path')->where('id', $cer_id)->get('staff_training_workshop')->row();
  }

  public function get_staff_provision_Preview($staffId){
    return $this->db_readonly->select("u.username,s.contact_number,s.id as staffId, CONCAT(ifnull(s.first_name,''),' ', ifnull(s.last_name,'')) AS staffName, u.id as userId, s.id as staffId, u.email")
      ->from('staff_master s')
      ->join('avatar a ', 'a.stakeholder_id=s.id')
      ->join('users u', 'a.user_id=u.id')
      ->where('a.avatar_type', '4')
      ->where('s.status', '2')
      ->where('s.id', $staffId)
      ->get()->row();
  }

  public function deactivate_provision_credentials_by_user_id($userId){
    $this->db->where_in('id', $userId);
    return $this->db->update('users', array('active' => 0, 'loggedin_atleast_once' => 0,'token'=>null));
  }

  public function add_qualifications($staff_id,$data,$path){
    // echo "<pre>"; echo print_r($data); die();
    $dataArray=array(
      "staff_id"=>$staff_id,
      "duration_of_complete" => $data['duration'],
      "combination_branch" => $data['branch'],
      "completed_month_year"=>$data['compition_date'],
      "supporting_document"=>($path['file_name'] == '') ? null : $path['file_name'],
      "qualification"=>$data['qualification'],
      "remarks"=>$data['remarks'],
      "degree_type"=>$data['degree_type'],
      "University_Institute"=>$data['university'],
      "Specialization"=>$data['specialization'],
      "created_by"=>$this->authorization->getAvatarId(),
      "status"=>1,
      "disabled_by"=>0
    );
    return $this->db->insert("staff_qualification_v2",$dataArray);
  }

  public function get_qualifications($staff_id){
    // echo "<pre>"; echo print_r($_POST); die();
    $result=$this->db_readonly->where("staff_id",$staff_id)->get("staff_qualification_v2")->result();
    foreach ($result as $key => $row) {
      $row->created_by_name = $this->get_staff_name_from_avatar_id($row->created_by);
      if($row->disabled_by>0){
        $row->disabled_by_name = $this->get_staff_name_from_avatar_id($row->disabled_by);
        }
    }

    return $result;
  }

  public function edit_qualification($staff_id,$data,$path){
    // echo "<pre>"; echo print_r($data); die();

    $qualificationId=$data["qualification_id"];

    $updateArray=array(
      "staff_id"=>$staff_id,
      "duration_of_complete" => $data['update_duration'],
      "completed_month_year"=>$data['update_compition_date'],
      "qualification"=>$data['update_qualification'],
      "combination_branch"=>$data['update_branch'],
      "remarks"=>$data['update_remarks'],
      "degree_type"=>$data['degree_type'],
      "University_Institute"=>$data['update_university'],
      "Specialization"=>$data['update_specialization'],
      "created_by"=>$this->authorization->getAvatarId(),
    );

    if($path['file_name'] !=''){
    $updateArray=array_merge($updateArray,["supporting_document"=>$path['file_name']]);
    }

    $this->db->where("id",$qualificationId);
    return $this->db->update('staff_qualification_v2',$updateArray);
  }

  public function get_particular_qualification($data){
    $q_id=$data["qualification_id"];
    $result= $this->db_readonly->where("id",$q_id)->get("staff_qualification_v2")->row();
    $result->created_by_name = $this->get_staff_name_from_avatar_id($result->created_by);
    if($result->status==0){
      $result->disabled_by_name = $this->get_staff_name_from_avatar_id($result->disabled_by);
    }
    if(!empty($result->supporting_document)){
      $result->supporting_document = $this->filemanager->getFilePath($result->supporting_document);
    }
    
    return $result;
  }

  public function get_document_detailsbyid($data){
    $q_id=$data["attribute_id"];
    $result= $this->db_readonly->where("id",$q_id)->get("staff_documents")->row();
    $result->created_by_name = $this->get_staff_name_from_avatar_id($result->created_by);
    if(!empty($result->document_url)){
       $result->document_url = $this->filemanager->getFilePath($result->document_url);
    }
    return $result;
    
  }

  public function disable_particular_qualification($staff_id,$post_data){
    // echo "<pre>"; print_r($post_data); die();
    $data = array(
      'status'=>$post_data['status'],
      'disabled_by'=>$this->authorization->getAvatarId()
    );
    $this->db->where("id",$post_data['qualification_id']);
    return $this->db->update("staff_qualification_v2", $data);
  }

  public function add_awards($staff_id,$data){
    // echo "<pre>"; echo print_r($data); die();
    $dataArray=array(
      "staff_id"=>$staff_id,
      "created_by"=>$this->authorization->getAvatarId(),
      "status"=>1,
      "disabled_by"=>0,
      "award_name"=>$data["award_name"],
      "awarded_by"=>$data["awarded_by"],
      "awarded_on"=>$data["awarded_on"],
      "award_cash_value"=>$data["award_cash_value"],
      "remarks"=>$data["remarks"],
    );
    
    return $this->db->insert("staff_awards",$dataArray);
  }

  public function get_awards($staff_id){
    $result=$this->db_readonly->where("staff_id",$staff_id)->get("staff_awards")->result();

    foreach($result as $row){
      $row->created_by_name = $this->get_staff_name_from_avatar_id($row->created_by);
      if($row->disabled_by>0){
        $row->disabled_by_name = $this->get_staff_name_from_avatar_id($row->disabled_by);
        }
      }
      return $result;
      // echo "<pre>"; echo print_r($result); die();
  }

  public function get_particular_award($data){
    // echo "<pre>"; echo print_r($data); die();
    $award_id=$data["award_id"];
    $result=$this->db->select("*")
    ->from("staff_awards")
    ->where("id",$award_id)
    ->get()->row();

    $result->created_by_name = $this->get_staff_name_from_avatar_id($result->created_by);
    if($result->status==0){
      $result->disabled_by_name = $this->get_staff_name_from_avatar_id($result->disabled_by);
    }

    return $result;
  }

  public function disable_particular_award($staff_id,$post_data){
    // echo "<pre>"; echo print_r($post_data); die();
    $data = array(
      'status'=>$post_data['status'],
      'disabled_by'=>$this->authorization->getAvatarId()
    );

    $this->db->where("id",$post_data['award_id']);
    return $this->db->update("staff_awards", $data);
  }

  public function edit_award($data){
    // echo "<pre>"; echo print_r($data); die();
    $this->db->set("staff_id",$data["staff_id"]);
    $this->db->set("award_name",$data["award_name"]);
    $this->db->set("awarded_by",$data["awarded_by"]);
    $this->db->set("awarded_on",date("Y-m-d", strtotime($data["awarded_on"])));
    $this->db->set("award_cash_value",$data["award_cash_value"]);
    $this->db->set("remarks",$data["remarks"]);
    $this->db->where("id",$data["awards_id"]);
    return $this->db->update("staff_awards");
  }

  public function add_experience($staff_id,$data,$path){
    // echo "<pre>"; echo print_r($data); die();
    $dataArray=array(
      "staff_id"=>$staff_id,
      "created_by"=>$this->authorization->getAvatarId(),
      "experience_type"=>$data["experience_type"],
      "worked_for"=>$data["worked_for"],
      "duration"=>$data["duration"],
      "location"=>$data["location"],
      "sub_taught"=>$data["sub_taught"],
      "grades_handle"=>$data["grades_handle"],
      "supporting_document"=>($path['file_name'] == '') ? null : $path['file_name'],
      "remarks"=>$data["remarks"],
      "created_by"=>$this->authorization->getAvatarId(),
      "status"=>1,
      "disabled_by"=>0
    );
    return $this->db->insert("staff_experience",$dataArray);

  }


  public function get_experiences($staff_id){
    $result=$this->db_readonly->where("staff_id",$staff_id)->get("staff_experience")->result();
    foreach($result as $row){
      $row->created_by_name = $this->get_staff_name_from_avatar_id($row->created_by);
      if($row->disabled_by>0){
        $row->disabled_by_name = $this->get_staff_name_from_avatar_id($row->disabled_by);
        }
      }

    return $result;
  }

  public function get_particular_experience($staff_id,$data){
    // echo "<pre>"; echo print_r($data); die();
    $exp_id=$data["experience_id"];
    $result=$this->db_readonly->select("*")
    ->from("staff_experience")
    ->where("id",$exp_id)
    ->get()->row();

    $result->created_by_name = $this->get_staff_name_from_avatar_id($result->created_by);
    if($result->status==0){
      $result->disabled_by_name = $this->get_staff_name_from_avatar_id($result->disabled_by);
    }

    if(!empty($result->supporting_document)){
      $result->supporting_document = $this->filemanager->getFilePath($result->supporting_document);
    }

    return $result;
  }

  public function disable_particular_experience($staff_id,$post_data){

    $data = array(
      'status'=>$post_data['status'],
      'disabled_by'=>$this->authorization->getAvatarId()
    );

    $this->db->where("id",$post_data['experience_id']);
    return $this->db->update("staff_experience", $data);
  }

  public function edit_experience($data,$path){
    // echo "<pre>"; print_r($path); die();
    $experdata = array(
      'experience_type'=>$data["experience_type"],
      'worked_for'=>$data["worked_for"],
      'duration'=>$data["duration"],
      'location'=>$data["location"],
      'sub_taught'=>$data["sub_taught"],
      'grades_handle'=>$data["grades_handle"],
      'remarks'=>$data["remarks"],
      'experience_type'=>$data["experience_type"],
      'experience_type'=>$data["experience_type"],
    );
    if($path['file_name'] !=''){
      $experdata= array_merge($experdata, ['supporting_document'=>$path['file_name']]);
    }
    $this->db->where("id",$data["experience_id"]);
    return $this->db->update("staff_experience",$experdata);
  }

  public function get_qualification_document_row($doc_id){
    // echo "<pre>"; echo print_r($result); die();
    return $this->db_readonly->select("supporting_document")->where("id",$doc_id)->get("staff_qualification_v2")->row();
  }

  public function get_experience_document_row($doc_id){
    // echo "<pre>"; echo print_r($result); die();
    return $this->db_readonly->select("supporting_document")->where("id",$doc_id)->get("staff_experience")->row();
  }
  
    


 public function stafftraning_workshop_insert_data($staffId, $path){
  $input = $this->input->post();
  $data = array(
    'staff_id' => $staffId,
    'training_name'  => $input['trainer_name'],
    'duration'  => $input['duration'],
    'institute_name'  => $input['institute_name'],
    'traning_related_to'  => $input['traning_related_to'],
    'certificate_path'  => ($path['file_name'] == '') ? '' : $path['file_name'],
    'remarks'  => $input['remarks'],
    'added_by' => $this->authorization->getAvatarStakeHolderId(),
    'status' => '1'
  );
  return $this->db->insert('staff_training_workshop', $data); 
}


public function get_staff_workshop_training($staffId){
  return $this->db->select('stw.*, date_format(stw.added_on,"%d-%m-%Y") as added_on, sm.first_name as added_by')
  ->from('staff_training_workshop stw')
  ->join('staff_master sm','sm.id=stw.added_by','left')
  ->where('staff_id',$staffId)
  ->get()->result();
}

public function get_staff_publication_citations($staffId){
   return $this->db->select('stw.*, sm.first_name as publication_added_by, date_format(stw.publication_added_on,"%d-%m-%Y") as publication_added_on, ifnull(sm.first_name,"") as publication_added_by')
 
  ->from('staff_publications_citations stw')
  ->join('staff_master sm','sm.id=stw.publication_added_by','left')
  ->where('stw.staff_id',$staffId)
  ->get()->result();
}

public function stafftraning_view_dataId($transid){
  $result = $this->db->select('stw.*,sm.first_name as added_by, date_format(stw.added_on,"%d-%m-%Y") as added_on')
    ->from('staff_training_workshop stw')
    ->join('staff_master sm','sm.id=stw.added_by','left')
    ->where('stw.id',$transid)
    ->get()->row();
   $result->certificate_img = '';
  if ($result->certificate_path != '') {
    $result->certificate_img = $this->filemanager->getFilePath($result->certificate_path);
  }
  return $result;
}
public function download_staff_traning_document($id){
    return $this->db->select('document_url')->where('id', $id)->get('staff_training_workshop')->row()->document_url;
  }

 public function staffpublications_citations_insert_data($staffId){
  $input = $this->input->post();

  $data = array(
    'staff_id' => $staffId,
    'publication_type'  => $input['publication_type'],
    'publication_type_other'  => $input['publication_type_other'],
    'publication_name'  => $input['publication_name'],
    'publication_url'  => $input['publication_url'],
    'publication_on'  => $input['publication_on'],
    'publication_remarks'  => $input['publication_remarks'],
    'publication_added_by'=> $this->authorization->getAvatarStakeHolderId(),
    'status' => '1'
  );
  return $this->db->insert('staff_publications_citations', $data); 
}
public function staffpublication_view_dataId($transid){
  $result = $this->db->select("spc.*, (case when spc.publication_type = 'Others' then spc.publication_type_other else spc.publication_type end) as publicationType, sm.first_name as published_by, date_format(spc.publication_added_on, '%d-%m-%Y') as publication_added_on, IF(STR_TO_DATE(spc.publication_on, '%Y-%m-%d') ='00-00-0000', '-', date_format(spc.publication_on, '%d-%m-%Y')) as publication_on")
    ->from('staff_publications_citations spc')
    ->join('staff_master sm', 'sm.id = spc.publication_added_by', 'left')
    ->where('spc.id', $transid)
    ->get()
    ->row();
  
  return $result;
}

public function staffinterest_insert_data($staffId){
  $input = $this->input->post();
  $data = array(
    'staff_id' => $staffId,
    'area_of_interest'  => $input['interests_type'],
    'specify_interest'  => $input['specify_interest'],
    'achievements'  => $input['achievements'],
    'created_by'=> $this->authorization->getAvatarStakeHolderId(),
    'status' => '1'
  );
  return $this->db->insert('staff_interest', $data); 
}

public function staffinterest_view_dataId($transid){
  $result = $this->db->select("spc.*, sm.first_name as created_by,date_format(spc.created_on,'%d-%m-%Y') as created_on")
  ->from('staff_interest spc')
  ->join('staff_master sm','sm.id=spc.created_by','left')
  ->where('spc.id',$transid)
  ->get()->row();
  
  return $result;
}

public function get_staff_interest($staffId){
  return $this->db->select('stw.*, date_format(stw.created_on,"%d-%m-%Y") as created_on, ifnull(sm.first_name,"") as created_by')
  ->from('staff_interest stw')
  ->join('staff_master sm','sm.id=stw.created_by','left')
  ->where('stw.staff_id',$staffId)
  ->get()->result();
}

public function disable_staffinterest($staff_interest_id, $status){
    $data = array(
      'status'  => $status,
    );
    $this->db->where('id',$staff_interest_id);
    return $this->db->update('staff_interest', $data); 
  }

   // Staff Initiative models
  public function get_all_staff_initiative($staff_id)
  {
      return $this->db_readonly->where("staff_id",$staff_id)->get("staff_initiative_master")->result();
  }

  public function get_particular_staff_initiative($staff_id,$data)
  {
      // echo "<pre>"; echo print_r($_POST); die();
      $initiative_id = $data["id"];
      $result=$this->db_readonly->where("id", $initiative_id)->get("staff_initiative_master")->row();
      if($result->created_by>0){
        $result->created_by_name = $this->get_staff_name_from_avatar_id($result->created_by);
      }
      return $result;
    }
  
    public function insert_staff_initiative_details($staff_id,$data,$path)
    {
      $data_array = array(
        'staff_id'=>$staff_id,
        'initiative_name' => $this->input->post('initiative_name'),
        'who_attend'     => $this->input->post('who_attend'),
        'from_date'          => date('Y-m-d', strtotime($this->input->post('from_date'))),
        'to_date'          => date('Y-m-d', strtotime($this->input->post('to_date'))),
        'created_by'    => $this->authorization->getAvatarId(),
        'status'        => 1, // created
        'initiative_details'=>$this->input->post('initiative_details'),
      );
      if ($path['file_name'] != '') {
        $data_array = array_merge($data_array, ["document" => $path['file_name']]);
      }
          
      if($data['sub_type'] =='edit'){
        return $this->db->where("id", $this->input->post("initiative_id"))->update("staff_initiative_master", $data_array);
      }else{
        return $this->db->insert('staff_initiative_master', $data_array);
      }
    }

    public function disable_particular_staff_initiative($staff_id,$data){
      $dataArray=array(
          "status"=>$data["status"],
      );
      return $this->db->where("id", $data["initiative_id"])->update("staff_initiative_master",$dataArray);
  }

  public function get_initiative_document_row($doc_id){
    return $this->db_readonly->where("id",$doc_id)->get("staff_initiative_master")->row();
  }

  public function disable_staffpublications($staff_pub_id, $status){
    $data = array(
      'status'  => $status,
    );
    $this->db->where('id',$staff_pub_id);
    return $this->db->update('staff_publications_citations', $data); 
  }

  public function disable_stafftraning_workshop($staff_training_id, $status) {
    $data = array(
      'status'  => $status,
    );
    $this->db->where('id',$staff_training_id);
    return $this->db->update('staff_training_workshop', $data); 
  }

  public function staffinterest_update_data() {
    $input = $this->input->post();
    $data = array(
      'specify_interest'  => $input['specify_interest_update'],
      'achievements'  => $input['achievements_update'],
      'created_by'=> $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->where('id',$input['interests_id']);
    return $this->db->update('staff_interest', $data); 
  }

  public function staffpublications_citations_update_data(){
    $input = $this->input->post();
    $data = array(
    'publication_type'  => $input['publication_type'],
      'publication_type_other'  => $input['publication_type_other'],
      'publication_name'  => $input['publication_name'],
      'publication_url'  => $input['publication_url'],
      'publication_on'  => $input['publication_on'],
      'publication_remarks'  => $input['publication_remarks'],
      'publication_added_by'=> $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->where('id',$input['interests_id1']);
    return $this->db->update('staff_publications_citations', $data); 
  }

  public function stafftraning_workshop_update_data(){
    $input = $this->input->post();
    $data = array(
      'training_name'  => $input['trainer_name'],
      'duration'  => $input['duration'],
      'institute_name'  => $input['institute_name'],
      'traning_related_to'  => $input['traning_related_to'],
      'remarks'  => $input['remarks'],
      'added_by'=> $this->authorization->getAvatarStakeHolderId()
    );
    $this->db->where('id',$input['interests_id2']);
    return $this->db->update('staff_training_workshop', $data); 
  }

  public function get_edit_staff_attribute_listbypost($from_date, $to_date, $staff_approved_status, $staff_id){
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));


    $this->db_readonly->select("'initiative' as attribute, initiative_name as attribute_name, sim.id as attribute_id, date_format(created_at,'%d-%m-%Y') as uploaded_on, staff_id, approved_status" )
    ->from('staff_initiative_master sim')
    ->join('staff_master sm', 'sm.id = sim.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(created_at,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $initiative = $this->db_readonly->get()->result_array();




    $this->db_readonly->select("'documents' as attribute, (case when document_type ='Others' then document_other else document_type end) as attribute_name, sd.id as attribute_id, date_format(sd.created_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status" )
    ->from('staff_documents sd')
    ->join('staff_master sm', 'sm.id = sd.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(sd.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $documents = $this->db_readonly->get()->result_array();


    $this->db_readonly->select("'experience' as attribute, experience_type  as attribute_name, se.id as attribute_id, date_format(se.created_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status")
    ->from('staff_experience se')
    ->join('staff_master sm', 'sm.id = se.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(se.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $experience = $this->db_readonly->get()->result_array();

    $this->db_readonly->select("'interest' as attribute, area_of_interest  as attribute_name, si.id as attribute_id, date_format(si.created_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status")
    ->from('staff_interest si')
    ->join('staff_master sm', 'sm.id = si.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(si.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $interest = $this->db_readonly->get()->result_array();

    $this->db_readonly->select("'publications-citations' as attribute, publication_type  as attribute_name, spc.id as attribute_id, date_format(publication_added_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status" )
    ->from('staff_publications_citations spc')
    ->join('staff_master sm', 'sm.id = spc.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(publication_added_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $citations = $this->db_readonly->get()->result_array();

    $this->db_readonly->select("'qualification' as attribute, degree_type  as attribute_name, sqv.id as attribute_id, date_format(sqv.created_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status" )
    ->from('staff_qualification_v2 sqv')
    ->join('staff_master sm', 'sm.id = sqv.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(sqv.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $qualification = $this->db_readonly->get()->result_array();

    $this->db_readonly->select("'training' as attribute, training_name  as attribute_name, stw.id as attribute_id, date_format(added_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status" )
    ->from('staff_training_workshop stw')
    ->join('staff_master sm', 'sm.id = stw.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(added_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $training = $this->db_readonly->get()->result_array();

    $this->db_readonly->select("'awards' as attribute, award_name  as attribute_name, sa.id as attribute_id, date_format(sa.created_on,'%d-%m-%Y') as uploaded_on, staff_id, approved_status" )
    ->from('staff_awards sa')
    ->join('staff_master sm', 'sm.id = sa.staff_id')
    ->where('sm.status', 2)
    ->where('date_format(sa.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    if ($staff_id) {
      $this->db_readonly->where('staff_id',$staff_id);
    }
    if ($staff_approved_status) {
      $this->db_readonly->where('approved_status',$staff_approved_status);
    }
    $awards = $this->db_readonly->get()->result_array();

    $merge = array_merge($documents, $experience, $interest,$citations,$qualification,$training ,$awards, $initiative);

    foreach ($merge as $key => &$val) {
      $val['uploaded_by'] = $this->get_staff_name_by_id($val['staff_id']);
    }
    return $merge;
  }

  public function update_approved_status_using_parameter($status, $tablename, $attribute_id, $remarks){
    $data= array(
      'approved_by'=>$this->authorization->getAvatarStakeHolderId(),
      'approved_status'=>$status,
      'approved_rejected_comments'=>$remarks
    );
    $this->db->where('id',$attribute_id);
    return $this->db->update($tablename, $data);
  }

  public function getCasteList () {
    $result = $this->db_readonly->select("distinct(caste) as caste")
      ->from('staff_master')
      ->order_by('caste')
      ->where('caste !=', NULL)
      ->get()->result();
    return $result;
    }

    public function getsalutations () {
      $salutation = $this->db_readonly->select("distinct(salutation) as salutation")
        ->from('staff_master')
        ->order_by('salutation')
        ->where('salutation !=', NULL)
        ->get()->result();
      return $salutation;
    }
  
    public function get_all_staff() {
      $staffs= $this->db_readonly->select('sm.id, CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')->where('status',2)->get('staff_master sm')->result();
      return $staffs;
      // echo '<pre>'; print_r($staffs); die();
    }

    public function get_staff_docs_by_staff_id($selected_staff_id) {
     
      $this->db_readonly->select('sd.document_type, sd.document_other, sd.document_url, sd.id as docs_id, sm.id as staff_id, CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staff_name')
      ->from('staff_master sm')
      ->join('staff_documents sd', 'sm.id = sd.staff_id','left')
      ->where('sm.status', 2);
      if($selected_staff_id != 'all')
        $this->db_readonly->where('sm.id', $selected_staff_id);

           
      $document_objects =$this->db_readonly->order_by('sm.first_name','asc')
        ->get()->result();
      
      $staff_docs_array = [];
      foreach ($document_objects as $key => $val) {
        if (!array_key_exists($val->staff_id, $staff_docs_array)) {
          $staffObj = new stdClass();
          $staffObj->staff_name = $val->staff_name;
          $staffObj->documents = [];
        }
        // if($staffObj->documents[$val->document_type] != 'Others')
        if($val->document_type !='Others' && $val->document_other =='NA'){
          $staffObj->documents['document'][$val->document_type] = $this->filemanager->getFilePath($val->document_url);
        }else if($val->document_type =='Others' && $val->document_other !='NA'){
          $staffObj->documents['other'][$val->document_other] = $this->filemanager->getFilePath($val->document_url);
        }
        
        $staff_docs_array[$val->staff_id] = $staffObj;
      }
      // echo '<pre>'; print_r($staff_docs_array); die();
      return $staff_docs_array;
    }

    public function get_staff_document_types() {
      $staffs= $this->db_readonly->select('*')->order_by('document_name')->get('staff_document_types')->result();
      return $staffs;
    }

    public function get_health_required_fields(){
      $result = $this->db_readonly->select('*')
      ->from('config')
      ->where('name','health_required_fields')
      ->get()->row();
      if (!empty($result)) {
         return json_decode($result->value);
      }else{
          return array();
      }
      
  }

  public function get_health_disabled_fields(){
      $result = $this->db_readonly->select('*')
      ->from('config')
      ->where('name','health_show_disabled_fields')
      ->get()->row();
      if (!empty($result)) {
         return json_decode($result->value);
      }else{
          return array();
      }
  }
  public function insert_health_configure_fields(){
    $input = $this->input->post();
    $types = array(
      'health_required_fields' => isset($input['health_required_fields'])?$input['health_required_fields']:[],
      'health_show_disabled_fields' => isset($input['health_show_disabled_fields'])?$input['health_show_disabled_fields']:[],
    );
    foreach ($types as $name => $value) {
        $query = $this->db->where('name',$name)->get('config');
        if ($query->num_rows() > 0) {
          if(empty($value)) {
            $row = $query->row();
            $this->db->where('id', $row->id)->delete('config');
          } else {
            $UpdaterFields = array(
                'name' =>$name,
                'value' => json_encode($value),
                'type' => 'multiple'
            );
            $this->db->where('name',$name);
            $this->db->update('config',$UpdaterFields);
          }
        }else{
            $rFields = array(
                'name' =>$name,
                'value' => json_encode($value),
                'type' => 'multiple'
            );
            $this->db->insert('config',$rFields);
        }
        
    }
    return 1;
  }

  public function add_staff_department_types($department_name, $department_status, $hod) {
    $check= $this->db->where('department', $department_name)->get('staff_departments');
    if($check->num_rows() == 1) {
      return -1;
    } else {
      $this->db->insert('staff_departments', array('department' => $department_name, 'status' => $department_status, 'head_of_department_id' => $hod));
      if ($this->db->affected_rows() > 0) {
          return $this->db->insert_id();
      } else {
          return -1;
      }
    }
  }

  public function get_staff_department_types() {
    $departments= $this->db_readonly->select("ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), '-') as staff, ifnull(sd.head_of_department_id, 0) as head_of_department_id_edit, sd.id, sd.status, sd.department, sd.approval_algorithm, sd.approver_1, sd.approver_2, sd.approver_3, sd.financial_approver, sd.min_approver_1_amount, sd.min_approver_2_amount, sd.min_approver_3_amount, 
    IFNULL(CONCAT(a1.first_name, ' ', IFNULL(a1.last_name, '')), '-') AS approver_1_name,
    IFNULL(CONCAT(a2.first_name, ' ', IFNULL(a2.last_name, '')), '-') AS approver_2_name,
    IFNULL(CONCAT(a3.first_name, ' ', IFNULL(a3.last_name, '')), '-') AS approver_3_name,
    IFNULL(CONCAT(a4.first_name, ' ', IFNULL(a4.last_name, '')), '-') AS financial_approver_name
    ")
    ->join('staff_master sm', 'sm.id = sd.head_of_department_id', 'left')
    ->join('staff_master a1', 'a1.id = sd.approver_1', 'left')
    ->join('staff_master a2', 'a2.id = sd.approver_2', 'left')
    ->join('staff_master a3', 'a3.id = sd.approver_3', 'left')
    ->join('staff_master a4', 'a4.id = sd.financial_approver', 'left')
    ->order_by('department')->get('staff_departments sd')->result();

    $s_d= $this->db_readonly->select('distinct(department)')->get('staff_master')->result();
    
    foreach($departments as $key => $val) {
      $x= 0;
      foreach($s_d as $a => $b) {
        if($val->department == $b->department) {
          $x= 1;
          break;
        }
      }
      if($x) {
        $departments[$key]->is_used= 1;
      } else {
        $departments[$key]->is_used= 0;
      }
    }
    
    return $departments;
  }

  public function edit_staff_department_status() {
    return $this->db->where('id', $_POST['department_edit_id'])->update('staff_departments', array('status' => $_POST['status']));
  }

  public function delete_department_type() {
    return $this->db->where('id', $_POST['primary_id'])->delete('staff_departments');
  }

  public function add_staff_designation_types($designation_name, $designation_status) {
    $check= $this->db->where('designation', $designation_name)->get('staff_designations');
    if($check->num_rows() == 1) {
      return -1;
    } else {
      $this->db->insert('staff_designations', array('designation' => $designation_name, 'status' => $designation_status));
      if ($this->db->affected_rows() > 0) {
          return $this->db->insert_id();
      } else {
          return -1;
      }
    }
  }

  public function get_staff_designation_types() {
    $designations= $this->db_readonly->order_by('designation')->get('staff_designations')->result();
    $s_d= $this->db_readonly->select('distinct(designation)')->get('staff_master')->result();
    
    foreach($designations as $key => $val) {
      $x= 0;
      foreach($s_d as $a => $b) {
        if($val->designation == $b->designation) {
          $x= 1;
          break;
        }
      }
      if($x) {
        $designations[$key]->is_used= 1;
      } else {
        $designations[$key]->is_used= 0;
      }
    }

    return $designations;
  }

  public function edit_staff_designation_status() {
    return $this->db->where('id', $_POST['designation_edit_id'])->update('staff_designations', array('status' => $_POST['status']));
  }

  public function delete_designation_type() {
    return $this->db->where('id', $_POST['primary_id'])->delete('staff_designations');
  }

  public function get_staff_by_stafftype($staffType, $department_id){
    
      $this->db->select("sm.id as staff_id,CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, sd.department");
      $this->db->join('staff_departments sd', 'sm.department=sd.id');
    if($staffType!=-1){
      $this->db->where('sm.staff_type', $staffType);
    }
    if ($department_id !=-1) {
        $this->db->where('sm.department', $department_id);
    }
    return $this->db->where('sm.status', 2)->order_by('sm.first_name')->get('staff_master sm')->result();
  }

  public function getTrackingUrlByThing($thingId) {
    $result = $this->db->select('tracking_url')->where('id', $thingId)->get('tx_things')->row();
    if(empty($result)) {
      return '';
    }
    return $result->tracking_url;
  }

  public function getRFID($studentId) {
    return $this->db->select("REPLACE(LTRIM(REPLACE(rfid_number,'0',' ')),' ','0') as rfid_number")->where('sa.id', $studentId)->get('student_admission sa')->row()->rfid_number;
  }

  public function getTodaysJourneyStaff($studentId){
    $journeys = array('PICKING' => [], 'DROPPING' => []);
    $date = date('Y-m-d');
    $day = date('l');
    $stdOverrides = $this->db->select("th.thing_name, th.id as thingId, th.tracking_url, j.id as journeyId, j.journey_name, CONCAT(ifnull(d.first_name,''), ' ', ifnull(d.last_name, '')) as driverName, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_type, d.phone_number, ts.stop_name, ts.id as stopId, j.status, 'no' as journey_change, d.first_name as driverName, 0 as attendance, d.attender_number, d.attender_name")
      ->from('tx_things th')
      ->join('tx_drivers d', 'd.id=th.driver_id')
      ->join('tx_journeys j', 'j.thing_id=th.id')
      ->join('tx_staff_journeys tsj', 'tsj.journey_id=j.id')
      ->join('tx_stops ts', 'ts.id=tsj.stop_id')
      ->where('tsj.staff_id', $studentId)
      ->where("tsj.day", $day);
    
    if (!empty($jIds))
      $this->db->where_not_in('j.id', $jIds);

    $stdRoute = $this->db->get()->result();

    foreach ($stdRoute as $k => $std) {
      $journeys[$std->journey_type][] = $std;
    }
    return $journeys;
  }

  public function timezone_setter($input) {
      $myDateTime = new DateTime($input, new DateTimeZone('GMT'));
      $myDateTime->setTimezone(new DateTimeZone('Asia/Kolkata'));
      return $myDateTime->format('Y-m-d H:i:s');
    }

    public function rfidJourneys($rfid) {
    $date = date('Y-m-d');
    $attendance = array();
    $journeys = array('PICKING' => [], 'DROPPING' => []);
    $result = $this->db->select("ta.id as att_id, t.tracking_url, ta.thing_id as thingId, ta.journey_id as journeyId, ta.journey_type, TIME_FORMAT(ta.updated_at, '%h:%i %p') as tap_at, TIME_FORMAT(j.tentative_start_time, '%h:%i %p') as start_time, TIME_FORMAT(j.tentative_end_time, '%h:%i %p') as end_time, j.journey_name, 0 as stopId, 1 as status, 'Running' as tracking, d.phone_number, d.first_name as driverName, d.attender_number, d.attender_name, 1 as attendance")
      ->from('tx_attendance ta')
      ->join('tx_things t', 't.id=ta.thing_id')
      ->join('tx_drivers d', 't.driver_id=d.id')
      ->join('tx_journeys j', 'j.id=ta.journey_id')
      ->where("ta.rfid", $rfid)
      ->where("DATE_FORMAT(ta.updated_at, '%Y-%m-%d')='$date'")
      // ->order_by('ta.id', 'desc')
      ->get()->result();
    foreach ($result as $key => $value) {
      $value->tap_at = date('h:i a',strtotime($this->timezone_setter($value->tap_at)));
      if(!array_key_exists($value->journeyId, $attendance)) {
        $attendance[$value->journeyId] = $value;
        $attendance[$value->journeyId]->taps = array();
      }
      $attendance[$value->journeyId]->taps[] = $value->tap_at;
    }

    foreach ($attendance as $key => $value) {
      $journeys[$value->journey_type][] = $value;
    }
    return $journeys;
    // echo "<pre>"; print_r($journeys); die();
  }

  public function staff_departments1() {
    return $this->db_readonly->select('distinct(department) as department')->get('staff_master')->result();
  }

  public function staff_designations1() {
    return $this->db_readonly->select('distinct(designation)as designation')->get('staff_master')->result();
  }

  public function delete_manage_group_assinged_staff($staff_id){
      $group_json = $this->db->select('*')->get('texting_groups')->result();
      $group_data = array();
      $staffIds = [];
      $updateArry =[];
      foreach ($group_json as $key => $val) {
        $group_data['students'] = array();
        $group_data['class_section'] = array();  
        $group_data['staff'] = array();
        $jsonDecode = json_decode($val->group_json);
        $group_data['students'] =$jsonDecode->students;
        $group_data['class_section'] =$jsonDecode->class_section;
        $staff_ids = $jsonDecode->staff;
        foreach ($staff_ids as $key => $staffId) {
          if($staffId != $staff_id){
            $group_data['staff'][] = $staffId;
          }
        }
        $groupJson = json_encode($group_data);
        $updateArry[] = array(
          'id'=>$val->id,
          'group_json'=>$groupJson
        );
      }      
      $this->db->update_batch('texting_groups',$updateArry,'id');
      return $this->db->affected_rows();
  }

  public function store_staff_edit_history($staffId,$old_val,$new_val){
    if($old_val == '{}' && $new_val == '{}'){
      return ;
    }
    $data = array(
      'staff_id'=>$staffId,
      'old_data'=>$old_val,
      'new_data'=>$new_val,
      'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
      'edited_on'=>$this->Kolkata_datetime()
    );
    return $this->db->insert('staff_edit_history',$data);
  }

  public function get_config_display_fields()
    {
        $this->db->select('value');
        $this->db->where('name', 'staff_fields_enabled_for_school');
        return $this->db->get('config')->result();
    }

    public function get_congif_staff_required_fields()
    {
        $this->db->select('value');
        $this->db->where('name', 'staff_fields_mandatory_for_school');
        return $this->db->get('config')->result();
    }
    private function _prepareStaffDataForUpadte()
    {
        $input = $this->input->post();
        $data = array(
            'last_modified_by' => $this->authorization->getAvatarId(),
        );

        if (isset($input['first_name'])) {
            $data['first_name'] = $input['first_name'];
        }
        if (isset($input['nationality'])) {
            $data['nationality'] = $input['nationality'];
        }
        if (isset($input['staff_type'])) {
            $data['staff_type'] = $input['staff_type'];
        }
        if (isset($input['short_name'])) {
            $data['short_name'] = $input['short_name'];
        }
        if (isset($input['department_name'])) {
            $data['department'] = $input['department_name'];
        }
        if (isset($input['designation_name'])) {
            $data['designation'] = $input['designation_name'];
        }
        if (isset($input['last_name'])) {
            $data['last_name'] = $input['last_name'];
        }
        if (isset($input['father_first_name'])) {
            $data['father_first_name'] = $input['father_first_name'];
        }
        if (isset($input['father_last_name'])) {
            $data['father_last_name'] = $input['father_last_name'];
        }
        if (isset($input['father_contact_no'])) {
            $data['father_contact_no'] = $input['father_contact_no'];
        }
        if (isset($input['mother_first_name'])) {
            $data['mother_first_name'] = $input['mother_first_name'];
        }
        if (isset($input['mother_last_name'])) {
            $data['mother_last_name'] = $input['mother_last_name'];
        }
        if (isset($input['mother_occupation'])) {
            $data['mother_occupation'] = $input['mother_occupation'];
        }
        if (isset($input['marital_status'])) {
            $data['marital_status'] = $input['marital_status'];
        }
        if (isset($input['date_of_birth'])) {
            $data['dob'] = date("Y-m-d", strtotime($input['date_of_birth']));
        }
        if (isset($input['gender'])) {
            $data['gender'] = $input['gender'];
        }
        if (isset($input['contact_number'])) {
            $data['contact_number'] = $input['contact_number'];
        }
        if (isset($input['spouse_name'])) {
            $data['spouse_name'] = $input['spouse_name'];
        }
        if (isset($input['alternative_number'])) {
            $data['alternative_number'] = $input['alternative_number'];
        }
        if (isset($input['adhaar_number'])) {
            $data['aadhar_number'] = $input['adhaar_number'];
        }
        if (isset($input['qualification_name'])) {
            $data['qualification'] = $input['qualification_name'];
        }
        if (isset($input['subject_specialization'])) {
            $data['subject_specialization'] = $input['subject_specialization'];
        }
        if (isset($input['education_exp'])) {
            $data['total_education_experience'] = $input['education_exp'];
        }
        if (isset($input['experience'])) {
            $data['total_experience'] = $input['experience'];
        }
        if (isset($input['total_education_experience'])) {
            $data['total_education_experience'] = $input['total_education_experience'];
        }
        if (isset($input['total_experience'])) {
            $data['total_experience'] = $input['total_experience'];
        }
        if (isset($input['status'])) {
            $data['status'] = $input['status'];
        }
        if (isset($input['spouse_contact_no'])) {
            $data['spouse_contact_no'] = $input['spouse_contact_no'];
        }
        if (isset($input['emergency_info'])) {
            $data['emergency_info'] = $input['emergency_info'];
        }
        if (isset($input['employee_code'])) {
            $data['employee_code'] = $input['employee_code'];
        }
        if (isset($input['blood_group'])) {
            $data['blood_group'] = $input['blood_group'];
        }
        if (isset($input['joining_date']) && $input['joining_date'] != '') {
            $data['joining_date'] = date("Y-m-d", strtotime($input['joining_date']));
        } else {
            $data['joining_date'] = null;
        }
        if (isset($input['math_high_grade'])) {
            $data['math_high_grade'] = $input['math_high_grade'];
        }
        if (isset($input['english_high_grade'])) {
            $data['english_high_grade'] = $input['english_high_grade'];
        }
        if (isset($input['social_high_grade'])) {
            $data['social_high_grade'] = $input['social_high_grade'];
        }
        if (isset($input['trained_to_teach'])) {
            $data['trained_to_teach'] = $input['trained_to_teach'];
        }
        if (isset($input['appointed_subject'])) {
            $data['appointed_subject'] = $input['appointed_subject'];
        }
        if (isset($input['classes_taught'])) {
            $data['classes_taught'] = $input['classes_taught'];
        }
        if (isset($input['main_sub_taught'])) {
            $data['main_sub_taught'] = $input['main_sub_taught'];
        }
        if (isset($input['add_sub_taught'])) {
            $data['add_sub_taught'] = $input['add_sub_taught'];
        }
        if (isset($input['boarding'])) {
            $data['boarding'] = $input['boarding'];
        }
        if (isset($input['voter_id'])) {
            $data['voter_id'] = $input['voter_id'];
        }
        if (isset($input['height'])) {
            $data['height'] = $input['height'];
        }
        if (isset($input['weight'])) {
            $data['weight'] = $input['weight'];
        }
        if (isset($input['allergies'])) {
            $data['allergies'] = $input['allergies'];
        }
        if (isset($input['medical_issues'])) {
            $data['medical_issues'] = $input['medical_issues'];
        }
        if (isset($input['identification_mark'])) {
            $data['identification_mark'] = $input['identification_mark'];
        }
        if (isset($input['person_with_disability'])) {
            $data['person_with_disability'] = $input['person_with_disability'];
        }
        if (isset($input['religion'])) {
            $data['religion'] = $input['religion'];
        }
        if (isset($input['category'])) {
            $data['category'] = $input['category'];
        }
        if (isset($input['caste'])) {
            $data['caste'] = $input['caste'];
        }
        if (isset($input['passport_number'])) {
            $data['passport_number'] = $input['passport_number'];
        }
        if (isset($input['passport_place_of_issue'])) {
            $data['passport_place_of_issue'] = $input['passport_place_of_issue'];
        }
        if (isset($input['passport_date_of_issue'])) {
            $data['passport_date_of_issue'] = date("Y-m-d", strtotime($this->input->post('passport_date_of_issue')));
        }
        if (isset($input['passport_expiry_date'])) {
            $data['passport_expiry_date'] = date("Y-m-d", strtotime($this->input->post('passport_expiry_date')));
        }
        if (isset($input['visa_details'])) {
            $data['visa_details'] = $input['visa_details'];
        }
        if (isset($input['has_completed_any_bed'])) {
            $data['has_completed_any_bed'] = $input['has_completed_any_bed'];
        }
        if (isset($input['nature_of_appointment'])) {
            $data['nature_of_appointment'] = $input['nature_of_appointment'];
        }
        if (isset($input['personal_mail_id'])) {
            $data['personal_mail_id'] = $input['personal_mail_id'];
        }
        if (isset($input['father_occupation'])) {
            $data['father_occupation'] = $input['father_occupation'];
        }
        if (isset($input['alternative_no'])) {
          $data['alternative_number'] = $input['alternative_no'];
        }
        if (isset($input['spouse_occupation'])) {
            $data['spouse_occupation'] = $input['spouse_occupation'];
        }
        if (isset($input['staff_reference_code'])) {
            $data['staff_reference_code'] = $input['staff_reference_code'];
        }
        if (isset($input['previous_designation_name'])) {
            $data['previous_designation_name'] = $input['previous_designation_name'];
        }
        if (isset($input['salutation'])) {
            $data['salutation'] = $input['salutation'];
        }
        if (isset($input['has_completed_any_pgdei'])) {
            $data['has_completed_any_pgdei'] = $input['has_completed_pgde'];
        }
        if (isset($input['custom1'])) {
            $data['custom1'] = $input['custom1'];
        }
        if (isset($input['custom2'])) {
            $data['custom2'] = $input['custom2'];
        }
        if (isset($input['custom3'])) {
            $data['custom3'] = $input['custom3'];
        }
        if (isset($input['custom4'])) {
            $data['custom4'] = $input['custom4'];
        }
        if (isset($input['custom5'])) {
            $data['custom5'] = $input['custom5'];
        }
        if (isset($input['custom6'])) {
            $data['custom6'] = $input['custom6'];
        }
        if (isset($input['custom7'])) {
            $data['custom7'] = $input['custom7'];
        }
        if (isset($input['custom8'])) {
            $data['custom8'] = $input['custom8'];
        }
        if (isset($input['custom9'])) {
            $data['custom9'] = $input['custom9'];
        }
        if (isset($input['custom10'])) {
            $data['custom10'] = $input['custom10'];
        }
        if (isset($input['custom11'])) {
            $data['custom11'] = $input['custom11'];
        }
        if (isset($input['custom12'])) {
            $data['custom12'] = $input['custom12'];
        }
        if (isset($input['custom13'])) {
            $data['custom13'] = $input['custom13'];
        }
        if (isset($input['custom14'])) {
            $data['custom14'] = $input['custom14'];
        }
        if (isset($input['custom15'])) {
            $data['custom15'] = $input['custom15'];
        }
        if (isset($input['custom16'])) {
            $data['custom16'] = $input['custom16'];
        }
        if (isset($input['custom17'])) {
            $data['custom17'] = $input['custom17'];
        }
        if (isset($input['custom18'])) {
            $data['custom18'] = $input['custom18'];
        }
        if (isset($input['custom19'])) {
            $data['custom19'] = $input['custom19'];
        }
        if (isset($input['custom20'])) {
            $data['custom20'] = $input['custom20'];
        }
        if (isset($input['spouse_dob'])) {
          $data['spouse_dob'] = date("Y-m-d", strtotime($input['spouse_dob']));
        }
        if (isset($input['spouse_gender'])) {
            $data['spouse_gender'] = $input['spouse_gender'];
        }
        if (isset($input['spouse_is_dependent'])) {
            $data['spouse_is_dependent'] = $input['spouse_is_dependent'];
        }
        if (isset($input['child1_first_name'])) {
            $data['child1_first_name'] = $input['child1_first_name'];
        }
        if (isset($input['child1_dob'])) {
            $data['child1_dob'] = date("Y-m-d", strtotime($input['child1_dob']));
        }
        if (isset($input['child1_gender'])) {
            $data['child1_gender'] = $input['child1_gender'];
        }
        if (isset($input['child1_is_dependent'])) {
            $data['child1_is_dependent'] = $input['child1_is_dependent'];
        }
        if (isset($input['child2_first_name'])) {
            $data['child2_first_name'] = $input['child2_first_name'];
        }
        if (isset($input['child2_dob'])) {
            $data['child2_dob'] = date("Y-m-d", strtotime($input['child2_dob']));
        }
        if (isset($input['child2_gender'])) {
            $data['child2_gender'] = $input['child2_gender'];
        }
        if (isset($input['child2_is_dependent'])) {
            $data['child2_is_dependent'] = $input['child2_is_dependent'];
        }
        if (isset($input['child3_first_name'])) {
            $data['child3_first_name'] = $input['child3_first_name'];
        }
        if (isset($input['child3_dob'])) {
            $data['child3_dob'] = date("Y-m-d", strtotime($input['child3_dob']));
        }
        if (isset($input['child3_gender'])) {
            $data['child3_gender'] = $input['child3_gender'];
        }
        if (isset($input['child3_is_dependent'])) {
            $data['child3_is_dependent'] = $input['child3_is_dependent'];
        }
        if (isset($input['mother_dob'])) {
            $data['mother_dob'] = date("Y-m-d", strtotime($input['mother_dob']));
        }
        if (isset($input['mother_is_dependent'])) {
            $data['mother_is_dependent'] = $input['mother_is_dependent'];
        }
        if (isset($input['father_dob'])) {
            $data['father_dob'] = date("Y-m-d", strtotime($input['father_dob']));
        }
        if (isset($input['father_is_dependent'])) {
            $data['father_is_dependent'] = $input['father_is_dependent'];
        }
        if (isset($input['spouse_last_name'])) {
            $data['spouse_last_name'] = $input['spouse_last_name'];
        }
        if (isset($input['child1_last_name'])) {
            $data['child1_last_name'] = $input['child1_last_name'];
        }
        if (isset($input['child2_last_name'])) {
            $data['child2_last_name'] = $input['child2_last_name'];
        }
        if (isset($input['child3_last_name'])) {
            $data['child3_last_name'] = $input['child3_last_name'];
        }
        if (isset($input['is_primary_instance'])) {
            $data['is_primary_instance'] = $input['is_primary_instance'];
        }
        if (isset($input['has_completed_pgde'])) {
          $data['has_completed_any_pgdei'] = $input['has_completed_pgde'];
        }
        if (isset($input['staff_house']) && $input['staff_house'] != '') {
            $data['staff_house'] = $input['staff_house'];
        }
        if (isset($input['include_father_insurance'])) {
            $data['include_father_insurance'] = $input['include_father_insurance'];
        }
        if (isset($input['include_mother_insurance'])) {
            $data['include_mother_insurance'] = $input['include_mother_insurance'];
        }
        if (isset($input['include_spouse_insurance'])) {
            $data['include_spouse_insurance'] = $input['include_spouse_insurance'];
        }
        if (isset($input['include_child1_insurance'])) {
            $data['include_child1_insurance'] = $input['include_child1_insurance'];
        }
        if (isset($input['include_child2_insurance'])) {
            $data['include_child2_insurance'] = $input['include_child2_insurance'];
        }
        if (isset($input['include_child3_insurance'])) {
            $data['include_child3_insurance'] = $input['include_child3_insurance'];
        }
        if (isset($input['employment_type'])) {
            $data['employment_type'] = $input['employment_type'];
        }

        return $data;

    }

    public function get_document_names(){
      return $this->db->select('*')
      ->from('staff_document_types')
      ->get()->result();
    }

    public function get_staff_names_with_docs($document_name,$staff_status){
      $result =  $this->db->select("sm.id,ifnull(sm.employee_code,'') as employee_code,concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
      ->from('staff_master sm')
      ->where('status',$staff_status)
      ->where('sm.is_primary_instance',1)
      ->order_by('sm.employee_code')
      ->get()->result();
      
      foreach($result as $key => $val){
        $val->document_url = $this->get_staff_document_by_id($val->id,$document_name);
      }
      
      return $result;
    }

    private function get_staff_document_by_id($staff_id,$document_name){
      $url = $this->db->select('document_url')
      ->from('staff_documents')
      ->where('staff_id',$staff_id)
      ->where('document_type',$document_name)
      ->get()->row();

      if(!empty($url)){
        return $this->filemanager->getFilePath($url->document_url);
      }else{
        return '';
      }
    }

    public function save_staff_document($document_name,$staff_id,$path){
      $data = array(
        'document_type' => $document_name,
        'document_url' => $path,
        'document_other' => 'NA',
        'staff_id' => $staff_id
      );
      return $this->db->insert('staff_documents',$data);
    }

    public function get_staff_caste(){
      $result = $this->db_readonly->select('DISTINCT(caste)')
                              ->from('staff_master')
                              ->where('caste IS NOT NULL')
                              ->where('TRIM(caste) !=', '')
                              ->where('caste !=', '-')
                              ->get()
                              ->result();

      return $result;
    }

    public function update_staff_employee_code_by_receipt_book($staff_employee_code_receipt_book_id){
     
      $this->load->library('fee_library');
      $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$staff_employee_code_receipt_book_id)->get()->row();
      if (!empty($receipt_book)) {
          $receipt_number =  $this->fee_library->receipt_format_get_update($receipt_book);
      }else{
          $receipt_number =  0;
      }
      return $receipt_number;
  }

  public function get_employee_code_format($id){
     return $this->db->select('*')->from('feev2_receipt_book')->where('id',$id)->get()->row();
  }

  public function update_designation_type($id,$designation_type){
    $this->db->where('id',$id);
    return $this->db->update('staff_designations',array('designation'=>$designation_type));
  }

  public function update_department_name($id,$department, $hod_id){
    $this->db->where('id',$id);
    return $this->db->update('staff_departments',array('department'=>$department, 'head_of_department_id' => $hod_id));
  }

  public function getHouseList () {
    $result = $this->db_readonly->select("distinct(staff_house) as house")
        ->from('staff_master')
        ->order_by('staff_house')
        ->where('staff_house !=', NULL)
        ->get()->result();
    
    return $result;
  }

  public function get_staff_profile_created_email_data($staff_id){
    $email_content = $this->db->select('*')->from('email_template')->where('name','staff_profile_created_email_confirmation')->get()->row();
    if(empty($email_content)){
      return '';
    }
    $staff_data = $this->db->select("concat(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name,employee_code")->from('staff_master')->where('id',$staff_id)->get()->row();
    $email_content->staff_name = $staff_data->staff_name;
    $email_content->employee_code = $staff_data->employee_code;
    return (array) $email_content;
  }

  public function save_sending_email_data($sent_data){
    $this->db->insert('email_sent_to', $sent_data);
    return $this->db->insert_id();
  }

  function getStaffs() {
    return $this->db_readonly
    ->select("id, CONCAT(first_name, ' ', IFNULL(last_name, '')) AS name, department as department_id")
    ->where('status', 2)
    ->get('staff_master')
    ->result();
  }

  public function get_exit_details($staff_id){
    $this->db_readonly->select("ifnull(resignation_date,'') as resignation_date,ifnull(exit_remarks,'') as exit_remarks ,ifnull(last_date_of_work,'') as last_date_of_work ");
    $this->db_readonly->from('staff_master');
    $this->db_readonly->where('id',$staff_id);
    return $this->db_readonly->get()->row();
  }

  public function Store_edit_history($staff_id,$old_val,$new_val){
      return $this->db->insert('staff_edit_history', [
          'staff_id' => $staff_id,
          'old_data' => $old_val,
          'new_data' => $new_val,
          'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
          'edited_on'=>$this->Kolkata_datetime()
      ]);
  }

  public function updating_reporting_manger_history_tracking($staff_id,$new_reporting_manager){
    $old_reporting_manager = $this->db->select("concat(ifnull(sm1.first_name,''),' ',ifnull(sm1.last_name,'')) as reporting_manager")->from('staff_master sm')->join('staff_master sm1','sm.reporting_manager_id=sm1.id','left')->where('sm.id',$staff_id)->get()->row();
    return $this->db->insert('staff_edit_history', [
      'staff_id' => $staff_id,
      'old_data' => 'Reporting Manager is changed : '.$old_reporting_manager->reporting_manager,
      'new_data' => 'Reporting Manager is changed : '.$new_reporting_manager,
      'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
      'edited_on'=>$this->Kolkata_datetime()
    ]);

  }

  public function save_department_approvers($payload){
    $deptId = isset($payload['deptId']) ? $payload['deptId'] : null;
    $algorithm = isset($payload['approvalAlgorithm']) ? $payload['approvalAlgorithm'] : null;
    $approvers = isset($payload['approvers']) ? $payload['approvers'] : [];

    // Validate required input
    if (!$deptId || !$algorithm || empty($approvers)) {
      return false;
    }

    $update = [
      'approval_algorithm' => $algorithm,
      'approver_1' => null,
      'approver_2' => null,
      'approver_3' => null,
      'min_approver_1_amount' => '0.00',
      'min_approver_2_amount' => '0.00',
      'min_approver_3_amount' => '0.00',
      'financial_approver' => null
    ];

    foreach ($approvers as $a) {
      if (!isset($a['id']))
        continue;

      $num = isset($a['approver_number']) ? (int) $a['approver_number'] : 0;
      $is_financial = isset($a['is_financial']) ? $a['is_financial']=="true" : "false";

      if ($is_financial) {
        $update['financial_approver'] = $a['id'];
      } else {
        $update['approver_' . $num] = $a['id'];
        $update['min_approver_' . $num . '_amount'] = isset($a['min_amount']) ? $a['min_amount'] : '0.00';
      }
    }
    $this->db->where('id', $deptId)->update('staff_departments', $update);
    return true;
  }

  public function get_staff_termination_old_data($staff_id){
    return $this->db->select("ifnull(last_date_of_work,'') as last_date_of_work,ifnull(resignation_date,'') as resignation_date,ifnull(exit_remarks,'') as exit_remarks,ifnull(resignation_letter_doc,'') as resignation_letter_doc,ifnull(exit_update_on,'') as exit_update_on,ifnull(exit_updated_by,'') as exit_updated_by")->from('staff_master')->where('id',$staff_id)->get()->row();
  }

  public function update_qr_code($staff_uid,$qrCode){
    $this->db->where('id',$staff_uid);
    return $this->db->update('staff_master',array('identification_code'=>$qrCode));
  }
}