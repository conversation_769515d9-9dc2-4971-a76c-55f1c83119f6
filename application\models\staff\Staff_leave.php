<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Staff_leave extends CI_Model {

  	private $yearId;
  	public function __construct()
  	{
    	// Call the CI_Model constructor
    	parent::__construct();
    	$this->yearId =  $this->acad_year->getAcadYearId();
		$this->load->library('filemanager');
  	}
	
	public function Kolkata_datetime(){
	   $timezone = new DateTimeZone("Asia/Kolkata" );
	   $date = new DateTime();
	   $date->setTimezone($timezone );
	   $dtobj = $date->format('Y-m-d H:i:s');
	   return $dtobj;
   	}

	public function getLeaveCategories() { //not to change
		return $this->db_readonly->select('*')->order_by('id asc')->get('leave_v2_category')->result_array();
	}

	public function getLeaveActiveCategories() {
		return $this->db_readonly->select('*')->where('status', 1)->order_by('has_quota desc')->get('leave_v2_category')->result_array();
	}

  	public function getReportingStaffDataperstaff($staffid){
     $sql = "select sm1.id as staff_id, CONCAT(ifnull(sm1.first_name,''),' ', ifnull(sm1.last_name,'')) AS staff_name, sm2.id as reporting_staff_id, CONCAT(ifnull(sm2.first_name,''),' ', ifnull(sm2.last_name,'')) AS reporting_to, sm1.is_reporting_manager, sm1.designation 
            from staff_master sm1 
            left join staff_master sm2 on sm2.id=sm1.reporting_manager_id 
            where sm1.status=2 AND sm1.id = $staffid
            order by sm1.first_name";
    return $this->db_readonly->query($sql)->row();
  }

  	public function getLeaveCategoriesHavingQuota() {
  		return $this->db_readonly->select('*')->where('status', 1)->where('has_quota', 1)->order_by('name')->get('leave_v2_category')->result_array();
  	}

  	public function saveLeaveCategory($data) {
  		$avatar_id = $this->authorization->getAvatarId();
  		$data['created_by'] = $avatar_id;
  		$data['last_modified_by'] = $avatar_id;
  		return $this->db->insert('leave_v2_category', $data);
  	}

  	public function getStaffList() {
      	return $this->db_readonly->select("id as staff_id,reporting_manager_id,CONCAT(ifnull(first_name, ''),' ', ifnull(last_name, '')) as staff_name, designation")->where('status', 2)->where('is_primary_instance', 1)->order_by('first_name')->get('staff_master')->result();
  	}

  	public function getReportingStaffList($type=1, $staff_id=0) {
        $this->db_readonly->select("id, concat(ifnull(first_name,''),' ', ifnull(last_name,'')) as staffName")->from('staff_master')->where('status','2')->where('is_primary_instance',1);
        if($type==2) {
            $this->db_readonly->where("reporting_manager_id=$staff_id OR id=$staff_id");
        }
        $this->db_readonly->order_by('staffName');
        return $this->db_readonly->get()->result();
    }

  	public function getStaffLeaveQuota($leave_year_id) {
  		$result = $this->db_readonly->select("lsq.id, lsq.staff_id, lsq.leave_category_id, lsq.total_quota, lsq.used_quota, lsq.quota_carried_to_next_year, lc.name, lc.short_name, lc.leave_type, lc.has_quota, lc.can_be_carried")->from('leave_v2_staff_quota lsq')->join('leave_v2_category lc', 'lc.id=lsq.leave_category_id')->where('lsq.leave_v2_year_id', $leave_year_id)->get()->result();
  		$staff_quota = array();
  		foreach ($result as $res) {
  			$staff_quota[$res->staff_id][$res->name] = $res;
  		}
		  return $staff_quota;
    }

    public function getLeaveYear() {
    	return $this->db->select("*")->where('is_active', 1)->get('leave_v2_year')->row();
    }

    public function getLeaveYears() {
    	return $this->db->select("*")->order_by("is_active","DESC")->get('leave_v2_year')->result();
    }

	public function get_month_wise_new_leave_report($leaveYearId, $staffTypeId, $staffStatusType){
		if($leaveYearId==-1){
			// get active leave year id
			$year_data = $this->db->select("*")->where("is_active", 1)->get('leave_v2_year')->row();
			if(!empty($year_data)){
				$leaveYearId=$year_data->id;
			}
		}else{
			$year_data = $this->db->select("*")->where("id", $leaveYearId)->get('leave_v2_year')->row();
		}

		//getting static data
		if($staffTypeId==-1){
			$get_staff_data = 'select ifnull(sm.employee_code,"NA") as employee_code, sm.id as staff_id, concat(ifnull(sm.first_name,"")," ",ifnull(sm.last_name,"")) as full_name, 
			ifnull(CONCAT(Upper(SUBSTRING(sd.designation,1,1)),SUBSTRING(sd.designation,2)),"NA") as designation, DATE_FORMAT(sm.joining_date,"%d-%m-%Y") as date_of_joining
			from staff_master sm 
			left join staff_designations sd on sd.id=sm.designation
			where sm.is_primary_instance=1';
		}else{
			$get_staff_data='select ifnull(sm.employee_code,"NA") as employee_code, sm.id as staff_id, concat(ifnull(sm.first_name,"")," ",ifnull(sm.last_name,"")) as full_name, 
			ifnull(CONCAT(Upper(SUBSTRING(sd.designation,1,1)),SUBSTRING(sd.designation,2)),"NA") as designation, DATE_FORMAT(sm.joining_date,"%d-%m-%Y") as date_of_joining
			from staff_master sm 
			left join staff_designations sd on sd.id=sm.designation
			where sm.is_primary_instance=1 and sm.staff_type='.$staffTypeId;
		}

		// Filtering by staff status
		if ($staffStatusType == 2) {
			$get_staff_data .= " AND sm.status = 2";
		} elseif ($staffStatusType == 4) {
			$get_staff_data .= " AND sm.status = 4";
			$get_staff_data .= " AND EXISTS (
						SELECT 1 FROM leave_v2_staff_quota q 
						WHERE q.staff_id = sm.id 
						AND q.leave_v2_year_id = $leaveYearId
					 )";
		}

		$staff_data_result=$this->db->query($get_staff_data)->result();

		$is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
		if ((int)$is_multi_level_leave_enabled == 1) {
			//getting monthly leave applied data for multi level
			$get_monthly_leave_data = 'select * 
				from leave_v2_staff ls 
				join leave_v2_category lc on lc.id=ls.leave_category_id
				where ls.final_status!=4 and ls.final_status!=3 and ls.leave_year_id='.$leaveYearId.' order by ls.id';
		} else {
			//getting monthly leave applied data for single level
			$get_monthly_leave_data='select * 
				from leave_v2_staff ls 
				join leave_v2_category lc on lc.id=ls.leave_category_id
				where ls.status!=4 and ls.status!=3 and ls.leave_year_id=' . $leaveYearId . ' order by ls.id';
		}

		$get_monthly_leave_data_result=$this->db->query($get_monthly_leave_data)->result();

		$staff_leaves = array();
		$staff_leaves_no_of_days = array();
		$leave_type_array=array();

		$leave_type_array=[];
		$leave_type_dates=[];

        foreach ($get_monthly_leave_data_result as $key => $leave) {
            $start_date_formatted = strtotime($leave->from_date);
            $end_date_formatted = strtotime($leave->to_date);
            $datediff = $end_date_formatted - $start_date_formatted;
            $diffDays = (int)($datediff / (60 * 60 * 24))+1;
			
            for($i=0;$i<$diffDays;$i++){
				$previous_date=date('Y-m', strtotime("+".$i." day", strtotime($leave->from_date)));
                $staff_leaves[$leave->staff_id][$previous_date][] = $previous_date;
                // $staff_leaves[$leave->staff_id]['dhfgrvkj'] = $get_monthly_leave_data_result;
				$leave_type_array[$leave->staff_id][$leave->leave_category_id][] = $leave->leave_category_id;
				$leave_type_dates[$leave->staff_id][$leave->leave_category_id][] = date('d-m-Y', strtotime("+".$i." day", strtotime($leave->from_date)));
            }

			$staff_leaves_no_of_days[$leave->staff_id][$previous_date]["no_of_days"][] = $leave->noofdays;

			$leave_type_array_no_of_days[$leave->staff_id][$leave->leave_category_id][] = $leave->noofdays;
        }
		
		foreach($staff_leaves as $staff_id => $val){
			$total_leaves=0;
			// getting montly leave  count
			foreach($val as $date => $data){
				$countNoOfDays=0;
				if(!empty($staff_leaves_no_of_days[$staff_id][$date]['no_of_days'])){
					foreach($staff_leaves_no_of_days[$staff_id][$date]['no_of_days'] as $key => $no_of_days){
						$countNoOfDays+=$no_of_days;
					}
				}
				$staff_leaves[$staff_id][$date]['count']=$countNoOfDays;
				$total_leaves+= $countNoOfDays;
			}

			//getting total leave count
			$staff_leaves[$staff_id]['total_leave_count']=$total_leaves;
		}

		foreach($leave_type_array as $staff_id => $val){
			foreach($val as $leave => $leavedata){
				$countNoOfDays = 0;
				if (!empty($leave_type_array_no_of_days[$staff_id][$leave])) {
					foreach ($leave_type_array_no_of_days[$staff_id][$leave] as $key => $no_of_days) {
						$countNoOfDays += $no_of_days;
					}
				}
				$leave_type_array[$staff_id][$leave]['count']=$countNoOfDays;
			}
		}

		// getting total leaves assigned to each staff
		$total_leaves_assigned=$this->db_readonly->select("quota.staff_id, quota.total_quota, quota.used_quota, quota.leave_category_id, cat.name, cat.short_name, cat.has_quota")
		->from("leave_v2_staff_quota quota")
		->join("leave_v2_category cat","cat.id=quota.leave_category_id")
		->where("leave_v2_year_id",$leaveYearId)
		->get()->result();

		$total_leaves_assigned_for_each_staff=[];
		if(!empty($total_leaves_assigned)){
			foreach($total_leaves_assigned as $key => $leave_quota){
				$total_leaves_assigned_for_each_staff[$leave_quota->staff_id][$leave_quota->leave_category_id]["total_quota"]= $leave_quota->has_quota==1 ? $leave_quota->total_quota : "-";
				$total_leaves_assigned_for_each_staff[$leave_quota->staff_id][$leave_quota->leave_category_id]["used_quota"] = $leave_quota->used_quota;
				$total_leaves_assigned_for_each_staff[$leave_quota->staff_id][$leave_quota->leave_category_id]["balance_quota"]= $leave_quota->has_quota==1 ?$leave_quota->total_quota-$leave_quota->used_quota : "-";
			}
		}

		foreach($staff_data_result as $key => $val){
			$val->leave_data = [];
			$val->taken_leaves_type = [];
			$val->taken_leaves_type_dates = [];

			if(array_key_exists($val->staff_id, $staff_leaves)){
				$val->leave_data= $staff_leaves[$val->staff_id];
				$val->taken_leaves_type=$leave_type_array[$val->staff_id];
				$val->taken_leaves_type_dates=$leave_type_dates[$val->staff_id];
			}
			if(array_key_exists($val->staff_id, $total_leaves_assigned_for_each_staff)){
				$val->total_leaves_assigned=$total_leaves_assigned_for_each_staff[$val->staff_id];
			}else{
				$val->total_leaves_assigned=[];
			}
		}
		
		$start    = new DateTime($year_data->start_date);
		$start->modify('first day of this month');
		$end      = new DateTime($year_data->end_date);
		$end->modify('first day of next month');
		$interval = DateInterval::createFromDateString('1 month');
		$period   = new DatePeriod($start, $interval, $end);
		$month_headers = [];
		foreach ($period as $dt) {
			$month_headers[$dt->format("Y-m")] = $dt->format("M-y");
		}

		$leavetype_header=[];

		$leave_categories=$this->db->select('id,name,short_name')->where('status', '1')->get('leave_v2_category')->result();
		foreach ($leave_categories as $lc => $val) {
			$leavetype_header[$val->id] = $val;
		}

		return array('header'=>$month_headers, 'staff_data'=>$staff_data_result, 'leave_types_header'=>$leavetype_header,'staffTypeId'=>$staffTypeId,'leaveYearId'=> $leaveYearId, 'staffStatusType' => $staffStatusType);
	}

    public function getLeaveYearById($leave_year_id) {
    	return $this->db->select("*")->where('id', $leave_year_id)->get('leave_v2_year')->row();
    }

  	public function saveAssignedQuota() {
	    $staff_ids = $_POST['staff_id'];
	    $leave_categories = $_POST['leave_category_id'];
	    $start_date = $_POST['start_date'];
	    $end_date = $_POST['end_date'];
	    $total_quota = $_POST['total_quota'];
	    $leave_year_id = $_POST['leave_year_id'];

	    $data = array();
	    foreach ($staff_ids as $staff_id) {
	      $categories = $leave_categories[$staff_id];
	      foreach ($categories as $category_id) {
	        $data[] = array(
	          'leave_v2_year_id' => $leave_year_id,
	          'staff_id' => $staff_id,
	          'leave_category_id' => $category_id,
	          'total_quota' => ($total_quota[$staff_id][$category_id])?$total_quota[$staff_id][$category_id]:0,
	          'used_quota' => 0,
	        );
	      }
	    }

	    // echo "<pre>"; print_r($data); die();

	    if(!empty($data)) {
	      return $this->db->insert_batch('leave_v2_staff_quota', $data);
	    }
	    return 1;
  	}

  	// public function updateAssignedQuota() {
	//     $quota_ids = $_POST['quota'];
	//     $start_date = $_POST['start_date'];
	//     $end_date = $_POST['end_date'];
	//     $total_quota = $_POST['total_quota'];
	//     $used_quota = $_POST['used_quota'];
	//     $data = array();
	//     foreach ($quota_ids as $quota_id) {
	//       $temp = [];
	//       $temp['id'] = $quota_id;
	//       if($total_quota[$quota_id] >= $used_quota[$quota_id]) {
	//         $temp['total_quota'] = $total_quota[$quota_id];
	//       }
	//       $data[] = $temp;
	//     }

	//     // echo "<pre>"; print_r($data); die();
	//     if(!empty($data)) {
	//       return $this->db->update_batch('leave_v2_staff_quota', $data, 'id');
	//     }
	//     return 1;
  	// }

  	public function getCountOfLeaves($id, $val){
	    if($val == 0){
	        $sql = "SELECT SUM(`noofdays`) as `total` FROM `leave_v2_staff` WHERE `staff_id`='$id' AND (`status`=1 OR `status`=2)";
	    } else {
	        $sql = "SELECT SUM(`noofdays`) as `total` FROM `leave_v2_staff` WHERE `staff_id`='$id'";
	    }
	    return $this->db->query($sql)->row();
  	}

  	public function list_staffbyuserId() {
	    $avatarId = $this->authorization->getAvatarId();

	    $this->db_readonly->select("staff_master.id,staff_master.first_name, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name");
	    $this->db_readonly->join('avatar', 'avatar.stakeholder_id=staff_master.id');
	    $this->db_readonly->where('avatar.id', $avatarId);
	    $this->db_readonly->where('staff_master.is_primary_instance', 1);
	    $this->db_readonly->from('staff_master');

	    return $this->db_readonly->get()->row();
  	}

  	public function is_reporting_manager($staff_id) {
    	return $this->db->select('is_reporting_manager')->where('id', $staff_id)->get('staff_master')->row()->is_reporting_manager;
  	}

  	public function getLeaveCategoryWithNotQuota() {
  		return $this->db_readonly->select('id, name, short_name, has_quota, 0 as total_quota, 0 as used_quota')->where('status', 1)->where('has_quota', 0)->order_by('name')->get('leave_v2_category')->result_array();
  	}

  	public function getAvailableStaffQuota($staff_id) {
		$admin = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');

	    $sql = "select lc.enable_leave_auto_approval_by_admin, lc.id, lc.name, lc.short_name, lc.has_quota, lsq.total_quota, lsq.used_quota, lc.consider_holiday, lc.leave_category_visibility
	            from leave_v2_staff_quota lsq 
	            join leave_v2_category lc on lc.id=lsq.leave_category_id and lc.status= 1 
	            where lsq.staff_id=$staff_id 
	            and lsq.leave_v2_year_id in (select id from leave_v2_year where is_active=1)";
	    $result=$this->db->query($sql)->result();

		$staff_leave_categories=[];
		foreach($result as $key => $val){
			if($admin==1 && $val->leave_category_visibility!=2){
				$staff_leave_categories[]=$val;
			}else if($admin==0 && $val->leave_category_visibility != 1){
				$staff_leave_categories[] = $val;
			}
		}
		
		return $staff_leave_categories;
  	}

  	public function checkAlreadyTaken($id = null){
	    $staffId = $this->input->post('staff_id');
	    $selection_type = $this->input->post('selection_type');
	    $from_date = date('Y-m-d', strtotime($this->input->post('from_date')));
	    $to_date = date('Y-m-d', strtotime($this->input->post('to_date')));
	    $check = "";
	    if($id){
	        $check = "AND `id`!='$id'";
	    }
		$sql = "SELECT id, from_date, to_date, leave_for FROM `leave_v2_staff` WHERE `staff_id`=$staffId $check AND ((`from_date`<='$from_date' AND `to_date`>='$from_date') OR (`from_date`<='$to_date' AND `to_date`>='$to_date')) AND status!=3 and status !=4";

		$result = $this->db_readonly->query($sql)->result();

		//If the number of row is zero, return as leave not taken
		if(count($result) == 0){
	        return 1;
	    }

		//If the number of rows is not zero, then we need to check conditions!

		if ($selection_type == 'fullday') {
			return 0;
		}

		//checking whether current applied half is same sa previous half day
		// if(count($result)==1 && $selection_type==$result[0]->leave_type){
		// 	return 0;
		// }

		//At this point, staff is applying for half day. So, need to do additional checks!
		if (count($result) > 1) {
			//If number of leaves taken in those days are more than 1, then one more half-day cannot be taken!
			return 0;
		}

		if ($result[0]->leave_for == 'fullday') {
			return 0;
		} else {
			return 1;
		}
  	}

	public function checkAlreadyTaken_3_level($id = null){
		$staffId = $this->input->post('staff_id');
		$selection_type = $this->input->post('selection_type');
		$from_date = date('Y-m-d', strtotime($this->input->post('from_date')));
		$to_date = date('Y-m-d', strtotime($this->input->post('to_date')));
		$check = "";
		if($id){
			$check = "AND `id`!='$id'";
		}
		$sql = "SELECT id, from_date, to_date, leave_for FROM `leave_v2_staff` WHERE `staff_id`=$staffId $check AND ((`from_date`<='$from_date' AND `to_date`>='$from_date') OR (`from_date`<='$to_date' AND `to_date`>='$to_date')) AND final_status!=3 AND final_status!=4";

		$result = $this->db_readonly->query($sql)->result();

		//If the number of row is zero, return as leave not taken
		if(count($result) == 0){
			return 1;
		}

		//If the number of rows is not zero, then we need to check conditions!

		if ($selection_type == 'fullday') {
			return 0;
		}

		//checking whether current applied half is same sa previous half day
		// if(count($result)==1 && $selection_type==$result[0]->leave_type){
		// 	return 0;
		// }

		//At this point, staff is applying for half day. So, need to do additional checks!
		if (count($result) > 1) {
			//If number of leaves taken in those days are more than 1, then one more half-day cannot be taken!
			return 0;
		}

		if ($result[0]->leave_for == 'fullday') {
			return 0;
		} else {
			return 1;
		}
  	}

	public function checkAlreadyRequested($input){
		$leave_applied_for = $input['leave_applied_for'];
		$leave_year_id = $input['leave_v2_year_id'];

		$leave_category_id = 0;
		$leave_category_detail = $this->db_readonly->select("id")->from("leave_v2_category")->where("short_name", "CO")->where("status",1)->get()->row();
		if (!empty($leave_category_detail)) {
			$leave_category_id = $leave_category_detail->id;
		}

		if ($leave_category_id == 0) {
			return 4;
		}

		// select * from leave_v2_staff_quota where leave_v2_year_id=3 and staff_id=92;

		$leave_quota=$this->db_readonly->select("id")
		->from("leave_v2_staff_quota")
		->where("leave_v2_year_id",$leave_year_id)
		->where("staff_id",$leave_applied_for)
		->where("leave_category_id", $leave_category_id)
		->get()->row();

		if(empty($leave_quota)){
			return 4;
		}

		// Check whether Leave quota assigned to him or not
		
		// check for Full 1 day leave, i.e: It should accept 2 half day leaves or 1 full day leave
		$leave_applied_for_no_of_days = array_key_exists("consider_half_day", $input) ? 0.5 : 1;
		$worked_on = date('Y-m-d', strtotime($input['worked_on']));

		$earlier_leaves_taken=$this->db_readonly->select("sum(no_of_days) as total_no_of_days_leaves_taken")
		->from("leave_v2_comp_off")
		->where("leave_applied_for", $leave_applied_for)
		->where("worked_on",$worked_on)
		->where_in("status",[0,1]) // 0-> Pending, 1-> Approved
		->get()->result();

		// check for if the full day leave for that particular day is already exists
		if(!empty($earlier_leaves_taken) && isset($earlier_leaves_taken[0]->total_no_of_days_leaves_taken)){
			if($earlier_leaves_taken[0]->total_no_of_days_leaves_taken==1){
				return 2;
			}else if($earlier_leaves_taken[0]->total_no_of_days_leaves_taken == 0.5 && $leave_applied_for_no_of_days == 1){
				return 3;
			}
		}
		return 0;
	}


  	public function getHoliday($date){
        $sql = "SELECT `id` FROM `school_calender` WHERE `event_type`!=1 AND `event_type`!=4 AND (`from_date`='$date' OR `to_date`='$date' OR (`from_date`<='$date' AND `to_date`>='$date')) and `acad_year_id`='$this->yearId'";
        $query = $this->db->query($sql);
        return $query->num_rows();
    }

	public function getHolidayCountV2($staff_id){
		$this->yearId = $this->acad_year->getAcadYearId();

		// getting holidays from calenderV1
		$holidays_list_1 = $this->db_readonly->select("from_date, to_date")
			->from("school_calender")
			->where_in("event_type", [2, 3]) // 2 -> Holiday, 3 -> Holiday range
			->where_in("applicable_to", [1, 3]) //1 All staff, 3 All staffs and All parents
			->where("acad_year_id", $this->yearId)
			->get()->result();

		// get all staff types
		$all_staff_types = $this->settings->getSetting("staff_type");
		// get staff type
		$staff_details = $this->db_readonly->select("staff_type")
			->from("staff_master")
			->where("id", $staff_id)
			->get()->row();

		$staff_type_id = "";
		if (!empty($all_staff_types) && !empty($staff_details)) {
			$staff_type_id = $staff_details->staff_type;
		}

		// getting holidays from calenderV2
		$holidays_list_2 = $this->db_readonly->select("cal.from_date, cal.to_date")
			->from("calendar_events_v2 cal")
			->join("calendar_v2_master cm","cm.id=cal.calendar_v2_master_id")
			->join("calendar_events_v2_assigned cal_assi", "cal_assi.calendar_v2_master_id=cal.calendar_v2_master_id")
			->where("cal_assi.assigned_type", "STAFF")
			->where("cal_assi.assigned_staff_type", $staff_type_id)
			->where_in("cal.event_type", ["holiday", "holiday_range"])
			->where("cm.academic_year", $this->yearId)
			->get()->result();

		$holidays = array_merge($holidays_list_1, $holidays_list_2);

		if (empty($holidays)) {
			return [];
		}

		$holidayList = [];

		foreach ($holidays as $key => $hol) {
			if (strtotime($hol->from_date) == strtotime($hol->to_date)) {
				// Single-day holiday
				$holidayList[date('Y-m-d', strtotime($hol->from_date))] = "Holiday";
			} else {
				// Multi-day holiday
				$dates_from_date_range = $this->getDatesFromRange($hol->from_date, $hol->to_date);
				if (!empty($dates_from_date_range)) {
					foreach ($dates_from_date_range as $index => $date) {
						$holidayList[$date] = "Holiday";
					}
				}
			}
		}

		return $holidayList;
	}

  	public function save_leave_appication($input) {
		$staff_id = $input['staff_id'];
		$leave_v2_year_id = $input['leave_v2_year_id'];
		$leave_category = $input['leave_category'];
		$from_date = date('Y-m-d', strtotime($input['from_date']));
		$to_date = date('Y-m-d', strtotime($input['to_date']));
		$leaveFor = $input['selection_type'];
		$no_of_days = ($leaveFor == 'fullday') ? $input['noofdays'] : 0.5;

		// START: validating for leave
		$leave_year_info = $this->db->select("id, start_date, end_date")
			->from("leave_v2_year")
			->where("is_active", 1)
			->get()->row();

		// check acad year validition is it correct as expected
		if (empty($leave_year_info) || $leave_year_info->id != $leave_v2_year_id) {
			return 0;
		}

		// 1. Check if the applied from and to-date is between current active academic year
		if (strtotime($from_date) < strtotime($leave_year_info->start_date) || strtotime($to_date) > strtotime($leave_year_info->end_date)) {
			return 0;
		}

		// 2. Check if he is elligible to take leaves i.e: He has enough leave to take from

		$applied_leave_quota_details = $this->db->select("lq.total_quota,lq.used_quota,lc.has_quota")
			->from("leave_v2_staff_quota lq")
			->join("leave_v2_category lc", "lc.id=lq.leave_category_id")
			->where("lq.leave_category_id", $leave_category)
			->where("lq.staff_id", $staff_id)
			->where("lq.leave_v2_year_id", $leave_v2_year_id)
			->get()->row();

		if (empty($applied_leave_quota_details)) {
			return 0;
		}

		if ($applied_leave_quota_details->has_quota == 1) {
			$remaining_leave_quota = $applied_leave_quota_details->total_quota - $applied_leave_quota_details->used_quota;

			if ($no_of_days > $remaining_leave_quota) {
				return 0;
			}
		}
		// END: validating for leave

		$leaveDocArray=[];
		foreach($_FILES as $key => $val){
			$leave_evidence_file = $this->s3FileUpload($_FILES[$key]);
			$leaveDocArray[$key]=$leave_evidence_file['file_name'];
		}

	    $appliedBy = $this->authorization->getAvatarStakeHolderId();
	    $author = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');

	    $approvedBy = NULL;
	    $status = 0;
	    $status_str = "Pending";
	    if($author == 1 && $input["enable_leave_auto_approval_by_admin"] == 1) {
	        $approvedBy = $appliedBy;
	        $status_str = "Auto Approved";
	        $status = 2;
	    }
		
	    $data = array(
	        'staff_id' => $staff_id,
			'leave_year_id' => $leave_v2_year_id,
	        'leave_category_id' => $leave_category,
	        'leave_for' => $leaveFor,
	        'leave_filed_by' => $appliedBy,
	        'leave_approved_by' => $approvedBy,
	        'request_date' => date('Y-m-d'),
	        'from_date' => $from_date,
	        'to_date' => $to_date,
	        'noofdays' => $no_of_days,
	        'reason' => $input['reason'],
	        'status' => $status,
	        'last_modified_by' => $this->authorization->getAvatarId(),
			'source_convert_leave_id' => isset($input['source_convert_leave_id']) ? $input['source_convert_leave_id'] : ""
			// 'leave_type' =>$input['selection_type']
	    );

		$data=array_merge($data,$leaveDocArray);

	    $this->db->trans_start();
	    $is_leave_applied_complete=$this->db->insert('leave_v2_staff', $data);
	    $current_leave_id = $this->db->insert_id();

	    // $this->_insertSubstituteData($current_leave_id, $staff_id, $from_date, $to_date, $leaveFor);
		
	    //update quota
		if ($is_leave_applied_complete) {
			// $sql = "update leave_v2_staff_quota set used_quota=used_quota+$no_of_days where leave_category_id=$leave_category and staff_id=$staff_id and leave_v2_year_id=$leave_v2_year_id";
			// $this->db->query($sql);

			$update_quota = $this->db
				->where("leave_category_id", $leave_category)
				->where("staff_id", $staff_id)
				->where("leave_v2_year_id", $leave_v2_year_id)
				->update("leave_v2_staff_quota", ["used_quota" => $applied_leave_quota_details->used_quota + $no_of_days]);
		}

	    $this->db->trans_complete();
	    if($this->db->trans_status() === FALSE) {
	        $this->db->trans_rollback();
	        return 0;
	    }
	    $this->db->trans_commit();
	    return 1;
  	}

	public function insert_comp_leave_details($input) {
		//echo "<pre>"; print_r(input->post('requested_by')); die();
		$worked_on = date('Y-m-d', strtotime($this->input->post('worked_on')));
		$comments = $this->input->post('comments');
		$leave_applied_for = $this->input->post('leave_applied_for');
		$requested_on = date('Y-m-d', strtotime($this->input->post('requested_on')));
		$requested_by = $this->authorization->getAvatarStakeHolderId();;
		$status = 0;
		$approved_on = NULL;
		$approved_by = NULL;
		$approval_comment = NULL;
		$leave_year_id = $this->input->post('leave_v2_year_id');

		// $leave_category_id=$query;
		//echo "<pre>"; print_r($leave_year_id);die();

		$no_of_days= array_key_exists("consider_half_day", $input) ? 0.5 : 1;

		$data = array(
			'worked_on' => $worked_on,
			'comments' => $comments,
			'requested_on' => $requested_on,
			'requested_by' => $requested_by,
			'status' => $status,
			'approved_on' => $approved_on,
			'approved_by' => $approved_by,
			'approval_comment' => $approval_comment,
			'leave_year_id' => $leave_year_id,
			'no_of_days' => $no_of_days,
			'leave_applied_for' => $leave_applied_for
		);

		//return $this->db->insert_batch('asset_tags',$data_insert);

		// echo "<pre>"; print_r($data); die();

		$this->db->trans_start();
	    $this->db->insert('leave_v2_comp_off', $data);
		//echo "<pre>"; print_r($a);

		//update quota
	    // $sql = "update leave_v2_staff_quota set total_quota=total_quota+1 where leave_category_id=$leave_category and staff_id=$requested_by and leave_v2_year_id=$leave_v2_year_id";
	    // $this->db->query($sql);

	    $this->db->trans_complete();
	    if($this->db->trans_status() === FALSE) {
	        $this->db->trans_rollback();
	        return 0;
	    }
	    $this->db->trans_commit();
	    return 1;
	}

	public function getCompLeaveDetails($staffId,$leaves_filed_type){
	    $this->db_readonly->select("leave_v2_comp_off.approved_by, leave_v2_comp_off.requested_on, leave_v2_comp_off.worked_on, leave_v2_comp_off.comments, leave_v2_comp_off.status, leave_v2_comp_off.approval_comment, leave_v2_comp_off.no_of_days, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name");
		$this->db_readonly->from('leave_v2_comp_off');
		$this->db_readonly->join('staff_master', 'staff_master.id=leave_v2_comp_off.leave_applied_for');
		
		if($leaves_filed_type==1){
			$this->db_readonly->where('leave_v2_comp_off.leave_applied_for', $staffId);
		}else{
			$this->db_readonly->where('leave_v2_comp_off.requested_by', $staffId);
		}

		$result = $this->db_readonly->order_by("leave_v2_comp_off.id","desc")->get()->result();

		foreach($result as $key => $val){
			$val->no_of_days_in_words=$val->no_of_days==0.5 ? "Half Day" : "Full day";
			if($val->status==0){
				$val->approved_by_name="-";
			}else{
				if($val->approved_by==0){
					$val->approved_by_name = "Admin";
				}else{
					$val->approved_by_name=$this->db_readonly->select("CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name")
					->from("staff_master sm")
					->where("id",$val->approved_by)
					->get()->row()->staff_name;
				}
			}
		}

		return $result;
	}

	public function getCompCountOfLeaves($id, $val){
	    if($val == 'total'){
	        $sql = "SELECT COUNT(`id`) as `total` FROM `leave_v2_comp_off` WHERE `leave_applied_for`='$id'";
	    } elseif($val == 'approved'){
	        $sql = "SELECT COUNT(`id`) as `total` FROM `leave_v2_comp_off` WHERE `leave_applied_for`='$id' AND `status`=1";
	    }
	    return $this->db->query($sql)->row();
  	}

	public function getCompDetails($staffId, $type, $data){
		$this->db_readonly->select("leave_v2_comp_off.id, leave_v2_comp_off.comments as comments, leave_v2_comp_off.status as status, leave_v2_comp_off.approval_comment as approval_comment, leave_v2_comp_off.leave_year_id as leave_year_id, leave_v2_comp_off.no_of_days as no_of_days,leave_v2_comp_off.leave_applied_for as leave_applied_for,  DATE_FORMAT(leave_v2_comp_off.requested_on, '%d-%m-%Y') as requested_on, DATE_FORMAT(leave_v2_comp_off.worked_on, '%d-%m-%Y') as worked_on_date, staff_master.id as staff_id ,staff_master.first_name, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name");
		$this->db_readonly->from('leave_v2_comp_off');
		$this->db_readonly->join('staff_master', 'staff_master.id=leave_v2_comp_off.leave_applied_for');
		if($type==2) {
            $this->db_readonly->where("staff_master.reporting_manager_id=$staffId");
        }

		if(!empty($data["status"])){
			$this->db_readonly->where_in("leave_v2_comp_off.status", $data["status"]);
		}

		return $this->db_readonly->order_by("leave_v2_comp_off.id", "desc")->get()->result();
	}

	public function getCompensatoryLeaveQuotaDetails($staff_id,$leave_v2_year_id){
		$leave_category_id=0;
		$leave_category_detail=$this->db_readonly->select("id")->from("leave_v2_category")->where("short_name","CO")->get()->row();
		if(!empty($leave_category_detail)){
			$leave_category_id=$leave_category_detail->id;
		}

		if($leave_category_id==0){
			return 0;
		}

		$quota=$this->db_readonly->select("total_quota,used_quota")
		->from("leave_v2_staff_quota")
		->where("staff_id",$staff_id)
		->where("leave_v2_year_id",$leave_v2_year_id)
		->where("leave_category_id",$leave_category_id)
		->get()->row();

		return $quota->total_quota-$quota->used_quota;
	}

	public function saveCompLeaveStatus($leave_id, $old_status, $new_status, $description, $staffId,$leave_applied_for,$no_of_days, $leave_v2_year_id) {
		$leave_category_id = 0;
		$leave_category_detail = $this->db_readonly->select("id")->from("leave_v2_category")->where("short_name", "CO")->get()->row();

		if (!empty($leave_category_detail)) {
			$leave_category_id = $leave_category_detail->id;
		}

		if ($leave_category_id == 0) {
			return 0;
		}

        $this->db->trans_start();
        if(($old_status == 0 or $old_status == 2) and $new_status == 1) {
			//add quota
	    	 $sql = "update leave_v2_staff_quota set total_quota=total_quota+$no_of_days where leave_category_id=$leave_category_id and staff_id=$leave_applied_for and leave_v2_year_id=$leave_v2_year_id";
	    	 $this->db->query($sql);
        }
		elseif($old_status == 1 and $new_status == 2){
			// subtract quota
			$sql = "update leave_v2_staff_quota set total_quota=total_quota-$no_of_days where leave_category_id=$leave_category_id and staff_id=$leave_applied_for and leave_v2_year_id=$leave_v2_year_id";
	    	$this->db->query($sql);
		}

		$data = array(
			'status' => $new_status,
			'approved_on' => date('Y-m-d'),
			'approved_by' => $staffId,
			'approval_comment' => $description
			
		);

        //$data = array('status' => $status, 'description' => $description);
        $this->db->where('id', $leave_id)->update('leave_v2_comp_off', $data);
        $this->db->trans_complete();
	    if($this->db->trans_status() === FALSE) {
	        $this->db->trans_rollback();
	        return 0;
	    }
	    $this->db->trans_commit();
	    return 1;
    }

	// Not being used anywhere in comp-off
	// public function saveRejectCompLeaveStatus($leave_id, $status, $description, $staffId,$requested_by) {
    // 	$leave_v2_year_id = $this->db->select('id')->where('is_active', 1)->get('leave_v2_year')->row()->id;
    //     $this->db->trans_start();
    //     if($status == 1) {

	// 		//update quota

	// 		$sql = "SELECT `id` FROM `leave_v2_category` WHERE `short_name`='CO'";
    //     	$query = $this->db->query($sql)->row();
	// 		$leave_category=$query->id;

	//     	 $sql = "update leave_v2_staff_quota set total_quota=total_quota-1 where leave_category_id=$leave_category and staff_id=$requested_by and leave_v2_year_id=$leave_v2_year_id";
	//     	 $this->db->query($sql);
    //     }

	// 	$data = array(
	// 		'status' => $status,
	// 		'approved_on' => date('Y-m-d'),
	// 		'approved_by' => $staffId,
	// 		'approval_comment' => $description
			
	// 	);

    //     //$data = array('status' => $status, 'description' => $description);
    //     $this->db->where('id', $leave_id)->update('leave_v2_comp_off', $data);
    //     $this->db->trans_complete();
	//     if($this->db->trans_status() === FALSE) {
	//         $this->db->trans_rollback();
	//         return 0;
	//     }
	//     $this->db->trans_commit();
	//     return 1;
    // }

	public function getCompLeaves($val){
		//echo $val; die();
	    if($val == 'total'){
	        $sql = "SELECT COUNT(`id`) as `total` FROM `leave_v2_comp_off`";
	    } elseif($val == 'approved'){
	        $sql = "SELECT COUNT(`id`) as `total` FROM `leave_v2_comp_off` WHERE `status`=1";
	    }
	    return $this->db->query($sql)->row();
  	}

	public function cancelCompLeave($leave_id, $reason, $staffId) {
	//status - 3 for cancelled leave
	return $this->db->where('id', $leave_id)->update('leave_v2_comp_off', ['status' => 3, 'approved_by' => $staffId, 'approved_on' => date('Y-m-d'), 'approval_comment' => $reason]);
	}

	public function getCompLeaveByDate($from_date, $to_date, $staff_ids, $staff_status_type, $leave_year_id) {
    	$sql = "select sm.id as staff_id, sm.reporting_manager_id,CONCAT(ifnull(sm.first_name, ''),' ', ifnull(sm.last_name, '')) as staff_name, sm.designation, COUNT(co.id) as total_applied, COUNT(CASE WHEN co.status=0 THEN 1 ELSE NULL END) as pending, COUNT(CASE WHEN co.status=1 THEN 1 ELSE NULL END) as approved, COUNT(CASE WHEN co.status=2 THEN 1 ELSE NULL END) as rejected 
    			from staff_master sm
				left join leave_v2_comp_off co on co.requested_by=sm.id and requested_on>='$from_date' AND requested_on<='$to_date' 
				where sm.is_primary_instance=1 ";

    	if(!empty($staff_ids)) {
    		$ids = implode(",", $staff_ids);
    	    $sql .= " AND staff_id in ($ids)";
    	}

		if ($staff_status_type == 2) {
			// Include all staff with status = 2
			$sql .= " AND sm.status = 2";
		} elseif ($staff_status_type == 4) {
			// Include only those with quota
			$sql .= " AND sm.status = 4";
			$sql .= " AND EXISTS (
						SELECT 1 FROM leave_v2_staff_quota q 
						WHERE q.staff_id = sm.id and q.leave_v2_year_id=$leave_year_id
					)";
		}

		$sql .= " group by sm.id order by sm.first_name";

        $leaves = $this->db->query($sql)->result();

		return $leaves;
    }

	public function getCompLeavesDetails($staff_id, $start_date, $end_date) {
		$sql = "select ls.id as leave_id, DATE_FORMAT(ls.worked_on, '%d-%m-%Y') as worked_on, DATE_FORMAT(ls.requested_on, '%d-%m-%Y') as requested_on,  ls.requested_by, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name, ls.status, ls.comments, ls.approval_comment 
		from leave_v2_comp_off ls  
    	left join staff_master sm on sm.id=ls.leave_applied_for 
    	where ls.requested_on>='$start_date' AND ls.requested_on<='$end_date' 
    	AND ls.leave_applied_for=$staff_id 
    	order by ls.worked_on";

    	$leaves = $this->db->query($sql)->result();
    	// echo '<pre>'; print_r($this->db->last_query()); die();
    	return $leaves;
    }

	public function getSingleCompData($leave_id){
		$this->db_readonly->select("lcf.*, DATE_FORMAT(lcf.worked_on, '%d-%m-%Y') as worked_on, sm.id as staff_id ,sm.first_name, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name");
		$this->db_readonly->from('leave_v2_comp_off lcf');
		$this->db_readonly->join('staff_master sm', 'sm.id=lcf.leave_applied_for');
		$this->db_readonly->where('lcf.id',$leave_id);
		return $this->db_readonly->get()->row();
    }


  	private function _insertSubstituteData($id, $staffId, $from_date, $to_date, $leaveFor){
        for ($d = $from_date; $d <= $to_date;) {
            $temp = $this->__getStartEndPeriod($d, $leaveFor);
            $start_period = $temp->start_period;
            $end_period = $temp->end_period;
            $sub_data[] = array(
                'leave_id'=> $id,
                'staff_id'=> $staffId,
                'substitute_date'=> $d,
                'from_period'=>$start_period,
                'to_period'=>$end_period,
                'substitution_added'=>0
            );
            $d = date("Y-m-d", strtotime($d." +1 day"));
        }
        return $this->db->insert_batch('leave_v2_staff_substitute',$sub_data);
    }

    private function __getStartEndPeriod($date, $leaveFor) {
        //Get the day of the week
        //Get the start and end periods of that weekday

        $weekDay = date("N",strtotime($date));
        $result = $this->db->select('week_day, max(period_seq_excl_break) as end_period')
            ->from('timetable_template_periods')
            ->group_by('week_day')
            ->where('week_day',$weekDay)
            ->get()->row();

        $result->start_period = 1;

        if($leaveFor == 'morning') $result->end_period = $this->settings->getSetting('timetable_half_day_period');
        if($leaveFor == 'noon') $result->start_period = $this->settings->getSetting('timetable_half_day_period') + 1;         			

        return $result;
    }

  	public function getStaffData($staffId) {
    	return $this->db->select("id, concat(ifnull(first_name,''),' ', ifnull(last_name,'')) as staffName")
          ->from('staff_master')
          ->where('id',$staffId)
          ->get()->row();
  	}

  	public function getLeavesData($staffId, $hasApplyLeave=1,$statusId,$staffLoggedInId,$staff_status_type){
        $today = date('Y-m-d');
        $this->db_readonly->select("ls.description,ls.id, lc.name, lc.name as leave_type, lc.short_name as short_name, DATE_FORMAT(ls.request_date,'%d-%m-%Y') as request_date, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays,ls.reason,ls.status, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name,ls.staff_id,ls.leave_filed_by, ls.cancel_reason, ifnull(lvy.name, '-') as leave_year_name, DATE_FORMAT(ls.created_on,'%h:%i:%s %p') as request_time")
        ->from('leave_v2_staff ls')
        ->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
        ->join('leave_v2_year lvy', 'ls.leave_year_id=lvy.id','left')
        ->join('staff_master sm', 'ls.staff_id=sm.id','left');
        
		// echo $hasApplyLeave; die();
		if($hasApplyLeave == 2){
			if($staffId==0){
				$this->db_readonly->where("(sm.reporting_manager_id=$staffLoggedInId)");
			}else{
				$this->db_readonly->where("ls.staff_id=$staffId");
			}
			// $this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		} else if($hasApplyLeave == 0) {
			$this->db_readonly->where("ls.staff_id=$staffId");
		}else if($hasApplyLeave == 1 && $staffId!=0){
			$this->db_readonly->where("ls.staff_id=$staffId");
		}

		if ($staff_status_type) {
			$this->db_readonly->where("sm.status", $staff_status_type);
		}

		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		// if($val){
		// 	$val==3 && $this->db_readonly->where("ls.status is not null",NULL, FALSE);
		// 	$val==2 && $this->db_readonly->where("ls.leave_filed_by",$staffId);
		// 	$val==1 && $this->db_readonly->where("ls.status",0);
		// }else{
			$this->db_readonly->where_in("ls.status", $statusId);
		//}

        $this->db_readonly->order_by('ls.request_date', 'desc');
        $result = $this->db_readonly->get()->result();

        $subData = $this->_getSubstituteStatus();
        foreach ($result as $key=>$val) {
			$val->request_time = date('h:i:s A', strtotime(local_time($val->request_time)));
			if (isset($subData[$val->id])) $val->sub = $subData[$val->id];
          $result[$key]->date_passed = 0;
          if(date('Y-m-d', strtotime($val->from_date)) < $today) {
            $result[$key]->date_passed = 1;
          }
        }

        return $result;
    }


	public function getLeaveData($leave_id) {
		$result=$this->db_readonly->select("ls.final_status, ls.id as leave_id, ls.leave_plan,ls.leave_evidence,description,ls.approval_mode, lc.name, lc.name as leave_type, lc.short_name, ls.leave_approved_by, ls.approved_by_2, ls.approved_by_3, ls.approve_status_2, ls.approve_status_3, ls.status,
		DATE_FORMAT(request_date,'%a %b %D') as request_date, 
		DATE_FORMAT(from_date,'%a %b %D') as from_date, 
		DATE_FORMAT(to_date,'%a %b %D') as to_date, 
		noofdays, reason, ls.status, staff_id, leave_filed_by")
		->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
		->where('ls.id', $leave_id)
		->get('leave_v2_staff ls')
		->row();
		
		$result->leave_plan_url = $result->leave_plan ? $this->filemanager->getFilePath($result->leave_plan) : "";
		$result->leave_evidence_url = $result->leave_evidence ? $this->filemanager->getFilePath($result->leave_evidence) : "";
		return $result;
	}

	public function getCompData($leave_id) {

		$this->db_readonly->select("leave_v2_comp_off.*, DATE_FORMAT(leave_v2_comp_off.worked_on, '%d-%m-%Y') as worked_on_date,staff_master.id as staff_id ,staff_master.first_name, CONCAT(ifnull(first_name,''),' ',ifnull(last_name,'')) as staff_name");
		$this->db_readonly->from('leave_v2_comp_off');
		$this->db_readonly->join('staff_master', 'staff_master.id=leave_v2_comp_off.leave_applied_for');
		$this->db_readonly->where('leave_v2_comp_off.id', $leave_id);

		return $this->db_readonly->get()->row();
    }

    public function saveLeaveStatus($leave_id, $status, $description) {
        $this->db->trans_start();
        if($status == 3) {
			//If leave is rejected, leave should be subtracted from the used quota.
			$this->substractApplieLeavesFromQuota($leave_id);
        }
		
		if($status==1){
			$data = array('status' => $status, 'description' => $description,'leave_approved_by'=>$this->authorization->getAvatarStakeHolderId(),'approved_date_1'=> date('Y-m-d h:i:s'));
		}else if($status == 3){
			$data = array('status' => $status, 'description' => $description, 'leave_cancelled_by' => $this->authorization->getAvatarStakeHolderId(),'leave_cancelled_datetime'=> date('Y-m-d h:i:s'));
		}

        $this->db->where('id', $leave_id)->update('leave_v2_staff', $data);
        $this->db->trans_complete();
	    if($this->db->trans_status() === FALSE) {
	        $this->db->trans_rollback();
	        return 0;
	    }
	    $this->db->trans_commit();
	    return 1;
    }

    private function _getSubstituteStatus(){
        $this->db_readonly->select("leave_id, substitution_added");
        $data = $this->db_readonly->get('leave_v2_staff_substitute')->result();
        //echo '<pre>';print_r($data);die();
        $subdata = array();
        foreach($data as $key=>$val){
            $subdata[$val->leave_id] = $val->substitution_added;
        }
        return $subdata;        
    }

    public function getStaffLeaveByDate($from_date, $to_date, $staff_ids,$leave_category_ids, $leave_year_id=0, $staff_status_type) {
		$is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");

		$join_conditions = "ls.staff_id = sm.id";

		// have a look to this conditon
		if (strtotime($from_date) == strtotime($to_date)) {
			$join_conditions .= " AND '$from_date'>=ls.from_date AND '$to_date'<=ls.to_date";
		} else {
			$join_conditions .= " AND ls.from_date >= '$from_date' AND ls.to_date <= '$to_date'";
		}

		if ($leave_year_id > 0) {
			$join_conditions .= " AND ls.leave_year_id = $leave_year_id";
		}

		if (!empty($leave_category_ids)) {
			$leave_category_ids = implode(",", $leave_category_ids);
			$join_conditions .= " AND ls.leave_category_id IN ($leave_category_ids)";
		}

		// status
		if((int)$is_multi_level_leave_enabled==1){
			// In case of multi level approval
			$sql = "select sm.id as staff_id, sm.reporting_manager_id,CONCAT(ifnull(sm.first_name, ''),' ', ifnull(sm.last_name, '')) as staff_name, sm.designation, SUM(noofdays) as total_applied, SUM(CASE WHEN ls.final_status=0 THEN noofdays ELSE 0 END) as pending, SUM(CASE WHEN ls.final_status=1 OR ls.final_status=2 THEN noofdays ELSE 0 END) as approved, SUM(CASE WHEN ls.final_status=3 THEN noofdays ELSE 0 END) as rejected, SUM(CASE WHEN ls.final_status=4 THEN noofdays ELSE 0 END) as cancelled 
			from staff_master sm
			left join leave_v2_staff ls on $join_conditions ";
		}else{
			// In case of single level approval
			$sql = "select sm.id as staff_id, sm.reporting_manager_id,CONCAT(ifnull(sm.first_name, ''),' ', ifnull(sm.last_name, '')) as staff_name, sm.designation, SUM(noofdays) as total_applied, SUM(CASE WHEN ls.status=0 THEN noofdays ELSE 0 END) as pending, SUM(CASE WHEN ls.status=1 OR ls.status=2 THEN noofdays ELSE 0 END) as approved, SUM(CASE WHEN ls.status=3 THEN noofdays ELSE 0 END) as rejected, SUM(CASE WHEN ls.status=4 THEN noofdays ELSE 0 END) as cancelled 
			from staff_master sm
			left join leave_v2_staff ls on $join_conditions ";
		}

		$sql .= " where sm.is_primary_instance = 1 ";

		if (!empty($staff_ids)) {
			$ids = implode(",", $staff_ids);
			$sql .= " AND staff_id in ($ids)";
		}

		// filtering Staff based on staff status
		if ($staff_status_type == 2) {
			$sql .= " AND sm.status = 2 ";
		} elseif ($staff_status_type == 4) {
			$sql .= " AND sm.status = 4 ";
			$sql .= " AND EXISTS (
				SELECT 1 FROM leave_v2_staff_quota q 
				WHERE q.staff_id = sm.id ";

			if($leave_year_id){
				$sql .= " AND q.leave_v2_year_id = $leave_year_id ";
			}

			$sql .= " ) ";
		}

		$sql .= " group by sm.id order by sm.first_name";

        $leaves = $this->db_readonly->query($sql)->result();

        return $leaves;
    }

    public function getStaffLeaveDetails($staff_id, $start_date, $end_date, $leave_category_ids=[], $leave_year_id=0) {
		$sql = "select ls.id as leave_id, noofdays, DATE_FORMAT(ls.from_date, '%d-%m-%Y') as from_date, DATE_FORMAT(ls.to_date, '%d-%m-%Y') as to_date,  DATE_FORMAT(ls.created_on, '%d-%m-%Y') as request_date, ls.leave_filed_by, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name, ls.status, ls.final_status, lc.name, lc.short_name, ls.approval_mode, ls.leave_approved_by, ls.approved_by_2, ls.approved_by_3, CONCAT(ifnull(sm1.first_name, ''),' ',ifnull(sm1.last_name,'')) as first_approver_name, CONCAT(ifnull(sm2.first_name, ''),' ',ifnull(sm2.last_name,'')) as second_approver_name, CONCAT(ifnull(sm3.first_name, ''),' ',ifnull(sm3.last_name,'')) as third_approver_name, ls.cancel_reason, CONCAT(ifnull(sm4.first_name, ''),' ',ifnull(sm4.last_name,'')) as leave_cancelled_by_name, ls.leave_cancelled_by, ls.approve_status_2, ls.approve_status_3
					from leave_v2_staff ls
					join leave_v2_category lc on lc.id=ls.leave_category_id
					left join staff_master sm on sm.id=ls.leave_filed_by 
					left join staff_master sm1 on sm1.id=ls.leave_approved_by
					left join staff_master sm2 on sm2.id=ls.approved_by_2
					left join staff_master sm3 on sm3.id=ls.approved_by_3
					left join staff_master sm4 on sm4.id=ls.leave_cancelled_by";

		if (strtotime($start_date) == strtotime($end_date)) {
			// for single date
			$sql .= " where '$start_date'>=from_date AND '$end_date'<=to_date AND ls.staff_id=$staff_id";
		} else {
			// for date range
			$sql .= " where ls.from_date>='$start_date' AND ls.to_date<='$end_date' AND ls.staff_id=$staff_id";
		}

		if ($leave_year_id > 0) {
			$sql .= " and ls.leave_year_id=$leave_year_id ";
		}

		if(!empty($leave_category_ids)){
			$leave_category_ids = implode(",", $leave_category_ids);
			$sql .= " AND ls.leave_category_id in ($leave_category_ids)";
		}

		$sql .= " order by ls.request_date desc";
    	$leaves = $this->db->query($sql)->result();

		$is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
		foreach ($leaves as $key => $val) {
			if ((int) $is_multi_level_leave_enabled == 1) {
				// check for cancell and check for rejected
				if ($val->final_status == 3 || $val->final_status == 4) {
					if($val->status == $val->final_status){
						$val->leave_cancelled_by = $val->leave_approved_by;
						$val->leave_cancelled_by_name = $val->first_approver_name;
					}else if($val->approve_status_2 == $val->final_status){
							$val->leave_cancelled_by = $val->approved_by_2;
							$val->leave_cancelled_by_name = $val->second_approver_name;
					}else if($val->approve_status_3 == $val->final_status){
							$val->leave_cancelled_by = $val->approved_by_3;
							$val->leave_cancelled_by_name = $val->third_approver_name;
					}
				}

				// setting-up final status to status
				$val->status = $val->final_status;
			}else{
				// for single level approval
				if($val->status==3 || $val->status == 4){
					// if leave is rejected
					$val->leave_cancelled_by = $val->leave_approved_by;
					$val->leave_cancelled_by_name = $val->first_approver_name;
				}
				// keep same if the leace is cancelled
			}
		}
    	return $leaves;
    }

	public function getLeavesStatistics($staffId,$leave_year_id){
		$leaves=$this->db_readonly->select("c.name as category_name, c.short_name, c.has_quota, q.total_quota, q.used_quota")
		->from("leave_v2_category c")
		->join("leave_v2_staff_quota q","q.leave_category_id=c.id")
		->where("staff_id",$staffId)
		->where("leave_v2_year_id",$leave_year_id)
		->get()->result();

		foreach($leaves as $key => $val){
			$val->remaining_quota=$val->has_quota ? $val->total_quota-$val->used_quota : 'Unlimited';
		}

		return $leaves;
	}

    public function cancelLeave($leave_id, $reason) {
      $leave = $this->db->select('noofdays, leave_category_id, staff_id, leave_year_id')->where('id', $leave_id)->get('leave_v2_staff')->row();
      $no_of_days = $leave->noofdays;
      $leave_category_id = $leave->leave_category_id;
      $staff_id = $leave->staff_id;
	  $leave_v2_year_id = $leave->leave_year_id;

      $this->db->trans_start();

		$used_quota = $this->db_readonly->select("used_quota")
		->from("leave_v2_staff_quota")
		->where("leave_v2_year_id", $leave_v2_year_id)
		->where("staff_id", $staff_id)
		->where("leave_category_id", $leave_category_id)
		->get()->row()->used_quota;

		if($used_quota < $no_of_days){
			return 0;
		}

      //update quota
      $sql = "update leave_v2_staff_quota set used_quota=used_quota-$no_of_days where leave_category_id=$leave_category_id and staff_id=$staff_id and leave_v2_year_id=$leave_v2_year_id";
      $this->db->query($sql);

      //status - 4 for cancelled leave
		$is_enabled_mult_level_approval_mode=$this->settings->getSetting("enable_multi_level_leave_approver_mode");
		if ($is_enabled_mult_level_approval_mode) {
			$this->db->where('id', $leave_id)->update('leave_v2_staff', ['leave_cancelled_by'=>$this->authorization->getAvatarStakeHolderId(),'leave_cancelled_datetime'=>date('Y-m-d h:i:s'),'final_status' => 4, 'cancel_reason' => $reason]);
		} else {
			$this->db->where('id', $leave_id)->update('leave_v2_staff', ['leave_cancelled_by' => $this->authorization->getAvatarStakeHolderId(), 'leave_cancelled_datetime' => date('Y-m-d h:i:s'),'status' => 4, 'cancel_reason' => $reason]);
		}

      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      }
      $this->db->trans_commit();
      return 1;

    }

    public function activateLeaveYear($leave_year_id) {
      $this->db->trans_start();
      
      $this->db->where('is_active', 1)->update('leave_v2_year', ['is_active' => 0]);
      $this->db->where('id', $leave_year_id)->update('leave_v2_year', ['is_active' => 1]);
      
      $this->db->trans_complete();
      if($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          return 0;
      }
      $this->db->trans_commit();
      return 1;
    }

    public function saveLeaveYear() {
      return $this->db->insert('leave_v2_year', $_POST);
    }

    public function getQuotaUsage($staff_id, $leave_v2_year_id=0) {
		if (!$leave_v2_year_id) {
			$row = $this->db_readonly->select('id')->where('is_active', 1)->get('leave_v2_year')->row();
			if ($row) {
				$leave_v2_year_id = $row->id;
			} else {
				return [];
			}
		}

      	return $this->db_readonly->select("lsq.id, lsq.staff_id, lsq.leave_category_id, lsq.total_quota, lsq.used_quota, lsq.quota_carried_to_next_year, lc.name, lc.short_name, lc.leave_type, lc.has_quota, lc.can_be_carried")->from('leave_v2_staff_quota lsq')->join('leave_v2_category lc', 'lc.id=lsq.leave_category_id')->where('lsq.leave_v2_year_id', $leave_v2_year_id)->where('lsq.staff_id', $staff_id)->get()->result();
    }

	public function get_staff_list(){
		return $this->db_readonly->select("*")->order_by('first_name')->get("staff_master")->result();
	}

	public function get_status_list(){
		return $this->db_readonly->select("id,status")->group_by("status")->order_by("status")->get("leave_v2_staff")->result();
	}

    public function getLeaveBalance($leave_year_id, $staff_status_type, $staff_type_id) {
    	$sql = "SELECT CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, sm.id as staff_id, sq.leave_category_id, sq.total_quota, sq.used_quota, cat.has_quota, cat.leave_type 
			from staff_master sm 
			left join leave_v2_staff_quota sq on sm.id=sq.staff_id and sq.leave_v2_year_id=$leave_year_id
			left join leave_v2_category cat on cat.id=sq.leave_category_id and cat.status= 1 
			where sm.is_primary_instance=1";

		/// Filtering by staff status
		if ($staff_status_type == 2) {
			$sql .= " AND sm.status = 2";
		} elseif ($staff_status_type == 4) {
			$sql .= " AND sm.status = 4";
			$sql .= " AND EXISTS (
						SELECT 1 FROM leave_v2_staff_quota q 
						WHERE q.staff_id = sm.id 
						AND q.leave_v2_year_id = $leave_year_id
					 )";
		}

		if($staff_type_id!=-1){
			$sql .= " AND sm.staff_type = $staff_type_id";
		}

		$sql .=" order by sm.first_name";

		$result = $this->db_readonly->query($sql)->result();

		$staff = [];
		foreach ($result as $key => $res) {
			$staff_id = $res->staff_id;
			if(!array_key_exists($staff_id, $staff)) {
				$staff[$staff_id] = [];
				$staff[$staff_id]['id'] = $staff_id;
				$staff[$staff_id]['name'] = $res->staff_name;
				$staff[$staff_id]['categories'] = [];
			}
			$staff[$staff_id]['categories'][$res->leave_category_id] = array(
				'total_quota' => $res->total_quota,
				'used_quota' => $res->used_quota,
				'has_quota' => $res->has_quota,
				'leave_type' => $res->leave_type
			);
		}

		$stf = [];
		foreach ($staff as $key => $value) {
			$stf[] = $value;
		}
		return $stf;
    }

	private  function getDatesFromRange($start, $end, $format = 'Y-m-d'){
        // Declare an empty array
        $array = array();
        // Variable that store the date interval
        // of period 1 day
        $interval = new DateInterval('P1D');
        $realEnd = new DateTime($end);
        $realEnd->add($interval);
        $period = new DatePeriod(new DateTime($start), $interval, $realEnd);
        
        // Use loop to store date into array
        foreach($period as $date){
            $array[] = $date->format($format);
        }
        // Return the array elements
        return $array;
    }

	private function getAllStaffLeaves($duration_from_date, $duration_to_date, $staff_leaves_data){
		// echo 'From Date: '.$duration_from_date.' ';
		// echo 'To Date: ' . $duration_to_date . ' '; die();

		$formatted_staff_leaves = [];

		if(empty($duration_from_date) || empty($duration_to_date) || empty($staff_leaves_data)) return $formatted_staff_leaves;

		$leave_taken_dates = $this->getDatesFromRange($duration_from_date, $duration_to_date);
		// format the above data,
		if (!empty($staff_leaves_data) && !empty($leave_taken_dates)) {
			// Iterate on dates
			foreach ($leave_taken_dates as $index => $leave_date) { // date in 'Y-M-D'
				foreach ($staff_leaves_data as $key => $leave_obj) {
					if ($leave_obj->used_quota <= 0)
						continue;

					if (strtotime($leave_date) >= strtotime($leave_obj->from_date) && strtotime($leave_date) <= strtotime($leave_obj->to_date)) {
						if ((strtotime($leave_date) == strtotime($leave_obj->from_date) && strtotime($leave_date) == strtotime($leave_obj->to_date))) {
							if (!array_key_exists($leave_obj->staff_id, $formatted_staff_leaves) || !array_key_exists($leave_obj->leave_category_id, $formatted_staff_leaves[$leave_obj->staff_id])) {
								// Insert leave object
								$formatted_staff_leaves[$leave_obj->staff_id][$leave_obj->leave_category_id] = $leave_obj;
							} else {
								// Update leave object
								$formatted_staff_leaves[$leave_obj->staff_id][$leave_obj->leave_category_id]->used_quota += $leave_obj->used_quota;
							}
						} else {
							$leave_duration = 0;
							if (strtotime($leave_date) >= strtotime($leave_obj->from_date) && strtotime($leave_date) < strtotime($leave_obj->to_date)) {
								$leave_duration = 1;
								// add full day leave
							} else if (strtotime($leave_date) == strtotime($leave_obj->to_date)) {
								if (fmod($leave_obj->used_quota, 1) == 0) {
									// add full day leave
									$leave_duration = 1;
								} else {
									// add half day leave
									$leave_duration = 0.5;
								}
							}

							if (!array_key_exists($leave_obj->staff_id, $formatted_staff_leaves) || !array_key_exists($leave_obj->leave_category_id, $formatted_staff_leaves[$leave_obj->staff_id])) {
								// insert leave object
								$leave_obj->used_quota = $leave_duration;
								$formatted_staff_leaves[$leave_obj->staff_id][$leave_obj->leave_category_id] = $leave_obj;
							} else {
								// update leave object
								$formatted_staff_leaves[$leave_obj->staff_id][$leave_obj->leave_category_id]->used_quota += $leave_duration;
							}
						}
					}
				}
			}
		}
		return $formatted_staff_leaves;
	}
    
	public function getLeaveMonthBalance($monthyear, $ly_id, $staffStatus, $selectedDateRangeType, $new_payroll_schedules_id){
		$leave_year_ids = [];
		$financial_start_date = '';
		$financial_end_date = '';

		if ($selectedDateRangeType == "payroll_monthly") {
			$get_financial_year_duration = $this->db_readonly->select("lop_start_date as start_date, lop_end_date as end_date")
				->from("new_payroll_schedules")
				->where("id", $new_payroll_schedules_id)
				->get()->row();

			if (empty($get_financial_year_duration) || empty($get_financial_year_duration->start_date) || empty($get_financial_year_duration->end_date)) {
				return [];
			}

			$financial_start_date = $get_financial_year_duration->start_date;
			$financial_end_date = $get_financial_year_duration->end_date;

			$monthyear = $financial_start_date;

			$leave_years = $this->db_readonly
				->select('id as leave_year_id')
				->from('leave_v2_year')
				->where('start_date <=', $financial_end_date)
				->where('end_date >=', $financial_start_date)
				->get()
				->result();

			if (!empty($leave_years)) {
				foreach ($leave_years as $leave_year) {
					$leave_year_ids[] = $leave_year->leave_year_id;
				}
			}

			if (empty($leave_year_ids)) {
				return [];
			}
		} else {
			$leave_year_ids = [$ly_id];
		}

		$this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, sm.id as staff_id, lsq.leave_category_id, lsq.total_quota, ifnull(sm.employee_code,'NA') as employee_code, ifnull(date_format(sm.joining_date,'%d-%m-%Y'),'NA') as joining_date, ifnull(date_format(sm.last_date_of_work,'%d-%m-%Y'),'-') as last_date_of_work")
			->from('staff_master sm')
			->join('leave_v2_staff_quota lsq',"sm.id = lsq.staff_id AND lsq.leave_v2_year_id IN (" . implode(',', $leave_year_ids) . ")",'left')
			->where('sm.is_primary_instance', 1);

		if ($staffStatus != "all") {
			$this->db_readonly->where('sm.status', $staffStatus);
		}

		// In case of resigned staff: We need only resigned staff who had leave quota
		if ($staffStatus == 4) {
			$this->db_readonly->where("EXISTS (
				SELECT 1 FROM leave_v2_staff_quota q 
				WHERE q.staff_id = sm.id 
				  AND q.leave_v2_year_id = (SELECT id FROM leave_v2_year WHERE is_active = 1 LIMIT 1)
			)"); // raw query in where
		}

		$this->db_readonly->order_by('sm.first_name');
		$staffMaster = $this->db_readonly->get()->result();

		if (empty($staffMaster)) {
			return [];
		}

		$staffLeaveQuota = [];
		foreach ($staffMaster as $val) {
			if (isset($staffLeaveQuota[$val->staff_id][$val->leave_category_id])) {
				$staffLeaveQuota[$val->staff_id][$val->leave_category_id]->total_quota += $val->total_quota;
			} else {
				$staffLeaveQuota[$val->staff_id][$val->leave_category_id] = $val;
			}
		}

		$is_multi_level_leave_enbled = (int) $this->settings->getSetting("enable_multi_level_leave_approver_mode");
		if ($is_multi_level_leave_enbled == 1) {
			$sql = "SELECT ls.staff_id, ls.leave_category_id, cat.has_quota, cat.leave_type, ls.noofdays as used_quota, from_date, to_date
			FROM leave_v2_staff ls 
			JOIN leave_v2_category cat ON cat.id=ls.leave_category_id AND cat.status=1 
			WHERE ls.final_status <= 2 and leave_year_id in (".implode(",", $leave_year_ids).")";
		} else {
			$sql = "SELECT ls.staff_id, ls.leave_category_id, cat.has_quota, cat.leave_type, ls.noofdays as used_quota, from_date, to_date
			FROM leave_v2_staff ls 
			JOIN leave_v2_category cat ON cat.id=ls.leave_category_id AND cat.status=1 
			WHERE ls.status <= 2 and leave_year_id in (".implode(",", $leave_year_ids).")";
		}
		$result = $this->db_readonly->query($sql)->result();

		$selected_month_start = date('Y-m-01', strtotime($monthyear));
		$selected_month_end = date('Y-m-t', strtotime($monthyear));
		$previous_month_end = date('Y-m-d', strtotime('-1 day', strtotime($selected_month_start)));

		$all_staff_taken_leaves = $this->getAllStaffLeavesForOBAndUsed($result, $previous_month_end, $selected_month_start, $selected_month_end);

		foreach ($staffLeaveQuota as $staffId => $value) {
			foreach ($value as $staff) {
				$staff->has_quota = 0;
				$staff->leave_type = '';
				$staff->used_quota = 0;
				$staff->ob = "-";
				$staff->balance_quota = "-";

				if (isset($all_staff_taken_leaves[$staffId][$staff->leave_category_id])) {
					$leave_data = $all_staff_taken_leaves[$staffId][$staff->leave_category_id];
					
					$staff->has_quota = $leave_data['has_quota'];
					$staff->leave_type = $leave_data['leave_type'];
					$staff->used_quota = $leave_data['current'];

					if ($staff->has_quota == 1) {
						$staff->ob = (float) $staff->total_quota - (float) $leave_data['previous'];
						$staff->balance_quota = $staff->ob - $staff->used_quota;
					}
				}else{
					$staff->ob = $staff->total_quota != 0 ? $staff->total_quota : '-';
					$staff->balance_quota = $staff->ob;
				}
			}
		}

		$staff = [];
		foreach ($staffLeaveQuota as $staffId => $categories) {
			foreach ($categories as $res) {
				if (!isset($staff[$staffId])) {
					$staff[$staffId] = [
						'id' => $res->staff_id,
						'name' => $res->staff_name,
						'employee_code' => $res->employee_code,
						'joining_date' => $res->joining_date,
						'date_of_exit' => $res->last_date_of_work,
						'categories' => []
					];
				}
				$staff[$staffId]['categories'][$res->leave_category_id] = [
					'total_quota' => $res->total_quota,
					'used_quota' => $res->used_quota,
					'has_quota' => $res->has_quota,
					'leave_type' => $res->leave_type,
					'ob' => $res->ob, // opening balance
					'balance_quota' => $res->balance_quota
				];
			}
		}

		array_multisort(array_column($staff, 'name'), SORT_ASC, $staff);
		return $staff;
	}

	private function getAllStaffLeavesForOBAndUsed($results, $previous_month_end, $current_start, $current_end){
		$leaveData = [];

		foreach ($results as $row) {
			$staff_id = $row->staff_id;
			$cat_id = $row->leave_category_id;

			if (!isset($leaveData[$staff_id][$cat_id])) {
				$leaveData[$staff_id][$cat_id] = [
					'previous' => 0,
					'current' => 0,
					'has_quota' => $row->has_quota,
					'leave_type' => $row->leave_type
				];
			}

			$from = $row->from_date;
			$to = $row->to_date;

			if ($to < $current_start) {
				// Ends before current month = previous
				$leaveData[$staff_id][$cat_id]['previous'] += $row->used_quota;
			} elseif ($from < $current_start && $to >= $current_start) {
				// Overlaps from previous month into current = count as previous
				$leaveData[$staff_id][$cat_id]['previous'] += $row->used_quota;
			} elseif ($from >= $current_start && $from <= $current_end) {
				// Starts in current month = current
				$leaveData[$staff_id][$cat_id]['current'] += $row->used_quota;
			}
		}
		return $leaveData;
	}

	public function get_acad_year_namebyid(){

	 	$sql="select acad_year from academic_year where id='$this->yearId'";
        $acad_year_name =  $this->db_readonly->query($sql)->row('acad_year');
        $acadyear=  explode('-', $acad_year_name);
        return $acadyear[0];
    }

	public function get_staff_leaves_acad_year_id($leave_year_id){
		$leave_year = $this->db_readonly->select("start_date")
			->from("leave_v2_year")
			->where("id", $leave_year_id)
			->get()->row()->start_date;

		$date = $leave_year;
		$year = explode("-", $date)[0];
		$leave_acad_year = substr($year, -2);

		if ($leave_acad_year >= $this->yearId) {
			return 1;
		} else {
			return 0;
		}
	}

	public function getStaffLeaveQuota_by_staffType($leave_year_id, $staff_type, $show_aprovals) {
		$leave_categories= $this->db_readonly->select("lc.id as leave_cat_id, lc.name, lc.short_name, lc.leave_type, lc.has_quota, lc.can_be_carried")->where('lc.status', 1)->get('leave_v2_category lc')->result();
		
		$this->db_readonly->select("sm.id as staff_id, sm.staff_type as s_type, concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as staff_name")->where('sm.status', 2)->where('sm.is_primary_instance', 1);

		if((int)!empty($staff_type)){
			$this->db_readonly->where_in('sm.staff_type', $staff_type);
		}
		$staffs= $this->db_readonly->get('staff_master sm')->result();

		foreach ($staffs as $key => $val) {
			// get approvers
			if($show_aprovals == 1) {
				$get_max_approval_result = $this->db_readonly->select('lsq.approver_mode')->where('lsq.staff_id', $val->staff_id)->where('lsq.leave_v2_year_id', $leave_year_id)->get('leave_v2_staff_quota lsq')->row();
				
				if (!empty($get_max_approval_result)) {
					$get_max_approval = $get_max_approval_result->approver_mode;
				} else {
					$get_max_approval = 3;
				}
				$arr= json_decode( $this->settings->getSetting('leave_approver_mode') );
				if($this->settings->getSetting('school_short_name') === 'iisb') {
					$arr = ["level_1","level_2","level_3"];
				}
				$approvers= "ifnull(lsq.approver_mode, 'Not Added') as approver_mode, ";
				$x= 1;
				for($keyss= 0; $keyss < $get_max_approval; $keyss++) {
					if($keyss == 0) {
						$approvers .= "ifnull( concat(ifnull(sm$x.first_name, ''), ' ', ifnull(sm$x.last_name, '')), 'Not Assigned') as approver_$x";
					} else {
						$approvers .= ", ifnull( concat(ifnull(sm$x.first_name, ''), ' ', ifnull(sm$x.last_name, '')), 'Not Assigned') as approver_$x";
					}
					$x++;
				}
				$x= 1;
				$this->db_readonly->select($approvers)->from('leave_v2_staff_quota lsq');
				for($ke= 0; $ke < $get_max_approval; $ke++) {
					$this->db_readonly->join("staff_master sm$x", "sm$x.id= lsq.approver_$x");
					$x++;
				}
				$staffs[$key]->approvers= $this->db_readonly->where('lsq.staff_id', $val->staff_id)
				->where('lsq.leave_v2_year_id', $leave_year_id)
				->get()->row();

				$x= '';
			}

			$staffs[$key]->assignedQuota= [];
			$temp= new stdClass();
			// $temp2= new stdClass();
			$z= false;
			foreach($leave_categories as $a => $b) {
				$n= $b->name;
				$quota= $this->db_readonly->select("lsq.id, lsq.staff_id, lsq.leave_category_id, lsq.total_quota, lsq.used_quota, lsq.quota_carried_to_next_year")
					->where('lsq.staff_id', $val->staff_id)
					->where('lsq.leave_category_id', $b->leave_cat_id)
					->where('lsq.leave_v2_year_id', $leave_year_id)
					->get('leave_v2_staff_quota lsq')->result();
					
					// $temp2->$n= new stdClass();
					$temp->$n= new stdClass();
					if(! empty($quota)) {
						$z= true;
						
						$temp->$n->leave_cat_id= $b->leave_cat_id;
						$temp->$n->name= $n;
						$temp->$n->short_name= $b->short_name;
						$temp->$n->leave_type= $b->leave_type;
						$temp->$n->has_quota= $b->has_quota;
						$temp->$n->can_be_carried= $b->can_be_carried;

						$temp->$n->lsq_id= $quota[0]->id;
						$temp->$n->lsq_staff_id= $quota[0]->staff_id;
						$temp->$n->leave_category_id= $quota[0]->leave_category_id;
						$temp->$n->total_quota= $quota[0]->total_quota;
						$temp->$n->quota_carried_to_next_year= $quota[0]->quota_carried_to_next_year;
						$temp->$n->used_quota= $quota[0]->used_quota;

					} 
					else {
						$temp->$n->leave_cat_id= $b->leave_cat_id;
						$temp->$n->name= $b->name;
						$temp->$n->short_name= $b->short_name;
						$temp->$n->leave_type= $b->leave_type;
						$temp->$n->has_quota= $b->has_quota;
						$temp->$n->can_be_carried= $b->can_be_carried;
					}
					
					$staffs[$key]->assignedQuota= $temp;

			}
			$staffs[$key]->leave_year_id= $leave_year_id;
			if($z) {
				$staffs[$key]->update= 1; 
			} else {
				$staffs[$key]->update= 0; 
			}
			
		}
	
		// echo '<pre>'; print_r($staffs); die();
		return $staffs;
	}

	public function mass_update_quota() {
		$input = $this->input->post();
		$staff_ids_arr= $input['staff_ids_arr'];
		$leave_year_id= $input['leave_year_id'];
		$add_update= $input['add_update'];

		// check for insert operation  if already staffs are added
		if( $add_update == 'add' ) {
			$check_if_added= $this->db->query("select id from leave_v2_staff_quota where leave_v2_year_id in ($leave_year_id) and staff_id in ($staff_ids_arr)");
			if( $check_if_added->num_rows() > 0 ) {
				return '7';
			}
		}

		$this->db->trans_start();
		foreach(explode(',', $staff_ids_arr) as $key_staff => $staff_id) {
			$categories= $this->db->select('id')->where('status', '1')->get('leave_v2_category')->result(); // getting all categories
			foreach( $categories as $key_category => $val) {
					$data=array(
						'total_quota' => isset(  $input["leave_category_". $val->id] ) ?  $input["leave_category_". $val->id] : 0.00,
						'leave_v2_year_id' => $leave_year_id,
						'staff_id' => $staff_id,
						'leave_category_id' => $val->id,
						'used_quota' => 0.00,
						'quota_carried_to_next_year' => 0.00
					);
					$this->db->insert('leave_v2_staff_quota', $data);
			}
		}
		$this->db->trans_complete();

		return $this->db->trans_status();

	}

	public function edit_quota_for_a_staff() {
		$input = $this->input->post();
		$staff_id= $input['staff_id'];
		
		$add_update= $input['add_update'];
		$staff_leave_year_id= $input['staff_leave_year_id'];
		
		// ans start
		$this->db->trans_start();
		$categories= $this->db->select('id')->where('status', '1')->get('leave_v2_category')->result(); // getting all categories
		foreach( $categories as $key => $val) {
			$check= $this->db->where('staff_id', $staff_id)->where('leave_category_id', $val->id)->where('leave_v2_year_id', $staff_leave_year_id)->get('leave_v2_staff_quota');
			if( $add_update == 'add' && $check->num_rows() > 0) {
				return '7';
			}
			if( $check->num_rows() > 0 && $add_update == 'update' ) { // check for update
				$data=array(
					'total_quota' => isset(  $input["leave_category_". $val->id] ) ?  $input["leave_category_". $val->id] : 0.00
				);
				$this->db->where('staff_id', $staff_id)
					->where('leave_category_id', $val->id)
					->where('leave_v2_year_id', $staff_leave_year_id)
					->update('leave_v2_staff_quota', $data);
			} else {
				$data=array(
					'total_quota' => isset(  $input["leave_category_". $val->id] ) ?  $input["leave_category_". $val->id] : 0.00,
					'leave_v2_year_id' => $staff_leave_year_id,
					'staff_id' => $staff_id,
					'leave_category_id' => $val->id,
					'used_quota' => 0.00,
					'quota_carried_to_next_year' => 0.00
				);
				$this->db->insert('leave_v2_staff_quota', $data);
			}
		}
		$this->db->trans_complete();

		return $this->db->trans_status();
    }

	public function get_leave_categories() {
		return $this->db_readonly->select('id, name, has_quota')->where('status', '1')->get('leave_v2_category')->result();
	}

	public function get_quota_for_single_staff() {
		$staff_id= $_POST['staff_id'];
		return $this->db_readonly->query("select leave_category_id, total_quota from leave_v2_staff_quota where staff_id= $staff_id and leave_v2_year_id in (select max(id) from leave_v2_year  where is_active= 1) ")->result();
	}

	public function disable_leave_category($category_id) {
		return $this->db->where('id', $category_id)->update('leave_v2_category', array('status' => 0));
	}

	public function activate_leave_category($category_id) {
		return $this->db->where('id', $category_id)->update('leave_v2_category', array('status' => 1));
	}

	public function updateLeaveCategory($data) {
		$avatar_id = $this->authorization->getAvatarId();
		// $data['created_by'] = $avatar_id;
		$data['last_modified_by'] = $avatar_id;
		return $this->db->where('id', $data['id'])->update('leave_v2_category', $data);
	}

	 public function getLeaveApplyInstruction($data){
		return $this->db_readonly->select("leave_apply_instruction")
		->from("leave_v2_category")
		->where("id",$data["leaveCategoryId"])
		->get()->row_array();
	 }

//Multi-level leave approval code
	public function edit_quota_for_a_staff_level3() {
		$input = $this->input->post();
		
		$approval_mode = $input['approver_mode'];
		$staff_id= $input['staff_id'];
		$add_update= $input['add_update'];
		$staff_leave_year_id= $input['staff_leave_year_id'];

		$approver1 = null;
		$approver2 = null;
		$approver3 = null;

		$status=null;
		$approve_status_2=null;
		$approve_status_3 = null;

		// ans start
		if ($approval_mode == 1) {
			$approver1 = $input['approver_1'];
			$approver2 = null;
			$approver3 = null;

			$status = 0;
		}

		if ($approval_mode == 2) {
			$approver1 = $input['approver_1'];
			$approver2 = $input['approver_2'];
			$approver3 = null;

			$status = 0;
			$approve_status_2 = 0;
		}

		if ($approval_mode == 3) {
			$approver1 = $input['approver_1'];
			$approver2 = $input['approver_2'];
			$approver3 = $input['approver_3'];

			$status = 0;
			$approve_status_2 = 0;
			$approve_status_3 = 0;
		}


		
		$this->db->trans_start();
		$categories= $this->db->select('id')->where('status', '1')->get('leave_v2_category')->result(); // getting all categories
		foreach( $categories as $key => $val) {
			$check= $this->db->where('staff_id', $staff_id)->where('leave_category_id', $val->id)->where('leave_v2_year_id', $staff_leave_year_id)->get('leave_v2_staff_quota');
			if( $add_update == 'add' && $check->num_rows() > 0) {
				return '7';
			}
			if( $check->num_rows() > 0 && $add_update == 'update' ) { // check for update
				$data=array(
					'total_quota' => isset(  $input["leave_category_". $val->id] ) ?  $input["leave_category_". $val->id] : 0.00,
					'approver_1' => isset($input["approver_1"]) ? $input["approver_1"] : null,
					'approver_2' => isset($approver2) ? $approver2 : null,
					'approver_3' => isset($approver3) ? $approver3 : null,
					'approver_mode'=> isset($approval_mode) ? $approval_mode :null
				);
				$this->db->where('staff_id', $staff_id)
					->where('leave_category_id', $val->id)
					->where('leave_v2_year_id', $staff_leave_year_id)
					->update('leave_v2_staff_quota', $data);
			} else {
				$data=array(
					'total_quota' => isset(  $input["leave_category_". $val->id] ) ?  $input["leave_category_". $val->id] : 0.00,
					'leave_v2_year_id' => $staff_leave_year_id,
					'staff_id' => $staff_id,
					'leave_category_id' => $val->id,
					'used_quota' => 0.00,
					'quota_carried_to_next_year' => 0.00,
					'approver_1' => isset($input["approver_1"]) ? $input["approver_1"] : null,
					'approver_2' => isset($approver2) ? $approver2 : null,
					'approver_3' => isset($approver3) ? $approver3 : null,
					'approver_mode'=> isset($approval_mode) ? $approval_mode :null
				);
				$this->db->insert('leave_v2_staff_quota', $data);
			}
		}
		// get all the pending leaves applied by this staff
		$previousAppliedLeaves=$this->db_readonly->select("id as leave_id, approval_mode, leave_approved_by, approved_by_2, approved_by_3")
		->from("leave_v2_staff")
		->where("leave_year_id",$staff_leave_year_id)
		->where("staff_id",$staff_id)
		->where("final_status",0)
		->get()->result();

		if(!empty($previousAppliedLeaves)){
			$data=[];
			foreach($previousAppliedLeaves as $key => $leave){
				if($leave->approval_mode==$approval_mode){
						if($approval_mode==1){
							if($leave->leave_approved_by==$approver1) continue;
						}else if($approval_mode==2){
							if($leave->leave_approved_by == $approver1 && $leave->approved_by_2 == $approver2) continue;
						} else if ($approval_mode == 3) {
							if($leave->leave_approved_by==$approver1 && $leave->approved_by_2==$approver2 && $leave->approved_by_3 == $approver3) continue;
						}

						$initial_data=[];
						$arr1=[];
						$arr2 = [];
						$arr3 = [];

						$initial_data["id"] = $leave->leave_id;
						$initial_data["approval_mode"] = $approval_mode;
						
						// then reset only the staff which is changed
						if($leave->leave_approved_by!=$approver1){
							$arr1["leave_approved_by"] = $approver1;
							$arr1["status"]=$status;
							$arr1["approved_date_1"] = null;
							$arr1["description"] = null;
						}

						if($leave->approved_by_2!=$approver2){
							$arr2["approved_by_2"] = $approver2;
							$arr2["approve_status_2"] = $approve_status_2;
							$arr2["approved_date_2"] = null;
							$arr2["approved_remarks_2"] = null;
						}

						if($leave->approved_by_3!=$approver3){
							$arr3["approved_by_3"] = $approver3;
							$arr3["approve_status_3"] = $approve_status_3;
							$arr3["approved_date_3"] = null;
							$arr3["approved_remarks_3"] = null;
						}

						$data[] = array_merge($initial_data, $arr1, $arr2, $arr3);
					}else{
						$data[]=array(
							"id"=>$leave->leave_id,
							"approval_mode"=>$approval_mode,
							"leave_approved_by"=>$approver1,
							"approved_by_2"=>$approver2,
							"approved_by_3"=>$approver3,
							"status"=>$status,
							"approve_status_2"=>$approve_status_2,
							"approve_status_3" => $approve_status_3,
							"approved_date_1" => null,
							"approved_date_2" => null,
							"approved_date_3" => null,
							"description" => null,
							"approved_remarks_2"=>null,
							"approved_remarks_3" => null
						);
					}
			}

			if(!empty($data)){
				$query = $this->db->update_batch('leave_v2_staff', $data, 'id');
			}
		}

		$this->db->trans_complete();
		return $this->db->trans_status();
    }

	public function is_reporting_manager_3level($staff_id) { 
    	return  $this->db_readonly->select('*');
				$this->db_readonly->from('leave_v2_staff');
				$this->db_readonly->where('leave_approved_by',$staff_id );
				$this->db_readonly->or_where('approved_by_2', $staff_id);
				$this->db_readonly->or_where('approved_by_3', $staff_id);
				$this->db_readonly->get();
  	}

	public function get_staff_list_3level(){
		return $this->db_readonly->distinct("")
        ->select("sm.status,ifnull(sm.first_name,'') as first_name,ifnull(sm.last_name,'') as last_name ,sm.id,sq.approver_1,sq.approver_2,sq.approver_3")
        ->join('leave_v2_staff_quota sq', 'sm.id = sq.staff_id')
		->group_start()
			->where("sm.status", 2)
			->or_where("sm.status", 4)
		->group_end()
		->where("sm.is_primary_instance",1)
		->group_by("sm.id")
        ->order_by('first_name')
        ->get("staff_master sm")
        ->result();
	}


  	public function getReportingStaffDataperstaff_3level($staffid){
		$leave_v2_year_id = $this->db->select('id')->where('is_active', 1)->get('leave_v2_year')->row()->id;
		$sql = "select sq.approver_mode,sq.approver_1 as rpid1,sq.approver_2 as rpid2,sq.approver_3 as rpid3, CONCAT(ifnull(sm1.first_name, ''),' ', ifnull(sm1.last_name, '')) as approver_1, CONCAT(ifnull(sm2.first_name, ''),' ', ifnull(sm2.last_name, '')) as approver_2,CONCAT(ifnull(sm3.first_name, ''),' ', ifnull(sm3.last_name, '')) as approver_3 
				from leave_v2_staff_quota sq
				left join staff_master sm on  sq.staff_id =sm.id 
				left join staff_master sm1 on  sq.approver_1 = sm1.id 
				left join staff_master sm2 on  sq.approver_2 = sm2.id 
				left join staff_master sm3 on  sq.approver_3 = sm3.id 
				where sq.staff_id = $staffid
				and sq.leave_v2_year_id = $leave_v2_year_id";
		$result=$this->db_readonly->query($sql)->row();

		if(!empty($result)){
			return $result;
		}else{
			return new stdClass();
		}
  	}
	public function get_reporting_manager_id($staff_id) {
		$result = $this->db_readonly->select('sq.approver_1 as a1_id ,sq.approver_2 as a2_id , sq.approver_3 as a3_id') 
		          ->from('leave_v2_staff_quota sq')
				  ->join('leave_v2_year ly','ly.id=sq.leave_v2_year_id')
				  ->where('sq.staff_id' ,$staff_id)
				  ->where('ly.is_active',1)
				  ->get()->row();
		return $result;
	}
        

	public function mass_update_quota_level3() {
		$input = $this->input->post();
		$approval_mode = $input['approver_mode'];
		$staff_ids_arr = $input['staff_ids_arr'];
		$leave_year_id = $input['leave_year_id'];
		$add_update = $input['add_update'];
		
		$approver1=null;
		$approver2=null;
		$approver3=null;

		if ($approval_mode == 1) {
			$approver1 = $input['approver_1'];
			$approver2 = null;
			$approver3 = null;
		}
		
		if ($approval_mode == 2) {
			$approver1 = $input['approver_1'];
			$approver2 = $input['approver_2']; 
			$approver3 = null;
		}

		if($approval_mode == 3){
			$approver1 = $input['approver_1'];
			$approver2 = $input['approver_2'];
			$approver3 = $input['approver_3'];
		}


		// check for insert operation  if already staffs are added
		if( $add_update == 'add' ) {
			$check_if_added= $this->db->query("select id from leave_v2_staff_quota where leave_v2_year_id in ($leave_year_id) and staff_id in ($staff_ids_arr)");
			if( $check_if_added->num_rows() > 0 ) {
				return '7';
			}
		}

		$this->db->trans_start();
		foreach(explode(',', $staff_ids_arr) as $key_staff => $staff_id) {
			$categories= $this->db->select('id')->where('status', '1')->get('leave_v2_category')->result(); // getting all categories
			foreach( $categories as $key_category => $val) {
				$check = $this->db->select('id') // check for individual category
					->where('staff_id', $staff_id)
					->where('leave_v2_year_id', $leave_year_id)
					->where('leave_category_id', $val->id)
					->get('leave_v2_staff_quota');
				if( $check->num_rows() > 0 ) { // check for update
					$data=array(
						'approver_1' => isset($input["approver_1"]) ? $input["approver_1"] : null,
						'approver_2' => isset($approver2) ? $approver2 : null,
						'approver_3' => isset($approver3) ? $approver3 : null,
						'approver_mode'=> isset($approval_mode) ? $approval_mode :null
					);
					$this->db->where('staff_id', $staff_id)
						->where('leave_category_id', $val->id)
						->where('leave_v2_year_id', $leave_year_id)
						->update('leave_v2_staff_quota', $data);
				} else {
					$data=array(
						'total_quota' => isset(  $input["leave_category_". $val->id] ) ?  $input["leave_category_". $val->id] : 0.00,
						'leave_v2_year_id' => $leave_year_id,
						'staff_id' => $staff_id,
						'leave_category_id' => $val->id,
						'used_quota' => 0.00,
						'quota_carried_to_next_year' => 0.00,
						'approver_1' => isset($input["approver_1"]) ? $input["approver_1"] : null,
						'approver_2' => isset($approver2) ? $approver2 : null,
						'approver_3' => isset($approver3) ? $approver3 : null,
						'approver_mode'=> isset($approval_mode) ? $approval_mode :null
					);
					$this->db->insert('leave_v2_staff_quota', $data);
				}
			}
		}
		$this->db->trans_complete();

		return $this->db->trans_status();

	}
    public function get_reporting_manager_name_by_status_type($status_type,$staff_id,$leave_id) {
		$staff_name = '';
		if($status_type == 1) {
			$staff_name =$this->db_readonly->select("CONCAT(ifnull(sm1.first_name, ''),' ', ifnull(sm1.last_name, '')) as staff_name")
			->from('leave_v2_staff ls')
			->join('staff_master sm1','sm1.id = ls.leave_approved_by')
			->where('ls.staff_id',$staff_id)
			->where('ls.id',$leave_id)
			->get()
			->row();
		}
		if($status_type == 2) {
			$staff_name =$this->db_readonly->select("CONCAT(ifnull(sm1.first_name, ''),' ', ifnull(sm1.last_name, '')) as staff_name")
			->from('leave_v2_staff ls')
			->join('staff_master sm1','sm1.id = ls.approved_by_2')
			->where('ls.staff_id',$staff_id)
			->where('ls.id',$leave_id)
			->get()
			->row();
		}
		if($status_type == 3) {
			$staff_name =$this->db_readonly->select("CONCAT(ifnull(sm1.first_name, ''),' ', ifnull(sm1.last_name, '')) as staff_name")
			->from('leave_v2_staff ls')
			->join('staff_master sm1','sm1.id = ls.approved_by_3')
			->where('ls.staff_id',$staff_id)
			->where('ls.id',$leave_id)
			->get()
			->row();
		}
		return $staff_name;

    
	}

	private function substractApplieLeavesFromQuota($leave_id){
		$leave = $this->db_readonly->select("noofdays, leave_category_id, staff_id, leave_year_id")->where('id', $leave_id)->get('leave_v2_staff')->row();
		$no_of_days = $leave->noofdays;
		$leave_category_id = $leave->leave_category_id;
		$staff_id = $leave->staff_id;
		$leave_v2_year_id = $leave->leave_year_id;
		$sql = "update leave_v2_staff_quota set used_quota=used_quota-$no_of_days where leave_category_id=$leave_category_id and staff_id=$staff_id and leave_v2_year_id=$leave_v2_year_id";
		$this->db->query($sql);
	}

    public function saveLeaveStatus_3level($leave_id, $status, $description,$approved_status_type,$approved_status_2,$approved_status_3,$approval_mode) {
		$multi_level_config=!empty($this->settings->getSetting('multi_level_leave_approval_strategy')) ? $this->settings->getSetting('multi_level_leave_approval_strategy') : "reject_if_any_one_rejects";
        $this->db->trans_start();

        if($multi_level_config=="reject_if_any_one_rejects" && $status == 3) {
			//If leave is rejected, leave should be subtracted from the used quota.
        	$this->substractApplieLeavesFromQuota($leave_id);
        }

		$data  = array();
        if ($approved_status_type == 1) {
			$data = array('status' => $status, 'description' => $description, 'approved_date_1'=>$this->Kolkata_datetime());
		}
		if ($approved_status_type == 2) {
			$data = array('approve_status_2' => $status, 'approved_remarks_2' => $description, 'approved_date_2'=>$this->Kolkata_datetime());
		}
		if($approved_status_type == 3) {
			$data = array('approve_status_3' => $status, 'approved_remarks_3' => $description, 'approved_date_3'=>$this->Kolkata_datetime());
		}
		//$this->db->where('id', $leave_id)->update('leave_v2_staff', $data);
		
		$this->db->set($data)->where('id', $leave_id)->update('leave_v2_staff');

		$this->db->trans_commit();
		$final_status = $this->db->select('status,approve_status_2,approve_status_3')->where('id',$leave_id)->get('leave_v2_staff')->row();
		$update_final_status =[];


		if($multi_level_config=="reject_if_any_one_rejects"){
			if ($approval_mode == 1) {
				if ($final_status->status == 1 ) {
				$update_final_status= array('final_status'=> 1);
				} else if($final_status->status == 4  ){
				$update_final_status= array('final_status'=> 4);
				} else {
				$update_final_status= array('final_status'=> 0);
				}
				if ($final_status->status == 3 ) {
				$update_final_status= array('final_status'=> 3);
				}
			}else if ($approval_mode == 2) {
				if ($final_status->status == 1 && $final_status->approve_status_2 == 1) {
				$update_final_status= array('final_status'=> 1);
				} else if($final_status->status == 4 || $final_status->approve_status_2 == 4 || $final_status->approve_status_3 == 4 ){
					$update_final_status= array('final_status'=> 4);
				} else {
					$update_final_status= array('final_status'=> 0);
				}
				if ($final_status->status == 3 || $final_status->approve_status_2 == 3 || $final_status->approve_status_3 == 3 ) {
				$update_final_status= array('final_status'=> 3);
				}

			}else if ($approval_mode == 3) {
				if ($final_status->status == 1 && $final_status->approve_status_2 == 1 && $final_status->approve_status_3 == 1 ) {
					$update_final_status= array('final_status'=> 1);
				} else if($final_status->status == 4 || $final_status->approve_status_2 == 4 || $final_status->approve_status_3 == 4 ){
					$update_final_status= array('final_status'=> 4);
				} else {
					$update_final_status= array('final_status'=> 0);
				}
				if ($final_status->status == 3 || $final_status->approve_status_2 == 3 || $final_status->approve_status_3 == 3 ) {
				$update_final_status= array('final_status'=> 3);
				}
			}
		}else if($multi_level_config=="level_wise_Approve"){
			if ($approval_mode == 1) {
				$update_final_status= array('final_status'=> $final_status->status);

				if($final_status->status==3){
					$this->substractApplieLeavesFromQuota($leave_id);
				}

			}else if ($approval_mode == 2) {
				$update_final_status= array('final_status'=> $final_status->approve_status_2);

				if($final_status->approve_status_2==3){
					$this->substractApplieLeavesFromQuota($leave_id);
				}
			}else if ($approval_mode == 3) {
				$update_final_status= array('final_status'=> $final_status->approve_status_3);

				if($final_status->approve_status_3==3){
					$this->substractApplieLeavesFromQuota($leave_id);
				}
			}
		}
       
		$this->db->set($update_final_status)->where('id', $leave_id)->update('leave_v2_staff');
		$this->db->trans_commit();
        $this->db->trans_complete();
	    if($this->db->trans_status() === FALSE) {
	        $this->db->trans_rollback();
	        return 0;
	    }
	    $this->db->trans_commit();
	    return 1;
    }

	public function get_leave_final_status($leave_id){
		return $this->db_readonly->query("select status, final_status from leave_v2_staff where id='$leave_id'")->row();
	}

    public function getLeaveData_3level($leave_id) {
        return $this->db_readonly->select("ls.approved_by_2,ls.approved_by_3,ls.final_status,ls.approved_remarks_2,ls.approved_remarks_3,ls.approve_status_2,ls.approve_status_3,ls.description,ls.leave_approved_by,ls.approve_status_2,ls.approved_date_2,ls.approved_date_3,ls.approve_status_3,lc.name, lc.name as leave_type, lc.short_name, DATE_FORMAT(request_date,'%d-%m-%Y') as request_date, DATE_FORMAT(from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(to_date,'%d-%m-%Y') as to_date, noofdays, reason, ls.status, staff_id, leave_filed_by")
		->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
		->where('ls.id', $leave_id)
		->get('leave_v2_staff ls')->row();
    }

	public function s3FileUpload($file) {
		if($file['tmp_name'] == '' || $file['name'] == '') {
		  return ['status' => 'empty', 'file_name' => ''];
		}        
		return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],'staff_leave');
	}

	public function get_leave_applied_category($leave_category_id){
		return $this->db_readonly->query("select name, short_name from leave_v2_category where id='$leave_category_id'")->row()->name;
	}

	private function is_staff_attendance_entry_exists($staffId,$from_date,$to_date,$for_single_day){
		$this->db_readonly->select("id,status");
		$this->db_readonly->from("st_attendance");
		if($for_single_day==1){
			$this->db_readonly->where("date", $from_date);
		}else{
			$this->db_readonly->where("date>=", $from_date);
			$this->db_readonly->where("date<=", $to_date);
		}
		$this->db_readonly->where("staff_id", $staffId);
		$attendance_login_entry = $this->db_readonly->get()->row();

		if (!empty($attendance_login_entry) && $attendance_login_entry->status!="AB") {
			return 1;
		} else {
			return 0;
		}
	}

	public function can_staff_apply_leave($data){
		$staffId = $data['staff_id'];
		$selection_type = $data['selection_type'];
		$from_date = date('Y-m-d', strtotime($data['from_date']));
		$to_date = date('Y-m-d', strtotime($data['to_date']));

		// for single date or from date==to date
		if($from_date==$to_date){
			$attendance_login_entry_exists=$this->is_staff_attendance_entry_exists($staffId,$from_date,$to_date,$for_single_day=1);
			// case 1: full day
			// a) if there exists any entry in st_atendance/ he has logged in already then he can't take full day leave return 0 else 1;
			if($selection_type=="fullday"){
				return (int)!$attendance_login_entry_exists;
			}else{
				// case 2: half day
				// b) if there exists a entry in st_attendance/ he has logged in then, update the st_attendance status to 'HD' and return 1
				if((int)$attendance_login_entry_exists){
					// if attendance exists then he can only take single half day
					// check for single half day leave exists for him
					$is_multi_level_leave_enabled = (int)$this->settings->getSetting("enable_multi_level_leave_approver_mode");
					if($is_multi_level_leave_enabled==1){
						$is_leave_already_exists = (int) $this->db_readonly->select("id")
							->from("leave_v2_staff")
							->where("staff_id", $staffId)
							->where("from_date", $from_date)
							->where("final_status!=", 3)
							->where("final_status!=", 4)
							->get()->row();
					}else{
						$is_leave_already_exists=(int)$this->db_readonly->select("id")
						->from("leave_v2_staff")
						->where("staff_id",$staffId)
						->where("from_date",$from_date)
						->where("status!=", 3)
						->where("status!=", 4)
						->get()->row();
					}

					if($is_leave_already_exists){
						return 0;
					}

					return 1;
					// return (int)$is_attendance_status_updated=$this->db->where("date",$from_date)
					// ->where("staff_id",$staffId)
					// ->update("st_attendance",["status"=>"HD"]);
				}else{
					return 1;
				}
			}
		}else{
			// for date range i.e: no. oof days > 1
			// case 1: full day/ half day
			// a) if no entry present for any date return 1 else 0
			return (int)!$attendance_login_entry_exists = $this->is_staff_attendance_entry_exists($staffId, $from_date, $to_date, $for_single_day = 0);
		}
	}

  	public function save_leave_appication_3level($input) {
		$staff_id = $input['staff_id'];
		$leave_v2_year_id = $input['leave_v2_year_id'];
		$leave_category = $input['leave_category'];
		$from_date = date('Y-m-d', strtotime($input['from_date']));
	    $to_date = date('Y-m-d', strtotime($input['to_date']));
		$leaveFor = $input['selection_type'];
		
		$no_of_days = ($leaveFor=='fullday')?$input['noofdays']:0.5;

		// START: validating for leave
		$leave_year_info=$this->db->select("id, start_date, end_date")
		->from("leave_v2_year")
		->where("is_active",1)
		->get()->row();
		
		// check acad year validition is it correct as expected
		if(empty($leave_year_info) || $leave_year_info->id!=$leave_v2_year_id){
			return 0;
		}

		// 1. Check if the applied from and to-date is between current active academic year
		if(strtotime($from_date)<strtotime($leave_year_info->start_date) || strtotime($to_date)>strtotime($leave_year_info->end_date)){
			return 0;
		}

		// 2. Check if he is elligible to take leaves i.e: He has enough leave to take from

		$applied_leave_quota_details=$this->db->select("lq.total_quota,lq.used_quota,lc.has_quota")
		->from("leave_v2_staff_quota lq")
		->join("leave_v2_category lc","lc.id=lq.leave_category_id")
		->where("lq.leave_category_id",$leave_category)
		->where("lq.staff_id",$staff_id)
		->where("lq.leave_v2_year_id",$leave_v2_year_id)
		->get()->row();

		if(empty($applied_leave_quota_details)){
			return 0;
		}

		if($applied_leave_quota_details->has_quota==1){
			$remaining_leave_quota=$applied_leave_quota_details->total_quota-$applied_leave_quota_details->used_quota;
			
			if($no_of_days>$remaining_leave_quota){
				return 0;
			}
		}
		// END: validating for leave
		
		$leaveDocArray=[];
		foreach($_FILES as $key => $val){
			$leave_evidence_file = $this->s3FileUpload($_FILES[$key]);
			$leaveDocArray[$key]=$leave_evidence_file['file_name'];
		}
	    
	    $appliedBy = $this->authorization->getAvatarStakeHolderId();
	    $author = $this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');

	    $approvedBy = NULL;
	    $status = 0;
	    $status_str = "Pending";
	    if($author == 1 && $input["enable_leave_auto_approval_by_admin"]==1) {
	        $approvedBy = $appliedBy;
	        $status_str = "Auto Approved";
	        $status = 2;
	    }
	    
	    $data = array(
	        'staff_id' => $staff_id,
			'leave_year_id' => $leave_v2_year_id,
	        'leave_category_id' => $leave_category,
	        'leave_for' => $leaveFor,
	        'leave_filed_by' => $appliedBy,
	        'leave_approved_by' => $input['approver_1'],
	        'request_date' => date('Y-m-d'),
	        'from_date' => $from_date,
	        'to_date' => $to_date,
	        'noofdays' => $no_of_days,
	        'reason' => $input['reason'],
	        'status' => 0,
	        'last_modified_by' => $this->authorization->getAvatarId(),
			'approved_by_2'=>$input['approver_2'],
			'approved_by_3'=>$input['approver_3'],
			'approve_status_2'=>0,
			'approve_status_3'=>0,
			'final_status'=>$status,
			'approval_mode' =>$input['approver_mode'],
			'source_convert_leave_id' => isset($input['source_convert_leave_id']) ? $input['source_convert_leave_id'] : ""
			// 'leave_type' =>$input['selection_type']
	    );

		$data=array_merge($data,$leaveDocArray);
		// echo "<pre>"; print_r($data); die();

	    $this->db->trans_start();
	    $is_leave_applied_complete=$this->db->insert('leave_v2_staff', $data);
	    $current_leave_id = $this->db->insert_id();

	    // $this->_insertSubstituteData($current_leave_id, $staff_id, $from_date, $to_date, $leaveFor);

	    //update quota
		if($is_leave_applied_complete){
			// $sql = "update leave_v2_staff_quota set used_quota=used_quota+$no_of_days where leave_category_id=$leave_category and staff_id=$staff_id and leave_v2_year_id=$leave_v2_year_id";
			// $this->db->query($sql);
			$update_quota=$this->db
			->where("leave_category_id",$leave_category)
			->where("staff_id",$staff_id)
			->where("leave_v2_year_id",$leave_v2_year_id)
			->update("leave_v2_staff_quota",["used_quota"=>$applied_leave_quota_details->used_quota+$no_of_days]);
		}

	    $this->db->trans_complete();
	    if($this->db->trans_status() === FALSE) {
	        $this->db->trans_rollback();
	        return 0;
	    }
	    $this->db->trans_commit();
	    return 1;
  	}

	// public function getSingleLeaveDetails3Level($leaveId,$hasApplyLeave,$staffLoggedInId){
	// 	$today = date('Y-m-d');

    //     $this->db_readonly->select("ls.request_date, lc.reduce_approver,ls.approval_mode,ls.final_status,CONCAT(ifnull(sm1.first_name, ''),' ',ifnull(sm1.last_name,'')) as approver_name1,CONCAT(ifnull(sm2.first_name, ''),' ',ifnull(sm2.last_name,'')) as approver_name2,CONCAT(ifnull(sm3.first_name, ''),' ',ifnull(sm3.last_name,'')) as approver_name3,ls.status,ls.approve_status_2,ls.approve_status_3,ls.leave_approved_by,ls.approved_by_2,ls.approved_by_3,ls.description,ls.id, lc.name, lc.name as leave_type, lc.short_name as short_name, DATE_FORMAT(ls.request_date,'%d-%m-%Y') as request_date, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays,ls.reason, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name,ls.staff_id,ls.leave_filed_by, ls.cancel_reason, ifnull(lvy.name, '-') as leave_year_name, ls.description, ls.approved_remarks_2,ls.approved_remarks_3,
	// 	DATE_FORMAT(ls.approved_date_1,'%d-%b-%Y %h:%i %p') as approved_date_1,DATE_FORMAT(ls.approved_date_2,'%d-%b-%Y %h:%i %p') as approved_date_2,DATE_FORMAT(ls.approved_date_3,'%d-%b-%Y %h:%i %p') as approved_date_3, ls.leave_cancelled_by, DATE_FORMAT(ls.leave_cancelled_datetime,'%d-%b-%Y %h:%i %p') as leave_cancelled_datetime, CONCAT(ifnull(sm4.first_name, ''),' ',ifnull(sm4.last_name,'')) as leave_cancelled_by_name, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date_format_for_cancel_leave")
    //     ->from('leave_v2_staff ls')
    //     ->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
    //     ->join('leave_v2_year lvy', 'ls.leave_year_id=lvy.id','left')
    //     ->join('staff_master sm', 'ls.staff_id=sm.id','left')
	// 	->join('staff_master sm1', 'ls.leave_approved_by=sm1.id','left')
	// 	->join('staff_master sm2', 'ls.approved_by_2=sm2.id','left')
	// 	->join('staff_master sm3', 'ls.approved_by_3=sm3.id', 'left')
	// 	->join('staff_master sm4', 'ls.leave_cancelled_by=sm4.id','left')
	// 	->where('sm.is_primary_instance',1)
	// 	->where('ls.id',$leaveId);

    //     $result = $this->db_readonly->get()->result();

	// 	if(empty($result)){
	// 		return [];
	// 	}

	// 	$filtered_results=array();
	// 	if($hasApplyLeave == 2 || $hasApplyLeave == 3){
	// 		foreach($result as $key => $val) {
	// 			if($val->leave_approved_by==$staffLoggedInId || $val->approved_by_2==$staffLoggedInId || $val->approved_by_3==$staffLoggedInId){
	// 				if($val->leave_approved_by==$staffLoggedInId){
	// 					array_push($filtered_results, $val);
	// 				} else if($val->approved_by_2==$staffLoggedInId){
	// 					if($val->reduce_approver==1 && $val->approval_mode==2){
	// 						// do not add this leave
	// 					}else{
	// 						array_push($filtered_results, $val);
	// 					}
	// 				} else if($val->approved_by_3==$staffLoggedInId){
	// 					if($val->reduce_approver==1 && $val->approval_mode==3){
	// 						// do not add this leave
	// 					}else{
	// 						array_push($filtered_results, $val);
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}

	// 	switch ($hasApplyLeave) {
	// 		case 0:
	// 			//Not an approver or admin
	// 			$final_result = [];
	// 			break;
	// 		case 1:
	// 			//Admin
	// 			$final_result = $result;
	// 			break;
	// 		case 2:
	// 		case 3:
	// 			$final_result = $filtered_results;
	// 			break;
	// 	}

    //     $subData = $this->_getSubstituteStatus();
    //     foreach ($final_result as $key=>$val) {
	// 	  if (isset($subData[$val->id])) $val->sub = $subData[$val->id];
    //       $result[$key]->date_passed = 0;
    //       if(date('Y-m-d', strtotime($val->from_date)) < $today) {
    //         $result[$key]->date_passed = 1;
    //       }
    //     }
    //     return $final_result;
	// }

  	public function getLeavesData_3level($staffId, $hasApplyLeave=1,$statusId,$staffLoggedInId, $leave_year_id, $staff_status_type){
        $today = date('Y-m-d');
        $this->db_readonly->select("ls.request_date, lc.reduce_approver,ls.approval_mode,ls.final_status,CONCAT(ifnull(sm1.first_name, ''),' ',ifnull(sm1.last_name,'')) as approver_name1,CONCAT(ifnull(sm2.first_name, ''),' ',ifnull(sm2.last_name,'')) as approver_name2,CONCAT(ifnull(sm3.first_name, ''),' ',ifnull(sm3.last_name,'')) as approver_name3,ls.status,ls.approve_status_2,ls.approve_status_3,ls.leave_approved_by,ls.approved_by_2,ls.approved_by_3,ls.description,ls.id, lc.name, lc.name as leave_type, lc.short_name as short_name, DATE_FORMAT(ls.request_date,'%d-%m-%Y') as request_date, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays,ls.reason, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name,ls.staff_id,ls.leave_filed_by, ls.cancel_reason, ifnull(lvy.name, '-') as leave_year_name, ls.description, ls.approved_remarks_2,ls.approved_remarks_3,
		DATE_FORMAT(ls.approved_date_1,'%d-%b-%Y %h:%i %p') as approved_date_1,DATE_FORMAT(ls.approved_date_2,'%d-%b-%Y %h:%i %p') as approved_date_2,DATE_FORMAT(ls.approved_date_3,'%d-%b-%Y %h:%i %p') as approved_date_3, ls.leave_cancelled_by, DATE_FORMAT(ls.leave_cancelled_datetime,'%d-%b-%Y %h:%i %p') as leave_cancelled_datetime, CONCAT(ifnull(sm4.first_name, ''),' ',ifnull(sm4.last_name,'')) as leave_cancelled_by_name, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date_format_for_cancel_leave, DATE_FORMAT(ls.created_on,'%h:%i:%s %p') as request_time")
        ->from('leave_v2_staff ls')
        ->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
        ->join('leave_v2_year lvy', 'ls.leave_year_id=lvy.id','left')
        ->join('staff_master sm', 'ls.staff_id=sm.id','left')
		->join('staff_master sm1', 'ls.leave_approved_by=sm1.id','left')
		->join('staff_master sm2', 'ls.approved_by_2=sm2.id','left')
		->join('staff_master sm3', 'ls.approved_by_3=sm3.id', 'left')
		->join('staff_master sm4', 'ls.leave_cancelled_by=sm4.id','left')
		->where('sm.is_primary_instance',1)
		->where('ls.leave_year_id',$leave_year_id);

		if($staff_status_type){
			$this->db_readonly->where("sm.status", $staff_status_type);
		}
	

		// echo $hasApplyLeave; die();
		// $staffId=48
		// if($hasApplyLeave == 1) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		if($staffId!=0){
			$this->db_readonly->where("sm.id", $staffId);
		}


		if (gettype($statusId) == "array") {
			$this->db_readonly->where_in("ls.final_status", $statusId);
		}

        $this->db_readonly->order_by('ls.request_date', 'desc');
		$this->db_readonly->order_by('ls.id', 'desc');
        $result = $this->db_readonly->get()->result();

		if(empty($result)){
			return [];
		}

		$filtered_results=array();
		if($hasApplyLeave == 2 || $hasApplyLeave == 3){
			foreach($result as $key => $val) {
				$val->request_time = date('h:i:s A', strtotime(local_time($val->request_time)));
				if($val->leave_approved_by==$staffLoggedInId || $val->approved_by_2==$staffLoggedInId || $val->approved_by_3==$staffLoggedInId){
					if($val->leave_approved_by==$staffLoggedInId){
						array_push($filtered_results, $val);
					} else if($val->approved_by_2==$staffLoggedInId){
						if($val->reduce_approver==1 && $val->approval_mode==2){
							// do not add this leave
						}else{
							array_push($filtered_results, $val);
						}
					} else if($val->approved_by_3==$staffLoggedInId){
						if($val->reduce_approver==1 && $val->approval_mode==3){
							// do not add this leave
						}else{
							array_push($filtered_results, $val);
						}
					}
				}
			}
		}

		switch ($hasApplyLeave) {
			case 0:
				//Not an approver or admin
				$final_result = [];
				break;
			case 1:
				//Admin
				$final_result = $result;
				break;
			case 2:
			case 3:
				$final_result = $filtered_results;
				break;
		}

		// echo '<pre>';print_r($final_result);die();

        $subData = $this->_getSubstituteStatus();
        foreach ($final_result as $key=>$val) {
		  if (isset($subData[$val->id])) $val->sub = $subData[$val->id];
          $result[$key]->date_passed = 0;
          if(date('Y-m-d', strtotime($val->from_date)) < $today) {
            $result[$key]->date_passed = 1;
          }
        }

        return $final_result;
    }
	public function get_report_leaves_data_3_level($staffId, $hasApplyLeave=1,$statusId,$staffLoggedInId,$leave_year_id,$staff_status_type){
        $today = date('Y-m-d');
        $this->db_readonly->select("ls.request_date, ls.approved_remarks_2,ls.approved_remarks_3,lc.reduce_approver, ls.approval_mode,ls.final_status,CONCAT(ifnull(sm1.first_name, ''),' ',ifnull(sm1.last_name,'')) as approver_name1,CONCAT(ifnull(sm2.first_name, ''),' ',ifnull(sm2.last_name,'')) as approver_name2,CONCAT(ifnull(sm3.first_name, ''),' ',ifnull(sm3.last_name,'')) as approver_name3,ls.status,ls.approve_status_2,ls.approve_status_3,ls.leave_approved_by,ls.approved_by_2,ls.approved_by_3,ls.description,ls.id, lc.name, lc.name as leave_type, lc.short_name as short_name, DATE_FORMAT(ls.request_date,'%d-%m-%Y') as request_date, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(ls.to_date,'%d-%m-%Y') as to_date,ls.noofdays,ls.reason, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name,ls.staff_id,ls.leave_filed_by, ls.cancel_reason, ifnull(lvy.name, '-') as leave_year_name, ls.leave_cancelled_by, DATE_FORMAT(ls.leave_cancelled_datetime,'%d-%M-%Y %h:%i:%s %p') as leave_cancelled_datetime, CONCAT(ifnull(sm4.first_name, ''),' ',ifnull(sm4.last_name,'')) as leave_cancelled_by_name")
        ->from('leave_v2_staff ls')
        ->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
        ->join('leave_v2_year lvy', 'ls.leave_year_id=lvy.id','left')
        ->join('staff_master sm', 'ls.staff_id=sm.id','left')
		->join('staff_master sm1', 'ls.leave_approved_by=sm1.id','left')
		->join('staff_master sm2', 'ls.approved_by_2=sm2.id','left')
		->join('staff_master sm3', 'ls.approved_by_3=sm3.id','left')
		->join('staff_master sm4', 'ls.leave_cancelled_by=sm4.id','left')
		->where('sm.is_primary_instance',1)
		->where('ls.leave_year_id',$leave_year_id);

		if ($staff_status_type) {
			$this->db_readonly->where("sm.status", $staff_status_type);
		}
	
		// echo $hasApplyLeave; die();
		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where('ls.leave_approved_by',$staffLoggedInId );
		// 		$this->db_readonly->or_where('ls.approved_by_2', $staffLoggedInId);
		// 		$this->db_readonly->or_where('ls.approved_by_3', $staffLoggedInId);
		// 		// $this->db_readonly->where("(sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// 	// $this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }
		
        // return $result;
		if ($staffId != 0) {
			$this->db_readonly->where("sm.id", $staffId);
		}

		$this->db_readonly->where_in("ls.final_status", $statusId);

		$this->db_readonly->order_by('ls.request_date', 'desc');
		$result = $this->db_readonly->get()->result();

		$filtered_results = array();
		if ($hasApplyLeave == 2 || $hasApplyLeave == 3) {
			foreach ($result as $key => $val) {
				if ($val->leave_approved_by == $staffLoggedInId || $val->approved_by_2 == $staffLoggedInId || $val->approved_by_3 == $staffLoggedInId) {
					array_push($filtered_results, $val);
				}
			}
		}

		switch ($hasApplyLeave) {
			case 0:
				//Not an approver or admin
				$final_result = [];
				break;
			case 1:
				//Admin
				$final_result = $result;
				break;
			case 2:
			case 3:
				$final_result = $filtered_results;
				break;
		}

		// echo '<pre>';print_r($final_result);die();

		$subData = $this->_getSubstituteStatus();
		foreach ($final_result as $key => $val) {
			if (isset($subData[$val->id]))
				$val->sub = $subData[$val->id];
			$result[$key]->date_passed = 0;
			if (date('Y-m-d', strtotime($val->from_date)) < $today) {
				$result[$key]->date_passed = 1;
			}
		}

		return $final_result;
    }

	public function get_leaves_data_by_value($staffId, $hasApplyLeave=1,$val,$staffLoggedInId,$leave_year_id){
        $today = date('Y-m-d');
        $this->db_readonly->select("ls.request_date, lc.reduce_approver, ls.approval_mode,ls.approve_status_2,ls.approve_status_3,ls.final_status,ls.description,ls.id, lc.name, lc.name as leave_type, lc.short_name as short_name, DATE_FORMAT(ls.request_date,'%M %d, %Y') as request_date, DATE_FORMAT(ls.from_date,'%M %d, %Y') as from_date, DATE_FORMAT(ls.to_date,'%M %d, %Y') as to_date,ls.noofdays,ls.reason,ls.status, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name,CONCAT(ifnull(sm1.first_name, ''),' ',ifnull(sm1.last_name,'')) as approver_1,CONCAT(ifnull(sm2.first_name, ''),' ',ifnull(sm2.last_name,'')) as approver_2,CONCAT(ifnull(sm3.first_name, ''),' ',ifnull(sm3.last_name,'')) as approver_3,ls.staff_id,ls.leave_filed_by, ls.cancel_reason, ifnull(lvy.name, '-') as leave_year_name, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date_format_for_cancel_leave")
        ->from('leave_v2_staff ls')
        ->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
        ->join('leave_v2_year lvy', 'ls.leave_year_id=lvy.id','left')
        ->join('staff_master sm', 'ls.staff_id=sm.id','left')
		->join('staff_master sm1', 'ls.leave_approved_by=sm1.id','left')
		->join('staff_master sm2', 'ls.approved_by_2=sm2.id','left')
		->join('staff_master sm3', 'ls.approved_by_3=sm3.id','left')
		->where('sm.is_primary_instance',1)
		->where('ls.leave_year_id',$leave_year_id);


        
		// echo $hasApplyLeave; die();
		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where("(sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// 	// $this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		
		// $val==3 && $this->db_readonly->where("ls.status is not null",NULL, FALSE);
		// $val==2 && $this->db_readonly->where("ls.leave_filed_by",$staffId);
		// $val==1 && $this->db_readonly->where("ls.status",0);

		if($val==1){
			// my all pending leaves
			$this->db_readonly->where("ls.staff_id=$staffId");
			$this->db_readonly->where("ls.final_status",0);
		}else if($val==2){
			//all the leaves which are filed by me
			$this->db_readonly->where("ls.leave_filed_by=$staffId");
		}else if($val==3){
			// all the leaves which  are mine
			$this->db_readonly->where("ls.staff_id=$staffId");
		}

        $this->db_readonly->order_by('ls.request_date', 'desc');
        $result = $this->db_readonly->get()->result();

        $subData = $this->_getSubstituteStatus();
        foreach ($result as $key=>$val) {
		  if (isset($subData[$val->id])) $val->sub = $subData[$val->id];
          $result[$key]->date_passed = 0;
          if(date('Y-m-d', strtotime($val->from_date)) < $today) {
            $result[$key]->date_passed = 1;
          }
        }

        return $result;
    }
	public function staff_leaves_by_value($staffId, $hasApplyLeave=1,$val,$staffLoggedInId,$leave_year_id){
        $today = date('Y-m-d');
        $this->db_readonly->select("ls.description,ls.id, lc.name, lc.name as leave_type, lc.short_name as short_name, DATE_FORMAT(ls.request_date,'%M %d, %Y') as request_date, DATE_FORMAT(ls.from_date,'%M %d, %Y') as from_date, DATE_FORMAT(ls.to_date,'%M %d, %Y') as to_date,ls.noofdays,ls.reason,ls.status, CONCAT(ifnull(sm.first_name, ''),' ',ifnull(sm.last_name,'')) as staff_name,ls.staff_id,ls.leave_filed_by, ls.cancel_reason, ifnull(lvy.name, '-') as leave_year_name, DATE_FORMAT(ls.from_date,'%d-%m-%Y') as from_date_format_for_cancel_leave")
        ->from('leave_v2_staff ls')
        ->join('leave_v2_category lc', 'lc.id=ls.leave_category_id')
        ->join('leave_v2_year lvy', 'ls.leave_year_id=lvy.id','left')
        ->join('staff_master sm', 'ls.staff_id=sm.id','left')
		->where("sm.is_primary_instance",1)
		->where('ls.leave_year_id', $leave_year_id);

		// echo $hasApplyLeave; die();
		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where("(sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// 	// $this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		// if($hasApplyLeave == 2){
		// 	if($staffId==0){
		// 		$this->db_readonly->where("(ls.staff_id=$staffLoggedInId OR sm.reporting_manager_id=$staffLoggedInId)");
		// 	}else{
		// 		$this->db_readonly->where("ls.staff_id=$staffId");
		// 	}
		// } else if($hasApplyLeave == 0) {
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }else if($hasApplyLeave == 1 && $staffId!=0){
		// 	$this->db_readonly->where("ls.staff_id=$staffId");
		// }

		
		// $val==3 && $this->db_readonly->where("ls.status is not null",NULL, FALSE);
		// $val==2 && $this->db_readonly->where("ls.leave_filed_by",$staffId);
		// $val==1 && $this->db_readonly->where("ls.status",0);

		if($val==1){
			// all my pending leaves
			$this->db_readonly->where("ls.staff_id=$staffId");
			$this->db_readonly->where("ls.status",0);
		}else if($val==2){
			//all the leaves filed by me
			$this->db_readonly->where("ls.leave_filed_by=$staffId");
		}else if($val==3){
			// all the leaves which  are mine
			$this->db_readonly->where("ls.staff_id=$staffId");
		}
	

        $this->db_readonly->order_by('ls.request_date', 'desc');
        $result = $this->db_readonly->get()->result();

        $subData = $this->_getSubstituteStatus();
        foreach ($result as $key=>$val) {
		  if (isset($subData[$val->id])) $val->sub = $subData[$val->id];
          $result[$key]->date_passed = 0;
          if(date('Y-m-d', strtotime($val->from_date)) < $today) {
            $result[$key]->date_passed = 1;
          }
        }

        return $result;
    }

	public function get_individual_staff_attendance_info($staffId,$requestDate){
		$staff_attendance_id=$this->db->select('id, status')->from("st_attendance")->where("staff_id",$staffId)->where("date","$requestDate")->get()->row();

		if(empty($staff_attendance_id)) return 0;

		$staff_attendance_info=$this->db->select('*')->from("st_attendance_transactions")->where("attendance_id",$staff_attendance_id->id)->get()->result();

		foreach($staff_attendance_info as $transaction){
			$transaction->event_time=date(' l jS M Y h:i:s A',strtotime(local_time($transaction->event_time)));
		}

		array_push($staff_attendance_info,["attendance_status"=>$staff_attendance_id->status]);
		
		return $staff_attendance_info;
	}

	public function get_leave_approvers($staff_ids){
		return $this->db_readonly->select("u.email, sm.id as staff_id")
		->from("staff_master sm")
		->join('avatar a', 'a.stakeholder_id=sm.id')
		->join('users u', 'a.user_id=u.id')
		->where('a.avatar_type', '4')
		->where('sm.status', 2)
		->where_in("sm.id",$staff_ids)
		->get()->result();
	}

	// private  function get_staff_absent_detail($staffId,$date){
	// 	$result=$this->db_readonly->select("*")
	// 	->from("st_attendance sta")
	// 	->where("sta.id",$staffId)
	// 	->where("sta.date",$date)
	// 	->get()->row();

	// 	if(empty($result)){
	// 		 return 1;
	// 	}else{
	// 		 return 0;
	// 	}
	// }

	// private function getDatesFromRange($start, $end, $format = 'Y-m-d'){
	// 	$array = array();
	// 	$interval = new DateInterval('P1D');

	// 	$realEnd = new DateTime($end);
	// 	$realEnd->add($interval);

	// 	$period = new DatePeriod(new DateTime($start), $interval, $realEnd);

	// 	foreach ($period as $date) {
	// 		$array[] = $date->format($format);
	// 	}

	// 	return $array;
	// }

	// public function check_is_Staff_absent($data){
	// 	$staffId=$this->authorization->getAvatarStakeHolderId();
	// 	$fromDate=$data["fromDate"];
	// 	$toDate= $data["toDate"];
	// 	// now, generate dates if from date and to date are not same else check for single date
	// 	if($fromDate==$toDate){
	// 		return $this->get_staff_absent_detail($staffId,$fromDate);
	// 	}else{
	// 		// for multiple generated dates
	// 		$dates=$this->getDatesFromRange($fromDate,$toDate);

	// 		forEach($dates as $kry => $val){
	// 			$res=$this->get_staff_absent_detail($staffId,$val);
	// 			if($res==0){
	// 				return 0;
	// 			}
	// 		}
	// 		return 1;
	// 	}
	// }

	public function reject_approved_staff_leave($data){
        $this->db->trans_start();

        $leaveId=$data["leaveId"];

        $is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");

        $result=$this->db_readonly->select("staff_id, leave_category_id, noofdays, status, final_status")
		->from("leave_v2_staff")
		->where("id",$leaveId)
		->get()->row();

        if($is_multi_level_leave_enabled && $result->final_status==3 || $result->status == 3){
            return 0;
        }

        $leave_year_id=$this->db_readonly->select("id")
        ->from("leave_v2_year")
        ->where("is_active",1)
        ->get()->row()->id;

        $used_quota=$this->db_readonly->select("used_quota")
        ->from("leave_v2_staff_quota")
        ->where("leave_v2_year_id",$leave_year_id)
        ->where("staff_id",$result->staff_id)
        ->where("leave_category_id",$result->leave_category_id)
        ->get()->row()->used_quota;

        $is_updated=$this->db->where("leave_v2_year_id",$leave_year_id)
        ->where("staff_id",$result->staff_id)
        ->where("leave_category_id",$result->leave_category_id)
        ->update("leave_v2_staff_quota",["used_quota"=>$used_quota-$result->noofdays]);

        if($is_updated){
            $this->db->where("id",$leaveId);
            if($is_multi_level_leave_enabled){
                $this->db->update("leave_v2_staff",["final_status"=>3]);
            }else{
                $this->db->update("leave_v2_staff",["status"=>3]);
            }
        }

        $this->db->trans_complete();

        if($this->db->trans_status()==false){
            $this->db->trans_rollback();
            return 0;
        }else{
            $this->db->trans_commit();
            return 1;
        }
	}

	function getStaffLeaveLops($data){
		$fromDate = date('Y-m-d', strtotime($data["fromDate"]));
		$toDate = date('Y-m-d', strtotime($data["toDate"]));
		$staffStatusType = $data["staffStatusType"];
	
		$is_multi_level_leave_approver_mode_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
	
		$this->db_readonly->select("
			sm.id as staff_id,
			lvs.id as leave_id,
			IFNULL(sm.employee_code, ' - ') as employee_id,
			CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) as staff_name,
			SUM(lvs.noofdays) as total_lops_taken,
			GROUP_CONCAT(DISTINCT DATE_FORMAT(lvs.from_date, '%d-%b-%Y'), ' to ', DATE_FORMAT(lvs.to_date, '%d-%b-%Y') SEPARATOR ', ') as leave_taken_date,
			IFNULL(lvs.reason, ' - ') as leave_reason
		")
		->from("staff_master sm");
	
		// Choose status field based on approval mode
		if ((int)$is_multi_level_leave_approver_mode_enabled == 1) {
			$this->db_readonly->join("leave_v2_staff lvs", "
				lvs.staff_id = sm.id AND 
				lvs.from_date >= '$fromDate' AND 
				lvs.to_date <= '$toDate' AND 
				lvs.leave_category_id IN (SELECT id FROM leave_v2_category WHERE is_loss_of_pay = 1) AND 
				lvs.final_status IN (0,1,2)
			", "left");
		} else {
			$this->db_readonly->join("leave_v2_staff lvs", "
				lvs.staff_id = sm.id AND 
				lvs.from_date >= '$fromDate' AND 
				lvs.to_date <= '$toDate' AND 
				lvs.leave_category_id IN (SELECT id FROM leave_v2_category WHERE is_loss_of_pay = 1) AND 
				lvs.status IN (0,1,2)
			", "left");
		}
	
		$this->db_readonly->where("sm.is_primary_instance", 1);
	
		// Staff status-based filtering
		if ($staffStatusType == 2) {
			$this->db_readonly->where("sm.status", 2);
		} elseif ($staffStatusType == 4) {
			$this->db_readonly->where("sm.status", 4);
			$this->db_readonly->where("EXISTS (
				SELECT 1 FROM leave_v2_staff_quota q 
				WHERE q.staff_id = sm.id 
				  AND q.leave_v2_year_id = (SELECT id FROM leave_v2_year WHERE is_active = 1 LIMIT 1)
			)"); // raw query in where
		}
	
		$this->db_readonly->group_by("sm.id");
		return $this->db_readonly->get()->result();
	}
	
	public function getStaffLeaveAutoApprovalPermissionForAdmin($leave_category_id){
		$enable_leave_auto_approval_by_admin=$this->db_readonly->select("enable_leave_auto_approval_by_admin")
		->from("leave_v2_category")
		->where("id",$leave_category_id)
		->get()->row();

		if(!empty($enable_leave_auto_approval_by_admin)){
			return $enable_leave_auto_approval_by_admin->enable_leave_auto_approval_by_admin;
		}else{
			return 0;
		}
	}

	// Staff leave conversion logic[starts here] in progress
	public function get_staff_leave_info($leave_id){
		$leave_info=$this->db_readonly->select("lvs.*, lvs.id as leave_v2_staff_id, lvc.id as leave_v2_category_id, lvc.consider_holiday, lvc.name as leave_category_name, lvc.short_name as leave_category_short_name, lvc.enable_leave_auto_approval_by_admin")
		->from("leave_v2_staff lvs")
		->join("leave_v2_category lvc","lvc.id=lvs.leave_category_id")
		->where("lvs.id",$leave_id)
		->get()->row();

		if(empty($leave_info)){
			return [];
		}else{
			return $leave_info;
		}
	}

	public function get_available_staff_leave_categories($leave_v2_category_id,$staff_id, $leave_year_id){
		$leave_categories=$this->db_readonly->select("lvc.id as leave_category_id, lvc.name, lvc.short_name, lvc.has_quota, lvq.total_quota, lvq.used_quota")
		->from("leave_v2_category lvc")
		->join("leave_v2_staff_quota lvq","lvq.leave_category_id=lvc.id")
		->where("lvq.staff_id", $staff_id)
		->where("lvq.leave_v2_year_id", $leave_year_id)
		->where("lvc.id!=",$leave_v2_category_id)
		->where("lvc.status",1)
		->get()->result();

		$original_available_leave_categories_with_balance_quota = [];
		$total_has_quota_leave_categories_with_balance_quota=[];

		if(empty($leave_categories)) return [];

		$available_leave_categories=[];
		foreach($leave_categories as $key => $leave){
			if($leave->has_quota==1){
				if(($leave->total_quota - $leave->used_quota) > 0){
					$available_leave_categories[$key]["leave_category_id"]=$leave->leave_category_id;
					$available_leave_categories[$key]["name"] = $leave->name;
					$available_leave_categories[$key]["short_name"] = $leave->short_name;
					$available_leave_categories[$key]["has_quota"] = $leave->has_quota;
					$original_available_leave_categories_with_balance_quota[$leave->leave_category_id] = $leave->total_quota - $leave->used_quota;
				}
				$total_has_quota_leave_categories_with_balance_quota[$leave->leave_category_id] = $leave->total_quota - $leave->used_quota;
			}else{
				$available_leave_categories[$key]["leave_category_id"] = $leave->leave_category_id;
				$available_leave_categories[$key]["name"] = $leave->name;
				$available_leave_categories[$key]["short_name"] = $leave->short_name;
				$available_leave_categories[$key]["has_quota"] = $leave->has_quota;
			}
		}

		$data["available_leave_categories"]=$available_leave_categories;
		$data["original_available_leave_categories_with_balance_quota"] = $original_available_leave_categories_with_balance_quota;
		$data["total_has_quota_leave_categories_with_balance_quota"] = $total_has_quota_leave_categories_with_balance_quota;
		return $data;
	}
	public function insert_approved_staff_leave_from_leave_convert($leave_info){
		unset($leave_info["id"]);
		unset($leave_info["leave_v2_staff_id"]);
		unset($leave_info["leave_v2_category_id"]);
		unset($leave_info["consider_holiday"]);
		unset($leave_info["leave_category_name"]);
		unset($leave_info["leave_category_short_name"]);
		unset($leave_info["selection_type"]);

		$this->db->trans_start();

		$this->db->insert("leave_v2_staff",$leave_info);
		$current_leave_id = $this->db->insert_id();

		// Update used quota
		$applied_leave_quota_details = $this->db->select("lq.total_quota,lq.used_quota,lc.has_quota")
			->from("leave_v2_staff_quota lq")
			->join("leave_v2_category lc", "lc.id=lq.leave_category_id")
			->where("lq.leave_category_id", $leave_info["leave_category_id"])
			->where("lq.staff_id", $leave_info["staff_id"])
			->where("lq.leave_v2_year_id", $leave_info["leave_year_id"])
			->get()->row();

		if (empty($applied_leave_quota_details)) {
			return 0;
		}

		if ($applied_leave_quota_details->has_quota == 1) {
			$remaining_leave_quota = $applied_leave_quota_details->total_quota - $applied_leave_quota_details->used_quota;

			if ($leave_info["noofdays"] > $remaining_leave_quota) {
				return 0;
			}
		}

		if($current_leave_id){
			return $this->db->where("leave_category_id", $leave_info["leave_category_id"])
				->where("staff_id", $leave_info["staff_id"])
				->where("leave_v2_year_id", $leave_info["leave_year_id"])
				->update("leave_v2_staff_quota", ["used_quota" => $applied_leave_quota_details->used_quota + $leave_info["noofdays"]]);
		}

		$this->db->trans_complete();
		if ($this->db->trans_status() === FALSE) {
			$this->db->trans_rollback();
			return 0;
		}
		$this->db->trans_commit();
		return 1;
	}

	
	// Staff leave conversion logic [ends here]
	public function getFinancialLeaveYears() {
    	return $this->db_readonly->select("py.id as financial_year_id, py.f_year as f_year_name, py.from_date, py.to_date")
			->from("new_payroll_financial_year py")
			->join("new_payroll_schedules ps","ps.financial_year_id=py.id")
			->where("ps.lop_start_date!=",null)
			->where("ps.lop_end_date!=", null)
			// ->where("is_visible_to_staff",1)
			->group_by("py.id")
			->order_by("py.id","DESC")
			->get()->result();
    }

	public function getPayrollMonthsByFinancialYearId($payload){
		return $this->db_readonly->select("id as new_payroll_schedules_id, financial_year_id, schedule_name, lop_start_date, lop_end_date")
		->from("new_payroll_schedules")
		->where("financial_year_id",$payload["selectedFinancialYear"])
		->where("lop_start_date!=",null)
		->where("lop_end_date!=", null)
		->get()->result();
	}

	public function getPreviousLeaves($payload){
		$staffId=$payload["staffId"];

		if($staffId<=0) return [];


		$is_multi_level_leave_enabled = $this->settings->getSetting("enable_multi_level_leave_approver_mode");
		
		$status="lvs.status";
		if ((int)$is_multi_level_leave_enabled == 1) {
			$status = "lvs.final_status";
		}

		return $this->db_readonly->select("concat(ifnull(sm.first_name,''), ' ', ifnull(sm.last_name,'')) as staff_name, DATE_FORMAT(lvs.request_date,'%d-%m-%Y') as applied_on, DATE_FORMAT(lvs.from_date,'%d-%m-%Y') as from_date, DATE_FORMAT(lvs.to_date,'%d-%m-%Y') as to_date, lvs.noofdays, concat(ifnull(sm2.first_name,''), ' ', ifnull(sm2.last_name,'')) as leave_filed_by, lvc.name as leave_taken_type")
		->from("leave_v2_staff lvs")
		->join("leave_v2_year ly","ly.id=lvs.leave_year_id")
		->join("staff_master sm", "sm.id=lvs.staff_id")
		->join("staff_master sm2", "sm2.id=lvs.leave_filed_by")
		->join("leave_v2_category lvc", "lvc.id=lvs.leave_category_id")
		->where("lvs.staff_id",$staffId)
		->where("ly.is_active", 1)
		->where_in($status,[0,1,2])
		->order_by("lvs.id","desc")
		->get()->result();
	}

	public function getFinalApprovalStatus($payload){
		$leaveId=$payload["leaveId"];

		$result=$this->db_readonly->select("final_status")
			->from("leave_v2_staff")
			->where("id", $leaveId)
			->get()->row();

		if(!empty($result)){
			return $result;
		}else{
			return new StdClass();
		}
    }
}