<?php
class Staff_mytasksv2_model extends CI_Model
{
	private $yearId;
	private $current_branch;
	public function __construct() {
		parent::__construct();
		$this->load->library('filemanager');
		$this->yearId = $this->acad_year->getAcadYearId();
	}

	public function _kolkata_datetime()	{
		$timezone = new DateTimeZone("Asia/Kolkata");
		$date = new DateTime();
		$date->setTimezone($timezone);
		$dtobj = $date->format('Y-m-d h:i:s');
		return $dtobj;
	}

	public function getStaffList() {
		$this->db_readonly->select("CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name,sm.id as staff_id");
		$this->db_readonly->from('staff_master sm');
		$this->db_readonly->where('sm.status', 2);
		$this->db_readonly->order_by('sm.first_name');

		return $this->db_readonly->get()->result();
	}

	public function add_to_basket($basket_name){
		$count = $this->db_readonly->where('basket_name', $basket_name)->from('stb_baskets')->count_all_results();

		if ($count > 0) {
			return -1;
		}

		//Add a row to the history table
		$data = array(
			'created_on' => $this->_kolkata_datetime(),
			'basket_name' => $basket_name,
			'description' => $basket_name,
			'created_by' => $this->authorization->getAvatarStakeHolderId(),
		);
		$result = $this->db->insert('stb_baskets', $data);

		if ($result == '1') {
			return $this->db->insert_id();
		} else {
			return '0';
		}
	}

	public function getBasketList(){
		$this->db_readonly->select("id as basket_id, basket_name");
		$this->db_readonly->from('stb_baskets');
		$this->db_readonly->where('created_by', $this->authorization->getAvatarStakeHolderId());
		$this->db_readonly->order_by('created_on', 'desc');

		return $this->db_readonly->get()->result();
	}

	public function getBackgroundImage(){
		$staff_logged_in = $this->authorization->getAvatarStakeHolderId();
		$image_url = $this->db_readonly->select('background_image_url')
							->from('stb_user_settings')
							->where('staff_id', $staff_logged_in)
							->get()->row();
		
		$background_image = '';
		if(!empty($image_url)){
			$background_image = $this->filemanager->getFilePath($image_url->background_image_url);
		}

		return $background_image;
	}

	public function getAllStaffTeam(){
		$team_details = $this->db_readonly->select('sst.id as id, sst.team_name as team_name, CONCAT(ifnull(sm.first_name, \'\'), \' \', ifnull(sm.last_name, \'\')) as staff_name')
											->from('stb_staff_teams sst')
											->where('sst.status', 1)
											->join('staff_master sm', 'sm.id = sst.primary_staff')
											->get()->result();
		return $team_details;
	}

	public function getTaskListCount(){
		$count = $this->db_readonly->select('COUNT(*) as count')
								->from('stb_mytasklists')
								->where('is_deleted', 0)
								->where('created_by', $this->authorization->getAvatarStakeHolderId())
								->get()
								->row()
								->count;

		return $count;
	}

	public function getAllMyTaskLists($input){
		$result = $this->db_readonly->select("smtl.id, smtl.list_name, smtl.list_background_color as backgroundColor")
			->from('stb_mytasklists smtl')
			->where('smtl.is_deleted', 0)
			->where('smtl.created_by', $this->authorization->getAvatarStakeHolderId())
			->where('smtl.board_id', $input['board_id'])
			->get()->result();
		
		return $result;
	}

	public function getallmylists($input) {
		$result = $this->db_readonly->select("smtl.id, smtl.list_name, smtl.list_background_color")
			->from('stb_mytasklists smtl')
			->where('smtl.is_deleted', 0)
			->where('smtl.created_by', $this->authorization->getAvatarStakeHolderId())
			->where('smtl.board_id', $input['board_id'])
			->get()->result();
		// $defaultTaskList = new stdClass();
		// $defaultTaskList->id = 0;
		// $defaultTaskList->list_name = 'General List';
		// $defaultTaskList->list_background_color = '#808080';
		// if(empty($result)){
		// 	$general_list_tasks = $this->db_readonly->select('count(stb_mytasklist_id) as general_list_tasks_count')
		// 	->from('stb_mytasks')
		// 	->where('stb_mytasklist_id', 0)
		// 	->where('created_by', $this->authorization->getAvatarStakeHolderId())
		// 	->get()->row();
		// 	if($general_list_tasks->general_list_tasks_count != 0){
		// 		$result = [$defaultTaskList];
		// 	}
		// } else {
		// 	array_unshift($result, $defaultTaskList);
		// }
		// echo "<pre>";print_r($result);die();
		foreach ($result as $tasklist) {
			$counts = $this->getCountsForTasks($tasklist->id);
			$tasklist->completed_tasks_count = $counts->completed_tasks_count ? $counts->completed_tasks_count : 0;
			$tasklist->delegated_tasks_count = $counts->delegated_tasks_count ? $counts->delegated_tasks_count : 0;
		}
		if (empty($result)) {
			return ['empty' => true, 'message' => 'No task lists found for the specified board.'];
		} else {
			return ['empty' => false, 'result' => $result];
		}
	}

	public function get_all_unscheduled_tasks($input) {
		// echo "<pre>";print_r($input);die();
		$result = $this->db_readonly->select("smt.id, smt.task_name, smt.due_date, if(mtl.list_background_color is null, '#808080', mtl.list_background_color) as task_color, if(mtl.list_name is null, 'General', mtl.list_name) as tasklist_name, mtl.id as tasklist_id, smt.priority as priority")
			->from('stb_mytasks smt')
			->join('stb_mytasklists mtl', 'mtl.id=smt.stb_mytasklist_id','left')
			->where('smt.status', 1)
			->where("start_date is null and due_date is null")
			->where('smt.is_deleted', 0)
			->where('mtl.board_id', $input['board_id'])
			// ->where('smt.delegated_to_project is NOT NULL')
			->where('smt.created_by', $this->authorization->getAvatarStakeHolderId())
			->order_by('smt.id', 'desc')
			->get()->result();
		return $result;
	}

	public function get_all_open_delegated_tasks_of_all_tasklists($board_id, $start_date, $end_date) {
		$result = $this->db_readonly->select("smt.id, smt.task_name, smt.priority as task_priority, smt.due_date, smt.start_date, IF(mtl.list_background_color IS NULL, '#3A87AD', mtl.list_background_color) as task_color, CASE WHEN mtl.list_name IS NULL THEN 'General' ELSE mtl.list_name END as tasklist_name, IF(mtl.id IS NULL, 0, mtl.id) as tasklist_id, smt.task_type, IFNULL(smt.description, '') as task_desc")
			->from('stb_mytasks smt')
			->join('stb_mytasklists mtl', 'mtl.id = smt.stb_mytasklist_id', 'left')
			->where("(mtl.board_id IS NULL OR mtl.board_id = '.$board_id.')")
			->where('smt.status', 1)
			->where("start_date between '$start_date' and '$end_date'")
			->where('smt.is_deleted', 0)
			// ->where('smt.delegated_to_project is NOT NULL')
			->where('smt.created_by', $this->authorization->getAvatarStakeHolderId())
			->order_by('smt.id', 'desc')
			->get()->result();
		foreach ($result as $task => $val) {
			if(!empty($val->task_type)){
				$val->task_type = json_decode($val->task_type);
			}
		}
		foreach ($result as $task) {
			$task->task_type = json_decode($task->task_type);
			
			if (!empty($task->task_type)) {
				// Fetch task type colors
				$this->db_readonly->select('id, task_type_color');
				$this->db_readonly->where_in('id', $task->task_type);
				$task_types = $this->db_readonly->get('stb_tasktypes')->row();
				if(!empty($task_types)){
					$task->task_type_colors = $task_types->task_type_color;
				} else {
					$task->task_type_colors = '#CCCCCC';
				}
			} else {
				$task->task_type_colors = '#CCCCCC';
			}
		}
		return $result;
	}

	public function get_delegated_tasklists(){

		$lists = $this->db_readonly->select("smtl.id, smtl.list_name, smtl.list_background_color, COUNT(smt.delegated_to_project) as delegated_task_count")
			->from('stb_mytasklists smtl')
			->where('smtl.is_deleted', 0)
			->join('stb_mytasks smt', 'smt.stb_mytasklist_id = smtl.id')
			->where('smt.is_deleted', 0)
			->where('smt.status', 1)
			->join('stb_tasks st', 'st.id = smt.delegated_to_task_id')
			->where('st.basket_id', -1)
			->join('stb_staff ss', 'ss.stb_id = smt.delegated_to_task_id')
			->where('ss.staff_id', $this->authorization->getAvatarStakeHolderId())
			->where('smt.delegated_to_project is NOT NULL')
			->group_by('smtl.id, smtl.list_name')
			->get()->result();

		foreach($lists as $list){
			$list->completed_task_count = $this->get_delegated_completed_task_count($list->id);
		}

		// echo "<pre>";print_r($lists);die();

		return $lists;
	}

	private function get_delegated_completed_task_count($list_id){
		$completed_task_count = $this->db_readonly->select("COUNT(smt.status) as completed_task_count")
			->from('stb_mytasklists smtl')
			->where('smtl.id', $list_id)
			->where('smtl.is_deleted', 0)
			->join('stb_mytasks smt', 'smt.stb_mytasklist_id = smtl.id')
			->where('smt.is_deleted', 0)
			->where('smt.status', 0)
			->join('stb_tasks st', 'st.id = smt.delegated_to_task_id')
			->where('st.basket_id', -1)
			->join('stb_staff ss', 'ss.stb_id = smt.delegated_to_task_id')
			->where('ss.staff_id', $this->authorization->getAvatarStakeHolderId())
			->where('smt.delegated_to_project is NOT NULL')
			->group_by('smtl.id, smtl.list_name')
			->get()->row();

		return $completed_task_count ? $completed_task_count->completed_task_count : 0 ;
	}

	public function get_delegated_tasks_to_you($list_id){

		$this->db_readonly->select('smt.id as id, smt.task_name as task_name, CONCAT(ifnull(sm.first_name, \'\'), \' \', ifnull(sm.last_name, \'\')) as delegated_by, sm.picture_url as staff_photo, smt.delegated_to_task_id as delegated_to_task_id, smt.priority as task_priority, smtl.list_name as list_name, date_format(st.complete_by,"%d-%m-%Y") as complete_by');
		$this->db_readonly->from('stb_mytasks smt');
		$this->db_readonly->join('staff_master sm', 'sm.id = smt.created_by');
		$this->db_readonly->join('stb_tasks st', 'st.id = smt.delegated_to_task_id');
		$this->db_readonly->where('st.basket_id', -1);
		$this->db_readonly->join('stb_mytasklists smtl', 'smtl.id = smt.stb_mytasklist_id');
		$this->db_readonly->where('smt.stb_mytasklist_id', $list_id);
		$this->db_readonly->where('smt.status', 1);
		$this->db_readonly->where('smt.is_deleted', 0);
		$this->db_readonly->where('smt.delegated_to_project IS NOT NULL');
		$this->db_readonly->order_by('smt.id', 'asc');

		$result = $this->db_readonly->get()->result();

		$task_data = [];
		foreach ($result as $row => $val) {
			$task_staff_details = $this->get_delegated_details($val->delegated_to_task_id);
			if (!empty($task_staff_details)) {
				foreach ($task_staff_details as $details) {
					$val->primary_staff_name = $details->primary_staff_name;
					$val->primary_staff_photo = $details->primary_staff_photo;
					$val->primary_staff_id = $details->primary_staff_id;
					$val->status = $details->status;
				}
			}

			// $comments = $this->get_task_comments_by_id($val->id);
			// $val->comments = $comments;

			if(!empty($val->staff_photo)){
				$val->staff_photo = $this->filemanager->getFilePath($val->staff_photo);
			}else{
				$val->staff_photo = base_url().'/assets/img/sample_boy_image.png';
			}

			if(isset($val->primary_staff_name)){
				$task_data[] = $val;
			}
		}

		$final_data = $task_data ? $task_data : null;

		// echo "<pre>";print_r($task_data);die();
		return $final_data;
	}

	private function get_delegated_details($delegated_task_id) {
		$task_staff_details = $this->db_readonly->select('stb_staff.staff_id as secondary_staff_id, stb_staff.stb_id as stb_id, stb_staff.role as role, stb_tasks.status as status')
			->from('stb_staff')
			->join('stb_tasks', 'stb_tasks.id = stb_staff.stb_id')
			// ->where('stb_staff.role', 'secondary')
			->where('stb_staff.staff_id', $this->authorization->getAvatarStakeHolderId())
			->where('stb_staff.stb_id', $delegated_task_id)
			->get()
			->result();

		if (empty($task_staff_details)) {
			return null;
		}
		
		foreach($task_staff_details as $staff_id => $staff){
			$result = $this->db_readonly->select('CONCAT(ifnull(sm.first_name, \'\'), \' \', ifnull(sm.last_name, \'\')) as primary_staff_name, sm.picture_url as primary_staff_photo, sm.id as primary_staff_id')
										->from('stb_staff ss')
										->join('staff_master sm', 'sm.id = ss.staff_id')
										->where('ss.stb_id', $staff->stb_id)
										->where('ss.role', 'primary')
										->get()
										->row();
			
			if(!empty($result->primary_staff_photo)){
				$result->primary_staff_photo = $this->filemanager->getFilePath($result->primary_staff_photo);
			}else{
				$result->primary_staff_photo = base_url().'/assets/img/sample_boy_image.png';
			}

			$staff->primary_staff_name = $result->primary_staff_name;
			$staff->primary_staff_photo = $result->primary_staff_photo;
			$staff->primary_staff_id = $result->primary_staff_id;
		}
		// echo "<pre>";print_r($task_staff_details);die();

		return $task_staff_details;
	}

	public function getAllBoards(){
		$result = $this->db->select('id, board_name')
				->from('stb_boards')
				->where('created_by', $this->authorization->getAvatarStakeHolderId())
				->get()
				->result();

		return $result;
	}

	private function getCountsForTasks($tasklistId) {
		$counts = $this->db_readonly->select("SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS completed_tasks_count, SUM(CASE WHEN delegated_to_project IS NOT NULL THEN 1 ELSE 0 END) AS delegated_tasks_count")
			->from('stb_mytasks')
			->where('stb_mytasklist_id', $tasklistId)
			->where('is_deleted', 0)
			->get()->row();

		return $counts;
	}

	public function addNewBoard($input){
		$data = array(
			'board_name' => $input['board_name'],
			'created_by' => $this->authorization->getAvatarStakeHolderId(),
			'created_on' => $this->_kolkata_datetime(),
			'is_deleted' => 0
		);
		$this->db->insert('stb_boards', $data);

		$result = array(
			'id' => $this->db->insert_id(),
			'board_name' => $input['board_name'],
		);

		return $result;
	}

	public function add_new_tasklist() {
		$dataArray = array(
			'list_name' => $this->input->post('list_name'),
			'created_by' => $this->authorization->getAvatarStakeHolderId(),
			'created_on' => $this->_kolkata_datetime(),
			'is_deleted' => 0,
			'board_id' => $this->input->post('board_id'),
			'list_background_color' => '#808080',
		);
	
		$this->db->insert('stb_mytasklists', $dataArray);
		$listId = $this->db->insert_id();

		$dataArray = array_merge($dataArray, ['list_id' => $listId, 'completed_tasks_count' => 0, 'delegated_tasks_count' => 0]);
		return $dataArray;	
	}

	public function change_list_color($input){
		$this->db->set('list_background_color', $input['list_background_color'])
				->where('id', $input['id']);
		return $this->db->update('stb_mytasklists');
	}

	public function add_user_background_img($path){
		$stb_staff_id = $this->authorization->getAvatarStakeHolderId();

		$existing_row = $this->db->get_where('stb_user_settings', array('staff_id' => $stb_staff_id))->row();

		if ($existing_row) {
			$this->db->set('background_image_url', $path);
			$this->db->where('staff_id', $stb_staff_id);
			$this->db->update('stb_user_settings');

			return true;
		} else {
			$data = array(
				'staff_id' => $stb_staff_id,
				'background_image_url' => $path,
			);

			$this->db->insert('stb_user_settings', $data);

			return true;
		}

		return false;
	}

	public function delete_list($input){
		$list_id = $input['id'];

		$this->db->trans_start();

		$this->db->set('deleted_by', $this->authorization->getAvatarStakeHolderId())
				->set('deleted_on', $this->_kolkata_datetime())
				->set('is_deleted', 1);
		$this->db->where('id', $list_id);
		$this->db->update('stb_mytasklists');

		$this->db->set('deleted_by', $this->authorization->getAvatarStakeHolderId())
				->set('deleted_on', $this->_kolkata_datetime())
				->set('is_deleted', 1);
		$this->db->where('stb_mytasklist_id', $list_id);
		$this->db->update('stb_mytasks');

		$this->db->trans_complete();
		return $this->db->trans_status();
	}

	public function editmylist($input){
		$this->db->set('list_name', $input['list_name'])
					->where('id', $input['id'])
					->update('stb_mytasklists');

		return $this->db->affected_rows();
	}

	public function add_mytask($input){
		$dataArray = array(
			'task_name' => $input['task_name'],
			'stb_mytasklist_id' => $input['list_id'],
			'created_by' => $this->authorization->getAvatarStakeHolderId(),
			'created_on' => $this->_kolkata_datetime(),
			'priority' => isset($input['task_priority']) ? $input['task_priority'] : 2,
			'is_deleted' => 0,
		);

		if(isset($input['task_from']) && $input['task_from'] == 'calendar'){

			if (isset($input['start_time']) && isset($input['end_time'])) {
				if (date('H:i:s', strtotime($input['start_time'])) === '00:00:00' && date('H:i:s', strtotime($input['end_time'])) === '01:00:00') {
					$current_time = date('H:i:s');
					$due_date = date('Y-m-d', strtotime($input['end_time']));
					$input['start_time'] = date('Y-m-d H:i:s', strtotime("$due_date $current_time"));
					
					$input['end_time'] = date('Y-m-d H:i:s', strtotime($input['start_time'] . ' +1 hour'));
				}
				$dataArray['start_date'] = $input['start_time'];
				$dataArray['due_date'] = $input['end_time'];
			}

			if(isset($input['task_desc']) && $input['task_desc'] != ''){
				$dataArray['description'] = $input['task_desc'];
			}
			if(isset($input['task_type']) && $input['task_type'] != ''){
				$dataArray['task_type'] = json_encode($input['task_type']);
			}
		}
		// echo "<pre>";print_r($dataArray);die();
		$this->db->insert('stb_mytasks', $dataArray);
		$task_id = $this->db->insert_id();

		if(isset($input['task_from']) && $input['task_from'] == 'calendar') {
			$dataArray = $this->db->select("smt.id, smt.task_name, smt.priority as task_priority, smt.due_date, smt.start_date, COALESCE(mtl.list_background_color, '#3A87AD') AS task_color, if(mtl.list_name is null, 'General', mtl.list_name) as tasklist_name, mtl.id as tasklist_id, ifnull(smt.description, '') as task_desc, smt.task_type, ifnull(smt.task_notes, '') as task_notes")
							->from('stb_mytasks smt')
							->join('stb_mytasklists mtl', 'mtl.id=smt.stb_mytasklist_id','left')
							->where('smt.id', $task_id)
							->where('smt.status', 1)
							->where('smt.is_deleted', 0)
							->where('smt.created_by', $this->authorization->getAvatarStakeHolderId())
							->get()
							->row();
			// if(!empty($dataArray->task_type)){
			// 	$dataArray->task_type = json_decode($dataArray->task_type);
			// }
			if(empty($dataArray->tasklist_id)){
				$dataArray->tasklist_id = 0;
			}
			if(!empty($dataArray->task_type)){
				$dataArray->task_type = json_decode($dataArray->task_type, true);
				$task_types = json_decode($dataArray->task_type);
				$this->db_readonly->select('id, task_type_color');
				$this->db_readonly->where('id', $task_types[0]);
				$task_type_color = $this->db_readonly->get('stb_tasktypes')->row();
				if(!empty($task_type_color)){
					$dataArray->task_type_colors = $task_type_color->task_type_color;
				} else {
					$dataArray->task_type_colors = '#CCCCCC';
				}
			} else {
				$dataArray->task_type_colors = '#CCCCCC';
			}
		}else{
			$dataArray = $this->db->select("smt.id as id, smt.task_name as task_name, smt.priority as priority, smt.status as status, smt.due_date as due_date, smt.created_on as created_on, smt.created_by as created_by, ifnull(smt.task_notes, '') as task_notes, ifnull(smt.description, '') as task_desc")
			->from('stb_mytasks smt')
			->where('smt.id', $task_id)
			->order_by('smt.id', 'desc')
			->get()
			->row();

			$attachments = $this->get_task_all_attachments($task_id);
			$url = '';

			if (!empty($attachments)) {
				foreach ($attachments as $file => $attachment) {
					$attachment->url = $this->filemanager->getFilePath($attachment->url);
				}
			}

			$dataArray->attachments = $attachments;
		}

		return $dataArray;
	}

	public function getallopenmytasks($list_id){
		$this->db_readonly->select('smt.id, smt.task_name, ifnull(smt.description, "") as task_desc, smt.task_type, smt.priority, smt.status, smt.created_by, smt.created_on, date_format(smt.due_date,"%d-%m-%Y %h:%i:%s %p") as due_date, ifnull(smt.task_notes, "") as task_notes');
		$this->db_readonly->from('stb_mytasks smt');
		$this->db_readonly->where('stb_mytasklist_id', $list_id);
		$this->db_readonly->where('created_by', $this->authorization->getAvatarStakeHolderId());
		$this->db_readonly->where('status', 1);
		$this->db_readonly->where('is_deleted', 0);
		$this->db_readonly->where("(delegated_to_project IS NULL)");
		$this->db_readonly->order_by('id', 'desc');

		$result = $this->db_readonly->get()->result();
		// echo "<pre>";print_r($result);die();

		// $attachments = $this->db_readonly->select('id, url, file_name')
		// 								->from('stb_mytasks_attachments')
		// 								->where('is_deleted', 1)
		// 								->get()->result();
		
		// echo "<pre>";print_r($attachments);die();

		foreach ($result as $task => $val) {
			if(!empty($val->task_type)){
				$val->task_type = json_decode($val->task_type);
			}
			$attachments = $this->get_task_all_attachments($val->id);
			$url = '';
			
			if(!empty($attachments)){
				foreach($attachments as $files => $url){
					$url->url = $this->filemanager->getFilePath($url->url);
				}
			}
			
			$val->attachments = $attachments;
		}

		return $result;
	}

	private function get_task_all_attachments($task_id) {
		return $this->db_readonly->select('url, file_name, id')
						->where('stb_mytasks_id', $task_id)
						->where('is_deleted', 0)
						->get('stb_mytasks_attachments')
						->result();
	}

	public function getalldelegatedmytasks($list_id) {
		$this->db_readonly->select('id, task_name, delegated_to_task_id, priority');
		$this->db_readonly->from('stb_mytasks');
		$this->db_readonly->where('stb_mytasklist_id', $list_id);
		$this->db_readonly->where('status', 1);
		$this->db_readonly->where('is_deleted', 0);
		$this->db_readonly->where('delegated_to_project IS NOT NULL');
		$this->db_readonly->order_by('id', 'asc');

		$result = $this->db_readonly->get()->result();

		// $delegated_task_array = [];

		// foreach ($result as $row => $val){
		// 	$delegated_task_array[] = $val->delegated_to_task_id;
		// }
		
		// if(!empty($delegated_task_array)){
		// 	$delegated_task_details = $this->db->select('stb_tasks.status as status, CONCAT(ifnull(sm.first_name, \'\'), \' \', ifnull(sm.last_name, \'\')) as staff_name, sm.picture_url as staff_photo')
		// 										->from('stb_tasks')
		// 										->where_in('stb_tasks.id', $delegated_task_array)
		// 										->join('stb_staff', 'stb_staff.stb_id = stb_tasks.id')
		// 										->join('staff_master sm', 'sm.id = stb_staff.staff_id')
		// 										->get()
		// 										->result();
		// 	foreach ($delegated_task_details as $details => $tasks){
		// 		foreach ($result as $row => $val) {
		// 			$val->status = $tasks->status;
		// 			$val->staff_name = $tasks->staff_name;
		// 			$val->staff_photo = $tasks->staff_photo;
		// 		}
		// 	}
		// }

		// echo "<pre>";print_r($delegated_task_details);die();

		foreach ($result as $row => $val) {
			$task_staff_details = $this->get_delegated_task_details($val->delegated_to_task_id);
			$val->status = $task_staff_details->status;
			$val->staff_name = $task_staff_details->staff_name;
			$val->staff_photo = $task_staff_details->staff_photo;

			// $comments = $this->get_task_comments_by_id($val->id);
			// $val->comments = $comments;
		}
		// echo "<pre>";print_r($result);die();
		return $result;
	}

	public function getAllComments($input){
		$comments = $this->get_task_comments_by_id($input['task_id']);
		return $comments;
	}

	private function get_delegated_task_details($delegated_task_id) {
		$task_staff_details = $this->db->select('stb_tasks.status as status, CONCAT(ifnull(sm.first_name, \'\'), \' \', ifnull(sm.last_name, \'\')) as staff_name, sm.picture_url as staff_photo')
			->from('stb_tasks')
			->where('stb_tasks.id', $delegated_task_id)
			->join('stb_staff', 'stb_staff.stb_id = stb_tasks.id')
			->join('staff_master sm', 'sm.id = stb_staff.staff_id')
			->get()
			->row();

		if(!empty($task_staff_details->staff_photo)){
			$task_staff_details->staff_photo = $this->filemanager->getFilePath($task_staff_details->staff_photo);
		}else{
			$task_staff_details->staff_photo = base_url().'/assets/img/sample_boy_image.png';
		}
		return $task_staff_details;
	}

	public function getallcompletedmytasks($input){
		$this->db_readonly->select('id, task_name, status');
		$this->db_readonly->from('stb_mytasks');
		$this->db_readonly->where('stb_mytasklist_id', $input);
		$this->db_readonly->where('status', 0);
		$this->db_readonly->where('is_deleted', 0);
		$this->db_readonly->where("(delegated_to_project IS NULL)");
		$this->db_readonly->order_by('id', 'asc');

		$result = $this->db_readonly->get()->result();
		// return !empty($result) ? $result : null;
		return $result;
	}

	public function updateTaskCompletion($input){
		$status = $input['status'];
		$task_id = $input['id'];

		$this->db->trans_start();
		if($status == 1){
			$this->db->set('status', 0)
					->set('completed_on', $this->_kolkata_datetime())
					->where('id', $task_id)
					->update('stb_mytasks');
		} else {
			$this->db->set('status', 1)
					->set('completed_on', null)
					->where('id', $task_id)
					->update('stb_mytasks');
		}
		$this->db->trans_complete();

		$result = [];
		if ($this->db->trans_status()) {
			$result[] = $this->get_task_completed($task_id);
		}

		return $result;
	}

	private function get_task_completed($task_id){
		$dataArray = $this->db->select("smt.id as id, smt.task_name as task_name, smt.priority as priority, smt.status as status, date_format(smt.due_date, '%d-%m-%Y') as due_date, smt.created_on as created_on, smt.created_by as created_by")
			->from('stb_mytasks smt')
			->where('smt.id', $task_id)
			->order_by('smt.id', 'desc')
			->get()
			->row();

		if ($dataArray) {
			$attachments = $this->get_task_all_attachments($dataArray->id);
			if (!empty($attachments)) {
				foreach ($attachments as $file => $attachment) {
					$attachment->url = $this->filemanager->getFilePath($attachment->url);
				}
			}
			$dataArray->attachments = $attachments;
		}

		return $dataArray;
	}

	public function delete_task($input){
		$task_id = $input['id'];
		$this->db->set('deleted_by', $this->authorization->getAvatarStakeHolderId())
				->set('deleted_on', $this->_kolkata_datetime())
				->set('is_deleted', 1);
		$this->db->where('id', $task_id);
    	return $this->db->update('stb_mytasks');
	}

	public function delete_delegated_task($input){
		
		$task_id = $input['id'];

		$this->db->trans_start();

		$this->db->set('deleted_by', $this->authorization->getAvatarStakeHolderId())
				->set('deleted_on', $this->_kolkata_datetime())
				->set('is_deleted', 1)
				->where('id', $task_id)
				->update('stb_mytasks');
		
		$delegated_task_id = $this->db->select('delegated_to_task_id')
								->from('stb_mytasks')
                                ->where('id', $task_id)
                                ->get()
                                ->row();

		$this->db->set('status', 'cancelled')
				->where('id', $delegated_task_id->delegated_to_task_id)
				->update('stb_tasks');
		
		$this->db->trans_complete(); 
		return $this->db->trans_status();
	}

	public function get_task_comments_by_id($task_id) {
		$delegated_task_id = $this->get_delegated_task_id($task_id);
        // echo "<pre>";print_r($delegated_task_id);die();
		if($delegated_task_id){
			$task_comments = $this->db->select("his.status, his.comment, DATE_FORMAT(his.created_on,'%d-%b %H:%i') as created_on, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as commented_by, sm.id as staff_id")
				->from("stb_tasks_status_history his")
				->join("staff_master sm", "sm.id=his.commented_by", "left")
				->where('his.stb_id', $delegated_task_id)
				->order_by('his.created_on', 'asc')
				->get()->result();
			return $task_comments;
		 }else{
			return '';
		 }
	}

	private function get_delegated_task_id($task_id) {
		$row = $this->db_readonly->select('delegated_to_task_id')
			->from('stb_mytasks')
			->where('id', $task_id)
			->get()->row();

		return $row ? $row->delegated_to_task_id : null;
	}

	public function get_detials_for_notification($task_id){
		$task_details = $this->db_readonly->select('delegated_to_task_id, task_name, created_by')
									->from('stb_mytasks')
									->where('id', $task_id)
									->get()->row();

		$staffs = $this->db_readonly->select('staff_id')
									->from('stb_staff')
									->where('stb_id', $task_details->delegated_to_task_id)
									->get()->result();

		$staff_ids = [];

		foreach($staffs as $staff){
			$staff_ids[] = $staff->staff_id;
		}

		$staff_details = $this->db_readonly->select("CONCAT(IFNULL(first_name, ''), ' ', IFNULL(last_name, '')) as staff_name, id as staff_id")
                            ->from('staff_master')
                            ->where('id', $this->authorization->getAvatarStakeHolderId())
                            ->get()
                            ->row();

		foreach($staff_ids as $key => $staff_id){
			if($staff_details->staff_id == $staff_id ){
				$staff_ids[$key] = $task_details->created_by;
			}
		}

		$details = [
			'task_name' => $task_details->task_name,
            'staff_ids' => $staff_ids,
            'staff_name' => $staff_details->staff_name,
		];

		return $details;
	}

	public function add_comment($input){
		$task_id = $this->get_delegated_task_id($input['task_id']);

		$this->db->trans_start();
		$task_data = $this->db_readonly->select('status, id')
									->from('stb_tasks')
									->where('id', $task_id)
									->get()->row();

		$data = array(
			'stb_id' => $task_data->id,
			'commented_by' => $this->authorization->getAvatarStakeHolderId(),
			'created_on' => $this->_kolkata_datetime(),
			'status' => $task_data->status,
			'comment' => $input['comment']
		);

		$result = $this->db->insert('stb_tasks_status_history', $data);

		$comment_id = $this->db->insert_id();

		$this->db->trans_complete();

		if ($this->db->trans_status()) {
			$comment = $this->db->select("his.status, his.comment, DATE_FORMAT(his.created_on,'%d-%b %H:%i') as created_on, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as commented_by, his.commented_by as staff_id")
								->from('stb_tasks_status_history his')
								->join('staff_master sm', 'sm.id = his.commented_by', "left")
								->where('his.id', $comment_id)
								->get()->row();

			$comment_details = array(
				'result' => 1,
				'comment' => $comment
			);
		}else{
			$comment_details = array(
				'result' => 0,
				'comment' => null
			);
		}

		return $comment_details;
	}

	public function update_list_tasks($input){
		$result = $this->db->set('stb_mytasklist_id', $input['list_id'])
				->where('id', $input['task_id'])
				->update('stb_mytasks');
		return $result;
	}

	public function update_priority($input){
		$priority = $input['priority'];
		$id = $input['id'];

		$this->db->set('priority', $priority)
				->where('id', $id);
		
		return $this->db->update('stb_mytasks');
	}

	public function update_task_type($input){
		$task_type = !empty($input['task_type']) ? json_encode($input['task_type']) : null;
		$check_not_null = json_decode(json_decode($task_type));
		if(empty($check_not_null[0])){
			$task_type = null;
		}
		$id = $input['id'];

		$this->db->set('task_type', $task_type)
				->where('id', $id);
		
		return $this->db->update('stb_mytasks');
	}

	public function get_staff_name(){
		$staff_name = $this->db_readonly->select("CONCAT(IFNULL(first_name, ''), ' ', IFNULL(last_name, '')) as staff_name")
                            ->from('staff_master')
                            ->where('id', $this->authorization->getAvatarStakeHolderId())
                            ->get()
                            ->row();

		return $staff_name->staff_name;
	}

	public function delegate_task($input){
		// echo "<pre>";print_r($input);die();
		$this->db->trans_start();
		if($input['radio_type'] == 'team'){
			$team_details = $this->db->select('primary_staff as primary_staff_id, secondary_staffs as secondary_staff_list')
											->from('stb_staff_teams')
											->where('id', $input['team_id'])
											->get()->row();
			
			$input['primary_staff_id'] = $team_details->primary_staff_id;
        	$input['secondary_staff_list'] = json_decode($team_details->secondary_staff_list);
		}
		
		$data = array(
			'task_title' => $input['task_name'],
			'created_by' => $input['created_by'],
			'created_on' => $input['created_on'],
			'status' => 'open',
			'priority' => $input['priority'] == 1 ? 'High' : ($input['priority'] == 2 ? 'Medium' : 'Low'),
			'complete_by' => $input['complete_by'],
			'basket_id' => $input['basket_id']
		);
		$result = $this->db->insert('stb_tasks', $data);

		$last_insert_id = $this->db->insert_id();

		$assigned_data[] = array(
			'staff_id' => $input['primary_staff_id'],
			'stb_id' => $last_insert_id,
			'role' => 'primary'
		);

		if (isset($input['secondary_staff_list']) && $input['secondary_staff_list'] != null) {
			foreach ($input['secondary_staff_list'] as $staff_id) {
				$assigned_data[] = array(
					'staff_id' => $staff_id,
					'stb_id' => $last_insert_id,
					'role' => 'secondary'
				);
			}
		}

		$result = $this->db->insert_batch('stb_staff', $assigned_data);

		$this->db->set('delegated_to_project', $input['basket_id'])
				->set('delegated_to_task_id', $last_insert_id)
				->where('id', $input['task_id'])
				->update('stb_mytasks');

		$this->db->trans_complete();
		
		$task_data = [];
		if($this->db->trans_status()){
			$task_data[] = $this->getDelegatedTaskDetailsById($input['task_id']);
			// echo "<pre>";print_r($task_data);die();
		}

		return $task_data;
	}

	private function getDelegatedTaskDetailsById($task_id){
		$this->db->select('smt.id as id, smt.task_name as task_name, smt.delegated_to_task_id as delegated_to_task_id, smt.priority as priority, st.status as status');
		$this->db->from('stb_tasks st');
		$this->db->from('stb_mytasks smt', 'st.id = smt.delegated_to_task_id');
		$this->db->where('smt.status', 1);
		$this->db->where('smt.is_deleted', 0);
		$this->db->where('smt.id', $task_id);
		$this->db->order_by('smt.id', 'asc');

		$result = $this->db->get()->row();

		$task_staff_details = $this->get_delegated_task_details($result->delegated_to_task_id);
		$result->status = $task_staff_details->status;
		$result->staff_name = $task_staff_details->staff_name;
		$result->staff_photo = $task_staff_details->staff_photo;

		// $comments = $this->get_task_comments_by_id($result->id);
		// $result->comments = $comments;

		return $result;
	}

	public function editmytask($input){
		$this->db->set('task_name', $input['task_name'])
				 ->where('id', $input['id'])
				 ->update('stb_mytasks');

		return $this->db->affected_rows();
	}

	public function editmytaskdesc($input){
		$this->db->set('description', $input['task_desc'])
				 ->where('id', $input['id'])
				 ->update('stb_mytasks');

		return $this->db->affected_rows();
	}

	public function update_task_timings($input){

		if($input['due_date'] == ''){
			$this->db->set('due_date', null)
				->set('start_date', null)
				->where('id', $input['task_id'])
				->update('stb_mytasks');

			if ($this->db->affected_rows() > 0) {
				return true;
			} else {
				return false;
			}
		}

		$due_date = $input['due_date'];
		$due_date_time = date("Y-m-d H:i:s", strtotime($due_date));

		$this->db->set('due_date', "DATE_ADD('$due_date_time', INTERVAL 1 HOUR)", false)
				->set('start_date', $due_date_time)
				->where('id', $input['task_id'])
				->update('stb_mytasks');

		if ($this->db->affected_rows() > 0) {
			return true;
		} else {
			return false;
		}
	}

	public function update_task_timings_calendar($input){
		$currentDateTime = new DateTime();
		$startTime = new DateTime($input['start_time']);
		$endTime = new DateTime($input['end_time']);

		if ($startTime->format('H:i:s') === '00:00:00' && $endTime->format('H:i:s') === '01:00:00') {
			$currentTime = new DateTime();
    		$currentTimeOnly = $currentTime->format('H:i:s');
			$input['end_time'] = $endTime->format('Y-m-d') . ' ' . $currentTimeOnly;
			$updatedEndTime = new DateTime($input['end_time']);
			$updatedStartTime = clone $updatedEndTime;
			$updatedStartTime->modify('-1 hour');
			$input['start_time'] = $startTime->format('Y-m-d') . ' ' . $updatedStartTime->format('H:i:s');
		}
		$this->db->set('due_date', $input['end_time'])
				->set('start_date', $input['start_time'])
				->where('id', $input['task_id'])
				->update('stb_mytasks');

		if ($this->db->affected_rows() > 0) {
			$dataArray = $this->db->select("smt.id, smt.task_name, smt.priority as task_priority, smt.due_date, smt.start_date, COALESCE(mtl.list_background_color, '#3A87AD') AS task_color, if(mtl.list_name is null, 'General', mtl.list_name) as tasklist_name, mtl.id as tasklist_id, ifnull(smt.description, '') as task_desc, smt.task_type")
							->from('stb_mytasks smt')
							->join('stb_mytasklists mtl', 'mtl.id=smt.stb_mytasklist_id','left')
							->where('smt.id', $input['task_id'])
							->where('smt.status', 1)
							->where('smt.is_deleted', 0)
							->where('smt.created_by', $this->authorization->getAvatarStakeHolderId())
							->get()
							->row();
			if(!empty($dataArray->task_type)){
				$dataArray->task_type = json_decode($dataArray->task_type);
				$this->db_readonly->select('id, task_type_color');
				$this->db_readonly->where_in('id', $dataArray->task_type);
				$task_types = $this->db_readonly->get('stb_tasktypes')->row();
				if(!empty($task_types)){
					$dataArray->task_type_colors = $task_types->task_type_color;
				} else {
					$dataArray->task_type_colors = '#CCCCCC';
				}
			} else {
				$dataArray->task_type_colors = '#CCCCCC';
			}
			return $dataArray;
		} else {
			return false;
		}
	}

	public function remove_due_date($input){
		$this->db->set('due_date', null)
			->set('start_date', null)
			->where('id', $input['id'])
			->update('stb_mytasks');

		if ($this->db->affected_rows() > 0) {
			return true;
		} else {
			return false;
		}
	}

	public function add_attachment($input, $attached_file_path, $attached_file_name){
    	$task_id = $input['stb_mytasks_id'];
		$uploaded_by = $this->authorization->getAvatarStakeHolderId();
		$uploaded_on = $this->_kolkata_datetime();

		$data = array(
			'stb_mytasks_id' => $task_id,
			'file_name' => $attached_file_name,
			'url' => $attached_file_path,
			'uploaded_by' => $uploaded_by,
			'uploaded_on' => $uploaded_on,
			'is_deleted' => 0
		);

		$this->db->insert('stb_mytasks_attachments', $data);

		if ($this->db->affected_rows() > 0) {
			$last_insert_id = $this->db->insert_id();

			$query = $this->db->select('id, url, file_name')
							->from('stb_mytasks_attachments')
							->where('id', $last_insert_id)
							->get();

			if ($query && $query->num_rows() > 0) {
				$row = $query->row();

				$row->url = $this->filemanager->getFilePath($row->url);

				return $row;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	public function delete_attachment($input){
		$this->db->set('is_deleted', 1)
				->set('deleted_by', $this->authorization->getAvatarStakeHolderId())
				->where('id', $input['id'])
				->update('stb_mytasks_attachments');

		return $this->db->affected_rows() > 0;
	}

	public function getallmytasksbydates($input){
		
		$startingDate = new DateTime($input['start_of_week']);

		$startingDayOfWeek = (int) $startingDate->format('N');
		if ($startingDayOfWeek !== 1) {
			$startingDate->modify('last monday');
		}

		$weekDates = [];
		for ($i = 0; $i < 7; $i++) {
			$date = clone $startingDate;
			$date->modify("+$i days");
			$weekDates[] = $date->format('Y-m-d');
		}

		$tasks_due_date = $this->db_readonly->select('*, DATE_FORMAT(stb_mytasks.due_date, "%d-%m-%Y") as due_date')
								->from('stb_mytasks')
								->where_in('DATE(stb_mytasks.due_date)', $weekDates)
								->where('is_deleted', 0)
								->get()
								->result();

		$tasks_created_date = $this->db_readonly->select('*, DATE_FORMAT(stb_mytasks.created_on, "%d-%m-%Y") as created_on')
								->from('stb_mytasks')
								->where_in('DATE(stb_mytasks.created_on)', $weekDates)
								->where('is_deleted', 0)
								->get()
								->result();
		
		$tasks_by_date = [];

		foreach ($tasks_due_date as $task) {
			$date = date('Y-m-d', strtotime($task->due_date));
			$tasks_by_date[$date]['due_on'][] = $task;
		}
		
		foreach ($tasks_created_date as $task) {
			$date = date('Y-m-d', strtotime($task->created_on));
			$tasks_by_date[$date]['created_on'][] = $task;
		}

		// echo "<pre>";print_r($tasks_by_date);die();
		return $tasks_by_date;
	}

	public function update_calendar_task_details($input){
		$task_id = $input['task_id'];
		$task_name = $input['task_name'];
		$list_id = $input['list_id'];
		$priority = $input['priority'];
		$task_desc = $input['task_desc'];
		if(empty($task_desc)){
			$$task_desc = null;
		}
		if(!empty($input['task_type'])){
			$task_type = json_encode($input['task_type']);
		} else {
			$task_type = null;
		}
		$due_date = $input['due_date'];
		if(!empty($due_date)){
			$dueDate = new DateTime($input['due_date']);
			$startTime = clone $dueDate;
			$startTime->modify('-1 hour');
			$formattedStartTime = $startTime->format('Y-m-d H:i:s');
		} else {
			$formattedStartTime = null;
		}
		
		$data = array(
			'task_name' => $task_name,
			'stb_mytasklist_id' => $list_id,
			'priority' => $priority,
			'description' => $task_desc,
			'task_type' => $task_type,
			'due_date' => !empty($due_date) ? $due_date : null,
		);
		if(empty($due_date)){
			$data['start_date'] = null;
		} else {
			$data['start_date'] = $formattedStartTime;
		}
		// echo "<pre>";print_r($data);die();
		$this->db->where('id', $task_id);
		$this->db->update('stb_mytasks', $data);

		if ($this->db->affected_rows() > 0) {
			return $task_id;
		} else {
			return false;
		}
	}

	public function saveTaskNotes($input){
		$notes = $input['notes'] == '' ? null : $input['notes'];
		$this->db->set('task_notes', $notes)
					->where('id', $input['task_id'])
					->update('stb_mytasks');

		return $this->db->affected_rows();
	}

	public function get_all_task_types(){
		$data = $this->db_readonly->select('id, task_type_name, task_type_color, task_type_icon')
		->from('stb_tasktypes')
		->where('module_name', 'task_basket')
		->where_in('created_by', ['0', $this->authorization->getAvatarStakeHolderId()])
		// ->where('created_by', $this->authorization->getAvatarStakeHolderId())
		->where('is_deleted', 0)->get()->result();
		if(!empty($data)){
			return $data;
		} else {
			return array();
		}
	}

	public function delete_calendar_task($input){
		$data = array(
			'is_deleted' => 1,
            'deleted_by' => $this->authorization->getAvatarStakeHolderId(),
			'deleted_on' => $this->_kolkata_datetime()
		);
		$this->db->where('id', $input['task_id']);
		$this->db->update('stb_mytasks', $data);
		return $this->db->affected_rows() > 0 ? $input['task_id'] : false;
	}
}
