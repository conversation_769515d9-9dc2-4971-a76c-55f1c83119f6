<?php
function requireFilter($filterArray, $filter)
{
    foreach ($filterArray as $f) {
        if ($f == $filter)
            return 1;
    }
    return 0;
}
?>
<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<?php $this->load->helper('reports_datatable');
echo render_saved_report_filters(); ?>

<div class="card-body" id="filtersContainer">
    <div class="col-md-12">
        <div class="row" style="margin: 0px">

            <div class="col-md-3 form-group" id="multiBlueprintSelect">
                <p class="label-text">Select Fee Type</p>
                <select class="form-control multiselect select" multiple title='All' id="fee_type" name="fee_type">
                    <?php foreach ($fee_blueprints as $key => $val) { ?>
                        <option <?php if ($fee_type == $val->id) echo 'selected' ?> value="<?= $val->id ?>"><?php echo $val->name ?></option>
                    <?php } ?>
                </select>
            </div>

            <div class="col-md-3 form-group" style="display: none;" id="blueprintSelect">
                <p class="label-text">Select Blueprint</p>
                <select class="form-control changeFeeType arrow-mark" id="fee_type" name="fee_type">
                    <option value="">All</option>
                    <?php foreach ($fee_blueprints as $key => $val) { ?>
                        <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
                    <?php } ?>
                    <!-- <option value="sales">Sales</option>
                    <option value="application">Applications</option> -->
                </select>
            </div>

            <div class="col-md-3 form-group" id="installmentType" style="display: none;">
                <p class="label-text">Select Installments Type</p>
                <select class="form-control arrow-mark" name="installment_type" id="installment_type"></select>
            </div>

            <div class="col-md-3 form-group" id="installment" style="display: none;">
                <p class="label-text">Select Installments</p>
                <select class="form-control onchange_installments" multiple title="All" name="installment" id="installmentId"></select>
            </div>

            <script>
                function toggleInstallmentMode(isInstallmentMode) {
                    if (isInstallmentMode) {
                        // Hide normal filter
                        $('#multiBlueprintSelect').hide();

                        // Show new installment-related filters
                        $('#blueprintSelect').show();
                        $('#installmentType').show();
                        $('#installment').show();
                    } else {
                        // Show normal filter
                        $('#multiBlueprintSelect').show();

                        // Hide installment-related filters
                        $('#blueprintSelect').hide();
                        $('#installmentType').hide();
                        $('#installment').hide();
                    }
                }
            </script>


            <div class="col-md-3 form-group">
                <p class="label-text">Class</p>
                <?php
                $array = array();
                // $array[0] = 'Select Classes';
                foreach ($classes as $key => $class) {
                    $array[$class->classId] = $class->className;
                }
                echo form_dropdown("class_name[]", $array, set_value("class_name", $clsId), "id='classId' multiple title='All' class='form-control classId select '");
                ?>
            </div>


            <div class="col-md-3 form-group">
                <p class="label-text">Class/Section</p>
                <?php
                $array = array();
                // $array[0] = 'Select Section';
                foreach ($classSectionList as $key => $cl) {
                    $array[$cl->id] = $cl->class_name . $cl->section_name;
                }
                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='All' class='form-control select'");
                ?>
            </div>
            <?php if ($this->settings->getSetting('fees_stuent_summary_report_show_combination_filter')) { ?>
                <div class="col-md-3 form-group">
                    <p class="label-text">Combination</p>
                    <?php
                    $array = array();
                    $array[0] = 'Select Combination';
                    foreach ($combination as $key => $cl) {
                        $array[$cl->combination] = strtoupper($cl->combination);
                    }
                    echo form_dropdown("combination", $array, '', "id='combination' multiple title='Select Combination' class='form-control select'");
                    ?>
                </div>
            <?php } ?>
            <div class="col-md-3 form-group">
                <p class="label-text">Admission Type</p>
                <?php
                $array = array();
                $array[0] = 'Select Admission Type';
                foreach ($admission_type as $key => $admission) {
                    $array[$key] = ucfirst($admission);
                }
                echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-control arrow-mark'");
                ?>
            </div>


            <div class="col-md-3 form-group">
                <p class="label-text">Payment Options</p>
                <select name="payment_status" id='payment_status' class='form-control arrow-mark'>
                    <option value="">Payment Option</option>
                    <option value="FULL">Full Payment</option>
                    <option value="PARTIAL">Partial Payment</option>
                    <option value="NOT_STARTED">Balance</option>
                </select>
            </div>
            <?php if (!empty($rteType)) { ?>
                <div class="col-md-3 form-group">
                    <p class="label-text">IS RTE</p>
                    <?php
                    $rte_nrte = array();
                    $rte_nrte[0] = 'All';
                    foreach ($rteType as $key => $rn) {
                        $rte_nrte[$key] = $rn;
                    }
                    echo form_dropdown("rte_nrte", $rte_nrte, set_value("rte_nrte"), "id='rte_nrteId' class='form-control arrow-mark'");
                    ?>
                </div>
            <?php } ?>



            <div class="col-md-3 form-group">
                <p class="label-text">Joining Academic Year</p>
                <?php
                $AcadArray = array();
                $AcadArray[0] = 'Joining Academic Year';
                foreach ($this->acad_year->getAllYearData() as $yearId => $year) {
                    $AcadArray[$year->id] = $year->acad_year;
                }

                echo form_dropdown("acad_year_id", $AcadArray, set_value("acad_year_id"), "id='acad_year_id' class='form-control arrow-mark'");
                ?>
            </div>
            <?php if ($this->settings->getSetting('fee_report_category_filter')) : ?>
                <div class="col-md-3 form-group">
                    <p class="label-text">Select Category</p>
                    <?php
                    $array = array();
                    $array[0] = 'Select Category';
                    foreach ($category as $key => $cat) {
                        $array[$key] = $cat;
                    }
                    echo form_dropdown("category", $array, set_value("category"), "id='category' class='form-control arrow-mark'");
                    ?>
                </div>
            <?php endif ?>
            <?php if ($this->settings->getSetting('fee_report_donor_filter')) : ?>
                <div class="col-md-3 form-group">
                    <p class="label-text">Select Donors</p>
                    <?php
                    $array = array();
                    $array[0] = 'Select Donors';
                    foreach ($donors as $key => $donor) {
                        $array['Null'] = 'Non Donors';
                        $array[$donor->donor] = ucfirst($donor->donor);
                    }
                    echo form_dropdown("donors", $array, set_value("donors"), "id='donorsId' multiple title='All' class='form-control select'");
                    ?>
                </div>
            <?php endif ?>
            <div class="col-md-3 form-group">
                <p for="sectionId" class="label-text">Admission Status</p>
                <select id="admission_status" name="admission_status" required class="form-control input-md arrow-mark">
                    <option value=""><?php echo "All" ?></option>
                    <?php foreach ($admission_status as $value => $type) { ?>
                        <option value="<?php echo $value ?>"><?php echo $type ?></option>
                    <?php } ?>
                </select>
            </div>

            <div class="col-md-3 form-group">
                <p class="label-text">Select Staff Kids</p>
                <select class="form-control arrow-mark" name="staff_kids" id="staff_kid">
                    <option value="all">All</option>
                    <option value="0">Exclude Staff Kids</option>
                    <option value="1">Staff Kids</option>
                </select>
            </div>

            <hr style="margin: 1rem auto; color: inherit; border: 0; border-top: 1px solid; opacity: 0.25;width:97%;">
            <h5 style="color: #161327; font-family: Inter; font-size: 14px; font-style: normal; font-weight: 500; line-height: 120%;">More Options</h5>
            <div class="d-flex gap-4 align-items-center" style="margin-top: 10px;">
                <label class="custom-checkbox-label" for="installment_search">
                    <input class="custom-checkbox" type="checkbox" id="installment_search">
                    <span>Show Installments</span>
                </label>

                <label class="custom-checkbox-label" for="transaction_hide_show">
                    <input class="custom-checkbox" type="checkbox" id="transaction_hide_show" checked>
                    <span>Hide Transactions</span>
                </label>

                <!-- This pushes the buttons to the right -->
                <div class="ms-auto d-flex" style="gap: 10px;">
                    <input type="button" name="clear" id="clear" class="btn btn-outline-primary" value="Clear">
                    <input type="button" onclick="get_report()" name="search" id="search" class="btn btn-primary" value="Get Report">
                </div>
            </div>

            <script>
                $(document).ready(function() {
                    // get_report();
                    get_predefined_filters();

                    $('#save_filter').on('click', function() {
                        saveFilter();
                    });

                    $('#update_filter').on('click', function() {
                        updateFilter();
                    });

                    $('.select').on('keydown', function(e) {
                        if (e.key === 'Escape') {
                            $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                        }

                        if (e.key === 'Enter') {
                            $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                        }

                        if (e.key === ' ' && !$(this).is(':focus')) {
                            e.preventDefault();
                        }
                    });

                    $(document).on('click', function(event) {
                        var $target = $(event.target);
                        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
                            $('.bootstrap-select').removeClass('open show');
                            $('.dropdown-menu').removeClass('show');
                        }
                    });
                });

                function saveFilter() {
                    bootbox.prompt({
                        inputType: 'text',
                        placeholder: 'Enter the Title name',
                        title: "Save filters",
                        className: 'half-width-box',
                        buttons: {
                            confirm: {
                                label: 'Yes',
                                className: 'btn-success'
                            },
                            cancel: {
                                label: 'No',
                                className: 'btn-danger'
                            }
                        },
                        callback: function(remarks) {
                            if (remarks === null) return;

                            $('.bootbox .error-message').remove();
                            remarks = remarks.trim();

                            if (!remarks) {
                                new PNotify({
                                    title: 'Missing Title',
                                    text: 'Please enter a name to save the filter.',
                                    type: 'error',
                                    addclass: 'custom-pnotify half-width-notify',
                                    cornerclass: '',
                                    animate: {
                                        animate: true,
                                        in_class: 'fadeInRight',
                                        out_class: 'fadeOutRight'
                                    },
                                    styling: 'bootstrap3',
                                    delay: 3000
                                });
                                return false;
                            }

                            if (remarks.length < 5 || remarks.length > 50) {
                                setTimeout(() => {
                                    $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                                }, 10);
                                return false;
                            }

                            let duplicate = false;
                            $('#filter_types option').each(function() {
                                if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
                                    duplicate = true;
                                    return false;
                                }
                            });

                            if (duplicate) {
                                setTimeout(() => {
                                    $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
                                }, 10);
                                return false;
                            }

                            let isInstallmentChecked = $('#installment_search').is(':checked');

                            const table = $('#fee_summary_data').DataTable();
                            let visibleCols = [];
                            table.columns().every(function(index) {
                                if (this.visible()) {
                                    visibleCols.push(index);
                                }
                            });

                            // Check if no columns are visible
                            if (visibleCols.length === 0) {
                                new PNotify({
                                    title: 'Action Required',
                                    text: 'Please click the "Get Report" button first before saving a filter.',
                                    type: 'warning',
                                    addclass: 'custom-pnotify half-width-notify',
                                    delay: 3000
                                });
                                return false; // Stop the saveFilter process
                            }

                            let filterData = {
                                title: remarks,
                                installment_search: isInstallmentChecked,
                                transaction_hide_show: $("#transaction_hide_show").is(":checked") ? 1 : 0,
                                admission_status: $('#admission_status').val(),
                                student_name_fees: $('#student_name_fees').val(),
                                fee_type: $('#fee_type').val(),
                                installment_type: $('#installment_type').val(),
                                installmentId: $('#installmentId').val(),
                                fee_type: $('#fee_type').val(),
                                payment_status: $('#payment_status').val(),
                                selectedColumns: $('#selectedColumns').val(),
                                classId: $('#classId').val(),
                                classSectionId: $('#classSectionId').val(),
                                admission_type: $('#admission_type').val(),
                                paymentModes: $('#paymentModes').val(),
                                mediumId: $('#mediumId').val(),
                                donorsId: $('#donorsId').val(),
                                boardingId: $('#boardingId').val(),
                                rte_nrteId: $('#rte_nrteId').val(),
                                created_byId: $('#created_byId').val(),
                                payment_modeJSON: $('#payment_modeJSON').val(),
                                stopId: $('#stopId').val(),
                                from_date: $('#from_date').val(),
                                to_date: $('#to_date').val(),
                                routeId: $('#routeId').val(),
                                itemId: $('#itemId').val(),
                                category: $('#category').val(),
                                acad_year_id: $('#acad_year_id').val(),
                                combination: $('#combination').val() || '',
                                staff_kid: $('#staff_kid').val(),
                                report_type: $('input[name="report_type"]:checked').val(),
                                column_visibility: JSON.stringify(visibleCols),
                            };

                            $.ajax({
                                url: '<?php echo site_url('feesv2/reports/save_filters1'); ?>',
                                type: 'POST',
                                data: filterData,
                                success: function(data) {
                                    if (data) {
                                        let lastId = 0;
                                        $('#filter_types option').each(function() {
                                            const val = parseInt($(this).val());
                                            if (!isNaN(val) && val > lastId) lastId = val;
                                        });

                                        const newId = lastId + 1;

                                        $('#filter_types').append(
                                            $('<option>', {
                                                value: newId,
                                                text: remarks
                                            })
                                        );

                                        $('#filter_types').val(newId).trigger('change');

                                        new PNotify({
                                            title: 'Success',
                                            text: 'Filter Saved Successfully',
                                            type: 'success',
                                            addclass: 'custom-pnotify half-width-notify'
                                        });
                                    } else {
                                        new PNotify({
                                            title: 'Error',
                                            text: 'Something went wrong',
                                            type: 'error',
                                            addclass: 'custom-pnotify half-width-notify'
                                        });
                                    }
                                }
                            });
                        }
                    });

                    setTimeout(() => {
                        $('.bootbox .modal-dialog').css({
                            'max-width': '400px',
                            'margin': '1.75rem auto'
                        });

                        $('.bootbox-input').on('input', function() {
                            const inputVal = $(this).val().trim();
                            if (inputVal.length > 50) {
                                $(this).val(inputVal.slice(0, 50)); // Truncate input to 50 characters
                                $('.bootbox .error-message').remove(); // Remove previous error message
                                $(this).after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                            } else {
                                $('.bootbox .error-message').remove(); // Remove error message when valid length
                            }
                        });
                    }, 10);
                }


                function collectFilterData() {
                    const table = $('#fee_summary_data').DataTable();
                    let visibleCols = [];
                    table.columns().every(function(index) {
                        if (this.visible()) {
                            visibleCols.push(index);
                        }
                    });

                    return {
                        title: $('#remarks').val() || '',
                        installment_search: $('#installment_search').is(":checked"),
                        transaction_hide_show: $("#transaction_hide_show").is(":checked") ? 1 : 0,
                        admission_status: $('#admission_status').val(),
                        student_name_fees: $('#student_name_fees').val(),
                        fee_type: $('#fee_type').val(),
                        installment_type: $('#installment_type').val(),
                        installmentId: $('#installmentId').val(),
                        fee_type: $('#fee_type').val(),
                        payment_status: $('#payment_status').val(),
                        selectedColumns: $('#selectedColumns').val(),
                        classId: $('#classId').val(),
                        classSectionId: $('#classSectionId').val(),
                        admission_type: $('#admission_type').val(),
                        paymentModes: $('#paymentModes').val(),
                        mediumId: $('#mediumId').val(),
                        donorsId: $('#donorsId').val(),
                        boardingId: $('#boardingId').val(),
                        rte_nrteId: $('#rte_nrteId').val(),
                        created_byId: $('#created_byId').val(),
                        payment_modeJSON: $('#payment_modeJSON').val(),
                        stopId: $('#stopId').val(),
                        from_date: $('#from_date').val(),
                        to_date: $('#to_date').val(),
                        routeId: $('#routeId').val(),
                        itemId: $('#itemId').val(),
                        category: $('#category').val(),
                        acad_year_id: $('#acad_year_id').val(),
                        combination: $('#combination').val() || '',
                        staff_kid: $('#staff_kid').val(),
                        report_type: $('input[name="report_type"]:checked').val(),
                        column_visibility: JSON.stringify(visibleCols)
                    };
                }



                function updateFilter() {
                    const selectedFilterId = $('#filter_types').val();

                    if (!selectedFilterId) {
                        bootbox.alert({
                            title: "No Filter Selected",
                            message: "Please select a filter to update.",
                            className: "half-width-box",
                            buttons: {
                                ok: {
                                    label: 'OK',
                                    className: 'btn-primary'
                                }
                            }
                        });

                        // Center the alert modal
                        setTimeout(() => {
                            $('.bootbox .modal-dialog').css({
                                'max-width': '400px',
                                'margin': '1.75rem auto'
                            });
                        }, 10);

                        return; // stop the update function
                    }

                    let filterData = collectFilterData();
                    filterData.filter_types_id = selectedFilterId;
                    filterData.title = $('#filter_types option:selected').text().trim();
                    filterData.stakeholder_id = $('#stakeholder_id').val();

                    bootbox.confirm({
                        title: "Update Filters",
                        message: 'Are you sure you want to update the filter?',
                        className: "half-width-box",
                        buttons: {
                            confirm: {
                                label: 'Yes',
                                className: 'btn-success'
                            },
                            cancel: {
                                label: 'No',
                                className: 'btn-danger'
                            }
                        },
                        callback: function(result) {
                            if (result) {
                                $.ajax({
                                    url: '<?php echo site_url('feesv2/reports/update_filters1'); ?>',
                                    type: 'POST',
                                    data: filterData,
                                    complete: function() {
                                        $.when(get_predefined_filters()).done(function() {
                                            if (
                                                $('#filter_types option[value="' + filterData.filter_types_id + '"]').length === 0
                                            ) {
                                                $('#filter_types').append(
                                                    $('<option>', {
                                                        value: filterData.filter_types_id,
                                                        text: filterData.title
                                                    })
                                                );
                                            }

                                            $('#filter_types').val(filterData.filter_types_id);
                                            selectFilters();

                                            new PNotify({
                                                title: 'Success',
                                                text: 'Filter updated successfully.',
                                                type: 'success',
                                                addclass: 'custom-pnotify half-width-notify'
                                            });
                                        });
                                    }
                                });
                            }
                        }
                    });

                    setTimeout(() => {
                        $('.bootbox .modal-dialog').css({
                            'max-width': '400px',
                            'margin': '1.75rem auto'
                        });
                    }, 10);
                }


                function get_predefined_filters() {
                    return $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters1'); ?>',
                        type: 'POST',
                        success: function(data) {
                            try {
                                var res_data = JSON.parse(data);

                                if (Array.isArray(res_data) && res_data.length > 0) {
                                    var html = '<option value="">Select</option>';
                                    res_data.forEach(filter => {
                                        html += `<option value="${filter.id}">${filter.title}</option>`;
                                    });

                                    $('#filter_types').html(html);
                                } else {
                                    console.warn("No predefined filters found.");
                                    $('#filter_types').html('<option value="">No Filters Available</option>');
                                }
                            } catch (error) {
                                console.error("Error parsing response:", error);
                                $('#filter_types').html('<option value="">Error Loading Filters</option>');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Error:", error);
                            $('#filter_types').html('<option value="">Error Fetching Filters</option>');
                        }
                    });
                }

                function selectFilters() {
                    const filterId = $('#filter_types').val();

                    if (!filterId || filterId.trim() === '') {
                        $('#reload_filter').prop('disabled', true);
                        return;
                    } else {
                        $('#reload_filter').prop('disabled', false);
                    }

                    $('#reload_filter').show();

                    $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters_by_id1'); ?>',
                        type: 'POST',
                        data: {
                            filter_id: filterId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response && response.success && response.filters_selected) {
                                const filters = response.filters_selected;

                                const updateSelect = (selector, values) => {
                                    if (values !== undefined && values !== null) {
                                        if (typeof values === 'string') values = values.split(',');
                                        $(selector).val(values);
                                        // Only call selectpicker refresh on multi-select dropdowns or those with 'select' class
                                        if ($(selector).attr('multiple') || $(selector).hasClass('select')) {
                                            $(selector).selectpicker?.('refresh');
                                        }
                                    }
                                };

                                $('#installment_search')
                                    .prop('checked', filters.installment_search === "true")
                                    .trigger('change');

                                $('#show_over_due').prop('checked', filters.show_over_due === "true");
                                $('#transaction_hide_show').prop('checked', filters.transaction_hide_show == "1").trigger('change');

                                updateSelect('#fee_type', filters.fee_type);
                                updateSelect('#admission_status', filters.admission_status);
                                updateSelect('#combination', filters.combination);
                                updateSelect('#rte_nrteId', filters.rte_nrteId);
                                updateSelect('#staff_kid', filters.staff_kid);
                                updateSelect('#payment_status', filters.payment_status);
                                updateSelect('#donorsId', filters.donorsId);
                                updateSelect('#paymentModes', filters.paymentModes);
                                updateSelect('#admission_type', filters.admission_type);

                                $('#student_name_fees').val(filters.student_name_fees);

                                const classSectionFlow = new Promise((resolve) => {
                                    updateSelect('#classId', filters.classId);
                                    $.ajax({
                                        url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
                                        type: 'POST',
                                        data: {
                                            feeclass: filters.classId
                                        },
                                        success: function(data) {
                                            const resdata = JSON.parse(data);
                                            let option = '';
                                            resdata.forEach(item => {
                                                option += `<option value="${item.section_id}">${item.class_section}</option>`;
                                            });
                                            $('#classSectionId').html(option);
                                            $('#classSectionId').selectpicker('refresh');
                                            $('#classSectionId').val(filters.classSectionId).selectpicker('refresh');
                                            resolve();
                                        }
                                    });
                                });

                                let installmentFlow = Promise.resolve();

                                if (filters.installment_search === "true") {
                                    $('#installmentType').show();
                                    $('#installment').show();

                                    installmentFlow = new Promise((resolve) => {
                                            setTimeout(() => {
                                                updateSelect('#fee_type', filters.fee_type);
                                                resolve();
                                            }, 200);
                                        })
                                        .then(() => {
                                            return new Promise((resolve) => {
                                                setTimeout(() => {
                                                    $.ajax({
                                                        url: '<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
                                                        type: 'POST',
                                                        data: {
                                                            bpType: filters.fee_type
                                                        },
                                                        success: function(data) {
                                                            const parsed = JSON.parse(data);
                                                            let output = '<option value="">Select Installments Type</option>';
                                                            parsed.forEach(item => {
                                                                output += `<option value="${item.id}">${item.name}</option>`;
                                                            });
                                                            $('#installment_type').html(output).val(filters.installment_type).trigger('change');
                                                            resolve();
                                                        }
                                                    });
                                                }, 200);
                                            });
                                        })
                                        .then(() => {
                                            return new Promise((resolve) => {
                                                setTimeout(() => {
                                                    $.ajax({
                                                        url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
                                                        type: 'POST',
                                                        data: {
                                                            installment_type: filters.installment_type
                                                        },
                                                        success: function(data) {
                                                            const parsed = JSON.parse(data);
                                                            let output = '';
                                                            parsed.forEach(item => {
                                                                output += `<option value="${item.id}">${item.name}</option>`;
                                                            });
                                                            $('#installmentId').html(output);
                                                            $('#installmentId').selectpicker();
                                                            $('#installmentId').val(filters.installmentId).selectpicker('refresh');
                                                            resolve();
                                                        }
                                                    });
                                                }, 200);
                                            });
                                        });
                                }

                                if (filters.report_type) {
                                    $(`input[name="report_type"][value="${filters.report_type}"]`).prop('checked', true);
                                    $('#getReport').trigger('click');
                                }

                                Promise.all([installmentFlow, classSectionFlow]).then(() => {
                                    $('#search').prop('disabled', true).val('Please wait...');
                                    get_report();

                                    if (filters.column_visibility) {
                                        setTimeout(() => {
                                            applySavedColumnVisibility(filters.column_visibility);
                                        }, 1000);
                                    }
                                });

                            } else {
                                alert('Failed to fetch filter details. Please try again.');
                            }
                        }
                    });
                }


                function applySavedColumnVisibility(column_visibility) {
                    const tableId = '#fee_summary_data';
                    let visibleCols;

                    try {
                        visibleCols = JSON.parse(column_visibility || '[]');
                    } catch (err) {
                        console.error('Failed to parse column_visibility JSON:', err);
                        return;
                    }

                    const waitForDataTable = () => {
                        const maxTries = 10;
                        let attempts = 0;

                        const interval = setInterval(() => {
                            if ($.fn.DataTable.isDataTable(tableId)) {
                                const table = $(tableId).DataTable();
                                table.columns().every(function(index) {
                                    const shouldBeVisible = visibleCols.includes(index);
                                    this.visible(shouldBeVisible);
                                });
                                clearInterval(interval);
                            } else if (++attempts >= maxTries) {
                                clearInterval(interval);
                                console.warn('⚠️ DataTable not initialized after max attempts');
                            }
                        }, 300);
                    };

                    waitForDataTable();
                }
            </script>
        </div>
    </div>


</div>

</div>


<?php $this->load->helper('reports_datatable');
echo progress_bar(); ?>

<?php $this->load->helper('reports_datatable'); ?>
<div id="no-data-template" style="display: none;">
    <?php echo no_data_message(); ?>
</div>

<div id="printArea">
    <div id="print_visible" style="display: none;" class="text-center">
        <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
            <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
        </h3>
        <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
            Fees summary Report
        </h4>
    </div>

    <div class="fee_summary">
    </div>


    <div class="mx-3">
        <div class="table-responsive" id="report_width_container">
            <table class="table">
            </table>
        </div>
    </div>
    <style>
        #report_width_container {
            padding-left: 30px;
            padding-right: 25px;
        }
    </style>
</div>

</div>
</div>
<div id="modalBackdrop" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background-color:rgba(0,0,0,0.2); z-index:999;" onclick="closeDisturbanceInfo()"></div>

<div id="disturbanceInfoModal" style="display:none; position:fixed; background:#fff; width:280px; font-family:'Poppins', sans-serif; padding:1rem 1.2rem 1.2rem; border-radius:10px; box-shadow:0 10px 30px rgba(0,0,0,0.15); z-index:1000; color:#333;">
    <!-- Arrow pointing up at the right end -->
    <div style="position:absolute; right:20px; top:-8px; width:0; height:0; border-left:8px solid transparent; border-right:8px solid transparent; border-bottom:8px solid #fff;"></div>

    <div style="font-size:0.95rem; line-height:1.5; color:#555;">
        <ul style="padding-left: 18px; margin: 0;">
            <li>This is the <strong>balance amount</strong> to be <strong>paid/recovered</strong> from the <strong>Non-Continuing students</strong> (those who are <strong>not promoted to this year</strong>).</li>
        </ul>
    </div>
</div>



</div>

<script>
    let hoverTimeout;

    function openDisturbanceInfo(event) {
        clearTimeout(hoverTimeout);

        const modal = document.getElementById('disturbanceInfoModal');
        const backdrop = document.getElementById('modalBackdrop');
        const icon = event.currentTarget;
        const iconRect = icon.getBoundingClientRect();

        const topPosition = iconRect.bottom + 10;
        const leftPosition = iconRect.left - 238; // Align left of the icon, considering modal width

        // Update modal position
        modal.style.position = 'fixed'; // Change to fixed positioning
        modal.style.top = topPosition + 'px';
        modal.style.left = leftPosition + 'px';
        modal.style.removeProperty('left!important'); // Remove any !important styles
        modal.style.removeProperty('top!important'); // Remove any !important styles

        modal.style.display = 'block';
        backdrop.style.display = 'block';

        // Keep modal open while hovering over it
        modal.addEventListener('mouseenter', () => clearTimeout(hoverTimeout));
        modal.addEventListener('mouseleave', closeDisturbanceInfo);
    }

    function delayedClose() {
        hoverTimeout = setTimeout(() => {
            closeDisturbanceInfo();
        }, 300); // delay in ms to allow modal mouseenter
    }

    function closeDisturbanceInfo() {
        document.getElementById('disturbanceInfoModal').style.display = 'none';
        document.getElementById('modalBackdrop').style.display = 'none';
    }
</script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>

<script type="text/javascript">
    var loan_column = '<?php echo $loan_column ?>';
    var adjustment = '<?php echo $fee_adjustment_amount ?>';
    var fineAmount = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
    var is_semester_scheme = '<?php echo $this->settings->getSetting('is_semester_scheme') ?>';
    var combination_column = '<?php echo $this->settings->getSetting('fees_stuent_summary_report_show_combination_filter') ?>';
    var cohortStudentIds = [];
    var completed = 0;
    var total_students = 0;
    var totalFeeAssingedStudentCount = 0;
    var aluminiCount = 0;
    var summaryData = [];
    var previous_data = 0;
    var excess_data = 0;
    var prevousYearname = '';
    var continue_students_previous_amount = 0;
    var continue_non_students_previous_amount = 0;
    $(document).ready(function() {
        var fee_type = $('#fee_type').val();
        var clsId = $('#classId').val();
        if (fee_type != null || clsId != null) {
            get_report();
        }
    });

    function get_report() {
        $("#exportButtons").hide();
        $('.table-responsive').html('');
        $('.fee_summary').html('');
        $('.total_student_summary').hide();
        $('#search').prop('disabled', true).val('Please wait..');
        $('select').prop('disabled', true);
        $('.selectpicker').prop('disabled', true);

        var fee_type = $('#fee_type').val();
        var payment_status = $('#payment_status').val();
        var selectedColumns = $('#selectedColumns').val();
        var clsId = $('#classId').val();
        var classSectionId = $('#classSectionId').val();
        var admission_type = $('#admission_type').val();
        var paymentModes = $('#paymentModes').val();
        var mediumId = $('#mediumId').val();
        var donorsId = $('#donorsId').val();
        var boardingId = $('#boardingId').val();
        var rte_nrteId = $('#rte_nrteId').val();
        var created_byId = $('#created_byId').val();
        var payment_modeJSON = $('#payment_modeJSON').val();
        var stopId = $('#stopId').val();
        var from_date = $('#from_date').val();
        var to_date = $('#to_date').val();
        var routeId = $('#routeId').val();
        var itemId = $('#itemId').val();
        var category = $('#category').val();
        var acad_year_id = $('#acad_year_id').val();
        var combination = $('#combination').val();
        var installment_type = $('#installment_type').val();
        var installmentId = $('#installmentId').val();
        var admission_status = $('#admission_status').val();
        var staff_kid = $('#staff_kid').val();

        if (combination == undefined) {
            combination = '';
        }
        completed = 0;
        total_students = 0;
        totalFeeAssingedStudentCount = 0;
        aluminiCount = 0;
        summaryData = [];
        previous_data = 0;
        excess_data = 0;
        continue_students_previous_amount = 0;
        continue_non_students_previous_amount = 0;
        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2'); ?>',
            data: {
                'clsId': clsId,
                'admission_type': admission_type,
                'paymentModes': paymentModes,
                'fee_type': fee_type,
                'mediumId': mediumId,
                'donorsId': donorsId,
                'boardingId': boardingId,
                'created_byId': created_byId,
                'payment_modeJSON': payment_modeJSON,
                'selectedColumns': selectedColumns,
                'from_date': from_date,
                'to_date': to_date,
                'routeId': routeId,
                'itemId': itemId,
                'stopId': stopId,
                'payment_status': payment_status,
                'category': category,
                'classSectionId': classSectionId,
                'rte_nrteId': rte_nrteId,
                'acad_year_id': acad_year_id,
                'combination': combination,
                'installment_type': installment_type,
                'installmentId': installmentId,
                'admission_status': admission_status,
                'staff_kid': staff_kid
            },
            type: "post",
            success: function(data) {
                // console.log(data);
                var cohort_student_ids = JSON.parse(data);
                if (!Array.isArray(cohort_student_ids) || cohort_student_ids.length === 0) {
                    var noDataHtml = $('#no-data-template').html();
                    $('.table-responsive').html(noDataHtml);
                    $('.no-data-state').show();
                    $(".render_report_buttons2").hide();
                    $('#search').prop('disabled', false).val('Get Report');
                    $('select').prop('disabled', false);
                    $('.selectpicker').prop('disabled', false);
                }

                cohortStudentIds = cohort_student_ids;
                total_students = parseInt(150 * (cohortStudentIds.length - 2)) + parseInt(cohortStudentIds[cohortStudentIds.length - 1].length);
                var progress = document.querySelector('.progress-bar');
                progress.style.width = (completed / total_students) * 100 + '%';
                $(".progress").show();
                $("#exportButtons2").hide();
                // console.log(cohortStudentIds);
                // setTimeout(function(){ callReportGetter(0); }, 500);
                callReportGetter(0);

                // $('#feeGenerationReport').html(data);
            },
            error: function(err) {
                alert('Network may be slow');
                console.log(err);
            }
        });
    }

    function callReportGetter(index) {
        if (index < cohortStudentIds.length) {
            getReport(index);
        } else {
            $(".progress").hide();
            $("#exportButtons2").show();
            $("#exportButtons").show();
            $('.total_student_summary').show();
            $('#search').prop('disabled', false).val('Get Report');
            $('select').prop('disabled', false);
            $('.selectpicker').prop('disabled', false);
            var fee_summary = '<table class="table table-bordered summary-table" >';
            fee_summary += '<thead>';
            fee_summary += '<tr>';
            fee_summary += '<th colspan="2" style="background-color: #CEC3F8 !important;">Previous Balance Amount (' + prevousYearname + ') </th>';
            fee_summary += '</tr>';

            fee_summary += '<tr>';
            fee_summary += '<td><b>Balance - Continuing Students</b></td>';
            fee_summary += '<td><b>' + in_currency(continue_students_previous_amount) + '</b></th>';
            fee_summary += '</tr>';

            fee_summary += '<tr>';
            fee_summary += '<td><b>Balance - Non-continuing Students</b></td>';
            fee_summary += '<td>' +
                '<div style="display: flex; justify-content: space-between; align-items: center;">' +
                '<b>' + in_currency(continue_non_students_previous_amount) + '</b>' +
                '<span>' +
                '<a href="javascript:void(0)" class="distrubanceAmount" onclick="check_distrubance_details()" ' +
                'style="color: #623CE7; font-size: 14px; text-decoration: underline;">Disturbance Amount</a> ' +
                '<i class="bi bi-info-circle" style="color: #623CE7; font-size: 14px; margin-left: 6px; cursor: pointer;" ' +
                'onmouseenter="openDisturbanceInfo(event)" onmouseleave="delayedClose()"></i>' +
                '</span>' +
                '</div>' +
                '</td>';
            fee_summary += '</tr>';



            fee_summary += '</table>';

            fee_summary += '<table class="table table-bordered summary-table" >';
            fee_summary += '<thead>';

            fee_summary += '<tr>';
            fee_summary += '<th>Fee Type</th>';
            fee_summary += '<th># Student Assigned</th>';
            fee_summary += '<th>Excess Amount</th>';
            fee_summary += '<th>Fee Amount</th>';
            fee_summary += '<th>Collected Amount</th>';
            fee_summary += '<th>Total Concession</th>';
            if (adjustment) {
                fee_summary += '<th>Total Adjustment</th>';
            }
            if (fineAmount) {
                fee_summary += '<th>Total Fine (Collected)</th>';
            }
            if (loan_column != 0) {
                fee_summary += '<th>Loan Provider Charge</th>';
            }
            fee_summary += '<th>Discount</th>';
            fee_summary += '<th>Balance</th>';
            fee_summary += '</tr>';
            fee_summary += '</thead>';
            fee_summary += '<tbody>';
            var gtotal_fee_amount = 0;
            var gtotal_fee_collected = 0;
            var gtotal_fee_concession = 0;
            var gtotal_fee_received_concession = 0;
            var gtotal_fee_applied_concession = 0;
            var gtotal_fee_applied_adjustment = 0;
            var gtotal_fee_applied_fine = 0;
            var gtotal_fee_balance = 0;
            var gtotal_total_students = 0;
            var gloan_provider_charges = 0;
            var gloan_total_discount = 0;
            // var gloan_total_previous_bal = 0;
            // var gloan_total_excess_amount = 0;
            for (var bp_name in summaryData) {
                gtotal_fee_amount += summaryData[bp_name]['total_fee_amount'];
                gtotal_fee_collected += summaryData[bp_name]['total_fee_collected'];
                gtotal_fee_concession += summaryData[bp_name]['total_fee_concession'];
                // gtotal_fee_received_concession += summaryData[bp_name]['total_fee_received_concession'];
                gtotal_fee_applied_concession += summaryData[bp_name]['total_fee_applied_concession'];
                gtotal_fee_applied_adjustment += summaryData[bp_name]['total_adjustment_applied'];
                gtotal_fee_applied_fine += summaryData[bp_name]['total_fine_applied'];
                gtotal_fee_balance += summaryData[bp_name]['total_fee_balance'];
                gtotal_total_students += summaryData[bp_name]['total_student_assigned'];
                gloan_provider_charges += summaryData[bp_name]['loan_provider_charges'];
                gloan_total_discount += summaryData[bp_name]['total_discount'];
                // gloan_total_previous_bal += summaryData[bp_name]['previous_balance'];
                // gloan_total_excess_amount += summaryData[bp_name]['excess_amount'];

                fee_summary += '<tr>';
                fee_summary += '<td style="font-size:12px!important; color:#161327"><b>' + bp_name + '</b></td>';
                fee_summary += '<td>' + in_currency(summaryData[bp_name]['total_student_assigned']) + '</td>';
                fee_summary += '<td>-</td>';
                fee_summary += '<td>' + in_currency(summaryData[bp_name]['total_fee_amount']) + '</td>';
                fee_summary += '<td>' + in_currency(summaryData[bp_name]['total_fee_collected'] - summaryData[bp_name]['loan_provider_charges'] - summaryData[bp_name]['total_discount']) + '</td>';
                fee_summary += '<td> ( ' + in_currency(summaryData[bp_name]['total_fee_concession']) + ' ) </td>';
                // fee_summary +='<td>'+in_currency(summaryData[bp_name]['total_fee_received_concession'])+'</td>';
                // fee_summary +='<td> ( '+in_currency(summaryData[bp_name]['total_fee_applied_concession'])+' )</td>';
                if (adjustment) {
                    fee_summary += '<td> ( ' + in_currency(summaryData[bp_name]['total_adjustment_applied']) + ' ) </td>';
                }
                if (fineAmount) {
                    fee_summary += '<td> ' + in_currency(summaryData[bp_name]['total_fine_applied']) + ' </td>';
                }
                if (loan_column != 0) {
                    fee_summary += '<td>' + in_currency(summaryData[bp_name]['loan_provider_charges']) + '</td>';
                }
                fee_summary += '<td>(' + in_currency(summaryData[bp_name]['total_discount']) + ')</td>';
                fee_summary += '<td>' + in_currency(summaryData[bp_name]['total_fee_balance']) + '</td>';
                fee_summary += '</tr>';
            }
            fee_summary += '</tbody>';
            fee_summary += '<tfoot>';
            fee_summary += '<tr>';
            fee_summary += '<td colspan="2" style="font-size:13px  !important;font-weight:700 !important;">Grand Total</td>';
            fee_summary += '<td>(' + in_currency(excess_data) + ')</td>';
            fee_summary += '<td>' + in_currency(gtotal_fee_amount) + '</th>';
            fee_summary += '<td>' + in_currency(gtotal_fee_collected - gloan_total_discount) + '</td>';
            fee_summary += '<td> ( ' + in_currency(gtotal_fee_concession) + ' ) </td>';
            // fee_summary +='<th>'+in_currency(gtotal_fee_received_concession)+'</th>';
            // fee_summary +='<th> ( '+in_currency(gtotal_fee_applied_concession)+' ) </th>';

            if (adjustment) {
                fee_summary += '<td> ( ' + in_currency(gtotal_fee_applied_adjustment) + ' ) </td>';
            }
            if (fineAmount) {
                fee_summary += '<td> ' + in_currency(gtotal_fee_applied_fine) + ' </td>';
            }

            if (loan_column != 0) {
                fee_summary += '<td>' + in_currency(gloan_provider_charges) + '</td>';
            }
            fee_summary += '<td>(' + in_currency(gloan_total_discount) + ')</td>';
            fee_summary += '<td>' + in_currency(gtotal_fee_balance) + '</td>';
            fee_summary += '</tr>';
            fee_summary += '<tr>';

            var colSpan = '7';
            if (adjustment || fineAmount || loan_column) {
                colSpan = '8';
            }
            if ((fineAmount && loan_column) || (adjustment && fineAmount && loan_column)) {
                colSpan = '9';
            }
            if (adjustment && fineAmount && loan_column) {
                colSpan = '10';
            }
            fee_summary += '<td colspan="' + colSpan + '" style="font-size:13px  !important;font-weight:700 !important;">Over all Balance (Previous Continuing Balance Amount + Current Year Balance - Excess Amount)</td>';
            fee_summary += '<td>' + in_currency((gtotal_fee_balance + continue_students_previous_amount) - excess_data) + '</td>';
            fee_summary += '</tr>';
            fee_summary += '</tfoot>';

            fee_summary += '</table>';
            $(".fee_summary").html(fee_summary);
            var transactionHideShow = $("#transaction_hide_show").is(":checked") ? 0 : 1;

            if (!transactionHideShow) {
                if ($.fn.DataTable.isDataTable('#fee_summary_data')) {
                    $('#fee_summary_data').DataTable().destroy();
                    $('#fee_summary_data').empty();
                }
                let colLen = 13;
                if (combination_column == 1 && is_semester_scheme == 1) {
                    colLen = 15;
                } else if (combination_column == 1 || is_semester_scheme == 1) {
                    colLen = 14;
                }
                let table = $('#fee_summary_data').DataTable({
                    ordering: false,
                    searching: true,
                    paging: true,
                    pageLength: 10,
                    pagingType: 'full_numbers',
                    dom: '<"row align-items-center mb-2" <"col-md-6"l> <"col-md-6 d-flex justify-content-end gap-2"fB> >rt<"row align-items-center mt-4" <"col-md-6 mb-2"i> <"col-md-6 d-flex justify-content-end mb-2"p> >',
                    info: true,
                    'columnDefs': [{
                            orderable: false,
                            targets: 1
                        },
                        {
                            targets: 4,
                            visible: false
                        },
                        {
                            targets: 5,
                            visible: false
                        },
                    ],
                    fixedHeader: true,
                    scrollY: 400,
                    scrollX: true,
                    scrollCollapse: true,
                    autoWidth: true,
                    language: {
                        search: "",
                        searchPlaceholder: "Search",
                        lengthMenu: "Show _MENU_ ",
                        info: "Showing _START_ to _END_ of _TOTAL_ ",
                        paginate: {
                            first: "&laquo;",
                            last: "&raquo;",
                            next: "Next &rsaquo;",
                            previous: "&lsaquo; Previous"
                        },
                        emptyTable: "No data available in table",
                        zeroRecords: "No matching records found"
                    },
                    lengthMenu: [
                        [10, 25, 50, -1],
                        [10, 25, 50, "All"]
                    ],
                    buttons: [{
                            extend: 'colvis',
                            text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/column.svg', [], true); ?> Columns</button>`,
                            className: 'btn btn-outline-primary',
                            columns: function(idx, data, node) {
                                return idx <= colLen;
                            },
                        },
                        {
                            extend: 'print',
                            text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                            title: 'Fee detail report',
                            footer: true,
                            exportOptions: {
                                columns: ':visible',
                            },
                            customize: function(win) {
                                $(win.document.body)
                                    .prepend($('.fee_summary').clone())
                                    .css('font-family', "'Poppins', sans-serif")
                                    .css('font-size', '10pt')
                                    .css('padding', '10px');

                                $(win.document.head).append(`
                                <style>
                                    @page {
                                    size: auto;
                                    margin: 12mm;
                                    }

                                    body {
                                    font-family: 'Poppins', sans-serif;
                                    -webkit-print-color-adjust: exact;
                                    print-color-adjust: exact;
                                    color: #333;
                                    background: #fff;
                                    }

                                    h2, h3 {
                                    text-align: center;
                                    margin-bottom: 15px;
                                    font-weight: 500;
                                    }

                                    table {
                                    border-collapse: collapse !important;
                                    width: 100% !important;
                                    margin-top: 20px;
                                    font-size: 10pt;
                                    color: #333;
                                    }

                                    th, td {
                                    border: 1px solid #ccc !important;
                                    padding: 8px 12px;
                                    text-align: left;
                                    vertical-align: middle;
                                    }

                                    th {
                                    background-color: #f4f7fc !important;
                                    font-weight: 600;
                                    color: #333;
                                    }

                                    .table-bordered {
                                    width: 100% !important;
                                    }

                                    .fee_summary table {
                                    margin-bottom: 25px;
                                    }

                                    .fee_summary th[colspan] {
                                    text-align: center;
                                    background: #e0ecff;
                                    font-size: 11pt;
                                    font-weight: 500;
                                    }

                                    tfoot th {
                                    background-color: #f9f9f9;
                                    font-weight: 600;
                                    }

                                    .distrubanceAmount {
                                    color: #007bff;
                                    text-decoration: underline;
                                    font-size: 10pt;
                                    }

                                    a {
                                    color: #007bff !important;
                                    }
                                </style>
                                `);
                            },
                        },
                        {
                            extend: 'excelHtml5',
                            text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
                            title: 'Fee detail report',
                            footer: true,
                            messageTop: '<?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?> ',
                            exportOptions: {
                                columns: ':visible',
                            },
                        },
                    ],
                });
                setTimeout(() => {
                    const $input = $('.dt-search input[type="search"]');
                    if ($input.length) {
                        if ($input.parent().hasClass('search-box')) {
                            $input.unwrap();
                        }
                        $input.siblings('.bi-search').remove();
                        $input.addClass('input-search');
                        $input.wrap('<div class="search-box position-relative"></div>');
                        $input.parent().prepend('<i class="bi bi-search"></i>');
                        const searchWrapper = $input.closest('.search-box');
                        $('.dt-search').empty().append(searchWrapper);
                        $input.off('input').on('input', function() {
                            table.search(this.value).draw();
                        });
                    }
                }, 0);
                const $filter = $('#fee_summary_data_filter');
                const $input = $filter.find('input');

                $filter.find('label').contents().filter(function() {
                    return this.nodeType === 3;
                }).remove();

                $input.attr('placeholder', 'Search');
                $filter.addClass('custom-search-box');
                $filter.find('label').wrapInner('<div class="search-box"></div>');
                $filter.find('.search-box').prepend('<i class="bi bi-search"></i>');

                if (transactionHideShow) {
                    $('#exportButtons').show();
                } else {
                    $('#exportButtons').hide();
                }
            }
        }
    }

    function getReport(index) {
        var cohortstudentids = cohortStudentIds[index];
        var fee_type = $('#fee_type').val();
        var payment_status = $('#payment_status').val();
        var selectedColumns = $('#selectedColumns').val();
        var clsId = $('#classId').val();
        var classSectionId = $('#classSectionId').val();
        var admission_type = $('#admission_type').val();
        var paymentModes = $('#paymentModes').val();
        var mediumId = $('#mediumId').val();
        var donorsId = $('#donorsId').val();
        var boardingId = $('#boardingId').val();
        var rte_nrteId = $('#rte_nrteId').val();
        var created_byId = $('#created_byId').val();
        var payment_modeJSON = $('#payment_modeJSON').val();
        var stopId = $('#stopId').val();
        var from_date = $('#from_date').val();
        var to_date = $('#to_date').val();
        var routeId = $('#routeId').val();
        var itemId = $('#itemId').val();
        var category = $('#category').val();
        var acad_year_id = $('#acad_year_id').val();
        var combination = $('#combination').val();
        if (combination == undefined) {
            combination = '';
        }
        var installment_type = $('#installment_type').val();
        var installmentId = $('#installmentId').val();
        var admission_status = $('#admission_status').val();
        var staff_kid = $('#staff_kid').val();
        var transaction_hide_show = $("#transaction_hide_show").is(":checked") ? 1 : 0;
        // console.log(cohortstudentids);
        url = '<?php echo site_url('feesv2/reports_v2/getStudentsForSummary_v2_details_new'); ?>';
        $.ajax({
            url: url,
            type: 'post',
            data: {
                cohortstudentids: cohortstudentids,
                'clsId': clsId,
                'admission_type': admission_type,
                'paymentModes': paymentModes,
                'fee_type': fee_type,
                'mediumId': mediumId,
                'donorsId': donorsId,
                'boardingId': boardingId,
                'rte_nrteId': rte_nrteId,
                'created_byId': created_byId,
                'payment_modeJSON': payment_modeJSON,
                'selectedColumns': selectedColumns,
                'from_date': from_date,
                'to_date': to_date,
                'routeId': routeId,
                'itemId': itemId,
                'stopId': stopId,
                'payment_status': payment_status,
                'category': category,
                'classSectionId': classSectionId,
                'rte_nrteId': rte_nrteId,
                'acad_year_id': acad_year_id,
                'combination': combination,
                'installment_type': installment_type,
                'installmentId': installmentId,
                'admission_status': admission_status,
                'staff_kid': staff_kid,
                'transaction_hide_show': transaction_hide_show
            },
            success: function(data) {
                var rData = $.parseJSON(data);
                var headers = rData.headers;
                var header = rData.header;
                var fee_data = rData.fee_data;
                var summary = rData.summary;
                var transactions = rData.transactions;
                var headerbpName = rData.summaryHeaderbp;
                prevousYearname = rData.prevousYearname;
                continue_non_students_previous_amount = rData.prevousNonContinueCount;
                // console.log(summary);
                constructFeeSummary(summary, headers, index, headerbpName);
                if (index == 0) {
                    constructFeeHeader(header);
                }
                completed += Object.keys(fee_data).length;
                var progress = document.querySelector('.progress-bar');
                progress.style.width = (completed / total_students) * 100 + '%';
                constructFeeReport(fee_data, headers, index, transactions, transaction_hide_show);

                // $('.table-responsive').html(data);
            }
        });
    }


    function constructFeeSummary(summary, headers, index, headerbpName, total_assign_std) {
        for (bp in headerbpName) {
            if ((summary).hasOwnProperty(bp)) {
                if (!(summaryData).hasOwnProperty(headerbpName[bp].blueprint_name)) {
                    summaryData[headerbpName[bp].blueprint_name] = [];
                    summaryData[headerbpName[bp].blueprint_name]['total_fee_amount'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_fee_collected'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_fee_concession'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_fee_applied_concession'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_fee_received_concession'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_fee_balance'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['loan_provider_charges'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_student_fee_assigned'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_student_assigned'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_adjustment_applied'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_fine_applied'] = 0;
                    summaryData[headerbpName[bp].blueprint_name]['total_discount'] = 0;
                    // summaryData[headerbpName[bp].blueprint_name]['previous_balance'] = 0;
                    // summaryData[headerbpName[bp].blueprint_name]['excess_amount'] = 0;
                }
                summaryData[headerbpName[bp].blueprint_name]['total_fee_amount'] += summary[bp].total_fee_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_fee_collected'] += summary[bp].total_fee_paid_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_fee_concession'] += summary[bp].total_concession_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_fee_applied_concession'] += summary[bp].applied_concession_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_fee_received_concession'] += summary[bp].received_concession_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_fee_balance'] += summary[bp].total_balance_summary;
                summaryData[headerbpName[bp].blueprint_name]['loan_provider_charges'] += summary[bp].loan_provider_charges;
                summaryData[headerbpName[bp].blueprint_name]['total_student_assigned'] += summary[bp].total_assigned;
                summaryData[headerbpName[bp].blueprint_name]['total_adjustment_applied'] += summary[bp].applied_adjustment_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_fine_applied'] += summary[bp].applied_fine_summary;
                summaryData[headerbpName[bp].blueprint_name]['total_discount'] += summary[bp].applied_discount;
                // summaryData[headerbpName[bp].blueprint_name]['previous_balance'] += summary[bp].previous_balance;
                // summaryData[headerbpName[bp].blueprint_name]['excess_amount'] += summary[bp].excess_amount;
            }
        }
    }

    function constructFeeHeader(header) {
        var h_output = '<div style="width: 100%; overflow: auto;"><table id="fee_summary_data" class="table table-bordered">';
        h_output += header;
        h_output += '</table></div>';
        $('.table-responsive').html(h_output);
        add_scroller('report_width_container');
    }

    function constructFeeReport(fee_data, headers, index, transactions, transaction_hide_show) {
        //console.log('trans',transaction_hide_show);
        var srNo = index * 150;
        var html = '';
        if (!transaction_hide_show) {
            html += '<tbody>';
        }
        if (transaction_hide_show) {
            var tbody = $('#fee_summary_data tbody');
            if (tbody.length === 0) {
                tbody = $('<tbody></tbody>');
                $('#fee_summary_data').append(tbody);
            }
        }

        var m = 0;
        var status_arr = {
            'NOT_STARTED': '<span class="text-danger">Not Paid</span>',
            'FULL': '<span class="text-success">Paid</span>',
            'PARTIAL': '<span class="text-warning">Partially-paid</span>'
        };
        for (var k in fee_data) {
            continue_students_previous_amount += parseFloat(fee_data[k].previous_bal);
            excess_data += parseFloat(fee_data[k].addt_amount);
            if (fee_data[k].admission_status != '2') {
                statusColor = '#d30c0c';
                aluminiCount++;
            } else {
                statusColor = '';
            }
            html += '<tr style="color:' + statusColor + '" >';
            html += '<td>' + (m + 1 + srNo) + '</td>';
            html += '<td>' + fee_data[k].student_name + '</td>';
            html += '<td>' + fee_data[k].className + '</td>';
            html += '<td>' + fee_data[k].sectionName + '</td>';
            html += '<td>' + fee_data[k].category + '</td>';
            html += '<td>' + fee_data[k].caste + '</td>';
            if (is_semester_scheme == 1) {
                if (fee_data[k].semester == null) {
                    html += '<td></td>';
                } else {
                    html += '<td>' + fee_data[k].semester + '</td>';
                }
            }

            html += '<td>' + fee_data[k].admission_no + '</td>';
            html += '<td>' + fee_data[k].enrollment_number + '</td>';
            html += '<td>' + fee_data[k].boarding_type + '</td>';
            html += '<td>' + fee_data[k].application_no + '</td>';
            if (combination_column == 1) {
                html += '<td>' + fee_data[k].combination + '</td>';
            }
            html += '<td>' + fee_data[k].parent_name + '</td>';
            html += '<td>' + fee_data[k].mobile_no + '</td>';
            var previous_bal = fee_data[k].previous_bal;
            if (fee_data[k].previous_bal == null || fee_data[k].previous_bal == undefined) {
                previous_bal = '0';
            }

            var addt_amount = fee_data[k].addt_amount;
            if (fee_data[k].addt_amount == null || fee_data[k].addt_amount == undefined) {
                addt_amount = '0';
            }

            html += '<td>' + previous_bal + '</td>';
            html += '<td>' + addt_amount + '</td>';

            html += '<td>' + in_currency(fee_data[k].total_fee) + '</td>';
            html += '<td>' + in_currency(fee_data[k].total_fee_paid - fee_data[k].loan_provider_charges - fee_data[k].discount) + '</td>';
            html += '<td> ( ' + in_currency(fee_data[k].total_concession) + ' ) </td>';
            var colspan = '8';
            if (adjustment == 1) {
                let adj = fee_data[k].total_adjustment;
                if (adj === undefined || adj === null || adj === '') {
                    html += '<td>-</td>';
                } else {
                    html += '<td> ( ' + in_currency(adj) + ' ) </td>';
                }
                colspan = '9';
            }
            if (fineAmount) {
                html += '<td> ' + in_currency(fee_data[k].total_fine) + ' </td>';
                colspan = '9';
            }
            if (fineAmount && adjustment) {
                colspan = '10';
            }

            if (loan_column != 0) {
                html += '<td>' + in_currency(fee_data[k].loan_provider_charges) + '</td>';
                colspan = '9';
            }
            html += '<td>' + in_currency(fee_data[k].discount) + '</td>';
            html += '<td>' + in_currency(fee_data[k].total_balance) + '</td>';

            const feeAmount = parseFloat(fee_data[k].total_fee);
            const balance = parseFloat(fee_data[k].total_balance);
            let percentage = 0;
            if (feeAmount > 0) {
                percentage = (balance / feeAmount) * 100;
            }
            html += '<td>' + percentage.toFixed(2) + "%" + '</td>';
            html += '<td>' + in_currency(fee_data[k].total_due_amount) + '</td>';
            var std_bp = fee_data[k].bpId;

            for (var hbpId in headers) {
                var insHeader = headers[hbpId];
                for (var hinsId in insHeader) {
                    if (hbpId in std_bp && hinsId in std_bp[hbpId]) {
                        var due_amount = parseInt(std_bp[hbpId][hinsId].installment_amount) - parseInt(std_bp[hbpId][hinsId].installment_amount_paid) - parseInt(std_bp[hbpId][hinsId].concession);
                        html += '<td style="border-left:2px solid #000" >' + in_currency(std_bp[hbpId][hinsId].installment_amount) + '</td>';
                        html += '<td>' + in_currency(std_bp[hbpId][hinsId].installment_amount_paid) + '</td>';
                        html += '<td> ( ' + in_currency(std_bp[hbpId][hinsId].concession) + ' )</td>';
                        if (adjustment == 1) {
                            if (std_bp[hbpId][hinsId] && std_bp[hbpId][hinsId].adjustment !== undefined) {
                                html += '<td>( ' + in_currency(std_bp[hbpId][hinsId].adjustment) + ' ) </td>';
                            } else {
                                html += '<td>-</td>';
                            }
                        }
                        if (fineAmount) {
                            html += '<td> ' + in_currency(std_bp[hbpId][hinsId].fine) + ' </td>';
                        }
                        html += '<td>' + in_currency(due_amount) + '</td>';
                        html += '<td>' + status_arr[std_bp[hbpId][hinsId].status] + '</td>';
                    } else {
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                        if (adjustment == 1) {
                            html += '<td>-</td>';
                        }
                        if (fineAmount == 1) {
                            html += '<td>-</td>';
                        }
                        html += '<td>-</td>';
                        html += '<td>-</td>';
                    }
                }
                if (!transaction_hide_show) {
                    if ((transactions).hasOwnProperty(fee_data[k].stdId)) {
                        var trans = transactions[fee_data[k].stdId].transaction;
                        html += '<td colspan="' + colspan + '" style="padding:0">';
                        html += '<table id="trans_table" style="width: 100%;">';
                        for (t in trans[hbpId]) {
                            html += '<tr>';
                            html += '<td style="width: 10%;">' + trans[hbpId][t].paid_date + '</td>';
                            html += '<td style="width: 11%;">' + trans[hbpId][t].receipt_number + '</td>';
                            html += '<td style="width: 11%;">' + in_currency(trans[hbpId][t].amount_paid) + '</td>';
                            html += '<td style="width: 11%;"> ( ' + in_currency(trans[hbpId][t].concession_amount) + ' ) </td>';
                            if (adjustment) {
                                html += '<td style="width: 11%;"> ( ' + in_currency(trans[hbpId][t].adjustment_amount) + ' ) </td>';
                            }
                            if (fineAmount) {
                                html += '<td style="width: 11%;">  ' + in_currency(trans[hbpId][t].fine_amount) + ' </td>';
                            }

                            html += '<td style="width: 14%;">' + paymenttypeAction(trans[hbpId][t].payment_type, trans[hbpId][t].reconciliation_status) + '</td>';
                            html += '<td style="width: 11%;">' + trans[hbpId][t].bank_name + '</td>';
                            if (trans[hbpId][t].payment_type == 1 || trans[hbpId][t].payment_type == 4 || trans[hbpId][t].payment_type == 8) {
                                html += '<td style="width: 11%;">' + trans[hbpId][t].cheque_or_dd_date + '</td>';
                            } else {
                                html += '<td style="width: 11%;"></td>';
                            }
                            html += '<td style="width: 21%;">' + trans[hbpId][t].cheque_dd_nb_cc_dd_number + '</td>';
                            html += '</tr>';
                        }
                        html += '</table>';
                        html += '</td>';

                    } else {

                        html += '<td colspan="' + colspan + '" style="font-size: 14px;font-weight: 600;color: #8f8484c7;text-align: center;">No transactions</td>';
                    }
                }

            }
            html += '</tr>';
            m++;
        }
        if (transaction_hide_show) {
            tbody.append(html);
        }
        if (!transaction_hide_show) {
            html += '</tbody>';
            $('#fee_summary_data').append(html);
        }
        index++;
        callReportGetter(index);
    }

    function in_currency(amount) {
        var formatter = new Intl.NumberFormat('en-IN', {
            // style: 'currency',
            currency: 'INR',
        });
        return formatter.format(amount);
    }

    function paymenttypeAction(paymentType, recon) {
        var NC = '';
        if (recon == 1) {
            NC = '<span style="color:red;"> <b> (N/C) </b><span>';
        } else if (recon == 2) {
            NC = '<span> <b> (C) </b><span>';
        }
        switch (paymentType) {
            case '1':
                pValue = 'DD ' + NC;
                break;
            case '2':
                pValue = 'Credit Card ' + NC;
                break;
            case '3':
                pValue = 'Debit Card ' + NC;
                break;
            case '4':
                pValue = 'Cheque ' + NC;
                break;
            case '5':
                pValue = 'Wallet Payment ' + NC;
                break;
            case '6':
                pValue = 'Challan ' + NC;
                break;
            case '7':
                pValue = 'Card (POS) ' + NC;
                break;
            case '8':
                pValue = 'Net Banking ' + NC;
                break;
            case '9':
                pValue = 'Cash';
                break;
            case '10':
                pValue = 'Online Payment';
                break;
            default:
                pValue = '';
                break;
        }
        return pValue;
    }
</script>

<script type="text/javascript">
    $('#installment_search').on('change', function() {
        if ($('#installment_search').is(':checked')) {
            $('#blueprintSelect').show();
            $('#multiBlueprintSelect').hide();
            $('.multiselect').removeAttr('id')
        } else {
            $('#blueprintSelect').hide();
            $('#multiBlueprintSelect').show();
            $('#installmentType').hide();
            $('#installment').hide();
            $('#installment_type').val('');
            $('#installmentId').val('');
            $('.multiselect').attr('id', 'fee_type');
        }
    });
</script>


<script type="text/javascript">
    $('.changeFeeType').on('change', function() {
        var bpType = $('.changeFeeType').val();
        $('#installment').hide();
        $('#installment_type').val('');
        $('#installmentType').hide();
        if (bpType != 'all') {
            changeInstallment_type(bpType);
            $('#installmentType').show();
        } else {
            $('#installmentId').val('');
            $('#installmentType').hide();
        }
    });

    function changeInstallment_type(bpType) {
        $.ajax({
            url: '<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
            type: 'post',
            data: {
                'bpType': bpType
            },
            success: function(data) {
                var data = JSON.parse(data);
                var output = '<option value="">Select Installments Type</option>';
                for (var i = 0; i < data.length; i++) {
                    output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                }
                $('#installment_type').html(output);
            }
        });
    }

    function changeInstallment() {
        $('#installment_type').on('change', function() {
            var installment_type = $(this).val();

            $('#installmentId').html('<option value="">Select Installments</option>');

            if (installment_type) {
                $.ajax({
                    url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
                    type: 'post',
                    data: {
                        'installment_type': installment_type
                    },
                    success: function(data) {
                        var data = JSON.parse(data);
                        var output = '';

                        for (var i = 0; i < data.length; i++) {
                            output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                        }

                        $('#installmentId').html(output);

                        $('#installment').show();

                        $('#installmentId').selectpicker('refresh');

                        var allValues = [];
                        $('#installmentId option').each(function() {
                            allValues.push($(this).val());
                        });

                        $('#installmentId').selectpicker('val', allValues);

                    },
                    error: function(xhr, status, error) {
                        console.error("Error fetching installments: ", error);
                    }
                });
            } else {
                $('#installmentId').html('<option value="">Select Installments</option>');
                $('#installment').hide();
            }
        });
    }


    $(document).ready(function() {
        changeInstallment();
    });

    $('#installment_type').on('change', function() {
        changeInstallment();
    });

    $("#classId").change(function() {
        const previouslySelectedSections = $('#classSectionId').val();

        $('#classSectionId').html('');
        const selectedValue = $(this).val();

        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
            data: {
                'feeclass': selectedValue
            },
            type: "post",
            success: function(data) {
                const resdata = JSON.parse(data);
                let option = '';
                resdata.forEach(item => {
                    option += `<option value="${item.section_id}">${item.class_section}</option>`;
                });

                $("#classSectionId").html(option);
                $('#classSectionId').selectpicker('refresh');

                if (previouslySelectedSections) {
                    $('#classSectionId').val(previouslySelectedSections).selectpicker('refresh');
                }
            },
            error: function(err) {
                console.log(err);
            }
        });
    });
</script>
<script type="text/javascript">
    function printProfile() {
        const printWindow = window.open('', '_blank');
        let printHeader = document.getElementById('print_visible').outerHTML;
        printHeader = printHeader.replace('display: none;', '');

        // Get the fee summary tables
        let feeSummaryContent = '';
        const feeSummaryElem = document.querySelector('.fee_summary');
        if (feeSummaryElem) {
            feeSummaryContent = feeSummaryElem.outerHTML;
        }

        // Get all data from DataTable
        let mainTableContent = '';
        if ($.fn.DataTable.isDataTable('#fee_summary_data')) {
            var table = $('#fee_summary_data').DataTable();
            var headers = $('#fee_summary_data thead').html();
            var footers = $('#fee_summary_data tfoot').length ? $('#fee_summary_data tfoot').html() : '';
            var allData = table.rows({
                search: 'applied'
            }).data();

            let rowsHtml = '';
            for (let i = 0; i < allData.length; i++) {
                // DataTables returns an array of <td> HTML strings for each row
                rowsHtml += '<tr>';
                for (let j = 0; j < allData[i].length; j++) {
                    rowsHtml += `<td>${allData[i][j]}</td>`;
                }
                rowsHtml += '</tr>';
            }

            mainTableContent = `
                <div class="table-responsive">
                    <table class="table table-bordered" style="width: 100%">
                        <thead>${headers}</thead>
                        <tbody>${rowsHtml}</tbody>
                        ${footers ? `<tfoot>${footers}</tfoot>` : ''}
                    </table>
                </div>
            `;
        } else {
            // fallback: clone the DOM (may be incomplete)
            const tableResponsiveElem = document.querySelector('.table-responsive');
            if (tableResponsiveElem && tableResponsiveElem.innerHTML.trim() !== '') {
                mainTableContent = tableResponsiveElem.outerHTML;
            }
        }

        // Add external CSS if needed (adjust the href as per your project)
        const externalCSS = `
            <link rel="stylesheet" href="<?php echo base_url('assets/css/bootstrap.min.css'); ?>">
            <link rel="stylesheet" href="<?php echo base_url('assets/css/your-custom-style.css'); ?>">
        `;

        let reportTitle = 'Student Fees Summary Report';
        const installmentChecked = document.getElementById('installment_search').checked;
        if (installmentChecked) {
            reportTitle = 'Student Fees Summary Report (Installments View)';
        }

        printWindow.document.write(`
            <html>
            <head>
                <title>${reportTitle}</title>
                ${externalCSS}
                <style>
                    body { font-family: 'Poppins', sans-serif; padding: 20px; margin: 0; }
                    table { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f5f5f5; font-weight: 600; }
                    .text-center { text-align: center; }
                    h3, h4 { margin: 15px 0; text-align: center; }
                    .fee_summary table { margin-bottom: 25px; }
                    .fee_summary th[colspan] { text-align: center; background: #e0ecff; font-size: 11pt; font-weight: 500; }
                    .table-bordered { border: 1px solid #ddd; }
                    .table-bordered th, .table-bordered td { border: 1px solid #ddd; }
                    tfoot th { background-color: #f8f9fa; font-weight: bold; }
                    .table-responsive { overflow: visible !important; }
                    @media print {
                        body { margin: 0; }
                        table { page-break-inside: auto; }
                        tr { page-break-inside: avoid; page-break-after: auto; }
                        thead { display: table-header-group; }
                        tfoot { display: table-footer-group; }
                        .fee_summary { page-break-after: avoid; }
                    }
                </style>
            </head>
            <body>
                ${printHeader}
                ${feeSummaryContent}
                ${mainTableContent}
                <script>
                    window.onload = function() { window.print(); };
                    window.onafterprint = function() { window.close(); };
                <\/script>
            </body>
            </html>
        `);

        printWindow.document.close();
    }

    function exportToExcel_daily() {
        var htmls = "";
        var uri = "data:application/vnd.ms-excel;base64,";
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/><style>body{font-family: Poppins, serif;}</style></head><body><table>{table}</table></body></html>';

        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)));
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            });
        };

        // Get header from print_visible div
        var header = $("#print_visible").html();

        // Clone tables and remove unwanted elements
        var summaryTable = $(".fee_summary").clone();
        var mainTable = $("#fee_summary_data").clone();

        mainTable.find(".dt-buttons").remove();
        summaryTable.find(".distrubanceAmount").remove();

        // Combine all content with proper spacing
        htmls = '<br><br>' + header + '<br><br>' + summaryTable.prop("outerHTML") + '<br><br>' + mainTable.prop("outerHTML");

        var ctx = {
            worksheet: "Fees Detail Report",
            table: htmls
        };

        if (navigator.msSaveOrOpenBlob) {
            var blob = new Blob([format(template, ctx)], {
                type: "application/vnd.ms-excel"
            });
            navigator.msSaveOrOpenBlob(blob, "Fee Detail Report.xls");
        } else {
            var link = document.createElement("a");
            link.download = "fees_summary_report.xls";
            link.href = uri + base64(format(template, ctx));
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    function check_distrubance_details() {
        // Show the backdrop to dim the background
        $('#modalBackdrop').css('z-index', 998).show();
        $('#distrubance_payment_details').modal('show');
        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/fees_distrubance_student_details') ?>',
            type: "post",
            success: function(data) {
                var resdata = $.parseJSON(data);
                var non_continue_list = resdata.studentData;
                $('#content_fees_student').html(construct_distrubance_report(non_continue_list));
                $('#distrubaneTable').DataTable({
                    ordering: false,
                    searching: true,
                    paging: true,
                    pageLength: 10,
                    scrollY: '40vh',
                    responsive: true,
                    "language": {
                        "search": "",
                        "searchPlaceholder": "Search",
                        lengthMenu: "Show _MENU_ ",
                        info: "Showing _START_ to _END_ of _TOTAL_ ",
                        paginate: {
                            first: "&laquo;",
                            last: "&raquo;",
                            next: "Next &rsaquo;",
                            previous: "&lsaquo; Previous"
                        },
                    },
                    "lengthMenu": [
                        [10, 25, 50, -1],
                        [10, 25, 50, "All"]
                    ],
                    "pageLength": 10,
                    dom: '<"row align-items-center mb-2" <"col-md-6"l> <"col-md-6 d-flex justify-content-end gap-2"fB> >rt<"row align-items-center mt-2" <"col-md-6 mb-2"i> <"col-md-6 d-flex justify-content-end mb-2"p> >',
                    buttons: [{
                            extend: 'print',
                            text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                            filename: 'distrubance report'
                        },
                        {
                            extend: 'excelHtml5',
                            text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
                            filename: 'distrubance report'
                        }

                    ]
                });

                console.log(resdata);
            }
        });
    }

    function construct_distrubance_report(resdata) {
        var html = '';
        html += '<table class="table table-bordered" id="distrubaneTable">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>#</th>';
        html += '<th>Student Name</th>';
        html += '<th>Class/Section</th>';
        html += '<th>Admission No.</th>';
        html += '<th>Balance Amount</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        for (let k = 0; k < resdata.length; k++) {
            html += '<tr>';
            html += '<td>' + (k + 1) + '</td>';
            html += '<td>' + resdata[k].student_name + '</td>';
            html += '<td>' + resdata[k].class_name + '</td>';
            html += '<td>' + resdata[k].admission_no + '</td>';
            html += '<td>' + resdata[k].balance + '</td>';
            html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';
        return html;

    }
    $('#clear').on('click', function() {
        location.reload();
    });
    $(document).on('click', '#distrubance_payment_details button[data-dismiss="modal"]', function() {
        $('#distrubance_payment_details').modal('hide');
        $('#modalBackdrop').hide();
    });

    $('#distrubance_payment_details').on('hidden.bs.modal', function() {
        $('#modalBackdrop').hide();
    });
</script>


<div id="distrubance_payment_details" class="modal fade" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document" style="max-width: 95vw; width: 1050px;">
        <div class="modal-content" style="margin: auto; width: 100%; max-width: 100%;background-color: #F9F7FE;">
            <div class="modal-header d-flex align-items-center border-0" style="background-color: #F9F7FE;">
                <a class="back_anchor me-2" href="javascript:void(0);" data-bs-dismiss="modal">
                    <span class="bi bi-chevron-left report-header-icon" role="button"></span>
                </a>
                <span class="report-header-title flex-grow-1 text-start">
                    Non-Continuing Student Balance Details
                </span>
            </div>

            <div class="modal-body" style="overflow-y:auto;">
                <div id="modal-loader" style="display: none;"></div>
                <div id="content_fees_student"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal" id="search">Close</button>
            </div>
        </div>
    </div>
</div>
<style>
    .dt-buttons {
        margin-top: 11px !important;
    }

    .fee_summary {
        margin-bottom: 18px;
    }

    .search-box {
        margin-bottom: 10px;
    }

    .dt-search {
        margin-top: 15px;
    }

    .progress {
        width: 93%;
        margin: 0 auto;
    }

    .fw-bold {
        margin-left: 40px !important;
    }

    .table-responsive tr,
    .table-responsive td,
    .table-responsive th {
        max-width: 100%;
    }

    .table-bordered td {
        text-align: center !important;
    }

    .table-bordered td:first-child {
        text-align: left !important;
    }

    .table-bordered th {
        white-space: nowrap !important;
    }
</style>